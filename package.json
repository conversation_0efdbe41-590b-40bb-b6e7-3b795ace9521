{"name": "ai-prompt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:development": "cross-env NEXT_PUBLIC_API_ENV=development next dev", "dev:test": "cross-env NEXT_PUBLIC_API_ENV=test next dev", "dev:pro": "cross-env NEXT_PUBLIC_API_ENV=newProduction next dev", "dev:preTest": "cross-env NEXT_PUBLIC_API_ENV=preTest next dev", "dev:newProduction": "cross-env NEXT_PUBLIC_API_ENV=newProduction next dev", "build": "cross-env NEXT_PUBLIC_API_ENV=production next build", "build:production": "cross-env NEXT_PUBLIC_API_ENV=production next build", "build:test": "cross-env NEXT_PUBLIC_API_ENV=test next build", "build:dev": "cross-env NEXT_PUBLIC_API_ENV=development next build", "build:pre": "cross-env NEXT_PUBLIC_API_ENV=pre next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "dependencies": {"@ant-design/cssinjs": "^1.17.2", "@ant-design/icons": "^5.0.1", "@antv/g2plot": "^2.3.32", "@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fingerprintjs/fingerprintjs": "^4.6.1", "@flowgram.ai/eslint-config": "0.1.24", "@flowgram.ai/free-container-plugin": "0.1.24", "@flowgram.ai/free-layout-editor": "0.1.24", "@flowgram.ai/free-lines-plugin": "0.1.24", "@flowgram.ai/free-node-panel-plugin": "0.1.24", "@flowgram.ai/free-snap-plugin": "0.1.24", "@flowgram.ai/minimap-plugin": "0.1.24", "@flowgram.ai/ts-config": "0.1.24", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.5.2", "@next/bundle-analyzer": "^14.2.3", "@q/easy-form-components": "^0.0.14", "@q/easy-render": "^0.0.20", "@q/flowgram.ai.free-layout-editor": "^0.0.9", "@q/flowgram.ai.free-lines-plugin": "^0.0.9", "@q/gui": "^0.0.73", "@svgr/webpack": "^8.1.0", "@types/fabric": "^5.3.9", "@types/node": "18.15.12", "@types/react": "18.0.37", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.0.11", "@uiw/react-json-view": "^2.0.0-alpha.32", "@xyflow/react": "^12.3.4", "antd": "^5.16.5", "antd-img-crop": "^4.21.0", "axios": "^1.6.2", "blueimp-md5": "^2.19.0", "browser-tool": "1.2.2", "compare-versions": "^6.1.0", "cron-builder-ts": "^1.0.5", "cron-parser": "^4.9.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "css-loader": "^7.1.2", "eslint": "8.38.0", "eslint-config-next": "13.3.0", "express": "^4.18.2", "fabric": "^6.3.0", "he": "^1.2.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "http-proxy-middleware": "^2.0.6", "husky": "^9.1.5", "jspdf": "^3.0.0", "marked": "^15.0.7", "mermaid": "^11.4.1", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "next": "13.3.0", "ramda": "^0.29.0", "re-resizable": "^6.9.11", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "18.2.0", "react-easy-crop": "^5.0.6", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "15.5.0", "react-transition-group": "^4.4.5", "recoil": "^0.7.7", "rehype-mathjax": "4.0.3", "rehype-raw": "^7.0.0", "remark-gfm": "^3.0.1", "remark-math": "5.1.1", "sass": "^1.69.5", "slate": "^0.117.2", "slate-dom": "^0.116.0", "slate-history": "^0.113.1", "slate-react": "^0.117.2", "state": "^0.2.0", "style-loader": "^4.0.0", "styled-components": "^6.1.18", "textarea-caret": "^3.1.0", "typescript-plugin-css-modules": "^5.1.0", "uuid": "^9.0.1", "vditor": "^3.9.6", "waterfalljs-layout": "^0.1.0"}, "devDependencies": {"@types/blueimp-md5": "^2.18.2", "@types/crypto-js": "^4.2.2", "@types/he": "^1.2.3", "@types/lodash-es": "^4.17.12", "@types/next": "^9.0.0", "@types/ramda": "^0.29.0", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-transition-group": "^4.4.10", "@types/uuid": "^10.0.0", "ace-builds": "^1.36.3", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "postcss": "^8.4.33", "react-ace": "^12.0.0", "tailwindcss": "^3.4.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}}