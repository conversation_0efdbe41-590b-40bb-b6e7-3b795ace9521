/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.zh-cn", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"数组",
		"布尔值",
		"类",
		"常数",
		"构造函数",
		"枚举",
		"枚举成员",
		"事件",
		"字段",
		"文件",
		"函数",
		"接口",
		"键",
		"方法",
		"模块",
		"命名空间",
		"Null",
		"数字",
		"对象",
		"运算符",
		"包",
		"属性",
		"字符串",
		"结构",
		"类型参数",
		"变量",
		"{0} ({1})",
	]
});