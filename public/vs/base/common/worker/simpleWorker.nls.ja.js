/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.ja", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"配列",
		"ブール値",
		"クラス",
		"定数",
		"コンストラクター",
		"列挙型",
		"列挙型メンバー",
		"イベント",
		"フィールド",
		"ファイル",
		"関数",
		"インターフェイス",
		"キー",
		"メソッド",
		"モジュール",
		"名前空間",
		"NULL",
		"数値",
		"オブジェクト",
		"演算子",
		"パッケージ",
		"プロパティ",
		"文字列",
		"構造体",
		"型パラメーター",
		"変数",
		"{0} ({1})",
	]
});