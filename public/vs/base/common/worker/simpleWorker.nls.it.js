/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.it", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"matrice",
		"valore booleano",
		"classe",
		"costante",
		"costruttore",
		"enumerazione",
		"membro di enumerazione",
		"evento",
		"campo",
		"file",
		"funzione",
		"interfaccia",
		"chiave",
		"metodo",
		"modulo",
		"spazio dei nomi",
		"Null",
		"numero",
		"oggetto",
		"operatore",
		"pacchetto",
		"proprietà",
		"stringa",
		"struct",
		"parametro di tipo",
		"variabile",
		"{0} ({1})",
	]
});