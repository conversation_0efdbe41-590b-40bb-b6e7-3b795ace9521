/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.ru", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"массив",
		"логическое значение",
		"класс",
		"константа",
		"конструктор",
		"перечисление",
		"элемент перечисления",
		"событие",
		"поле",
		"файл",
		"функция",
		"интерфейс",
		"ключ",
		"метод",
		"модуль",
		"пространство имен",
		"NULL",
		"число",
		"объект",
		"оператор",
		"пакет",
		"свойство",
		"строка",
		"структура",
		"параметр типа",
		"Переменная",
		"{0} ({1})",
	]
});