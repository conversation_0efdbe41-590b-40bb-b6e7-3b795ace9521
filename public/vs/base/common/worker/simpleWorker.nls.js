/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

/*---------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/
define("vs/base/common/worker/simpleWorker.nls", {
	"vs/base/common/platform": [
		"_"
	],
	"vs/editor/common/languages": [
		"array",
		"boolean",
		"class",
		"constant",
		"constructor",
		"enumeration",
		"enumeration member",
		"event",
		"field",
		"file",
		"function",
		"interface",
		"key",
		"method",
		"module",
		"namespace",
		"null",
		"number",
		"object",
		"operator",
		"package",
		"property",
		"string",
		"struct",
		"type parameter",
		"variable",
		"{0} ({1})"
	]
});