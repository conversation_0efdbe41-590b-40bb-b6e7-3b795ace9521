/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.ko", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"배열",
		"부울",
		"클래스",
		"상수",
		"생성자",
		"열거형",
		"열거형 멤버",
		"이벤트",
		"필드",
		"파일",
		"함수",
		"인터페이스",
		"키",
		"메서드",
		"모듈",
		"네임스페이스",
		"Null",
		"숫자",
		"개체",
		"연산자",
		"패키지",
		"속성",
		"문자열",
		"구조체",
		"형식 매개 변수",
		"변수",
		"{0}({1})",
	]
});