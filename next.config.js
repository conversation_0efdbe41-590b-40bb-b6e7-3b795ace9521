/*
 * @Author: yh
 * @Date: 2024-04-23 15:12:21
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-01-15 15:53:23
 * @FilePath: \prompt-web\next.config.js
 */
const apiProxyConfig = require('./api.proxy.config');

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

// 设置后端接口环境
const apiENV = process.env.NEXT_PUBLIC_API_ENV ?? 'test'
const rewriteList = apiProxyConfig[apiENV]

/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        // 为所有路由启用CORS
        source: '/:path((?!knowledge_api).*)?',
        // source: '/(.*)',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          // { key: 'Access-Control-Allow-Origin', value: 'https://test.chat.di.qihoo.net' },
          // { key: 'Access-Control-Allow-Origin', value: 'https://chat.di.qihoo.net' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version' },
        ],
      },
    ]
  },
  // process.env.NEXT_PUBLIC_API_ENV != 'development' || process.env.NEXT_PUBLIC_API_ENV == 'undefined'
  reactStrictMode: false,
  output: 'standalone',
  async rewrites() {
    return rewriteList;
  },
  experimental: {
    proxyTimeout: 60 * 10 * 1000, // 60秒超时
  },
  webpack(config, {isServer}) {
    // 对 JavaScript 和 CSS 文件名添加哈希值
    if (!isServer) {
      config.output.filename = 'static/chunks/[name].[contenthash].js';
    }
    // https://react-svgr.com/docs/next/
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.('.svg'),
    )

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url

        // resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: /url/,
        use: [{
          loader: '@svgr/webpack',
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: 'preset-default',
                  params: {
                    overrides: {
                      removeViewBox: false
                    }
                  }
                },
                {
                  name: 'removeAttrs',
                  params: {
                    attrs: ['fill', 'stroke', 'class']
                  }
                },
                {
                  name: 'prefixIds',
                  active: true
                }
              ]
            },
            svgProps: { fill: 'currentColor' },
          }
        }],
      },
    )

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i

    return config
  }
}

module.exports = withBundleAnalyzer(nextConfig)
