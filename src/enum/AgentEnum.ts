/*
 * @Author: yh
 * @Date: 2024-05-06 20:01:57
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-01-17 10:55:52
 * @FilePath: \prompt-web\src\enum\AgentEnum.ts
 */
export enum FlowType {
    TOOLS = 'tools', // knowledge，function_call 类型统一到tools下，自己定义，后端接口返回中无此类型
    QUESTION = 'question',
    KNOWLEDGE = 'knowledge',
    FUNCTION_CALL = 'function_call',
    TOOL_RESPONSE = 'tool_response',
    ANSWER = 'answer',
    FOLLOW_UP = 'follow_up',
    CONFIRMED = 'confirmed',
}

// 智能体对话走老版本逻辑agentid列表
const agentSubmitList = () => {
    const testChatList = ['353', '357', '265', '1052']
    const proChatList = [
        '29', // 小红书文案(加强版)
        '434', // 旅行规划师
        '436', // 小红书文案写手
        '1074', // 小红书文案
        '1429', // 产品演示使用
        '1432', // 产品演示使用
        '1610', // 产品演示使用
    ]
    if(process.env.NEXT_PUBLIC_API_ENV == undefined || process.env.NEXT_PUBLIC_API_ENV == 'test' || process.env.NEXT_PUBLIC_API_ENV == 'development') {
        return testChatList
    }
    return proChatList
    return ['29', '357', '265', '353', '434', '436', '1074', '1252', '1429', '1432', '1610', '697']
}

export const agentChatList = agentSubmitList()
