/*
 * @Author: yh
 * @Date: 2025-01-02 10:07:12
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-02-07 14:52:17
 * @FilePath: \prompt-web\src\atoms\agentDetailAtoms.ts
 */
import { atom } from "recoil";

export const agentDetailAtom = atom({
  key: "agentDetailAtom",
  default: {
    agent_id: "",
    publish_status: null,
    fork_nums: null,
    template_check_status: null,
    template_check_id: null,
    params: {
      system_info: {
        name: "",
        icon: "",
        description: "",
      },
      basic_info: {
        prompt: "",
        llm_model: 1,
        llm_param: {
          top_k: 1,
          top_p: 0.6,
          temperature: 0.4,
          max_tokens: 2048,
          num_beams: 1,
          repetition_penalty: 1.05,
        },
        greeting_message: "",
        greeting_question: [],
        is_suggestion: 1,
        suggestion: "",
        audio_status: 2, // 语音功能
        audio_input_status: 2, // 语音输入功能
        voice_type: 1, // 声音类型
        memory_status: 3, // 记忆设置 记忆状态，1表示启用记忆但不启用图谱，2表示启用记忆和图谱，3表示都不启用。目前用1和3
        memory_setting: ''
      },
      flow: [],
      knowledge: [],
      plugin: [],
    },
  },
});

export const agentDetailErrorAtom = atom({
  key: "agentDetailError",
  default: false,
});

export const agentChattingAtom = atom({
  key: "agentChatting",
  default: false,
});
