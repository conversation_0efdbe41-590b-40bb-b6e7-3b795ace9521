import {
    atom
} from 'recoil';

export const choosedApiAtom = atom({
    key: "choosedApiAtom",
    default: {
        "api_name": "",
        "api_desc": "",
        "id": 0, // 动作 id
        "api_id": 0, // 插件 id
        "api_version_id": 0, // 版本 id
        "template_id": 0,
        "url": "",
        "method": "GET",
        "body_type": 'json', // json formdata
        "headers": new Array(),
        "body": new Array(),
        "querystring": new Array(),
        "response_filter": new Array(),
        "test_result": {},
        "filter_result": {},
        "template_check_status": -1,
        "auth_id": 0,
        "is_auth": 2, // 2 不需要授权 1 需要授权,
        "is_public": false, // 插件是否公开
        "publish_version": 0 // 插件版本
    }
})

export const choosedStampAtom = atom({
    key: "choosedStampAtom",
    default: 0
})

export const useApiStampAtom = atom({
    key: "useApiStampAtom",
    default: 0
})

// functioncall 模块选择的 API列表
export const fcSelectedFCApiListAtom = atom({
    key: "fcSelectedFCApiListAtom",
    default: []
})
// 点击查看的当前API
export const fcCurrentCheckFcApiAtom = atom({
    key: "fcCurrentCheckFcApiAtom",
    default: {
        "api_name": "",
        "api_desc": "",
        "id": 0,
        "template_id": 0,
        "url": "",
        "method": "GET",
        "body_type": 'json', // json formdata
        "headers": new Array(),
        "body": new Array(),
        "querystring": new Array(),
        "response_filter": new Array(),
        "test_result": {},
        "filter_result": {},
        "template_check_status": -1
    }
})
// 点击确定，选定API
export const fcChooseStampAtom = atom({
    key: "fcChooseStampAtom",
    default: 0
})
// 点击使用，选定API
export const fcUseApiStampAtom = atom({
    key: "fcUseApiStampAtom",
    default: 0
})

export const choosedFlowAtom = atom({
    key: "choosedFlowAtom",
    default: {
        "flow_plugin_id": 0,
        "isAddBlock": false,
        "flow_plugin_name": '',
        "flow_params_key_mapping": []
    }
})

export const choosedFlowStampAtom = atom({
    key: "choosedFlowStampAtom",
    default: 0
})

export const choosedCardAtom = atom({
    key: "choosedCardAtom",
    default: {
        "card_url": 0,
        "isAddBlock": false,
        "card_name": '',
        "card_image": '',
        "card_data": '',
        "card_id": ''

    }
})

export const choosedCardStampAtom = atom({
    key: "choosedCardStampAtom",
    default: 0
})

export const mcpToolListAtom = atom({
 	key:"mcpToolListAtom",
	default: []
})