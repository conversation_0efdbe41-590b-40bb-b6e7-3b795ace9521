import {
    atom
} from 'recoil';

export const activeBlockIdAtom = atom({
    key: "activeBlockIdAtom",
    default: ''
})

export const blocksAtom = atom({
    key: "blocksAtom",
    default: {}
})
// 专门处理属性变化
export const blocksPropertyAtom = atom({
    key: "blocksPropertyAtom",
    default: {}
})
export const edgesAtom = atom({
    key: "edgesAtom",
    default: []
})

export const loopConfigsAtom = atom({
    key: "loopConfigsAtom",
    default: {}
})

export const addApiDataAtom = atom({
    key: "addApiDataAtom",
    default: {}
})

// 拖拽的模块类型
export const dragTypeAtom = atom({
    key: "dragTypeAtom",
    default: ''
})

// 复制节点
export const cloneBlockAtom = atom({
    key: "cloneBlockAtom",
    default: {}
})

// 试运行激活
export const isRunTestStatusAtom = atom({
    key: "isRunTestStatusAtom",
    default: false
})

// 添加Agent节点
export const addAgentDataAtom = atom({
    key: "addAgentDataAtom",
    default: {}
})

// 是否选中编辑
export const isEditActiveAtom = atom({
    key: "isEditActiveAtom",
    default: false
})
// 删除节点 边
export const deleteInfoAtom = atom({
    key: "deleteInfoAtom",
    default: {
        node: [],
        edge: []
    }
})

// 在边上添加广场里的节点 记录边
export const curLineInfoAtom = atom({
    key: "curLineInfoAtom",
    default: {
        sourceId: "",
        targetId: '',
        sourcePortId: '',
        position: {}
    }
})
// 运行中 调试中
export const isDisabledAtom = atom({
    key: "isDisabledAtom",
    default: false
})

// 删除边 添加边的节点 需要去更新port
export const updatePortBlockAtom = atom({
    key: "updatePortBlockAtom",
    default: ''
})

// 节点运行结果
export const runNodeResultAtom = atom({
    key: "runNodeResultAtom",
    default: {}
    /*
    {
       '123456': {
          status: 3,
          type: '',
          inputs: {}，
          result: {}
       }
    }
    */
})

// 被删除的分支id
export const deletedBranchInfoAtom = atom({
    key: "deletedBranchInfoAtom",
    default: {
        blockid: '',
        branch_id: '',
        branch_index: 0
    }
})

// 显示隐藏提示
export const showDropTipsAtom  = atom({
    key: "showDropTipsAtom",
    default: {}
})

export const createEdgeInfoAtom = atom({
    key: "createEdgeInfoAtom",
    default: {
        sourceNodeID: '',
        targetNodeID: '',
    }
})

// 当前缩放比
export const playgroundAtom = atom({
    key: "playgroundAtom",
    default: {
        zoom: 1,
        scrollX: 0,
        scrollY: 0
		}
})

export const createNextEdgeInfoAtom = atom({
    key: "createNextEdgeInfoAtom",
    default: {
        sourceNodeID: '',
        targetNodeID: '',
    }
})