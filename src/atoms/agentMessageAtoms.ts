/*
 * @Author: zx
 * @Date: 2024-12-05 18:05:43
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-12-10 18:15:52
 * @FilePath: \prompt-web\src\atoms\agentMessageAtoms.ts
 */
import { any } from "ramda";
import { atom } from "recoil";

// 对话id
export const agentSessionId = atom({
  key: 'agentSessionIdAtom',
  default: ''
})

// 对话记录列表
export const agentChatRecord = atom({
  key: 'agentChatRecordAtom', 
  default: [] as any
})

// 对话记录page
export const agentRecordPage = atom({
  key: 'agentRecordPageAtom', 
  default: {
    page_num: 1,
    page_size: 20
  }
})

export const agentRecordHasMore = atom({
  key: 'agentRecordHasMoreAtom', 
  default: true
})

export const agentRecordLoading = atom({
  key: 'agentRecordLoadingAtom', 
  default: false
})

// 小红书，旅行计划 !sse提示问题
export const agentRuestionBlock = atom({
  key: 'agentRuestionBlockAtom', 
  default: {} as any
})

// sse 对话建议列表
export const agentSuggestionList = atom({
  key: 'agentSuggestionListAtom', 
  default: [] as any
})

// sse 是否展示自动建议
export const agentShowSuggest = atom({
  key: 'agentShowSuggestAtom', 
  default: false
})

// 当前对话状态
export const agentIsChatting = atom({
  key: 'agentIsChattingAtom',
  default: false
})

// 停止对话状态
export const agentStopChatting = atom({
  key: 'agentStopChattingAtom',
  default: false
})

// 智能体详情是否不可编辑
export const isEnEditable = atom({
  key: 'agentSIsEnEditableAtom',
  default: false
})