import { atom, selector } from "recoil";
import * as constants from "@/constants/appConstants";

export interface UserInfo {
    user_id?: string | number,
    username?: string,
    email?: string,
    phone_number?: string,
    system_role?: string | number, // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
    src?: string,
  }

// 用户信息
export const userInfoAtom = atom<UserInfo | undefined>({
    key: "userInfo",
    default: undefined
});
