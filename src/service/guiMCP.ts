import { fetchData } from '@/utils/fetch';

// 获取 GUI MCP 卡片列表
export const reqGuiMCPCardList = async ({
  flow_id,
  block_key,
}: {
  flow_id: number;
  block_key: number;
}) => {
  try {
    const response = await fetchData(
      '/api/v2/nami_ui_mcp/list',
      {
        flow_id,
        block_key,
      },
      'get'
    );
    
    // 安全地解析列表中每个卡片的 form_data JSON 字符串
    if (Array.isArray(response)) {
      return response.map(card => {
        if (card && card.form_data) {
          try {
            if (typeof card.form_data === 'string') {
              card.form_data = JSON.parse(card.form_data);
            }
            // 如果已经是对象，则不需要解析
          } catch (parseError) {
            console.warn(`解析卡片 ${card.id} 的 form_data JSON 失败，使用原始数据:`, parseError);
            // 如果解析失败，保持原始数据，避免程序崩溃
          }
        }
        return card;
      });
    }
    
    return response;
  } catch (error) {
    console.error('获取卡片列表失败:', error);
    throw error;
  }
};

// 创建 GUI MCP 卡片
export const createGuiMCPCard = ({
  flow_id,
  block_key,
  title,
  form_data,
}: {
  flow_id: number;
  block_key: number;
  title?: string;
  form_data: any;
}) => {
  return fetchData(
    `/api/v2/nami_ui_mcp/create`,
    {
      flow_id,
      block_key,
      title: title || '自定义卡片',
      form_data: JSON.stringify(form_data),
    },
    'post'
  );
};

// 获取 GUI MCP 卡片详情
export const reqGuiMCPCardDetail = async ({ id }: { id: number }) => {
  try {
    const response = await fetchData(`/api/v2/nami_ui_mcp/${id}`, {}, 'get');
    
    // 安全地解析 form_data JSON 字符串
    if (response && response.form_data) {
      try {
        if (typeof response.form_data === 'string') {
          response.form_data = JSON.parse(response.form_data);
        }
        // 如果已经是对象，则不需要解析
      } catch (parseError) {
        console.warn('解析 form_data JSON 失败，使用原始数据:', parseError);
        // 如果解析失败，保持原始数据，避免程序崩溃
      }
    }
    
    return response;
  } catch (error) {
    console.error('获取卡片详情失败:', error);
    throw error;
  }
};

// 更新 GUI MCP 卡片
export const updateGuiMCPCard = ({
  id,
  flow_id,
  block_key,
  title,
  form_data,
}: {
  id: number;
  flow_id: number;
  block_key: number;
  title?: string;
  form_data: any;
}) => {
  const payload: any = {
    flow_id,
    block_key,
    form_data: JSON.stringify(form_data),
  };
  
  // 只有当 title 存在时才添加到 payload 中
  if (title !== undefined) {
    payload.title = title;
  }
  
  return fetchData(
    `/api/v2/nami_ui_mcp/${id}`,
    payload,
    'put'
  );
};

// 删除 GUI MCP 卡片
export const deleteGuiMCPCard = ({ id }: { id: number }) => {
  return fetchData(`/api/v2/nami_ui_mcp/${id}`, {}, 'delete');
};
