import { fetchData } from '@/utils/fetch';

// member
// addmember
// export const reqAddMember: (params:any) => Promise<any> = (params) => {
//   return fetchData(
//     '/api/member/add',
//     params,
//     'post'
//   );
// }
// addwaiwangmember
export const reqAddMember: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/member/add_member',
    params,
    'post'
  );
}
// changerole
export const reqChangeRole: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/member/update',
    params,
    'post'
  );
}
// deletemember
export const reqDeleteMember: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/member/delete',
    params,
    'post'
  );
}
// memberList
export const reqMemberList: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/member/list',
    params,
    'get'
  );
}
// roleList
export const reqRoleList: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/member/role/list',
    {},
    'get'
  );
}
// domainList
// export const reqDomainList: (params:any) => Promise<any> = (params) => {
//   return fetchData(
//     '/api/member/domain/list',
//     params,
//     'get'
//   );
// }
// waiwangdomainList
export const reqDomainList: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/member/account/list',
    params,
    'get'
  );
}