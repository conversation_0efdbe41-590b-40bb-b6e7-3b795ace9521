
import { fetchData } from '@/utils/fetch';
// 审核列表
export const reqMarkAuditsList: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/market-audits',
                                params,
                                'get'
                );
}
/**
 * 
 * @param params 
 * status	int	审核状态， 固定为 3 拒绝
 * status	int	审核状态， 固定为 2 同意
 * @returns 
 */

export const reqAudit: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/market-audits/'+params.id,
                                params,
                                'put'
                );
}