import { fetchData } from '@/utils/fetch';

// 渠道列表
export const reqChannelList: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/channel/list',
      params,
      'post'
    );
}

// 新建渠道
export const reqAddChannel: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/channel/create',
      params,
      'post'
    );
}

// 渠道编辑
export const reqEditChannel: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/channel/rename',
      params,
      'put'
    );
}

// 分类管理列表
export const reqClassifyList: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/channel/listTag',
      params,
      'post'
    );
}

// 新建分类标签(一级分类 & 二级分类)
export const reqAddClassify: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/channel/createTag',
      params,
      'post'
    );
}

// 编辑分类标签(一级分类 & 二级分类)
export const reqEditClassify: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/channel/renameTag',
      params,
      'put'
    );
}

// 渠道数据
export const reqDataList: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/publish_data',
    params,
    'post'
  );
}

// 分类查询渠道下已发布的agent
export const reqAgentClassify: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agents/channel_agents_admin',
    params,
    'post'
  );
}
