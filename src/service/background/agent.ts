import { fetchData } from '@/utils/fetch';

// agent列表
export const reqAgentList: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/admin_agents/published_agents',
      params,
      'post'
    );
}

// agent发布信息
export const reqAgentPublish: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/admin_agents/agent_publish_channels',
    params,
    'post'
  );
}

// 取消agent渠道下发布
export const reqUnpublishedChannels: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/admin_agents/unpublished_channels',
    params,
    'post'
  );
}

//agent发布开关管理
export const reqAgentSwitch: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/admin_agents/agent_publish_manage',
    params,
    'post'
  );
}

//获取agent发布开关状态
export const reqGetAgentPublishStatus: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/admin_agents/get_agent_publish',
    params,
    'get'
  );
}


