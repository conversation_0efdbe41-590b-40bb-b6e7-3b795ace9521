import { fetchData } from '@/utils/fetch';
// 项目列表
export const reqAdminList: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/admin/list',
                                params,
                                'get'
                );
}
export const reqEditAdmin: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/admin/' + params.id,
                                params,
                                'put'
                );
}