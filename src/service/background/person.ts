
import { fetchData } from '@/utils/fetch';
// 人员列表
export const reqSystemMemberList: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/member/system/list',
                                params,
                                'get'
                );
}


export const reqDeleteMember: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/member/system/delete?team_id=' + params.team_id,
                                params,
                                'post'
                );
}
export const reqSearchAccount: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/member/account/list',
                                params,
                                'post'
                );
}
export const reqAddMember: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/member/system/add_member?team_id='+params.team_id,
                                params,
                                'post'
                );
}
export const updateSysRole: (params: any) => Promise<any> = (params) => {
                return fetchData(
                                '/api/member/system/update?team_id=' + params.team_id,
                                params,
                                'post'
                );
}