/*
 * @Author: yh
 * @Date: 2025-04-10 17:09:07
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-04-10 17:09:21
 * @FilePath: \prompt-web\src\service\background\agentsort.ts
 */
import { fetchData } from '@/utils/fetch';


//agent排序查询列表
export const reqChannelAgentList: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/channel_agent_list',
    params,
    'get'
  );
}



//修改热度
export const reqModifyForkNums: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/modify_fork_nums',
    params,
    'post'
  );
}

//修改自定义排序
export const reqModifyWeight: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/modify_weight',
    params,
    'post'
  ); 
}


