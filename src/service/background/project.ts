import { fetchData } from '@/utils/fetch';

// 项目列表
export const reqProjectList: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/team/team_list',
      params,
      'get'
    );
}

// 添加项目
export const reqAddProject: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/team',
      params,
      'post'
    );
}

// 编辑项目
export const reqUpdateProject: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/team/'+params.id,
      params,
      'put'
    );
}

// 重命名项目
export const reqRenameProject: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/team/rename',
      params,
      'post'
    );
}

// 删除项目
export const reqDeleteProject: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/team/'+params.id,
      params,
      'delete'
    );
}

//审核列表
export const reqAuditeList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/team-audits',
    params,
    'get'
  );
}
//
export const reqAudite: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/team-audits/'+params.id,
    params,
    'put'
  );
}

//用户申请项目
export const reqApplyProject: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/team-audits/add',
    params,
    'post'
  );
}

// 获取供应商列表
export const reqGetModelProvider: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/provider/model-providers',
    params,
    'get'
  );
}

// 获取供应商列下的模型列表
export const reqGetModelProviderModels: (providerName:any) => Promise<any> = (providerName) => {
  return fetchData(
    `/api/provider/model-providers/models/${providerName}/models`,
    {},
    'get'
  );
}

//添加供应商认证信息
export const reqAddModelProvider: (providerName: string,params: any) => Promise<any> = (providerName,params) => {
  return fetchData(
    `/api/provider/model-providers/${providerName}`,
    params,
    'post'
  );
}

// 删除供应商认证信息
export const reqDeleteModelProvider: (providerName: string) => Promise<any> = (providerName) => {
  return fetchData(
    `/api/provider/model-providers/${providerName}`,
    {},
    'delete'
  );
}

//添加供应商模型信息
export const reqAddModelProviderModels: (providerName: string,params: any) => Promise<any> = (providerName,params) => {
  return fetchData(
    `/api/provider/model-providers/${providerName}/models`,
    params,
    'post'
  );
}

// 删除供应商下面的模型
export const reqDeleteModelProviderModel: (providerName: string,params:any) => Promise<any> = (providerName,params) => {
  return fetchData(
    `/api/provider/model-providers/${providerName}/models`,
    params,
    'delete'
  );
}

// 获取配置的供应商的参数填写数据
export const reqGetModelProviderParams: (providerName:any) => Promise<any> = (providerName) => {
  return fetchData(
    `/api/provider/model-providers/models/${providerName}/credentials`,
    {},
    'get'
  );
}

// 获取配置的供应商的模型参数填写数据
export const reqGetAddModelParams: (providerName:any,params: any) => Promise<any> = (providerName,params) => {
  return fetchData(
    `/api/provider/model-providers/${providerName}/models/credentials`,
    params,
    'get'
  );
}

// 获取团队下模型列表
export const reqGetTeamModels: (modelType:any) => Promise<any> = (modelType) => {
  return fetchData(
    `/api/provider/model-providers/models/model-types/${modelType}`,
    {},
    'get'
  );
}

// 获取模型的默认配置参数
export const reqGetModelsDefaultParams: (providerName:string, modelType:string) => Promise<any> = (providerName, modelType) => {
  return fetchData(
    `/api/provider/model-providers/${providerName}/models/parameter-rules`,
    {model: modelType},
    'get'
  );
}

// SRC 项目来源列表
export const reqGetSrcList: () => Promise<any> = () => {
  return fetchData(
    '/api/team/src_list',
    {},
    'get'
  );
}

