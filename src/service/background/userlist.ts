/*
 * @Author: zx
 * @Date: 2024-12-23 14:16:07
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-12-30 17:28:06
 * @FilePath: \prompt-web\src\service\background\userlist.ts
 */
import { fetchData } from '@/utils/fetch';

//获取外网用户列表
export const reqGetOutnetUserList: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/external_user/list',
      params,
      'get'
    );
}


  //用户白名单状态更新
  export const reqUpdateUserStatus: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/external_user/update',
      params,
      'post'
    );
}

//根据手机号/邮箱/用户名查询360用户信息
export const reqGetUserInfo: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/external_user/search',
      params,
      'post'
    );
}

//添加外网白名单用户
export const reqAddUser: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/external_user/add',
      params,
      'post'
    );
}

//添加外网白名单列表  按文件添加
export const reqAddUserFile: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/external_user/add_user_file',
      params,
      'post'
    );
}
