import { fetchData } from '@/utils/fetch';
import exp from 'constants';
import he from 'he';

// agent列表
export const reqAgentList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents',
    params,
    'get'
  );
}

// agent 发现列表页面
// export const reqAgentDiscoveryList: (params:any) => Promise<any> = (params) => {
//   return fetchData(
//     '/api/v2/discovery',
//     params,
//     'get'
//   );
// }

export const reqAgentDiscoveryList: (params: any) => Promise<any> = (params) => {
  const urlParams = new URLSearchParams();
  for (const key in params) {
    if (Array.isArray(params[key])) {
      params[key].forEach((value: any) => urlParams.append(key, value));
    } else {
      urlParams.append(key, params[key]);
    }
  }
  return fetchData(
    `/api/v2/discovery?${urlParams.toString()}`,
    {},
    'get'
  );
}

// agent 发现列表Tags 列表
export const reqAgentTagList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/prompt-tags',
    params,
    'get'
  );
}

// 创建agent
export const reqCreateAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/create',
    params,
    'post'
  );
}

// ai 获取插件
export const reqAiAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/suggest_plugin',
    params,
    'post'
  );
}

// 更新agent
export const reqUpdateAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/update',
    params,
    'post'
  );
}

// ai创建智能体
export const reqAiCreateAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/auto_create_agent',
    params,
    'post'
  );
}


// 删除agent
export const reqDeleteAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/v2/agents/delete`,
    params,
    'delete'
  );
}

// 复制agent
export const reqCopyAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/copy',
    params,
    'post'
  );
}

// agent 详情
export const reqAgentDetail: (params: any) => Promise<any> = async (params) => {
  const res = await fetchData(
    '/api/v2/agents/detail',
    params,
    'get'
  )
  if (typeof res === 'string') {
    return he.decode(res);
  } else if (typeof res === 'object' && res !== null) {
    return JSON.parse(he.decode(JSON.stringify(res)));
  }

  return res;
}

// agent 编辑
export const reqEditAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/update',
    params,
    'post'
  );
}

// agent 触发词
export const reqAgentTriggerWords: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/get_agent_trigger_words',
    params,
    'get'
  );
}
// 发布-没有地方使用，可删除
export const reqPublishAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/publish',
    params,
    'post'
  );
}

// 版本发布
export const reqAgentPackPublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/pack',
    params,
    'post'
  );
}

// 版本删除
export const reqAgentPackDelete: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/agent_version/delete',
    params,
    'post'
  );
}

// 取消发布
export const reqCancelPublishAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/unpublished',
    params,
    'post'
  );
}

// 发布详情
export const reqPublishAgentDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/publish_detail',
    params,
    'get'
  );
}

// 智能体从市场中复制
export const reqAgentsCopyMarket: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/copy_market',
    params,
    'post'
  );
}

// 智能体 提示词优化 - 弃用（改流式）
export const reqOptimiseWord: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/agents/ai_system_prompt',
    params,
    'post'
  );
}

// AI生成开场白
export const reqCreatePrologue: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/agents/ai_greeting',
    params,
    'post'
  );
}


//agent Preview列表
export const reqAgentPreviewList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/preview/list',
    params,
    'get'
  );
}

//agent Preview数据平台部规划
export const reqAgentPreviewPlan: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/preview/plan',
    params,
    'post'
  );
}

// 去除背景
export const reqAigcRemoval: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/public/aigc_removal',
    params,
    'post'
  );
}

// 文件上传
export const reqAgentChatUpload: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/file/upload',
    params,
    'post'
  )
}

// 渠道列表
export const reqGetChannelList: (params?: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/prompt-tags/list',
    params,
    'post'
  )
}

// 根据渠道获取标签列表
export const reqChannelTagList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/prompt-tags/listTag',
    params,
    'post'
  )
}

// 智能体发布 历史记录列表
export const reqAgentPublishedList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/agent_version',
    params,
    'post'
  )
}

// 智能体渠道下agentlist获取-前台
export const regChannelAgentList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/channel_agents',
    params,
    'post'
  )
}
// 后台 
export const regChannelAdminAgentList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/admin_agents/channel_agents',
    params,
    'post'
  )
}
//agent发布信息
export const reqAgentPublishChannels: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/agent_publish_channels',
    params,
    'post'
  );
}

//新渠道发布
export const reqNewPublishAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/publish',
    params,
    'post'
  );
}


//获取渠道标签列表
export const reqOperationChannelTagList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/listTag',
    params,
    'post'
  );
}

//更新渠道标签
export const reqUpdateChannelTag: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/channel-tag/updateTag',
    params,
    'post'
  );
}


//新取消发布

export const reqCancelPublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/unpublished_channels',
    params,
    'post'
  );
}


// agent撤销发布

export const reqCancelAgentPublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/unpublished',
    params,
    'post'
  );
}

//获取api密钥

export const reqGetApiKey: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/get_agent_api_key',
    params,
    'get'
  );
}


//获取agent的api调用详情

export const reqGetAgentApiDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/openapi_detail',
    params,
    'get'
  );
}

//设置openapi是否开启-只有智能体发布使用，已弃用
export const reqSetOpenApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/set_openapi_status',
    params,
    'post'
  );
}

//获取API调用列表
export const reqApiAuthList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/api_auth_list',
    params,
    'post'
  );
}

//创建api渠道秘钥
export const reqApiAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/api_auth',
    params,
    'post'
  );
}

//更新api秘钥状态-(删除, 启用)
export const reqApiAuthUpdate: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/api_auth_update',
    params,
    'post'
  );
}

//微信授权
export const reqWxAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/generate_authorization_link',
    params,
    'post'
  );
}

// Agent发布到推推AI工作台
export const reqApiTuiTuiAi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/channel_auth_set_ttai',
    params,
    'post'
  );
}


//微信获取重定向url
export const reqWxRedircet: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/weixin/redirect',
    params,
    'post'
  );
}

// 版本管理-生成新版本
export const reqAgentVersionManageAdd: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/generate',
    params,
    'post'
  );
}

// 版本管理-获取版本列表
export const reqAgentVersionManageList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/list',
    params,
    'get'
  );
}

// 版本管理-删除版本
export const reqAgentVersionManageDel: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/delete',
    params,
    'post'
  );
}

// 版本管理-获取当前已发布版本详情
export const reqAgentVersionManageOnlineVersion: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/published_info',
    params,
    'get'
  );
}

// 版本管理-版本发布, 取消发布
export const reqAgentVersionManagePublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/publish',
    params,
    'post'
  );
}

// 版本管理-版本描述信息更新
export const reqAgentVersionManageUpdateDesc: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/update_desc',
    params,
    'post'
  );
}

// 版本管理-使用此配置版本
export const reqAgentVersionManageUseSetting: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/restore',
    params,
    'post'
  );
}


//版本说明
export const reqSaveAgentVersionDesc: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/version_description',
    params,
    'post'
  );
}

//获取当前版本信息
export const reqAgentVersionInfo: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/version_description',
    params,
    'get'
  );
}





// 取消渠道授权
export const reqAgentChannelCancelAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    "/api/v2/agents/channel_auth_cancel",
    params,
    'post'
  )
}

// 渠道授权
export const reqAgentChannelSetAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    "/api/v2/agents/channel_auth_set",
    params,
    'post'
  )
}


//获取网站嵌入列表
export const reqAgentIfrmeList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/iframe_list',
    params,
    'get'
  );
}

//获取网站嵌入
export const reqGetAgentIframe: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/get_iframe',
    params,
    'get'
  );
}

//新建agent网站嵌入
export const reqAddAgentIframe: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/create_iframe',
    params,
    'post'
  );
}


//网站嵌入删除
export const reqDelAgentIframe: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/delete_iframe',
    params,
    'post'
  );
}


//域名删除
export const reqDelAgentUrl: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/delete_url',
    params,
    'post'
  );
}

//更新网站嵌入
export const reqUpdateAgentIframe: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/update_iframe',
    params,
    'post'
  );
}

//api调用与网站嵌入状态获取

export const reqGetAgentApiStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/get_agent_publish',
    params,
    'get'
  );
}









