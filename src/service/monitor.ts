/*
 * @Author: zx
 * @Date: 2024-11-12 14:56:09
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-11-13 14:53:13
 * @FilePath: \prompt-web\src\service\monitor.ts
 */
import { fetchData } from '@/utils/fetch';


// 服务监控柱状图
export const reqMonitorBar: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/service_monitoring/bar',
        params,
        'get'
    );
}
// 服务监控日志
export const reqMonitorLog: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/service_monitoring/log',
        params,
        'get'
    );
}
//服务监控详情
export const reqMonitorDetail: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/service_monitoring/process_log',
        params,
        'get'
    );
}

// 服务监控列表
export const reqMonitorList: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/service_monitoring/access/list',
        params,
        'get'
    );
}

// 服务监控甘特图
export const reqMonitorGantt: (params:any) => Promise<any> = (params) => {
    return fetchData(
        // '/api/service_monitoring/gantt/query',
        '/api/service_monitoring/gantt/query_by_log_id',
        params,
        'get'
    );
}

//服务监控总量
export const reqMonitorTeamBar: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/service_monitoring/team_bar',
        params,
        'get'
    );
}
