import { fetchData } from '@/utils/fetch';

// api列表
export const reqApiList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/list',
    params,
    'post'
  );
}

// api列表 V2
export const reqApiListV2: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/list',
    params,
    'post'
  );
}

// 插件详情 V2
export const reqApiDetailV2: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/detail',
    params,
    'get'
  )
}

// 官方api列表
export const reqOfficalApiList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/official_list',
    params,
    'get'
  );
}


// 发现列表
export const reqPublishApiList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/discovery_list',
    params,
    'post'
  );
}

// 发现列表 V2
export const reqPublishApiListV2: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/discovery_list',
    params,
    'post'
  )
}

// 创建插件动作
export const reqCreateApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/create',
    params,
    'post'
  );
}

// 创建api V2
export const reqCreateApiV2: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/create',
    params,
    'post'
  );
}

// api详情
export const reqGetApiDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/detail/' + params.api_id,
    {},
    'get'
  );
}

// 已发布api详情
export const reqGetPublishedApiDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/public_detail/' + params.api_id,
    {},
    'get'
  );
}

// 更新插件动作
export const reqUpdateApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/update',
    params,
    'put'
  );
}

// 更新api V2
export const reqRenameApiV2: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/update',
    params,
    'post'
  )
}

// 删除插件动作
export const reqDeleteApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/delete/' + params.api_id,
    {
      type: params.type
    },
    'delete'
  );
}

// 删除插件
export const reqDeleteApiV2: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/apis/v2/delete/${params.api_id}`,
    {},
    'delete'
  );
}

// 复制api
export const reqCopyApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/copy/' + params.api_id,
    {},
    'post'
  );
}

// 调试api
export const reqDebugApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/debug/'+params.api_id,
    params,
    'post'
  );
}

// 获取格式化result
export const reqGetFilterResponse: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/response_filtering/' + params.api_id,
    params,
    'post'
  );
}

// 重命名
export const reqReNameApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/rename',
    params,
    'post'
  );
}

// api已经公开选择的标签列表
export const reqGetPublishTagList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/discovery_tag_list',
    params,
    'get'
  );
}

// 发布api
export const reqPublishApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/publish/' + params.api_id,
    {},
    'post'
  );
}

// 发布详情
export const reqGetPublishApiDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/publish_detail/' + params.api_id,
    params,
    'get'
  );
}

// 取消发布
export const reqCancelPublishApi: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/cancel_publish/' + params.api_id,
    params,
    'post'
  );
}

// 自定义--发布后测试
export const reqDebugPublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/debug_publish',
    params,
    'post'
  );
}
// 发现--发布后测试
export const reqDebugPublic: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/debug_public',
    params,
    'post'
  );
}

// 发布/公开后 结果过滤
export const reqGetDebugFilterResponse: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/body_filter_publish',
    params,
    'get'
  );
}

// 文生文
export const reqGenerateWord: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/text_text',
    params,
    'get'
  )
}

// 文生图
export const reqGenerateImg: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/text_image',
    params,
    'get'
  )
}

// 文本翻译
export const reqGenerateEnglish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/apis/text_ename?text=${params.text}&type=plugin`,
    {},
    'get'
  )
}

// 图片上传
export const reqImageUpload: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/user/upload_image',
    params,
    'post'
  )
}
//创建agent时优化提示词
export const reqGenerateSystemPrompt: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/generate_system_prompt',
    params,
    'post'
  )
}

//产生开场白和开场问题
export const reqGenerateGreetingInfo: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/agents/generate_greeting_info',
    params,
    'post'
  )
}

// 插件关联智能体
export const reqPluginAddAgent: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/apis/api_agent?agent_id=${params.agent_id}&api_id=${params.api_id}&template_id=${params.template_id}&api_version=${params.api_version}`,
    params,
    'put'
  )
}

// 插件动作列表
export const reqPluginActionList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/action_list',
    params,
    'get'
  )
}

// 简略公开插件列表
export const reqPluginBriefList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/discovery_list_brief',
    params,
    'get'
  )
}

// 发布插件的详情
export const reqPublishPluginDetail: (api_id: string, is_action: number) => Promise<any> = (api_id, is_action) => {
  return fetchData(
    `/api/apis/v2/public_detail/${api_id}`,
    {
      is_action
    },
    'get'
  )
}

// 发布插件
export const reqPublishPlugin: (api_id: string) => Promise<any> = (api_id) => {
  return fetchData(
    `/api/apis/v2/publish/${api_id}`,
    {},
    'post'
  )
}

// 取消发布插件
export const reqCancelPublishPlugin: (api_id: string) => Promise<any> = (api_id) => {
  return fetchData(
    `/api/apis/v2/publish_cancel/${api_id}`,
    {},
    'post'
  )
}

// 检查插件是否需要授权
export const reqCheckPluginAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/check_api_auth',
    params,
    'get'
  )
}

// 插件动作关联 Agent/Flow
export const reqPluginAddAgentFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/api_to_relation',
    params,
    'post'
  )
}

// Agent/Flow 删除插件动作关联
export const reqDeletePluginAgentFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `api/apis/v2/api_to_relation`,
    params,
    'delete'
  )
}

// 创建触发动作
export const reqCreateTriggerAction: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/create_trigger',
    params,
    'post'
  )
}

// 更新触发动作
export const reqUpdateTriggerAction: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/update_trigger',
    params,
    'post'
  )
}

// 获取触发动作
export const reqGetTriggerAction: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/get_trigger',
    params,
    'get'
  )
}

// json/yaml格式创建插件
export const reqCreatePlugin: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/create_import',
    params,
    'post'
  )
}

// 设置-gui卡片
// 获取卡片列表
export const reqGuiCardList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/card/list',
    params,
    'get'
  )
}

// 卡片创建
export const reqGuiCardCreate: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/card/create',
    params,
    'post'
  )
}

// 卡片详情
export const reqGuiCardDetail: (id: any) => Promise<any> = (id) => {
  return fetchData(
    'api/card/detail/' + id,
    {},
    'get'
  )
}

// 卡片编辑
export const reqGuiCardUpdate: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/card/update',
    params,
    'post'
  )
}

// 卡片删除
export const reqGuiCardDelete: (id: any) => Promise<any> = (id) => {
  return fetchData(
    'api/card/delete/' + id,
    {},
    'DELETE'
  )
}

// curl 导入
export const reqCurlImport: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/perform_action_import_curl',
    params,
    'post'
  )
}

// curl 导出
export const reqCurlExport: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/apis/v2/perform_curl_import_action',
    params,
    'post'
  )
}

// 取消公开
export const reqCancelPublic: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/cancel_plugin_public',
    params,
    'post'
  )
}

// 新增/编辑/删除标签
export const reqAddDelTag: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/channel/modify_plugin_tag',
    params,
    'post'
  )
}

// 插件版本部署
export const reqPluginDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/deploy',
    params,
    'post'
  )
}

// 智能体详情-插件版本检查更新 
export const reqPluginCheck: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_check_update',
    params,
    'post'
  )
}

// 插件版本列表
export const reqPluginVersionList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    ' /api/apis/v2/version_list',
    params,
    'post'
  )
}

// 插件版本发布
export const reqPluginVersionPublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_publish',
    params,
    'post'
  )
}

// 插件版本取消发布
export const reqPluginVersionCancelPublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_cancel',
    params,
    'post'
  )
}

// 插件版本删除
export const reqPluginVersionDelete: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_delete',
    params,
    'post'
  )
}

// 插件版本关联查询
export const reqPluginVersionRelation: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_quote_check',
    params,
    'post'
  )
}

// 插件版本已发布列表
export const reqPluginVersionPublishList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_list_publish',
    params,
    'get'
  )
}

// 插件版本更新分类
export const reqPluginVersionUpdateCategory: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_update_tag',
    params,
    'post'
  )
}

// 插件版本公开
export const reqPluginVersionPublic: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_public',
    params,
    'post'
  )
}

// 插件版本取消公开
export const reqPluginVersionCancelPublic: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_public_cancel',
    params,
    'post'
  )
}

// 插件版本取消公开撤销
export const reqPluginVersionPublicCancelRevoke: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_public_rollback',
    params,
    'post'
  )
}

// 插件版本分类
export const reqPluginVersionCategory: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_api_tag',
    params,
    'get'
  )
}

// 插件版本更新描述
export const reqPluginVersionUpdateDesc: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_detail_update',
    params,
    'post'
  )
}

// 插件版本取消审核
export const reqPluginVersionCancelAudit: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_audit_cancel',
    params,
    'post'
  )
}

// 插件版本市场列表
export const reqPluginVersionMarketList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_discovery_list',
    params,
    'post'
  )
}

// 插件版本公开详情
export const reqPluginVersionPublicDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_info_public',
    params,
    'post'
  )
}

// 插件版本我的插件列表
export const reqPluginVersionMyPluginList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_my_list',
    params,
    'post'
  )
}

// 插件版本查看详情
export const reqPluginVersionDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_info',
    params,
    'post'
  )
}

// 插件版本发布动作详情
export const reqPluginVersionActionDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_template_info',
    params,
    'post'
  )
}

// 插件版本公开动作详情
export const reqPluginVersionPublicActionDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/apis/v2/version_template_info_public',
    params,
    'post'
  )
}

// 插件版本查看测试
export const reqPluginVersionTest: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/channel/admin_debug/${params.api_id}`,
    params,
    'post'
  )
}

// 查询MCP
export const reqGetMCPDetail: (params: any) => Promise<any> = (params) =>  {
  return fetchData(
    '/api/v2/mcp_servers/detail',
    params,
    'get'
  )
}

// 发布为MVCP
export const reqPublishMCP: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/v2/mcp_servers/publish`,
    params,
    'post'
  )
}

// 取消发布
export const reqCancelPublishMCP: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/v2/mcp_servers/unpublish`,
    params,
    'get'
  )
}
