/**
 * agent-flows
 */
import { fetchData } from "@/utils/fetch";

// agent列表
export const reqAgentFlowList: (params: any) => Promise<any> = (params) => {
  return fetchData("/api/agent_flow/list", params, "get");
};

// agent 标签列表
export const reqAgentFlowTagList: (params?: any) => Promise<any> = (params) => {
  return fetchData("/api/agent_flow/flow_tags_list", params, "get");
};

// agent 发现列表
export const reqAgentFlowDiscoverList: (params: any) => Promise<any> = (
  params
) => {
  return fetchData("/api/agent_flow/template_list", params, "get");
};
