import knowledgeExternalAxios from '@/utils/knowledgeExternalAxios'
import { fetchData } from '@/utils/fetch';

export interface IKnowledgeExternalResponse {
    context: {
        message: string;
        code: number;
        request_id: string;
        timestamp: number;
    };
    data: any;
    pagination?: {
        total: number;
        hasMore: boolean;
    };
}

// 获取外部知识库列表
export const reqExternalKnowledgeList = (params: {
    page: number;
    page_size: number;
    keyword?: string;
    user_id?: number;
}): Promise<IKnowledgeExternalResponse> => {
    const { page, page_size, keyword = '', user_id = 0 } = params;
    return knowledgeExternalAxios.get(`/api/knowledge_external/list?page=${page}&page_size=${page_size}&keyword=${keyword}&user_id=${user_id}`);
}

// 连接外部知识库
export const reqAddExternalKnowledge = (data: {
    name: string;
    description: string;
    imageUrl: string;
    endpoint: string;
    secret: string;
    knowledge_id: string;
}): Promise<IKnowledgeExternalResponse> => {
    return knowledgeExternalAxios.post('/api/knowledge_external/add', data);
}

// 获取外部知识库详情
export const reqExternalKnowledgeDetail = (id: string): Promise<IKnowledgeExternalResponse> => {
    return knowledgeExternalAxios.get(`/api/knowledge_external/detail?id=${id}`);
}

// 更新外部知识库配置
export const reqUpdateExternalKnowledge = (data: {
    id: number;
    top_k?: number;
    score?: string;
}): Promise<IKnowledgeExternalResponse> => {
    return knowledgeExternalAxios.post('/api/knowledge_external/update', data);
}

// 删除外部知识库
export const reqDeleteExternalKnowledge = (id: string): Promise<IKnowledgeExternalResponse> => {
    return knowledgeExternalAxios.delete(`/api/knowledge_external/delete/${id}`);
}

// 重命名外部知识库
export const reqRenameExternalKnowledge = (data: {
    id: number;
    name: string;
    description?: string;
    imageUrl?: string;
}): Promise<IKnowledgeExternalResponse> => {
    return knowledgeExternalAxios.post('/api/knowledge_external/rename', data);
}

// 召回
export const reqRecall = (data: {
  id: number;
  question: string;
}): Promise<IKnowledgeExternalResponse> => {
    return knowledgeExternalAxios.post('/api/knowledge_external/recall', data);
}