import { fetchData } from '@/utils/fetch';

// 知识库列表
export const getKnowledgeList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/nami_agent_flow/knowledge_list',
    params,
    'get'
  );
}


// 查询mcp工具详情
export const getMcpDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/nami_agent_flow/mcp_detail_single',
    params,
    'get'
  );
}

// 127.0.0.1:8001/api/v2/nami_agent_flow/prompt_optimization_gen

// 自动生成提示词 
export const getPromptOptimizationGen: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/nami_agent_flow/prompt_optimization_gen',
    params,
    'post'
  );
}

export const getPromptReplaceLog: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/nami_agent_flow/prompt_optimization_action',
    params,
    'post'
  );
}

// 获取提示词替换日志
export const getPromptStopLog: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/nami_agent_flow/prompt_optimization_break',
    params,
    'post'
  );
}

export const getNamiUIServer: (params: any) => Promise<any> = (params) => {
  return fetchData(
    'api/v2/nami_agent_flow/mcp_ui_list',
    params,
    'get'
  );
}