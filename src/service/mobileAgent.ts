/*
 * @Author: yh
 * @Date: 2025-02-19 15:46:21
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-02-20 20:22:16
 * @FilePath: \prompt-web\src\service\mobileAgent.ts
 */
import { fetchData } from '@/utils/fetch';

export const reqMobileAgentDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mobile/agent_public_detail',
    params,
    'get'
  );
}

export const reqMobileAgentChatHistory: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mobile/chat_history_list',
    params,
    'get'
  );
}

export const reqMobileAgentChatSession: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mobile/chat_session',
    params,
    'get'
  );
}

export const reqMobileAgentDiscoveryList: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/v2/mobile/agent_discovery',
      params,
      'get'
    );
  }
  

export const reqMobileAgentChatStop: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mobile/chat_stop',
    params,
    'get'
  );
}

export const reqMobileAgentTags: (params:any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mobile/agent_tags',
    params,
    'get'
  );
}
