import { fetchData } from '@/utils/fetch';

import { sseRequest } from '@/utils/sseRequest'

// flow列表
export const reqFlowList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/list',
    params,
    'get'
  );
}

// 官方flow列表
export const reqOfficalFlowList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/official_list',
    params,
    'get'
  );
}

// 文生文列表
export const reqPromptList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/prompt/list',
    params,
    'get'
  );
}

// 知识库列表
export const reqKnowledgeListNew: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/knowledge/knowledge_data_list',
    params,
    'get'
  );
}


// 知识问答列表
export const reqKnowledgeQAList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/knowledge_qa/list',
    params,
    'get'
  );
}

// 关联知识库和flow
export const reqQuoteKnowledge: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/knowledge_api/api/knowledge_data/agent_flow_sync/quote',
    params,
    'post'
  );
}

// 新建flow
export const reqAddFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/create',
    params,
    'post'
  );
}

// 编辑flow
export const reqUpdateFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/update',
    params,
    'post'
  );
}

// 编辑flow desc
export const reqUpdateFlowDesc: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/update_desc',
    params,
    'post'
  );
}

// 编辑flow title
export const reqUpdateFlowTitle: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/update_title',
    params,
    'post'
  );
}

// 复制flow
export const reqCopyFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/copy',
    params,
    'post'
  );
}

// 复制从发现进去的flow
export const reqCopyTemplateFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent_flow/copy',
    params,
    'post'
  );
}


// 删除节点
export const reqDeleteBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/delete_block',
    params,
    'POST'
  );
}

// flow详情
export const reqFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/detail/' + params.template_id,
    params,
    'get'
  );
}

// 发现进入的flow详情
export const reqTemplateFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent_flow/detail/' + params.template_id,
    params,
    'get'
  );
}

// flow发布详情
export const reqFlowPublishDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/publish_detail',
    params,
    'get'
  );
}


//  调试flow
export const reqDebugFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/debug',
    params,
    'post'
  );
}

//  运行flow
export const reqRunFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/run/' + params.template_id,
    {},
    'post'
  );
}
// 停止flow
export const reqStopFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/stop',
    { ...params },
    'get'
  );
}

// 发布后 停止团队的flow
export const reqStopTeamFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/team_stop',
    params,
    'get'
  );
}

// 发布后 停止公开的flow
export const reqStopPublicFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/public_stop',
    params,
    'get'
  );
}

//  导出flow
export const reqDownLoadFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/download/' + params.template_id,
    { download_type: params.download_type },
    'get'
  );
}

//  删除flow
export const reqDeleteFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/delete/' + params.id,
    {},
    'DELETE'
  );
}

//  模块状态
export const reqBlockDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/block_detail',
    params,
    'get'
  );
}

//  整体模块状态
export const reqBlocksDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/status/' + params.template_id,
    params,
    'get'
  );
}

// 编辑单个模块
export const reqUpdateBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/update_block',
    params,
    'post'
  );
}

// 发布 取消发布
export const reqPublishFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/publish_status',
    params,
    'post'
  );
}



// 发布后运行

// 团队中 发布后运行
export const reqTestTeamPublishFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/team_run_test',
    params,
    'post'
  );
}

// 发现中 发布后运行
export const reqTestPublicPublishFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/public_run_test',
    params,
    'post'
  );
}

// 最后一次运行记录详情

// 团队中 最后一次运行记录详情
export const reqLastTeamFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/team_last_detail',
    params,
    'get'
  );
}

// 发现中 最后一次运行记录详情
export const reqLastPublicFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/public_last_detail',
    params,
    'get'
  );
}

// 运行记录 单个模块详情

// 团队中 单个模块详情
export const reqTeamBlockDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/team_block_detail',
    params,
    'get'
  );
}

// 发现中 单个模块详情
export const reqPublicBlockDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/public_block_detail',
    params,
    'get'
  );
}


// 运行记录 全部模块status

// 团队中 全部模块status
export const reqTeamBlocksStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/team_flow_status',
    params,
    'get'
  );
}

// 发现中 全部模块status
export const reqPublicBlocksStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/public_flow_status',
    params,
    'get'
  );
}


// 运行记录 全部模块详情

// 团队中 全部模块详情
export const reqTeamFlowBlocksDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/team_flow_detail',
    params,
    'get'
  );
}

// 发现中 全部模块详情
export const reqPublicBlocksDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/test/public_flow_detail',
    params,
    'get'
  );
}

// 增加普通模块
export const reqAddBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/create_block',
    params,
    'post'
  );
}

// 增加条件模块
export const reqAddConditionBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/create_condition_block',
    params,
    'post'
  );
}

// 增加分支
export const reqAddBranch: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/create_branch',
    params,
    'post'
  );
}

// 删除分支
export const reqDeleteBranch: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/delete_branch',
    params,
    'post'
  );
}

// 编辑分支
export const reqUpdateBranch: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/update_branch',
    params,
    'post'
  );
}

// 整体校验
export const reqValidateFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/validate',
    params,
    'post'
  );
}
// 验证block
export const reqValidateBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/validate_block',
    params,
    'post'
  );
}

// 校验api授权是否存在
export const reqCheckApiAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/block_api_auth_check',
    params,
    'get'
  );
}

// 校验卡片数据
export const reqCheckCardData: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/block_card_data_check',
    params,
    'get'
  );
}

//导出flow 
export const reqOutputFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/output',
    params,
    'get'
  );
}

// 导出指定版本
export const reqOutputVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/output',
    params,
    'get'
  )
}

// 运行历史版本
export const reqRunVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/run/' + params.template_id,
    params,
    'post'
  );
}

// 版本详情
export const reqFlowVersionDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/detail',
    params,
    'get'
  );
}

// 已发布版本详情
export const reqVersionFlowPublishDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/published_detail',
    params,
    'get'
  );
}


// 上一次版本详情
export const reqLastVersionFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/last_detail',
    params,
    'get'
  );
}

// 停止版本
export const reqStopVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/stop_version_flow',
    params,
    'get'
  );
}


// version 全部模块status
export const reqVersionBlocksStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/version_flow_status',
    params,
    'get'
  );
}
// 复制version的flow
export const reqCopyVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/version/copy',
    params,
    'post'
  );
}
//通过文件导入创建flow
export const reqImportFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/import',
    params,
    'post'
  );
}

// 在创建好的flow导入文件覆盖当前flow
export const reqImportCreatedFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/import_overwrite',
    params,
    'post'
  )
}

// flow 引 flow
export const reqFlowPluginList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/flow_plugin/list',
    params,
    'get'
  );
}

export const reqFlowPluginTemplateList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/flow_plugin/template_list',
    params,
    'post'
  );
}

export const reqFlowPluginDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/flow_plugin/detail/' + params.template_id,
    params,
    'get'
  );
}

export const reqFlowCreateRelation: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/flow_plugin/relation/create',
    params,
    'post'
  );
}

export const reqFlowRelationList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/flow_plugin/relation/list/' + params.template_id,
    params,
    'get'
  );
}

// 获取开始节点信息
export const reqInputBlockResult: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/card_detail/' + params.template_id,
    params,
    'get'
  );
}


export const getSSEFlowData = (params = {}, url = '', cb = {}, method = 'POST') => {
  sseRequest(params, url, cb, method)
  // cb   onOpenCb: (data:any) => {}, onSuccessCb : (data:any) => {}, onFinishCb: (data:any) => {}, onFailCb: (data:any) => {}

}
// 获取app应用列表
export const reqGetApplyList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/gui_white/list',
    params,
    'get'
  );
}

// app应用列表添加
export const reqApplyListAdd: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/gui_white/add',
    params,
    'post'
  );
}

// 获取app应用列表更新
export const reqApplyListUpdate: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/gui_white/update',
    params,
    'post'
  );
}

// 连接器节点，获取插件版本信息
export const reqConnectorVersionCheck: (params: any) => Promise<any> = (params) => {
  return fetchData(
    ' /api/apis/v2/version_check_update',
    params,
    'post'
  );
}

// 循环体内模块创建
export const reqLoopCreateBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/create_loop_inner_block',
    params,
    'post'
  );
}

// 循环体内删除节点
export const reqLoopDeleteBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/v2/delete_loop_inner_block',
    params,
    'post'
  );
}

// 上传
export const reqUploadFlowSnapshot: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/nami_file/upload',
    params,
    'post'
  );
}

// 上传流的快照
export const upateFlowSnapshot: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update_flow_thumb',
    params,
    'post'
  );
}