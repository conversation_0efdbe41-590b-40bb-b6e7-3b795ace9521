import { fetchData } from '@/utils/fetch';

// 智能体数据类型定义
export interface AgentItem {
  id: string;
  title: string;
  visitable: number;
  intro: string;
  greeting: string;
  icon: string;
  creator: string;
  agent_type: number;
  super_switch: number;
  limited_free: boolean;
  conversation_id: string;
  detail_status: number;
}

export interface AgentCategory {
  category: string;
  intro: string;
  title: string;
  list: AgentItem[];
}

export interface AgentListResponse {
  context: {
    message: string;
    code: number;
    request_id: string;
    timestamp: number;
  };
  data: {
    list: AgentCategory[];
  };
}

// 获取智能体列表
export const getNamiAgentList = async (): Promise<any> => {
  try {
    const response = await fetchData('/api/v2/mcp/nami_agent_list', {}, 'GET');
    console.log('API响应类型:', typeof response);
    console.log('API响应:', response);
    console.log('API响应keys:', response ? Object.keys(response) : 'null');

    // 检查响应结构
    if (!response) {
      throw new Error('API响应为空');
    }

    return response;
  } catch (error) {
    console.error('获取智能体列表失败:', error);
    throw error;
  }
};

// 获取所有分类标签（用于NavTabs）
export const getAgentTags = async () => {
  try {
    const response = await getNamiAgentList();
    console.log('getAgentTags - response:', response);

    // 检查响应结构
    if (!response) {
      console.error('响应为空');
      return [];
    }

    // fetchData的checkStatus函数会根据code===0时返回data，而不是完整response
    // 所以response实际上就是原始response.data
    if (response.list) {
      // 添加"全部"选项
      const allTab = { id: 'all', tag_name: '全部', icon: null };
      const categoryTabs = response.list.map(category => ({
        id: category.category,
        tag_name: category.title,
        icon: null
      }));
      return [allTab, ...categoryTabs];
    }

    console.error('响应格式不正确，没有list字段:', response);
    return [];
  } catch (error) {
    console.error('获取标签失败:', error);
    return [];
  }
};

// 根据分类获取智能体列表
export const getAgentsByCategory = async (category?: string): Promise<AgentItem[]> => {
  try {
    const response = await getNamiAgentList();
    console.log('getAgentsByCategory - response:', response);

    // 检查响应结构
    if (!response) {
      console.error('响应为空');
      return [];
    }

    // fetchData的checkStatus函数会根据code===0时返回data，而不是完整response
    // 所以response实际上就是原始response.data
    if (response.list) {
      if (!category || category === 'all') {
        // 返回所有智能体
        return response.list.reduce((acc, cat) => {
          return acc.concat(cat.list);
        }, [] as AgentItem[]);
      } else {
        // 返回指定分类的智能体
        const targetCategory = response.list.find(cat => cat.category === category);
        return targetCategory ? targetCategory.list : [];
      }
    }

    console.error('响应格式不正确，没有list字段:', response);
    return [];
  } catch (error) {
    console.error('获取智能体列表失败:', error);
    return [];
  }
};