import { fetchData } from '@/utils/fetch';


const apiENV = process.env.NEXT_PUBLIC_API_ENV ?? 'development'
// development环境：test_agent
// 老线上：prophet_360agent
// 新线上：prophet_agent
// 测试：prophet_agent 
// pre测试：agent_test

let p = '';
if(apiENV === 'development'){
    p = 'test_agent'
}else if(apiENV === 'test'){
    p = 'prophet_agent'
}else if(apiENV === 'production'){
    p = 'prophet_agent'
}else if(apiENV === 'preTest'){
    p = 'agent_preprd'
}
// console.log(process.env.NEXT_PUBLIC_API_ENV, apiENV, p)

export const reqLoginUrl: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/loginApi/user/login_url?platform_type='+p,
        {},
        'get'
    );
}
// prophet_agent
export const reqLoginToken: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/loginApi/user/iam_ticket?platform_type='+p+'&ticket='+params.ticket,
        {},
        'get'
    );
}

export const reqUserInfo: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/loginApi/user/info?user_id='+params.userid,
        {},
        'get'
    );
}

export const reqLogout: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/loginApi/user/logout_url?platform_type='+p,
        {},
        'get'
    );
}

export const reqLoginWaiWang: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/login',
        {},
        'post'
    );
}

export const reqLogoutWaiWang: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/logout',
        {},
        'get'
    );
}
// 推推、织语单点登录逻辑，获取token
export const reqTuituiLogin: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/login_sso',
        params,
        'post'
    );
}
// 亿方云盘 单点登录逻辑，获取token
export const reqYunPanLogin: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/fangcloud_login',
        params,
        'post'
    );
}

// 企业智脑 单点登录逻辑，获取token
export const reqZhiNaoLogin: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/login_zhinao',
        params,
        'post'
    );
}

// Qpaas llmops 单点登录逻辑，获取token 公共接口，后期可复用
export const reqClientLoginCommon: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/common_login',
        params,
        'post'
    );
}

// 智汇云登录打通
export const reqTicketLogin: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/user/zyun_login',
        params,
        'post'
    );
}