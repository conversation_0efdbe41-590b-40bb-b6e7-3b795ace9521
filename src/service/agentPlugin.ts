/**
 * agent-插件
 */
import { fetchData } from "@/utils/fetch";

// agent 插件列表
export const reqAgentPluginList: (params: any) => Promise<any> = (params) => {
  return fetchData("/api/apis/list", params, "get");
};

// agent 插件标签列表
export const reqAgentPluginTagList: (params?: any) => Promise<any> = (params) => {
  return fetchData("/api/apis/discovery_tag_list", params, "get");
};

// agent 插件发现列表
export const reqAgentPluginDiscoverList: (params: any) => Promise<any> = (
  params
) => {
  return fetchData("/api/apis/discovery_list", params, "get");
};
