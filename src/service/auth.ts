import { fetchData } from "@/utils/fetch";

// 授权插件列表
export const reqAuthPluginList: (params:any) => Promise<any> = (params) => {
    return fetchData(
        'api/auth/list_api',
        params,
        'get'
    )
}

// 插件下的授权列表
export const reqPluginAuthList: (params:any) => Promise<any> = (params) => {
    return fetchData(
        'api/auth/list_api_auth',
        params,
        'post'
    )
}

// 插件下关联技能、智能体
export const reqPluginFlowInell: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/apis/v2/api_relation_list',
        params,
        'post'
    )
}

// 创建授权
export const reqCreateAuth: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/auth/create',
        params,
        'post'
    )
}

// 编辑授权
export const reqEditAuth: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/auth/update',
        params,
        'post'
    )
}

// 删除授权
export const reqDeleteAuth: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/auth/delete',
        params,
        'get'
    )
}
