import knowledgeAxios from '@/utils/knowledgeAxios'
import { fetchData } from '@/utils/fetch';

export interface IKnowledgeResponse {
    context: any;
    code: number
    msg: string
    data: any
}

// 获取知识数据列表
export const reqKnowledgeDataList: (page: string, page_size: string, name?: string, status?: string, role_num?:number) => Promise<IKnowledgeResponse> = (page, page_size,name = '',status='',role_num=0) => {
    //  return knowledgeAxios.get(`/api/knowledge/knowledge_data_list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`)
    return fetchData(
        `/api/knowledge/knowledge_data_list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`,
        {},
        'get'
      );
}

// 新增知识数据
export const reqAddKnowledgeData: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/api/knowledge_data/add', data)
    // return fetchData(
    //     `/api/knowledge_data/add`,
    //     data,
    //     'post'
    // );
}

// 删除知识数据
export const reqDeleteKnowledgeData: (data: any) => Promise<IKnowledgeResponse> = (id) => {
     return knowledgeAxios.get(`/api/knowledge_data/delete/${id}`)
    // return fetchData(
    //     `/api/knowledge_data/delete/${id}`,
    //     {},
    //     'get'
    // );
}

// 复制知识数据
export const reqCopyKnowledgeData: (data: any) => Promise<IKnowledgeResponse> = (id) => {
     return knowledgeAxios.post(`/api/knowledge_data/copy/${id}`)
    // return fetchData(
    //     `/api/knowledge_data/copy/${id}`,
    //     {},
    //     'post'
    // );
}

// 编辑知识数据
export const reqEditKnowledgeData: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    return knowledgeAxios.post('/api/knowledge_data/update', data)
    // return fetchData(
    //     '/api/knowledge_data/update',
    //     data,
    //     'post'
    // );
}

// 获取知识数据详情
export const reqKnowledgeDataDetail: (id: string) => Promise<IKnowledgeResponse> = (id) => {
    return knowledgeAxios.get(`/api/knowledge_data/info/${id}`)
    // return fetchData(
    //     `/api/knowledge_data/info/${id}`,
    //     {},
    //     'get'
    // );
}

// 知识抽取
export const reqKnowledgeExtract: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    return knowledgeAxios.post('/v1/extract', data)
    // return fetchData(
    //     '/v1/extract',
    //     data,
    //     'post'
    // );
}

// 获取知识片段列表
export const reqKnowledgeSliceList: (dataset_id: string, page: string, page_size: string) => Promise<IKnowledgeResponse> =
    (dataset_id, page, page_size) => {
        return knowledgeAxios.get(`/v1/knowledge_fragment?dataset_id=${dataset_id}&page=${page}&page_size=${page_size}`)
        // return fetchData(
        //     `/v1/knowledge_fragment?dataset_id=${dataset_id}&page=${page}&page_size=${page_size}`,
        //     {},
        //     'get'
        // );
    }

// 获取知识片段详情
export const reqKnowledgeSliceDetail: (id: string) => Promise<IKnowledgeResponse> =
    (id) => {
        return knowledgeAxios.get(`/v1/knowledge_fragment/${id}`)
        // return fetchData(
        //     `/v1/knowledge_fragment/${id}`,
        //     {},
        //     'get'
        // );
    }

// 编辑知识片段
export const reqEditKnowledgeSlice: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    return knowledgeAxios.put(`/v1/knowledge_fragment/${data.id}`, data)
    // return fetchData(
    //     `/v1/knowledge_fragment/${data.id}`,
    //     data,
    //     'put'
    // );
}

// 删除知识片段
export const reqEditKnowledgeSliceDelete: (id: string) => Promise<IKnowledgeResponse> = (id) => {
    return knowledgeAxios.delete(`/v1/knowledge_fragment/${id}`)
    // return fetchData(
    //     `/v1/knowledge_fragment/${id}`,
    //     {},
    //     'delete'
    // );
}

// 批量删除知识片段
export const reqKnowledgeSliceMultiDelete: (id: object) => Promise<IKnowledgeResponse> = (data) => {
    // return knowledgeAxios.post(`/v1/knowledge_fragment/delete`, data)
    return fetchData(
        '/api/knowledge/delete_knowledge_fragment',
        data,
        'post'
    );
}

// 获取问答列表
export const reqKnowledgeQAList: (dataset_id: string, page: string, page_size: string) => Promise<IKnowledgeResponse> =
    (dataset_id, page, page_size) => {
        return knowledgeAxios.get(`/v1/qa?dataset_id=${dataset_id}&page=${page}&page_size=${page_size}`)
        // return fetchData(
        //     `/v1/qa?dataset_id=${dataset_id}&page=${page}&page_size=${page_size}`,
        //     {},
        //     'get'
        // );
    }


// 获取QA对详情
export const reqKnowledgeQADetail: (id: string) => Promise<IKnowledgeResponse> =
    (id) => {
        return knowledgeAxios.get(`/v1/qa/${id}`)
        // return fetchData(
        //     `/v1/qa/${id}`,
        //     {},
        //     'get'
        // );
    }

// 删除问答对
export const reqEditKnowledgeQaDelete: (id: string) => Promise<IKnowledgeResponse> = (id) => {
     return knowledgeAxios.delete(`/v1/qa/${id}`)
    // return fetchData(
    //     `/v1/qa/${id}`,
    //     {},
    //     'delete'
    // );
}

// 批量删除问答对
export const reqKnowledgeQaMultiDelete: (data: object) => Promise<IKnowledgeResponse> = (data) => {
    return knowledgeAxios.post('/v1/qa/delete', data)
}

// 生成相似问题列表(只生成，不入库)
export const reqKnowledgeGenerateSimilarQuestion: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/v1/generate_similar_question', data)
    // return fetchData(
    //     `/v1/generate_similar_question`,
    //     data,
    //     'post'
    // );
}

// 将相似问题入库
export const reqKnowledgeAddSimilarQuestion: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/v1/similar_question', data)
    // return fetchData(
    //     `/v1/similar_question`,
    //     data,
    //     'post'
    // );
}

// 删除相似问题
export const reqKnowledgeDeleteSimilarQuestion: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    return knowledgeAxios.delete('/v1/similar_question', {
        data: data,
        headers: {
            'Content-Type': 'application/json'
        },
    })
    // return fetchData(
    //     `/v1/similar_question`,
    //     data,
    //     'delete'
    // );
}

// 编辑问答对、更新问答对
export const reqEditKnowledgeQA: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.put(`/v1/qa/${data.id}`, data)
    // return fetchData(
    //     `/v1/qa/${data.id}`,
    //     data,
    //     'put'
    // );
}

// 获取文档列表
export const reqKnowledgeDataDocList: (datasetId: string,page?: number,pageSize?:number) => Promise<IKnowledgeResponse> = (datasetId,page,pageSize) => {
    return knowledgeAxios.get(`/v1/doc/list_by_page?dataset_id=${datasetId}&page=${page}&page_size=${pageSize}`)
    // return fetchData(
    //     `/v1/doc/list_by_page?dataset_id=${datasetId}&page=${page}&page_size=${pageSize}`,
    //     {},
    //     'get'
    // );
}

// 删除文档
export const reqKnowledgeDataDocDelete: (id: string) => Promise<IKnowledgeResponse> = (id) => {
    return knowledgeAxios.delete(`/v1/doc/${id}`)
    // return fetchData(
    //     `/v1/doc/${id}`,
    //     {},
    //     'delete'
    // );
}


// 获取知识问答列表
export const reqKnowledgeChatList: (page: string, page_size: string,name?: string, status?: string,role_num?:number) => Promise<IKnowledgeResponse> = (page, page_size,name='',status='',role_num=0) => {
     return knowledgeAxios.get(`/api/knowledge_qa/list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`)
    // return fetchData(
    //     `/api/knowledge_qa/list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`,
    //     {},
    //     'get'
    //   );
}

// 新增知识问答
export const reqAddKnowledgeChat: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/api/knowledge_qa/add', data)
    // return fetchData(
    //     `/api/knowledge_qa/add`,
    //     data,
    //     'post'
    // );
}


// 重命名知识问答的接口
export const reqRenameKnowledgeChat: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    return knowledgeAxios.post('/api/knowledge_qa/rename', data)
}

// 新增知识问答
export const reqAddKnowledgeChatV2: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    return fetchData(
        `/api/knowledge/knowledge_qa_add`,
        data,
        'post'
    );
}

// 
export const reqRenameKnowledgeChatV2: (data: any) => Promise<IKnowledgeResponse> = (data) => {
    // return knowledgeAxios.post('/api/knowledge/knowledge_qa_rename', data)
    return fetchData(
        `/api/knowledge/knowledge_qa_rename`,
        data,
        'post'
    );
}

// 编辑知识问答
export const reqEditKnowledgeChat: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/api/knowledge_qa/update', data)
    // return fetchData(
    //     `/api/knowledge_qa/update`,
    //     data,
    //     'post'
    // );
}

// 知识问答训练
export const reqTrainKnowledgeChat: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/v1/train', data)
    // return fetchData(
    //     `/v1/train`,
    //     data,
    //     'post'
    // );
}

// 获取知识问答列表
export const reqKnowledgeChatTrainStatus: (taskId: string) => Promise<IKnowledgeResponse> = (taskId) => {
     return knowledgeAxios.get(`/v1/train_task_status/${taskId}`)
    // return fetchData(
    //     `/v1/train_task_status/${taskId}`,
    //     {},
    //     'get'
    // );
}

// 删除知识问答
export const reqDeleteKnowledgeChat: (data: any) => Promise<IKnowledgeResponse> = (id) => {
     return knowledgeAxios.get(`/api/knowledge_qa/delete/${id}`)
    // return fetchData(
    //     `/api/knowledge_qa/delete/${id}`,
    //     {},
    //     'get'
    // );
}

export const reqCopyKnowledgeChat: (id: any) => Promise<IKnowledgeResponse> = (id) => {
    return knowledgeAxios.get(`/api/knowledge_qa/copy/${id}`)
//    return fetchData(
//     `/api/knowledge_qa/copy/${id}`,
//     {},
//     'get'
//  );
}
// 匹配测试接口
export const reqQaMatchTest: (data: any) => Promise<IKnowledgeResponse> = (data) => {
     return knowledgeAxios.post('/v1/qa_match_test', data)
    // return fetchData(
    //     `/v1/qa_match_test`,
    //     data,
    //     'post'
    //  );
}

// 获取知识问答详情
export const reqKnowledgeChatDetail: (id: string) => Promise<IKnowledgeResponse> = (id) => {
    return knowledgeAxios.get(`/api/knowledge_qa/info/${id}`)
    // return fetchData(
    //     `/api/knowledge_qa/info/${id}`,
    //     {},
    //     'get'
    //  );
}
// 抓取网页
export const reqKnowledgeWeb_crawl: (params:any) => Promise<any> = (params) => {
    return fetchData(
      '/api/function/web_crawl',
      params,
      'post'
    );
}

// 上传文件
export const reqKnowledgeDataUpload: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/knowledge_api/api/knowledge_data/upload',
        params,
        'post'
    );
}
// 上传文件 新接口，漏洞修复
export const reqKnowledgeDataUploadNew: (params:any) => Promise<any> = (params) => {
    return fetchData(
        '/api/knowledge/upload',
        params,
        'post'
    );
}
// 删除知识数据 新接口
// /api/knowledge_data/delete todotodo
export const reqDeleteKnowledgeDataList: (dataset_id: string| number) => Promise<any> = (dataset_id) => {
     return fetchData(
        `/api/knowledge/delete/${dataset_id}`,
        {},
        'delete'
    );
}

// 校验在线文档地址 todotodo没有
export const reqCheckKnowledgeFile: (data: any) => Promise<any> = (data) => {
     return fetchData(
        `/api/knowledge/check_file`,
        data,
        'post'
     );
}

// 训练文件
// /v1/train todotodo
export const reqTrainKnowledgeFile: (data: any) => Promise<any> = (data) => {
   // return knowledgeAxios.post('/api/knowledge/train', data)
    return fetchData(
        `/api/knowledge/train`,
        data,
        'post'
     );
}

// 获取知识数据文档列表
// /api/knowledge_data/list todotodo
export const reqKnowledgeDataFileList: (page: number, page_size: number,dataset_id?: number) => Promise<any> = (page, page_size,dataset_id) => {
    // return knowledgeAxios.get(`/api/knowledge/list?page=${page}&page_size=${page_size}&dataset_id=${dataset_id}`)
     return fetchData(
        `/api/knowledge/list?page=${page}&page_size=${page_size}&dataset_id=${dataset_id}`,
        {},
        'get'
     );
}


// 删除知识文档
// /api/knowledge_data/delete todo
export const reqDeleteKnowledgeFile: (id: string) => Promise<any> = (id) => {
    // return knowledgeAxios.delete(`/api/knowledge/delete_file/${id}`)
     return fetchData(
        `/api/knowledge/delete_file/${id}`,
        {},
        'delete'
    );
}

// 获取知识片段列表
// /v1/knowledge_fragment todotodo
export const reqKnowledgeSliceSignalFileList: (dataset_id: string, page: string, page_size: string) => Promise<any> =
    (dataset_id, page, page_size) => {
        // return knowledgeAxios.get(`/api/knowledge/segment_list?id=${dataset_id}&page=${page}&page_size=${page_size}`)
        return fetchData(
            `/api/knowledge/segment_list?id=${dataset_id}&page=${page}&page_size=${page_size}`,
            {},
            'get'
         );
    }

// 获取问答列表
// v1/qa todotodo
export const reqKnowledgeSignalFileQAList: (dataset_id: string, page: string, page_size: string) => Promise<any> =
    (dataset_id, page, page_size) => {
       // return knowledgeAxios.get(`/api/knowledge/qa_list?id=${dataset_id}&page=${page}&page_size=${page_size}`)
        return fetchData(
            `/api/knowledge/qa_list?id=${dataset_id}&page=${page}&page_size=${page_size}`,
            {},
            'get'
         );
    }

// 更新知识文档
// /api/knowledge_qa/update todotodo 
export const reqEditKnowledgeFile: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/update`,
        data,
        'put'
     );
}

// 更新问答对
// v1/qa todotodo 
export const reqKnowledgeUpdateQaFile: (data: any) => Promise<any> = (data) => {
    // return knowledgeAxios.post('/api/knowledge/qa_update', data)
    return fetchData(
        `/api/knowledge/qa_update`,
        data,
        'post'
     );
}

// 批量删除问答对
// /api/knowledge_qa/delete todotodo
export const reqKnowledgeQaMultiDeleteNew: (data: object) => Promise<any> = (data) => {
    // return knowledgeAxios.post('/api/knowledge/qa_delete', data)
    return fetchData(
        `/api/knowledge/qa_delete`,
        data,
        'post'
     );
}

export const reqKnowledgeAnswerHistoryList: (knowledge_qa_id: string | number, page: string | number, page_size: string | number) => Promise<any> =
    (knowledge_qa_id, page, page_size) => {
        return fetchData(
            `/api/knowledge/history?knowledge_qa_id=${knowledge_qa_id}&page=${page}&page_size=${page_size}`,
            {},
            'get'
         );
    }
// 检查知识问答是否可以使用
export const reqCheckKnowledgeAnswer: (dataset_id: string) => Promise<any> =
    (dataset_id) => {
        return fetchData(
            `/api/knowledge/status/${dataset_id}`,
            {},
            'get'
        );
    }
   
// 删除问答历史数据
export const reqDeleteKnowledgeHistoryList: (knowledge_qa_id: string| number) => Promise<any> = (knowledge_qa_id) => {
    // return knowledgeAxios.delete(`/api/knowledge/delete_file/${id}`)
     return fetchData(
        `/api/knowledge/delete_chat`,
        {knowledge_qa_id},
        'delete'
    );
}
// 删除单条历史记录
export const reqDeleteKnowledgeSignalHis: (knowledge_qa_id: string| number,question_id:string | number) => Promise<any> = (knowledge_qa_id,question_id) => {
    // return knowledgeAxios.delete(`/api/knowledge/delete_file/${id}`)
     return fetchData(
        `/api/knowledge/delete_single_chat`,
        {knowledge_qa_id,question_id},
        'delete'
    );
}


// 检查知识问答是否可以使用
export const reqGetFragmentationAnswer: (knowledge_qa_id: string | number,question:string, get_scores: boolean, recall_topk: number) => Promise<any> =
    (knowledge_qa_id, question, get_scores, recall_topk) => {
        return fetchData(
            `/api/knowledge/get_answer_fragmentation?knowledge_qa_id=${knowledge_qa_id}&question=${question}&get_scores=${get_scores}&recall_topk=${recall_topk}`,
            {},
            'get'
        );
    }
// 轮训知识数据训练状态接口
export const reqGetKnowledgeTrainStatus: (dataset_id: string | number) => Promise<any> = (dataset_id) => {
    return fetchData(
        `/api/knowledge/obtain_status/${dataset_id}`,
        {},
        'get'
    );
}
// 新增知识数据，新版本
// /api/knowledge_data/add todotodo
export const reqAddKnowledgeDataV2: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/add`,
        data,
        'post'
    );
}

// 获取网页抓取结果
export const reqGetKnowledgeWebInfo: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/get_web_info`,
        data,
        'post'
    );
}



// 重命名知识数据 ，新版本
// /api/knowledge_data/update todotodo
export const reqKnowledgeRename: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/rename`,
        data,
        'post'
    );
}

// 知识数据列表 新接口
export const reqKnowledgeDataListV2: (page: string, page_size: string,name?: string, status?: string,role_num?:number) => Promise<any> =
    (page, page_size,name='',status='',role_num=0) => {
        return fetchData(
            `/api/knowledge/knowledge_list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`,
            {},
            'get'
        )
    }

// 知识数据详情 新接口 
export const reqGetKnowledgeInfo: (dataset_id: string | number) => Promise<any> = (dataset_id) => {
    return fetchData(
        `/api/knowledge/info/${dataset_id}`,
        {},
        'get'
    );
}

// AI 默认回复
export const reqAiDefaultAnswer: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/default_answer`,
        data,
        'post'
    );
}

// Ai 开场白
export const reqAiGreeting: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/greeting`,
        data,
        'post'
    );
}

// AI 角色设定
export const reqAiCharacterPrompt: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/character_prompt`,
        data,
        'post'
    );
}

// 知识数据详情 新接口 
export const reqKnowledgeDownLoadApi: (id: string | number) => Promise<any> = (id) => {
    return fetchData(
        `/api/knowledge/file_info/${id}`,
        {},
        'get'
    );
}

// 获取知识问答列表
export const reqKnowledgeChatListV3: (page: string, page_size: string,name?: string, status?: string, role_num?:number) => Promise<IKnowledgeResponse> = (page, page_size,name='',status='',role_num=0) => {
    return fetchData(
       `/api/knowledge_qa/list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`,
       {},
       'get'
    );
}

// 新增知识问答
export const reqAddKnowledgeChatV3: (data: any) => Promise<any> = (data) => {
    return fetchData(
        '/api/knowledge_qa/add',
        data,
        'post'
    );
}

// 获取知识片段详情
export const reqKnowledgeSliceDetailV3: (data: any) => Promise<any> =
    (data) => {
        return fetchData(
            `/api/knowledge/knowledge_fragment`,
            data,
            'get'
        );
    }
// 获取QA对详情
export const reqKnowledgeQADetailV3: (data: any) => Promise<any> =
    (data) => {
        return fetchData(
            `/api/knowledge/qa_info`,
            data,
            'get'
        );
    }
    
// 生成相似问题列表(只生成，不入库)
export const reqKnowledgeGenerateSimilarQuestionV3: (data: any) => Promise<any> = (data) => {
   return fetchData(
       `/api/knowledge/generate_similar_question`,
       data,
       'post'
   );
}

// 删除相似问题
export const reqKnowledgeDeleteSimilarQuestionV3: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/similar_question`,
        data,
        'delete'
    );
}

// 将相似问题入库
export const reqKnowledgeAddSimilarQuestionV3: (data: any) => Promise<any> = (data) => {
   return fetchData(
       `/api/knowledge/similar_question`,
       data,
       'post'
   );
}

// 编辑知识片段
export const reqEditKnowledgeSliceV3: (data: any) => Promise<any> = (data) => {
    return fetchData(
        `/api/knowledge/update_fragment`,
        data,
        'put'
    );
}

// 删除知识问答
export const reqDeleteKnowledgeChatV3: (data: any) => Promise<any> = (id) => {
    return fetchData(
        `/api/knowledge_qa/delete/${id}`,
        {},
        'get'
    );
}

// 获取知识问答详情
export const reqKnowledgeChatDetailV3: (id: string) => Promise<any> = (id) => {
    return fetchData(
        `/api/knowledge_qa/info/${id}`,
        {},
        'get'
     );
}

// 编辑知识问答
export const reqEditKnowledgeChatV3: (data: any) => Promise<any> = (data) => {
   return fetchData(
       `/api/knowledge_qa/update`,
       data,
       'post'
   );
}

// 知识问答训练
export const reqTrainKnowledgeChatV3: (data: any) => Promise<any> = (data) => {
   return fetchData(
       `/api/knowledge_qa/train`,
       data,
       'post'
   );
}

// 获取训练状态
export const reqKnowledgeChatTrainStatusV3: (taskId: string) => Promise<any> = (taskId) => {
   return fetchData(
       `/api/knowledge_qa/train_task_status/${taskId}`,
       {},
       'get'
   );
}

// 知识问答，数据选择列表 新接口
export const reqKnowledgeChatDataListV3: (page: string, page_size: string,name?: string, status?: string,role_num?:number) => Promise<any> =
    (page, page_size,name='',status='',role_num=0) => {
        return fetchData(
            `/api/knowledge/knowledge_data_list?page=${page}&page_size=${page_size}&keyword=${name}&publish_status=${status}&user_id=${role_num}`,
            {},
            'get'
        )
    }