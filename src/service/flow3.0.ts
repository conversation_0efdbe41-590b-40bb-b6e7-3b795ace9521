import { fetchData } from '@/utils/fetch';

import { sseRequest } from '@/utils/sseRequest'

// flow列表
export const reqFlowList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/list',
    params,
    'get'
  );
}

// 官方flow列表
export const reqOfficalFlowList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/official_list',
    params,
    'get'
  );
}

// 文生文列表
export const reqPromptList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/prompt/list',
    params,
    'get'
  );
}

// 知识库列表
export const reqKnowledgeListNew: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/knowledge/knowledge_data_list',
    params,
    'get'
  );
}


// 知识问答列表
export const reqKnowledgeQAList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/knowledge_qa/list',
    params,
    'get'
  );
}

// 关联知识库和flow
export const reqQuoteKnowledge: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/knowledge_api/api/knowledge_data/agent_flow_sync/quote',
    params,
    'post'
  );
}

// 新建flow
export const reqAddFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/create',
    params,
    'post'
  );
}

// 编辑flow
export const reqUpdateFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update',
    params,
    'post'
  );
}

// 编辑flow desc
export const reqUpdateFlowDesc: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update_desc',
    params,
    'post'
  );
}

// 编辑flow title
export const reqUpdateFlowTitle: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update_title',
    params,
    'post'
  );
}

// 复制flow
export const reqCopyFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/copy',
    params,
    'post'
  );
}

// 复制从发现进去的flow
export const reqCopyTemplateFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent_flow/copy',
    params,
    'post'
  );
}


// 删除节点
export const reqDeleteBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/delete_block',
    params,
    'POST'
  );
}

// flow详情
export const reqFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/detail/' + params.template_id,
    params,
    'get'
  );
}

// 发现进入的flow详情
export const reqTemplateFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent_flow/detail/' + params.template_id,
    params,
    'get'
  );
}

// flow发布详情
export const reqFlowPublishDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/publish_detail',
    params,
    'get'
  );
}


//  调试flow
export const reqDebugFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/debug',
    params,
    'post'
  );
}

//  运行flow
export const reqRunFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/run/' + params.template_id,
    {...params},
    'post'
  );
}
// 停止flow
export const reqStopFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/stop',
    { ...params },
    'get'
  );
}

// 发布后 停止团队的flow
export const reqStopTeamFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/team_stop',
    params,
    'get'
  );
}

// 发布后 停止公开的flow
export const reqStopPublicFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/public_stop',
    params,
    'get'
  );
}

//  导出flow
export const reqDownLoadFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/download/' + params.template_id,
    { download_type: params.download_type },
    'get'
  );
}

//  删除flow
export const reqDeleteFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/delete/' + params.id,
    {},
    'DELETE'
  );
}

//  模块状态
export const reqBlockDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/block_detail',
    params,
    'get'
  );
}

//  整体模块状态
export const reqBlocksDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/status/' + params.template_id,
    params,
    'post'
  );
}

// 交互，继续运行
export const reqBlocksDetailContinueRun: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/continue_run',
    params,
    'post'
  );
}

// 编辑单个模块
export const reqUpdateBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update_block',
    params,
    'post'
  );
}

// 保存flow
export const reqSaveFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update',
    params,
    'post'
  );
}



// 发布 取消发布
export const reqPublishFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/publish_status',
    params,
    'post'
  );
}



// 发布后运行

// 团队中 发布后运行
export const reqTestTeamPublishFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/team_run_test',
    params,
    'post'
  );
}

// 发现中 发布后运行
export const reqTestPublicPublishFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/public_run_test',
    params,
    'post'
  );
}

// 最后一次运行记录详情

// 团队中 最后一次运行记录详情
export const reqLastTeamFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/team_last_detail',
    params,
    'get'
  );
}

// 发现中 最后一次运行记录详情
export const reqLastPublicFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/public_last_detail',
    params,
    'get'
  );
}

// 运行记录 单个模块详情

// 团队中 单个模块详情
export const reqTeamBlockDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/team_block_detail',
    params,
    'get'
  );
}

// 发现中 单个模块详情
export const reqPublicBlockDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/public_block_detail',
    params,
    'get'
  );
}


// 运行记录 全部模块status

// 团队中 全部模块status
export const reqTeamBlocksStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/team_flow_status',
    params,
    'get'
  );
}

// 发现中 全部模块status
export const reqPublicBlocksStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/public_flow_status',
    params,
    'get'
  );
}


// 运行记录 全部模块详情

// 团队中 全部模块详情
export const reqTeamFlowBlocksDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/team_flow_detail',
    params,
    'get'
  );
}

// 发现中 全部模块详情
export const reqPublicBlocksDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/test/public_flow_detail',
    params,
    'get'
  );
}

// 增加普通模块
export const reqAddBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/create_block',
    params,
    'post'
  );
}

// 增加条件模块
export const reqAddConditionBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/create_condition_block',
    params,
    'post'
  );
}

// 增加分支
export const reqAddBranch: (params: any) => Promise<any> = (params) => {
return fetchData(
    '/api/flow/create_branch',
    params,
    'post'
  );
}

// 删除分支
export const reqDeleteBranch: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/delete_branch',
    params,
    'post'
  );
}

// 编辑分支
export const reqUpdateBranch: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update_branch',
    params,
    'post'
  );
}

// 整体校验
export const reqValidateFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/validate',
    params,
    'post'
  );
}
// 验证block
export const reqValidateBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/validate_block',
    params,
    'post'
  );
}

// 校验api授权是否存在
export const reqCheckApiAuth: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/block_api_auth_check',
    params,
    'get'
  );
}

// 校验卡片数据
export const reqCheckCardData: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/block_card_data_check',
    params,
    'get'
  );
}

//导出flow 
export const reqOutputV3Flow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/output',
    params,
    'get'
  );
}

// 导出指定版本
export const reqOutputV3VersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/output',
    params,
    'get'
  )
}

// 运行历史版本
export const reqRunVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/run/' + params.template_id,
    params,
    'post'
  );
}

// 版本详情
export const reqFlowVersionDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/detail',
    params,
    'get'
  );
}

// 已发布版本详情
export const reqVersionFlowPublishDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/published_detail',
    params,
    'get'
  );
}


// 上一次版本详情
export const reqLastVersionFlowDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/last_detail',
    params,
    'get'
  );
}

// 停止版本
export const reqStopVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/stop_version_flow',
    params,
    'get'
  );
}


// version 全部模块status
export const reqVersionBlocksStatus: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/version_flow_status',
    params,
    'get'
  );
}
// 复制version的flow
export const reqCopyVersionFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/copy',
    params,
    'post'
  );
}
//通过文件导入创建flow
export const reqImportFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/import',
    params,
    'post'
  );
}

// 在创建好的flow导入文件覆盖当前flow
export const reqImportCreatedFlow: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/import_overwrite',
    params,
    'post'
  )
}

// flow 引 flow
export const reqFlowPluginList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_plugin/list',
    params,
    'get'
  );
}

export const reqFlowPluginTemplateList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_plugin/template_list',
    params,
    'post'
  );
}

export const reqFlowPluginDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_plugin/detail/' + params.template_id,
    params,
    'get'
  );
}

export const reqFlowCreateRelation: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_plugin/relation/create',
    params,
    'post'
  );
}

export const reqFlowRelationList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_plugin/relation/list/' + params.template_id,
    params,
    'get'
  );
}

// 获取开始节点信息
export const reqInputBlockResult: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/card_detail/' + params.template_id,
    params,
    'get'
  );
}


export const getSSEFlowData = (params = {}, url = '', cb = {}, method = 'POST', isMsgExt = false) => {
  sseRequest(params, url, cb, method, isMsgExt)
  // cb   onOpenCb: (data:any) => {}, onSuccessCb : (data:any) => {}, onFinishCb: (data:any) => {}, onFailCb: (data:any) => {}

}
// 获取web/app列表
export const reqGetApplyListV3: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/gui_white/list',
    params,
    'get'
  );
}

// web/app列表添加
export const reqApplyListAddV3: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/gui_white/add',
    params,
    'post'
  );
}

// 获取web/app列表更新
export const reqApplyListUpdateV3: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/gui_white/update',
    params,
    'post'
  );
}

// 连接器节点，获取插件版本信息
export const reqConnectorVersionCheck: (params: any) => Promise<any> = (params) => {
  return fetchData(
    ' /api/apis/version_check_update',
    params,
    'post'
  );
}

// 循环体内模块创建
export const reqLoopCreateBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/create_loop_inner_block',
    params,
    'post'
  );
}

// 循环体内删除节点
export const reqLoopDeleteBlock: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/delete_loop_inner_block',
    params,
    'post'
  );
}

// 创建边
export const reqCreateEdge: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/create_edges',
    params,
    'post'
  );
}


// 删除边
export const reqDeleteEdge: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/delete_edges',
    params,
    'post'
  );
}

// flow ai to code
export const reqFlowAiToCode: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/generate_code',
    params,
    'post'
  )
}

// 获取MCP标签
export const reqGetMCPTags: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/prompt-tags?tag_type=${params.tag_type}`,
    {},
    'get'
  );
}

// 获取MCP列表
export const reqGetMCPList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mcp/version_discovery_list',
    {
      page: params.page || 1,
      keyword: params.keyword || '',
      tag_id: params.tag_id || 0,
      page_size: params.page_size || 50,
      teamid: params.teamid
    },
    'post'
  );
}

// 版本管理-生成新版本
export const reqAgentVersionV3ManageAdd: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/generate',
    params,
    'post'
  );
}

// 版本管理-删除版本
export const reqAgentVersionV3ManageDel: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/delete',
    params,
    'post'
  );
}

// 版本管理-获取当前已发布版本详情
export const reqAgentVersionV3ManageOnlineVersion: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/published_info',
    params,
    'get'
  );
}

// 版本管理-版本发布, 取消发布
export const reqAgentVersionV3ManagePublish: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/publish',
    params,
    'post'
  );
}

// 版本管理-版本描述信息更新
export const reqAgentVersionV3ManageUpdateDesc: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/update_desc',
    params,
    'post'
  );
}

// 版本管理-使用此配置版本
export const reqAgentVersionV3ManageUseSetting: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/version/restore',
    params,
    'post'
  );
}

// 部署
export const reqDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/deployment',
    params,
    'post'
  );
}

// 部署列表
export const reqDeployList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/list',
    params,
    'post'
  );
}

// 文件名列表
export const reqDeployFileList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/chain_deployed_list',
    params,
    'get'
  );
}

// 重新部署
export const reqReDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/redeployment',
    params,
    'post'
  );
}

// 取消部署
export const reqCancelDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/cancel_deployment',
    params,
    'post'
  );
}

// 部署详情
export const reqDeployDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/detail',
    params,
    'get'
  );
}

// web/app 部署开启
export const reqDeployEnable: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/publish',
    params,
    'post'
  );
}

// web/app 部署关闭
export const reqDeployDisable: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/unpublish',
    params,
    'post'
  );
}
// 获取纳米agent列表
export const reqGetNmAgentList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mcp/nami_agent_list',
    {},
    'get'
  );
}

// 获取纳米agent详情
export const reqGetNmAgentDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mcp/nami_agent_detail/'+params.id,
    {},
    'get'
  );
}
// 获取纳米mcp列表
export const reqGetNmMcpList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mcp/nami_mcp_list',
    {},
    'get'
  );
}


// Flow发布页-渠道列表
export const reqFlowChannelList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_channel_list',
    params,
    'get'
  );
}

// Flow发布页-渠道标签
export const reqFlowChannelTags: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_channel_tags',
    params,
    'post'
  );
}
// Flow发布到渠道
export const reqFlowPublic: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_public',
    params,
    'post'
  );
}

// Flow发布渠道审核中-撤销发布
export const reqFlowInReviewCancel: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_submit_cancel',
    params,
    'post'
  )
}

// Flow取消渠道发布
export const reqFlowCancelPublic: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/flow_public_cancel',
    params,
    'post'
  );
}
// 保存位置
export const reqSaveFlowPosition: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/update_meta',
    params,
    'post'
  );
}

// 纳米mcp模型列表
export const reqGetNmMcpModel: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/mcp/nami_mcp_models',
    {},
    'get'
  );
}


// 保存prompt记录
export const reqSavePromptHistory: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/flow/create_prompt_history',
    params,
    'post'
  );
}
// nami-agent 获取MCP列表
export const reqGetNmAgentMcpList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/nami_agent_flow/mcp_list',
    {},
    'get'
  );
}

// nami-agent 获取模型列表
export const reqGetNmAgentModelList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/nami_agent_flow/model_list',
    {},
    'get'
  );
}

// nami-agent 删除节点绑定的自定义UI卡片上所有的formlist
export const reqDelUIMCPAllData: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/v2/nami_ui_mcp/delete_ui_all',
    params,
    'post'
  );
}

// nami-agent 获取MCP详细列表
export const reqGetNmAgentMcpDetailList: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/v2/nami_agent_flow/mcp_detail_list',
    params,
    'get'
  );
}

// 获取提示词历史记录
export const reqGetPromptHistory: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/flow/prompt_history_list',
    params,
    'post'
  );
}

// 复制节点对应的用户界面
export const reqCopyUImcp: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/v2/nami_ui_mcp/copy',
    params,
    'post'
  );
}

// 检查开始节点
export const checkFlow: (params: any) => Promise<any> = (params = {}) => {
	return fetchData(
		'/api/flow/check_flow',
		params,
		'post'
	)
}
// 获取运行历史记录
export const reqGetRunFlowHistory: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/history/flow/access_list',
    params,
    'get'
  );
}

// 获取flow运行详情
export const reqGetRunFlowDetail: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/history/flow/access_flow_detail',
    params,
    'get'
  );
}

// 获取节点详情
export const reqGetRunFlowNodeDetail: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/history/flow/access_block_detail',
    params,
    'get'
  );
}

// 删除节点历史记录
export const reqDelRunFlowNodeHistory: (params: any) => Promise<any> = (params = {}) => {
  return fetchData(
    '/api/history/flow/delete/' + params.log_id,
    {},
    'DELETE'
  );
}
