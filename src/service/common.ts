import { fetchData } from '@/utils/fetch';

// 项目下拉列表
export const reqProjectSelectList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/team/list/select',
    params,
    'get'
  );
}

// 个人信息
export const reqUserInfo: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/user/info',
    {},
    'get'
  );
}

//判断手机号是否在白名单逻辑

export const reqIsWhiteList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    `/api/white_list/${params.phone_number}`,
    params,
    'get'
  );
}

// 部署
export const reqDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/deployment',
    params,
    'post'
  );
}

// 部署列表
export const reqDeployList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/list',
    params,
    'post'
  );
}

// 设置-运维服务-默认agent列表
export const reqAgentTableList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent/report/agent_list',
    params,
    'get'
  );
}

// 设置-运维服务-agent 渠道列表
export const reqAgentChannelList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent/report/channels',
    params,
    'get'
  );
}


// 设置-运维服务-agentCardData
export const reqAgentCardData: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent/report/overview',
    params,
    'get'
  );
}

// 设置-运维服务-agent-服务监控-agentTableList
export const reqChatQuestionList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent/report/question_list',
    params,
    'get'
  );
}

// 设置-运维服务-agentLog
export const reqAgentLog: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent/report/log_detail',
    params,
    'get'
  );
}

// 设置-运维服务-echarts
export const reqAgentEcharts: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agent/report/daily_statistics',
    params,
    'get'
  );
}

// 文件名列表
export const reqDeployFileList: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/chain_deployed_list',
    params,
    'get'
  );
}

// 重新部署
export const reqReDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/redeployment',
    params,
    'post'
  );
}

// 取消部署
export const reqCancelDeploy: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/cancel_deployment',
    params,
    'post'
  );
}

// 部署详情
export const reqDeployDetail: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/deployment/detail',
    params,
    'get'
  );
}

// 公开
// 公开模型
export const reqPublicPrompt: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/front/public_chain',
    params,
    'post'
  );
}
// 撤销提交
export const reqCancelSubmitPublicPrompt: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/front/cancel_submit_public',
    params,
    'post'
  );
}
// 取消公开
export const reqCancelPublicPrompt: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/front/cancel_public',
    params,
    'post'
  );
}

// 语音转文字
export const reqVoiceToText: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/public/asr',
    params,
    'post'
  )
}

// 文字转语音
export const reqTextToVoice: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/public/tts',
    params,
    'post'
  )
}

/**
 * 列表置顶
 * @param params 
 *  template_id: constr(min_length=1) = Field(..., description='实例ID，不可为空')
 *  template_type: conint(ge=1, le=4) = Field(..., description='实例类型：1.prompt, 2:插件, 3:flow, 4:agent')
 *  update_type: conint(ge=1, le=2) = Field(..., description='类型：1.置顶, 2:取消置顶')
 * @returns 
 */
export const reqItemTopUp: (params: any) => Promise<any> = (params) => {
  return fetchData(
    '/api/agents/update_pin',
    params,
    'post'
  )
}
