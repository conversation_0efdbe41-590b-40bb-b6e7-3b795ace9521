import { fetchData } from '@/utils/fetch';
import he from 'he';

// 获取智能体详情 routerfrom =2
export const reqAgentDetailInfo: (params: any) => Promise<any> = async (params) => {
    const res = await fetchData(
        '/api/v2/agents/detail',
        params,
        'get'
    )
    if (typeof res === 'string') {
        return he.decode(res);
    } else if (typeof res === 'object' && res !== null) {
        return JSON.parse(he.decode(JSON.stringify(res)));
    }
   
    return res;
}

// 获取智能体详情-发现 routerfrom =1
export const reqAgentDiscoverDetailInfo: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/agents/public_detail',
        params,
        'get'
    );
}

// 创建/获取会话
export const reqChatSession: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/session',
        params,
        'get'
    );
}

// 提交问题获取结果
export const reqChatSubmit: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/submit',
        params,
        'post'
    );
}

// 获取知识库和技能的结果信息
export const reqChatAnswer: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/answer',
        params,
        'post'
    );
}

// 获取调用flow的基本详情
export const reqChatFlowSend: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/flow_send',
        params,
        'post'
    );
}

let times = 0
// 获取调用flow的每个节点的详情
export const reqChatFlowDetail: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/flow_detail',
        params,
        'get'
    );
}

// 获取每次对话后的建议
export const reqChatSuggestion: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/suggestion',
        params,
        'get'
    );
}

// 获取当前登录人最近一次会话的聊天记录
export const reqChatHistory: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/history',
        params,
        'get'
    );
}

// 获取当前登录人最近一次会话的聊天记录(新)
export const reqChatTalkHistory: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/history_list',
        params,
        'get'
    );
}


// 清除会话
export const reqDeleteChatSession: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/session/' + params.session_id,
        params,
        'delete'
    );
}

// 删除会话中的信息
export const reqDeleteChatMessage: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/message/' + params.message_id,
        {
            agent_id: params.agent_id,
            session_id: params.session_id
        },
        'delete'
    );
}

// 获取所有flow执行信息
export const reqChatFlowDisplay: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/display/' + params.message_id,
        {},
        'get'
    );
}


//智能体停止响应
export const reqAgentStop: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/chat/stop',
        params,
        'post'
    );
}

//智能体,长期记忆，获取列表
export const reqAgentMemoryList: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/memory/list',
        params,
        'get'
    );
}

//智能体,长期记忆，更新
export const reqAgentMemoryUpdate: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/memory/update',
        params,
        'post'
    );
}

//智能体,长期记忆，删除
export const reqAgentMemoryDelete: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/memory/delete',
        params,
        'post'
    );
}
//智能体,长期记忆，全部删除
export const reqAgentMemoryDelAll: (params: any) => Promise<any> = (params) => {
    return fetchData(
        '/api/v2/memory/delete_all',
        params,
        'post'
    );
}
