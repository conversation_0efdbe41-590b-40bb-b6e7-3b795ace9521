<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="48" height="48" rx="24" fill="url(#paint0_linear_7959_65264)"/>
<g filter="url(#filter0_d_7959_65264)">
<path d="M13.875 19.5L20.6286 12.75H32.9975C33.6202 12.75 34.125 13.2622 34.125 13.8658V34.1342C34.125 34.7505 33.6245 35.25 33.0074 35.25H14.9926C14.3754 35.25 13.875 34.7439 13.875 34.1173V19.5ZM21.75 14.4375L15.5625 20.625H21.75V14.4375Z" fill="url(#paint1_linear_7959_65264)"/>
</g>
<defs>
<filter id="filter0_d_7959_65264" x="10.875" y="11.25" width="26.25" height="28.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.5"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00782986 0 0 0 0 0.170833 0 0 0 0 0.141493 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7959_65264"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7959_65264" result="shape"/>
</filter>
<linearGradient id="paint0_linear_7959_65264" x1="24" y1="0" x2="24" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="#7FB5FF"/>
<stop offset="1" stop-color="#1A7BFF"/>
</linearGradient>
<linearGradient id="paint1_linear_7959_65264" x1="24" y1="12.75" x2="24" y2="35.25" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FCFDFF"/>
</linearGradient>
</defs>
</svg>
