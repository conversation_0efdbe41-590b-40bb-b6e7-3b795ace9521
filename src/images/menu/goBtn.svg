<svg width="170" height="68" viewBox="0 0 170 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.75" y="0.75" width="168.5" height="66.5" rx="33.25" fill="white" fill-opacity="0.08"/>
<rect x="0.75" y="0.75" width="168.5" height="66.5" rx="33.25" stroke="url(#paint0_linear_53_8530)" stroke-width="1.5"/>
<rect x="0.75" y="0.75" width="168.5" height="66.5" rx="33.25" stroke="url(#paint1_radial_53_8530)" stroke-opacity="0.2" stroke-width="1.5"/>
<g filter="url(#filter0_i_53_8530)">
<g clip-path="url(#clip0_53_8530)">
<rect x="6" y="6" width="158" height="56" rx="28" fill="url(#paint2_linear_53_8530)"/>
<path d="M30.1 25.58H45.94V27.6H42.8V32.2H46.94V34.22H42.8V42.8H40.7V34.22H35.52C35.32 36.18 34.92 37.84 34.34 39.18C33.56 40.86 32.28 42.16 30.5 43.06L29.36 41.28C30.98 40.34 32.06 39.16 32.62 37.76C32.98 36.76 33.24 35.58 33.4 34.22H29.08V32.2H33.54C33.54 32.08 33.56 31.98 33.56 31.86V27.6H30.1V25.58ZM35.68 27.6V31.86C35.68 31.98 35.66 32.08 35.66 32.2H40.7V27.6H35.68ZM50.98 24.42L53.02 24.62C52.86 25.9 52.7 27.08 52.54 28.18H55.34V29.32C55.24 32.5 54.74 35.26 53.84 37.62C54.68 38.52 55.38 39.34 55.94 40.06L54.56 41.7C54.08 41.06 53.52 40.38 52.88 39.66C52.12 41.02 51.2 42.2 50.12 43.22L48.8 41.56C49.88 40.6 50.76 39.46 51.46 38.14C50.68 37.34 49.84 36.5 48.92 35.62C49.36 33.94 49.78 32.12 50.14 30.16H48.8V28.18H50.46C50.66 26.96 50.82 25.7 50.98 24.42ZM52.34 36.1C52.94 34.34 53.3 32.36 53.42 30.16H52.22C51.88 32.08 51.54 33.68 51.18 34.98C51.58 35.36 51.98 35.74 52.34 36.1ZM65.84 34.22V43.1H63.84V42.02H58.64V43.1H56.66V34.22H65.84ZM58.64 40.06H63.84V36.12H58.64V40.06ZM56.02 32.88L55.68 31C56.6 30.8 57.96 28.64 59.74 24.52L61.74 25.2C60.7 27.42 59.64 29.32 58.56 30.88C60.36 30.76 62.1 30.56 63.8 30.3C63.36 29.36 62.92 28.48 62.46 27.66L64.24 26.8C65.24 28.52 66.18 30.44 67.08 32.54L65.16 33.48C64.96 32.96 64.76 32.5 64.58 32.06C62.02 32.42 59.16 32.68 56.02 32.88ZM78.64 28.02L80.38 28.7C79.58 30.56 78.5 31.98 77.14 32.96L75.64 31.66C77 30.74 78 29.52 78.64 28.02ZM82.88 27.92C84.24 29.36 85.3 30.62 86.04 31.7L84.36 32.86C83.62 31.66 82.62 30.34 81.34 28.88L82.88 27.92ZM71.28 43H69.52L69.06 41C69.58 41.08 70.08 41.14 70.56 41.14C71 41.14 71.24 40.9 71.24 40.42V36.2C70.56 36.44 69.9 36.68 69.22 36.9L68.68 34.8C69.54 34.62 70.4 34.38 71.24 34.1V30.18H69.1V28.2H71.24V24.64H73.32V28.2H74.88V30.18H73.32V33.3C73.82 33.06 74.32 32.82 74.84 32.56V34.64C74.32 34.88 73.82 35.12 73.32 35.36V40.94C73.32 42.3 72.64 43 71.28 43ZM86.4 25.2V29.22H84.44V27.08H77.44V29.3H75.48V25.2H86.4ZM78.34 35.88H75.22V33.98H79.9V32.04H81.88V33.98H86.6V35.88H83.42C84.22 37.44 85.5 38.84 87.26 40.08L85.94 41.76C84.06 40 82.74 38.04 81.94 35.88H81.88V43H79.9V35.88H79.82C78.86 38.32 77.3 40.34 75.18 41.94L74.12 40.16C76 39.02 77.4 37.6 78.34 35.88ZM90.44 25.92H96.94V24.58H99.06V25.92H105.56V27.8H99.06V29.04H106.62V33.04H104.58V30.84H97.74L98.52 31.24C97.48 32.04 96.4 32.74 95.3 33.38C96.7 33.34 97.88 33.3 98.82 33.26C99.5 32.78 100.18 32.28 100.9 31.76L102.56 32.9C100.6 34.34 98.42 35.54 96 36.48C98.08 36.4 100.1 36.32 102.1 36.2C101.74 35.8 101.38 35.4 101.02 35.02L102.52 34.08C103.92 35.48 105 36.72 105.76 37.78L104.2 38.88C103.9 38.44 103.58 38 103.24 37.56C101.82 37.64 100.46 37.72 99.16 37.8V41.18C99.16 42.42 98.5 43.06 97.18 43.06H95.36L94.94 41.14C95.48 41.22 96 41.28 96.5 41.28C96.88 41.28 97.08 41.08 97.08 40.68V37.9C94.96 38 93 38.08 91.2 38.14L90.78 36.28C91.38 36.32 91.98 36.34 92.56 36.34C93.8 36 95.1 35.46 96.48 34.7C95.36 34.74 94.1 34.78 92.74 34.82L92.34 33.3C92.62 33.24 92.9 33.12 93.22 32.96C94.26 32.34 95.26 31.64 96.2 30.84H91.42V33.02H89.38V29.04H96.94V27.8H90.44V25.92ZM101.1 38.48C103.18 39.22 105.06 40.12 106.76 41.16L105.72 42.8C103.88 41.62 102 40.7 100.08 40.02L101.1 38.48ZM94.48 38.44L95.68 39.82C94.12 41 92.2 41.98 89.96 42.74L89.1 40.92C91.3 40.3 93.08 39.48 94.48 38.44Z" fill="white"/>
<path d="M136 18C144.837 18 152 25.1634 152 34C152 42.8366 144.837 50 136 50C127.163 50 120 42.8366 120 34C120 25.1634 127.163 18 136 18ZM137.388 27C137.266 27.0005 137.145 27.0244 137.033 27.0703C136.921 27.1162 136.82 27.1835 136.735 27.2676C136.651 27.3517 136.585 27.4516 136.541 27.5605C136.497 27.6694 136.477 27.7858 136.481 27.9023C136.486 28.0189 136.514 28.1334 136.565 28.2393C136.617 28.3452 136.69 28.4411 136.78 28.5195L141.829 33.127H128.911C128.67 33.127 128.437 33.2191 128.267 33.3828C128.096 33.5465 128 33.7686 128 34C128 34.2314 128.096 34.4535 128.267 34.6172C128.437 34.7809 128.67 34.873 128.911 34.873H141.829L136.78 39.4805C136.69 39.5589 136.617 39.6548 136.565 39.7607C136.514 39.8666 136.486 39.9811 136.481 40.0977C136.477 40.2142 136.498 40.3306 136.541 40.4395C136.585 40.5484 136.651 40.6483 136.735 40.7324C136.82 40.8165 136.921 40.8838 137.033 40.9297C137.145 40.9756 137.266 40.9995 137.388 41C137.51 41.0005 137.631 40.9777 137.743 40.9326C137.856 40.8875 137.958 40.8208 138.043 40.7373L144.722 34.6279C144.81 34.5466 144.88 34.4495 144.928 34.3418C144.976 34.2338 145 34.1173 145 34C145 33.8827 144.976 33.7662 144.928 33.6582C144.88 33.5505 144.81 33.4533 144.722 33.3721L138.043 27.2627C137.958 27.1792 137.856 27.1125 137.743 27.0674C137.631 27.0223 137.51 26.9995 137.388 27Z" fill="white"/>
<g style="mix-blend-mode:plus-lighter" filter="url(#filter1_f_53_8530)">
<ellipse cx="99" cy="60.2695" rx="72" ry="10.5" fill="#BBF8FF"/>
</g>
<g style="mix-blend-mode:plus-lighter" filter="url(#filter2_f_53_8530)">
<ellipse cx="130" cy="-0.730469" rx="33" ry="15.5" fill="#F2FEFF" fill-opacity="0.8"/>
</g>
<g style="mix-blend-mode:plus-lighter" opacity="0.3" filter="url(#filter3_f_53_8530)">
<path d="M19.5 61.7696L145 61.7695" stroke="url(#paint3_linear_53_8530)" stroke-width="2"/>
</g>
<g style="mix-blend-mode:plus-lighter" filter="url(#filter4_f_53_8530)">
<path d="M141.484 -0.230435C94.4844 3.26943 48.3708 2.31744 33.9388 7.76953C-11.0611 24.7696 14.8319 66.3243 13.9847 62.2698C13.1375 58.2152 -41.5153 32.2696 33.9388 -4.54688C49.0401 -7.7023 140.637 -4.28499 141.484 -0.230435Z" fill="#E5D6FF"/>
</g>
</g>
<rect x="6.65385" y="6.65385" width="156.692" height="54.6923" rx="27.3462" stroke="white" stroke-opacity="0.05" stroke-width="1.30769"/>
</g>
<defs>
<filter id="filter0_i_53_8530" x="6" y="6" width="158" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.96154"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_53_8530"/>
</filter>
<filter id="filter1_f_53_8530" x="-3" y="19.7695" width="204" height="81" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_53_8530"/>
</filter>
<filter id="filter2_f_53_8530" x="67" y="-46.2305" width="126" height="91" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_53_8530"/>
</filter>
<filter id="filter3_f_53_8530" x="19" y="60.2695" width="126.5" height="3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_53_8530"/>
</filter>
<filter id="filter4_f_53_8530" x="-15.9336" y="-15.7822" width="167.418" height="88.3291" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_53_8530"/>
</filter>
<linearGradient id="paint0_linear_53_8530" x1="18.7202" y1="-2.9413e-08" x2="38.085" y2="22.0583" gradientUnits="userSpaceOnUse">
<stop stop-color="#C4DDFF"/>
<stop offset="1" stop-color="#006BFF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_53_8530" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(12.0792 3) rotate(40.8254) scale(99.4254 43.6876)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint2_linear_53_8530" x1="13.3488" y1="9.15632" x2="175.906" y2="51.2787" gradientUnits="userSpaceOnUse">
<stop stop-color="#E14CFF"/>
<stop offset="0.432108" stop-color="#4D47F1"/>
<stop offset="1" stop-color="#2A43E4"/>
</linearGradient>
<linearGradient id="paint3_linear_53_8530" x1="24.5" y1="62.7695" x2="145" y2="62.7695" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.49923" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_53_8530">
<rect x="6" y="6" width="158" height="56" rx="28" fill="white"/>
</clipPath>
</defs>
</svg>
