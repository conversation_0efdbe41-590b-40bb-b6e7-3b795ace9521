.knowledgeOutWrapper {
    padding: 0px;
    box-sizing: border-box;
    background-size: cover;
    // width: calc(100% - 220px);
    height: 100vh;
    min-width: 1028px;
    overflow: hidden;
    position: relative;
}
.knowledgeOutFullWrapper{
    width: calc(100vw - 0px);
    background: #f7f8fa;
}

.imageWrapper {
    margin-right: 12px;
    margin-top: 4px;
    border-radius: 8px;
}

.knowledgeWrapper {
    height: 100%;
    // background-color: #1A60FF;

    .knowledgeListWrapper {
        width: 100%;
        height: calc(100vh - 116px);
        overflow: auto;
    }

    .knowledgeListWrapper :hover {
        .addText {
            color: #006BFF;
        }
    }
}

.detailHeaderContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.detailHeader {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    height: 38px;
    width: 206px;
    padding: 4px;
    box-sizing: border-box;
    border-radius: 6px;
    border: 1px solid #FFF;
    background: rgba(235, 240, 245, 0.40);
    box-shadow: 0px 2px 4px 0px rgba(152, 141, 220, 0.22) inset;
    margin-bottom: 8px;
}
.detailHeaderItem {
    width: 45%;
    height: 100%;
    line-height: 30px;
    text-align: center;
    border-radius: 4px;
    font-family: PingFang SC;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    color: #626F84;
    cursor: pointer;
}

.detailHeaderItemActive {
    background: #fff;
    color: #356DEE !important;
    font-weight: 600;
}

// .searchRight {
//     /* height: 42px; */
// }
// .addKnowledgeDataOutWrapper {
//     /* width: 100% !important; */
// }

.knowledgeDataCardWrapper {
    border-radius: 4px;
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.60);
    padding: 16px;
    display: flex;
    width: calc(calc(100% - calc(16px * 3)) / 4);
    height: 122px;
    position: relative;
    float: left;
    box-sizing: border-box;
    min-width: 215px;
    cursor: pointer;
    margin: 0px 16px 16px 0px;

    .addKnowledgeDataWrapper {
        border-radius: 4px;
        width: 100%;
        height: 94px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        cursor: pointer;

        .addText {
            margin-top: 12px;
            color: #626F84;
            cursor: pointer;
        }

    }

    .knowledgeDataCardRight {
        margin-left: 16px;
        width: calc(100%);
        .knowledgeDataCardRightTop {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            height: 28px;
            width: 100%;
            .knowledgeDataCardTitle {
                font-weight: 600;
                margin-right: 8px;
                user-select: text;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 60%;
                color: var(--main-text, #1B2532);
                font-feature-settings: 'clig' off, 'liga' off;

                /* 加粗/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
            }
        }

        .knowledgeDataCardDes {
            color: #9BA7BA;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            height: 66px;
            overflow: hidden;
            user-select: text;
            width: 92%;
            overflow: hidden;

            /* 常规/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
        }
    }
}

.knowledgeDataCardWrapper:nth-child(4n) {
    margin-right: 0px;
}

.knowledgeDataCardWrapper:hover {
    border: 1px solid #3872F6;
    background: linear-gradient(190deg, rgba(255, 255, 255, 0.20) -30.15%, rgba(255, 255, 255, 0.20) 37.18%), #F2F7FF;
    box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.10);
    background: url(../images/knowledge-item-bg.svg);
    background-repeat: no-repeat;
    background-position:left bottom;
}
.statusBlock {
    padding: 5px 8px;
    font-size: 12px;
    border-radius: 4px;

    &.succeed {
        background-color: #F1FEE1;
        color: #4EC01E;
    }
}

.knowledgeDataDetailWrapper {

    width: 100%;
    height: 100%;

    .knowledgeAgentPreview {
        width: 44px;
        height: 63px;
        background: url(../images/knowledge-preview-bg.png);
        background-repeat: no-repeat;
        position: absolute;
        left: 0px;
        bottom: 124px;
        z-index: 100;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @keyframes leftWrapperAnimationOpen {
        from {
            opacity: 0;
            visibility: hidden;
            width: 44px;
            bottom: 124px;
        }

        to {
            opacity: 1;
            visibility: visible;
            width: 25%;
            bottom: 0;
        }
      }

      @keyframes leftWrapperAnimationClose {
        from {
            opacity: 1;
            visibility: visible;
            width: 25%;
            bottom: 0;
        }
      
        to {
            opacity: 0;
            visibility: hidden;
            width: 44px;
            bottom: 124px;
        }
      }

      .leftWrapperAnimationClose {
        animation: leftWrapperAnimationClose 0.3s ease-in-out;
        display: none;
      }

      .leftWrapperAnimationOpen {
        animation: leftWrapperAnimationOpen 0.3s ease-in-out;
      }

    .knowledgeDataDetailWrapperLeft {
        width: 25%;
        height: 100vh;
        border-right: 1px solid #FFF;
        border-left: 1px solid #FFF;
        box-sizing: border-box;
        background: rgba(255, 255, 255, 0.60);
        position: absolute;
        right: 0;
        top: 0;
    }

    .knowledgeDataDetailWrapperLeftHidden {
        width: 25%;
        height: 100vh;
        display: none;
        border-right: 1px solid #FFF;
        border-left: 1px solid #FFF;
        box-sizing: border-box;
        background: rgba(255, 255, 255, 0.60);
        position: absolute;
        left: 0;
        top: 0;
    }

    @keyframes wrapperRightAnimationOpen {
        from {
            width: 75%;
            margin-right: 25%;
        }
      
        to {
            width: 100%;
            margin-right: 0;
        }
      }

      @keyframes wrapperRightAnimationClose {
        from {
            width: 100%;
            margin-right: 0;
        }
      
        to {
            width: 75%;
            margin-right: 25%;
        }
      }

    .wrapperRightAnimationOpen {
        animation: wrapperRightAnimationOpen 0.3s ease-in-out;
    }

    .wrapperRightAnimationClose {
        animation: wrapperRightAnimationClose 0.3s ease-in-out;
    }

    .knowledgeDataDetailWrapperRight {
        width: 75%;
        margin-right: 25%;
        height: 100%;
        padding: 0 0 0 8px;
        box-sizing: border-box;
    }

    // .knowledgeDataDetailWrapperRightFullScreen {
    //     width: 100%;
    //     margin-left: 0;
    //     height: 100%;
    //     box-sizing: border-box;
    // }

    .knowledgeDataDetailHeader {
        padding: 0px 0 16px 0;
        display: flex;
        align-items: center;

        .backBtn {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .nameInput {
            width: 260px;
            margin-right: 2px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            background-color: transparent;
            color: #1A60FF;
        }

        .descriptionInput {
            margin-left: 2px;
            margin-right: 16px;
            border: none;
            color: #626F84;
            background-color: transparent;
        }
    }
}

.knowledgeDataDetailHeaderContent {
    position: relative;

    .rightWrapper{
        position: absolute;
        top: 6px;
        right: 16px;
    }
    .knowledgeManageTypeSelect {
        // background-color: white;
        border-radius: 4px;
        // border: 1px solid #FFF;
        color: #1B2532;
        .ant-select-selector{
            border: 0;
            background: transparent;
        }
    }
    .multiBtnWrapper{
        display: inline-block;
    }
}

.knowledgeImportWrapper {
    display: flex;
    padding: 16px;
}

.knowledgeImportCardWrapper {
    border-radius: 4px;
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.60);

    padding: 24px;
    box-sizing: border-box;
    cursor: pointer;
    margin-right: 16px;

    .knowledgeImportCardTop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;

        .knowledgeImportCardName {
            font-size: 16px;
            font-weight: 600;
        }

        .knowledgeImportCardRadio {
            margin-right: 0px;
        }
    }

    .knowledgeImportCardDes {
        color: #9BA7BA;
    }

    .knowledgeImportCardConfig {
        margin-top: 24px;
        width: 252px;
        border-radius: 4px;

        .knowledgeImportCardConfigItem {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .knowledgeImportCardConfigItemLabel {
                /* width: 160px; */
                margin-right: 8px;
                color: #626F84;
            }
        }
    }

    .knowledgeImportCardConfigItemWrapper {
        padding: 24px 0;
    }

    .imgWrapper {
        background-color: #EBF0F5;
        display: flex;
        justify-content: center;
        padding: 16px;
    }

    .knowledgeImportCardBtns {
        display: flex;
        justify-content: space-around;

        .knowledgeImportCardTemplateDownloadBtn {
            background-color: transparent;
            border-color: #006BFF;
            color: #006BFF;
        }
    }

    .knowledgeImportCardBtnsFlexCenter {
        justify-content: center;
    }
}

.knowledgeImportCardWrapper:hover {
    border: 1px solid #006BFF;
}

.knowledgeImportCardSelected {
    border: 1px solid #006BFF;
    position: relative;
    // background-color: #F2F7FF;

    // .knowledgeImportCardConfig {
    //     // background-color: #E0EEFF;
    // }

    .knowledgeImportCardBtns {
        background: linear-gradient(180deg, rgba(242, 247, 255, 0.00) -6.11%, #FFF 20%);
        padding: 24px 0 24px;
        position: absolute;
        width: 100%;
        bottom: 0px;
        left: 0;
        border-radius: 4px;
    }
}

.knowledgeImportAddFileModalWrapper {
    .knowledgeImportAddFileLinkBtn {
        padding-right: 0;
    }

    .knowledgeImportAddFileItemWrapper {

        .knowledgeImportAddFileItemTitle {
            margin-bottom: 13px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .knowledgeImportAddFileItemTitleLeft {
                display: flex;
                align-items: center;

                .knowledgeImportAddFileItemTitleText {
                    margin-left: 8px;
                    color: #626F84;
                }
            }
        }

        .knowledgeImportAddFileItemInputWrapper {
            max-height: 200px;
            width: 100%;
            overflow: auto;
            .knowledgeImportAddFileItemInputItem {
                display: flex;
                margin-bottom: 8px;
            }
            .knowledgeImportAddLocalFileItemInputItem {
                display: flex;
                flex-direction: row;
                height: 32px;
                width: 100%;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 8px;

                .knowledgeUploadFile {
                    width: 100%;
                    height: 100%;
                    line-height: 32px;
                    text-align: left;
                    background: #F7F9FA;
                    cursor: pointer;
                    padding-left: 8px;
                    border-radius: 6px;
                    overflow: hidden;
                    color:  #BCCAD6;
                    text-overflow: ellipsis;

                    /* 常规/14px */
                    font-family: PingFang SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    .uploadBtn{
                        color: #1D7CFF;
                        margin-right: 5px;
                    }
                }

                .knowledgeUploadFileActive {
                    color: #1B2532;
                }
            }
        }
    }
}

.knowledgeManageWrapper {
    width: 100%;
    height: calc(100vh - 180px);
    overflow-y: auto;
    padding: 16px;

    .knowledgeManageList {
        display: flex;
        flex-wrap: wrap;
        gap: 1.3%;
    }
}

.knowledgeManageWrapperFooter {
    width: 100%;
    height: 78px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    color:  #626F84;
    padding: 0 16px;
    // border-top: 1px solid #FFF;

    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}


.knowledgeManageCardWrapper {
    border-radius: 4px;
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.60);
    padding: 16px;
    width: 24%;
    margin-bottom: 16px;
    cursor: pointer;

    .knowledgeManageCardWrapperTop {
        display: flex;
        align-items: center;
        position: relative;

        .knowledgeManageCardWordCount {
            color: #626F84;
            font-size: 12px;
            margin-left: 8px;
        }

        .knowledgeManageCardQAIcon {
            border-radius: 4px;
            border: 1px solid #006BFF;
            color: #006BFF;
            padding: 0px 8px;
            font-weight: 600px;
            font-size: 12px;
            margin-bottom: 8px;
        }
    }

    .knowledgeManageCardWrapperMiddle {
        margin-top: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 7;
        -webkit-box-orient: vertical;
        height: 157px;
        overflow: hidden;
        user-select: text;
        font-size: 14px;

        .knowledgeManageCardQuestion {
            font-weight: 600;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;  /* 限制显示两行 */
            -webkit-box-orient: vertical;
            overflow: hidden;  /* 超出部分隐藏 */
            text-overflow: ellipsis;  /* 显示省略号 */
        }

        .knowledgeManageCardAnswer {
            color: #626F84;
            display: -webkit-box;
            -webkit-line-clamp: 5;  /* 限制显示五行 */
            -webkit-box-orient: vertical;
            overflow: hidden;  /* 超出部分隐藏 */
            text-overflow: ellipsis;  /* 显示省略号 */
        }
    }
    .deleteCheckbox{
        position: absolute;
        right: 0;
        top: 1px;
    }

    .knowledgeManageCardWrapperBottom {
        margin-top: 8px;
        border-radius: 4px;
        background: #F7F8FA;
        padding: 8px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;

        .knowledgeManageCardWrapperBottomFileName {
            color: #626F84;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: text;
            margin-left: 8px;
        }
    }

}

.knowledgeManageCardWrapper:hover {
    border-color: #006BFF;
}
.knowledgeManageCardActiveWrapper{
    border-color: #006BFF;
}

.titleBorderCardWrapper {
    border-radius: 4px;
    border: 1px solid #D8E0E8;

    .titleBorderCardTitle {
        padding: 8px 12px;
        border-bottom: 1px solid #D8E0E8;
        color: #1B2532;
        font-weight: 600;
    }

    .titleBorderCardContent {
        padding: 10px 12px;
    }
}

.emptyTipsWrapper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .emptyTipsIcon {
        width: 88px;
        height: 88px;
    }

    .emptyTipsText {
        color: #9BA7BA;
    }
}

.knowledgeManageSliceModalWrapper {
    display: flex;
    gap: 16px;

    .knowledgeManageSliceContent {
        width: 60%;

        .knowledgeManageSliceContentTextArea {
            border: 0 none;
            height: 350px;
        }
    }

    .knowledgeManageSliceTagWrapper {
        flex: 1;

        .knowledgeManageSliceTagContent {
            height: 350px;
            overflow-y: auto;
        }

        .knowledgeManageSliceTagContentEmpty {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .knowledgeManageSliceTag {

            .knowledgeManageSliceTagItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;

                .knowledgeManageSliceTagItemContent {
                    margin-right: 8px;
                    // flex: 1;
                    max-width: 100px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .knowledgeManageSliceTagItemBtns {
                    display: flex;
                    gap: 8px;
                }
            }
        }
    }
}

.knowledgeManageQAModalWrapper {
    display: flex;
    gap: 16px;

    .knowledgeManageQAModalLeft {
        // width: 60%;

        .knowledgeManageQaTextArea {
            border: 0 none;
        }

        .knowledgeManageQAModalQuestion {
            margin-bottom: 16px;

            .knowledgeManageQaTextArea {
                height: 100px;
            }
        }

        .knowledgeManageQAModalAnswer {

            .knowledgeManageQaTextArea {
                height: 200px;
            }
        }
    }

    .knowledgeManageQASimilarQuestionWrapper {
        flex: 1;

        .knowledgeManageQASimilarQuestionContent {

            .knowledgeManageQASimilarQuestionContentTop {
                height: 355px;
                overflow: auto;
            }

            .aiGenerateSimilarQuestion {
                border-radius: 4px;
                border: 1px solid #006BFF;
                padding: 3px;
                text-align: center;
                color: #006BFF;
                cursor: pointer;
            }
        }



        .knowledgeManageQASimilarQuestionContentEmpty {

            .knowledgeManageQASimilarQuestionContentTop {
                height: 347px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .knowledgeManageQASimilarQuestion {

            .knowledgeManageQASimilarQuestionItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;

                .knowledgeManageQASimilarQuestionItemContent {
                    margin-right: 8px;
                    flex: 1;
                }

                .knowledgeManageQASimilarQuestionItemBtns {
                    display: flex;
                    gap: 8px;
                }
            }
        }
    }
}

.KnowledgeManageDocListWrapper {

    .KnowledgeDataDocStatusWrapper {
        display: flex;
        align-items: center;

        .KnowledgeDataDocStatusPoint {
            width: 6px;
            height: 6px;
            border-radius: 100%;
            margin-right: 8px;
            flex-shrink: 0;
        }
    }
}

.knowledgeTitleCardWrapper {
    padding: 16px;
    // border-radius: 8px;
    // border: 1px solid #FFF;
    // background: rgba(255, 255, 255, 0.40);

    .knowledgeTitleCardHead {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .knowledgeTitleCardHeadTitle {
            color: #1B2532;
            font-weight: 600;
        }

        .knowledgeTitleCardHeadTips {
            color: #626F84;
            font-size: 12px;
        }
    }

    .knowledgeTitleCardContent {
        margin-top: 16px;
    }
}

.knowledgeChatDetailWrapper {
    .knowledgeChatDetail{
        display: flex;
        height: calc(100vh - 120px);
    }
    .knowledgeChatDetailContent {
        width: 70%;
        height: 100%;
        display: flex;
        flex-direction: column;
        // gap: 16px;
        overflow-y: auto;
    }
}
.knowledgeChatDetailReplay {
    .model {
        // margin-top: 20px;
        margin-bottom: -8px;
        .nameTitle {
            color: #626F84;
            text-align: left;
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            margin-bottom: 8px;
        }
    }
}

.policyTitle {
    color: #626F84;
    text-align: left;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 8px;
}

.policyContent {
    margin-bottom: 16px;
}

.dragContentItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 4px 0px;
    margin-bottom: 8px;
    color: var(---, #1B2532);
    font-feature-settings: 'clig' off, 'liga' off;

    /* 常规/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}

.dragContentItemLeftTitle {
    margin-left: 8px;
}
.dragContentItemLeft {
    width: 75px;
}
.knowledgeTitleCardHeadUnfoldCommon {
    margin-left: 4px;
    transition: transform 0.3s ease-in-out;
    cursor: pointer;
    width: 20px;
    vertical-align: middle;
}
.knowledgeTitleCardHeadUnfold {
   // 旋转 180度
    transform: rotate(180deg);
    transition: transform 0.3s ease-in-out;
}
.knowledgeChatDetailAiIcon {
    margin-left: 8px;
    cursor: pointer;
    width: 16px;
    vertical-align: middle;
}

.knowledgeDataSimpleCardWrapper {
    border-radius: 8px;
    border: #FFF;
    background: rgba(255, 255, 255, 0.80);
    padding: 16px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 100px;
    border: 1px solid transparent;

    .knowledgeDataSimpleCardHead {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .knowledgeDataSimpleCardHeadLeft {
            display: flex;
            align-items: center;
            width: 90%;

            .knowledgeDataSimpleCardHeadLeftTitle {
                font-weight: 600;
                margin-right: 8px;
                color: #1B2532;
                font-feature-settings: 'clig' off, 'liga' off;
                /* 加粗/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                width: 80%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .knowledgeDataSimpleCardBottom {
        color: #9BA7BA;
        margin-top: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        /* 常规/14px */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }
}

.knowledgeDataSimpleCardWrapperSelected {
    border-radius: 4px;
    border: 1px solid #3872F6;
    background: #F2F7FF;
}

.knowledgeDataSelectModalOutWrapper{
    height: 500px;
    overflow: auto;
}

.knowledgeChatDetailDataSelectWrapper {
   
    max-height: 232px;
    overflow-y: auto;
    overflow-x: hidden;
    .knowledgeChatDetailDataSelectAddCard {
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.80);
        padding: 16px;
        box-sizing: border-box;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #9BA7BA;
        text-align: center;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
    }
}

.knowledgeChatDetailConfigWrapper {
    width: 100%;

    .knowledgeChatDetailConfigItem {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        // background-color: #006BFF;
        margin-bottom: -8px;

        &:last-child {
            margin-bottom: -24px;
        }

        .knowledgeChatDetailConfigItemLabel {
            color: #626F84;
            /* 常规/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            margin-bottom: 8px;
        }
    }
}

.knowledgeChatDetailDataCheckBox {
    color: #1B2532;

    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-top: 16px;
}

.knowledgeChatDetailDataCheckBoxTis {
    color: var(---, #626F84);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/12px */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
}

.knowledgeChatDetailTestPreviewWrapper {
    height: 100%;
    width: 30%;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    border-left: 1px solid var(---Gray-20, #EBF0F5);

    .knowledgeChatDetailTestPreviewWrapperHeader {
        width: 100%;
        display: flex;
        justify-content: space-between;    
        background: #F7F8FA;
        padding: 16px;
        
        .headerTitle {
            color: #1B2532;         
            /* 加粗/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
        }
    }

    .knowledgeChatDetailTestPreviewMessageWrapper {
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        gap: 32px;
        padding: 16px 16px 5px 16px;
        height: calc(100% - 112px);
        overflow: auto;

        .knowledgeChatDetailTestPreviewMessageItem {
            display: flex;
            gap: 8px;

            .knowledgeChatDetailTestPreviewMessageItemAvatar {
                width: 24px;
                height: 24px;
                flex-shrink: 0;

                .knowledgeChatDetailTestPreviewMessageItemAvatarImg {
                    width: 100%;
                    height: 100%;
                }
            }

            .knowledgeChatDetailTestPreviewMessageItemContent {
                padding: 16px;
                border-radius: 4px;
                user-select: text;
                max-width: 100%;
                word-break: break-word;
                color: var(---, #1B2532);
                font-feature-settings: 'clig' off, 'liga' off;
                margin-left: 32px;
                background: var(---g-10, #F7F8FA);


                /* 常规/14px */
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                // background: var(---g-10, #F7F8FA);

                .knowledgeChatDetailTestPreviewMessageItemContentValue{
                    white-space: pre-wrap;
                }

                .knowledgeChatDetailTestPreviewMessageItemContentSource{
                    margin-top: 16px;
                    display: flex;
                    flex-direction: column;

                    .knowledgeChatDetailTestPreviewMessageItemContentSourceFileWrapper{
                        border-radius: 4px;
                        background-color: #F7F9FA;
                        padding: 4px 8px 4px 0;

                        .knowledgeChatDetailTestPreviewMessageItemContentSourceFileItem{
                            display: flex;
                            align-items: center;
                            cursor: pointer;

                            .knowledgeChatDetailTestPreviewMessageItemContentSourceFileIcon{
                                flex-shrink: 0;
                                display: flex;
                                align-items: center;
                            }

                            .knowledgeChatDetailTestPreviewMessageItemContentSourceFileName{
                                color: #626F84;
                            }

                            .knowledgeChatDetailTestPreviewMessageItemContentValueScore {
                                color: #626F84;
                                font-size: 12px;
                                line-height: 20px;
                                border-radius: 11px;
                                background: rgba(0, 0, 0, 0.05);
                                padding: 0 8px;
                                margin-top: 2px;
                                display: inline-block;
                                margin-left: 8px;
                                margin-bottom: 2px;
                            }
                        }
                    }
                }
            }
        }

        .knowledgeChatDetailTestPreviewMessageItemRight {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
        }
    }

    .knowledgeChatDetailTestPreviewInputWrapper {
        display: flex;
        position: absolute;
        width: 100%;
        left: 0;
        padding: 16px;
        margin-top: 16px;
        background: #fff;
        box-shadow: 0px -11px 25px 4px rgba(27, 37, 50, 0.07);
        input{
            margin-right: 12px;
        }
    }

}
.itemMoreImg {
    width: 20px;
    height: 20px;
    position: absolute;
    display: inline-block;
    right: 16px;
    top: 20px;
    border-radius: 2.857px;
    border: 0.714px solid #FFF;
    background: #F3F7FF;
}

.itemManageMoreImg {
    width: 20px;
    height: 20px;
    position: absolute;
    display: inline-block;
    right: 0px;
    top: 0px;
    border-radius: 2.857px;
    border: 0.714px solid #FFF;
    // background: #F3F7FF;
}

.matchTextContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    height: calc(100vh - 158px);
    padding: 16px;

    .matchTextQa {
        width: 100%;
        margin-bottom: 16px;
        height: 130px;

        .matchTextQaContent {
            width: 100%;
            height: 98px;
            border-radius: 4px;
            border: 1px solid #fff;
            background: rgba(255, 255, 255, 0.60);
        }
    }

    .matchTextResult {
        flex: 1;
        width: 100%;
    }

    .matchTextTitle {
        color: #1B2532;
        font-feature-settings: 'clig' off, 'liga' off;
        /* 加粗/14px */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-bottom: 8px;
    }
    .matchTextResultContentNoData {
        width: 100%;
        height: calc(100% - 10px);
        border-radius: 4px;
        border: 1px solid #fff;
        background: #fff;
        padding: 16px;
        box-sizing: border-box;
        overflow-y: auto;
    }

    .matchTextResultContent {
        width: 100%;
        height: calc(100vh - 344px);
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.60);
        overflow-y: auto;
    }

    .matchTextResultItem {
        width: 100%;
        padding: 16px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #fff;
        /* background: rgba(255, 255, 255, 0.60);  */
        margin-bottom: 8px;
    }

    .matchTextResultItemContent {
        color: var(--subordinate-text, #626F84);
        font-feature-settings: 'clig' off, 'liga' off;
        text-overflow: ellipsis;

        /* 常规/14px */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    .matchTextResultItemRadio {
        width: 100%;
        height: 38px;
        line-height: 38px;
        text-align: left;
        border-radius: 4px;
        background: rgba(235, 240, 245, 0.70);
        margin-top: 8px;
        overflow: hidden;
        color: var(--subordinate-text, #626F84);
        font-feature-settings: 'clig' off, 'liga' off;
        text-overflow: ellipsis;
        padding-left: 8px;

        /* 常规/14px */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }
}

.descWrapper {
    margin-top: 4px;
    max-width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;

    .descWrapperItem {
        padding: 1px 8px;
        border-radius: 4px;
        background: var(---Gray-20, #EBF0F5);
        margin-right: 8px;
        color: var(---, #626F84);
        font-feature-settings: 'clig' off, 'liga' off;
        text-wrap: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300px;
        display: inline-block;

        /* 常规/12px */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
    }
}
.fileNameContent {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}
.nameWrapperTop {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-top: 3px;
}
.nameWrapper {
    color: #1B2532 !important;
    font-weight: 600;
    // line-height: 28px;
    font-size: 18px;
    margin-right: 5px;
    max-width: 600px;
    overflow: hidden; 
    text-overflow: ellipsis; 
    white-space: nowrap; 
    display: inline-block;
}
.autoSaveTis {
    border-radius: 2px;
    background: var(---Gray-20, #EBF0F5);
    color: var(---, #626F84);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/12px */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    padding: 2px 8px;
    margin-left: 32px;
}
.detailContent {
    width: 100%;
    height: calc(100% - 96px);
}
.knowledgeStopBtn {
    position: absolute;
    left: 40%;
    bottom: 60px;
}
.knowledgeChatAgentContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}
.knowledgeChatUserAnswer {
    // background: #1D7CFF;

    .userContent {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;

        .userContentImg {
          border-radius: 12px;
          width: 24px;
          height: 24px;
        }
        .userContentName {
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;
            margin-left: 8px;            
            /* 常规/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
.knowledgeChatUserAnswerMessage {
    padding: 2px 10px;
    border-radius: 4px;
    background: var(----70-, #006BFF);
    color: #FFF;
    /* 加粗/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    margin-left: 32px;
    line-height: 22px; /* 157.143% */
}

.knowledgeDataSelectModalHeader {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.popoverContent {
    padding:  0 24px 24px 24px;
    border-radius: 8px;
    box-sizing: border-box;
    width: 400px;
    height: 400px;
    .popoverContentHeader {
        padding: 16px 8px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        span {
            color: var(---Main-Text, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;

            /* 加粗/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
        }

        img {
            cursor: pointer;
        }
    }
    .popoverContentBody {
        height: calc(100% - 50px);
        border-radius: 4px;
        background: var(---g-10, #F7F8FA);
        padding: 16px;
        box-sizing: border-box;
        overflow-y: auto;
    }
}