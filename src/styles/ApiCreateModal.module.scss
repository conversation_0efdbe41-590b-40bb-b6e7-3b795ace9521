.header {
    display: flex;
    flex-direction: column;
    padding: 0 8px;
    margin-bottom: 24px;
    .typeBtnWrapper{
        display: flex;
        margin-bottom: 24px;
        .typeBtn{
            width: 50%;
            border-radius: 8px;
            border: 1px solid #D8E0E8;
            padding: 12px 5px 12px 0;
            text-align: center;
            margin-right: 16px;
            cursor: pointer;
            img{
                margin-right: 8px;
                width: 14px;
                height: 14px;
                position: relative;
                top: 2px;
            }
            &:last-child{
                margin-right: 0px;
            }
        }
        .activeBtn{
            border: 1px solid #006BFF;
            background: #F2F7FF;
            color: #006BFF;
            font-weight: 600;
        }
    }
    .nameInput {
        border: none;
        border-bottom: 1px solid #006BFF;
        margin-bottom: 24px;
        caret-color: #006BFF;
        padding: 5px 5px 5px 0;
        color: #1B2532;
        font-size: 14px;
        line-height: 22px;
    }

    .nameInput:focus {
        outline: none;
    }

    .aiBtn {
        display: flex;
        flex-direction: row;
        align-items: center;

        .leftBar, .rightBar {
            background-color: #EBF0F5;
            width: 138px;
            height: 1px;
        }

        .aiButton, .aiButtonActive {
            border-radius: 8px;
            border: 1px solid #EBF0F5;
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 5px 16px;
            margin: 0 9px;
            min-width: 182px;
            box-sizing: border-box;
            background-color: #F7F9FA;                

            .aiIcon {
                width: 14px;
                height: 14px;
                margin-right: 4px;
            }

            .aiText {
                font-size: 14px;
                line-height: 22px;
                color: #BCCAD6;
            }
        }

        .aiButtonActive {
            border: 1px solid #006BFF;
            color: #006BFF;
            background-color: #FFFFFF;
            margin-left: 148px;
            cursor: pointer;

            .aiIcon {
                fill: #006BFF;
            }

            .aiText {
                color: #006BFF;
            }
        }
    }
}

.content {
    display: flex;
    flex-direction: row;
    background-color: #F7F9FA;
    width: calc(100% + 45px);
    margin: 0 0 0 -23px;
    padding: 24px;

    .baseCon {
        display: flex;
        flex-direction: row;
        width: 100%;

        .conLeft, .conRight {
            background-color: #FFFFFF;
            border-radius: 8px;
        }
    
        .conLeft {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 134px;
            height: 200px;
            border-radius: 8px;
            margin-right: 16px;
            position: relative;
    
            .localRepeat {
                position: absolute;
                top: 48px;
                width: 64px;
                height: 64px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
    
                img {
                    width: 0;
                }
            }
    
            .localRepeat:hover {
                background: rgba(19, 19, 26, 0.60);
                backdrop-filter: blur(1px);
    
                img {
                    width: 17px;
                    height: 17px;
                }
            }
    
            .glass {
                position: absolute;
                top: 50px;
                width: 66px;
                height: 66px;
                border-radius: 8px;
                backdrop-filter: blur(6px);
                display: flex;
                align-items: center;
                justify-content: center;
    
                @keyframes turn {
                    0% {
                        transform: rotate(0deg);
                    }
    
                    20% {
                        transform: rotate(72deg);
                    }
    
                    40% {
                        transform: rotate(144deg);
                    }
    
                    60% {
                        transform: rotate(216deg);
                    }
    
                    80% {
                        transform: rotate(288deg);
                    }
    
                    100% {
                        transform: rotate(360deg);
                    }
                }
    
                img {
                    animation: turn 3s linear infinite;
                }
            }
    
            .aiImage {
                width: 64px;
                height: 64px;
                border-radius: 8px;
                margin-bottom: 16px;
            }
    
            .localUpload {
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 2px 12px;
                border-radius: 4px;
                background-color: #F7F9FA;
                cursor: pointer;
    
                .uploadIcon {
                    width: 12px;
                    height: 12px;
                    margin-right: 4px;
                }
    
                .uploadText {
                    color: #006BFF;
                    font-size: 12px;
                    line-height: 20px;
                }
            }
    
            .aiLoading {
                display: flex;
                flex-direction: row;
                align-items: center;
    
                img {
                    width: 12px;
                    height: 12px;
                    margin-right: 4px;
                }
    
                .loadText {
                    color: #9BA7BA;
                    font-size: 12px;
                    line-height: 20px;
                }
            }
    
            .repeatAi {
                display: flex;
                flex-direction: row;
                align-items: center;
                background-color: #F7F9FA;
                font-size: 12px;
                line-height: 20px;
                color: #006BFF;
                padding: 2px 12px;
                border-radius: 8px;
    
                img {
                    width: 14px;
                    height: 14px;
                    margin-right: 4px;
                }
    
                .repeatBtn {
                    cursor: pointer;
                }
            }
        }
    
        .conRight {
            width: 100%;
            position: relative;

            :global {
                .ant-input-outlined:focus-within {
                    border: 1px solid #1677EF !important;
                    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
                }
            }
    
            .textArea {
                height: 100% !important;
                width: 100% !important;
                border-radius: 8px !important;
            }
    
            .repeatAi {
                display: flex;
                flex-direction: row;
                align-items: center;
                font-size: 12px;
                line-height: 20px;
                color: #006BFF;
                border-radius: 8px;
                position: absolute;
                left: 5px;
                bottom: 12px;
                z-index: 99;
    
                img {
                    width: 14px;
                    height: 14px;
                    margin-right: 4px;
                }
    
                .repeatBtn {
                    cursor: pointer;
                }
            }
    
            :global .ant-input-affix-wrapper-focused {
                border: none !important;
            }
    
            :global .ant-input-data-count {
                right: 12px;
                bottom: 5px;
            }
    
            :global .ant-input-textarea-show-count > .ant-input {
                height: 80%;
            }
        }
    }
}

.filepond {
    display: none;
}



.createMode{
    margin-top: 24px;
    margin-bottom: -31px;
  .importContent{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .importImg{
    display: flex;
    width: 28px;
    height: 28px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: #FFF;
  }

  .linkIcon{
    cursor: pointer;
    fill: #9EA7B8;
  }
  .importContent:hover{
    .linkIcon {
        fill: #006BFF;
    }
    .importText{
        color: #006BFF;
    }
  }
  :global {
          .ant-upload-drag{
              margin-top: 12px;
              display: flex;
              height: 100px;
              padding: 0px 16px;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              border-radius: 4px;
              border: 1px dashed  #D8E0E8;
              background:  #F7F9FA;
          }
          .ant-upload-drag:hover{
              border: 1px dashed  #006BFF;
          }
  }
}

.externalApiFields {
  margin-top: 24px;
}
.fieldContainer {
  margin-bottom: 24px;
}
.fieldLabel {
  margin-bottom: 4px;
  color: #626F84;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  display: flex;
  align-items: center;
}
.required {
  color: #ff4d4f;
  margin-right: 4px;
}
.fieldInput {
  width: 100%;
}


