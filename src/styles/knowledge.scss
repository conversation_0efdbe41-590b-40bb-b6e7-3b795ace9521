.knowledge{
    .ant-btn-link{
      color: #1D7CFF;
    }
    .ant-btn-link:disabled{
      color: #BCCAD6;
    }
    .ant-btn-primary{
      background: #1D7CFF;
    }
    .ant-btn-default{
      border-color: #D8E0E8;
    }
    
    .ant-tabs-nav-list{
      margin-left: 16px;
    }
  
    .waiting{
      background-color: #EBF0F5;
      color: #626F84;
    }
    .doing{
      background-color: #EEF6FF;
      color: #477FFF;
    }
    .succeeded{
      background-color: #F1FEE1;
      color: #4EC01E;
    }
    .failed{
      background-color: #FFF4F2;
      color: #FF5E68;
    }
  
    .knowledge-data-doc-status-point{
      &.waiting{
        background-color:#626F84;
      }
      &.doing{
        background-color:#477FFF;
      }
      &.succeeded{
        background-color:#4EC01E;
      }
      &.failed{
        background-color: #FF5E68;
      }
    }
   
    .add-knowledge-data-out-wrapper:hover .add-icon,
    .back-btn:hover .back-icon,
    .edit-icon:hover,
    .remove-icon:hover{
      fill: #1D7CFF !important;
    }
  
    .ant-table-wrapper .ant-table-thead>tr>th,
    .ant-table-wrapper .ant-table-tbody>tr>td{
      border-bottom-color: #D8E0E8;
      padding: 9px 16px;
    }
  
    .ant-table-wrapper .ant-table-thead>tr>th{
      color: #626F84;
    }
  
    .ant-table-wrapper .ant-table-tbody>tr>td{
      color: #1B2532;
    }
  
    .ant-table-cell .ant-btn-link{
      padding-left: 0;
    }
  
    .knowledge-chat-detail-test-preview-message img{
      width: 100%;
      height: auto;
    }
  
}
div.knowledge-modal{
    .ant-modal-content{
      background-color: #F7F9FA;
      padding: 0;
    }
    .ant-modal-header {
      background: transparent;
      padding: 16px 24px;
      border-bottom: 1px solid var(--gray-cold-gray-20, #EBF0F5);
      margin-bottom: 0;
    }
    .ant-modal-body{
      background-color: white;
      padding: 16px 24px;
    }
    .ant-modal-footer{
      border-top: 1px solid #EFF0F2;
      background-color: white;
      margin-top: 0;
      padding: 12px 24px;
      border-radius: 0px 0px 8px 8px;
    }
}
.knowledge-item-dropdown .ant-dropdown-menu{
    padding:8px 2px;
}
  
.knowledge-item-dropdown .ant-dropdown-menu .ant-dropdown-menu-item {
    color: #1B2532 !important;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}
  
.knowledge-item-modal-content {
    background-color: #fff !important;
}
  
.knowledge-index-tabs .ant-tabs-nav {
    display: none;
}
  
.knowledge-data-detail-tabs .ant-tabs-nav {
    display: block;
}
  
.knowledge-data-detail-tabs{
    border-radius: 8px;
    border: 1px solid #fff;
    /* background: linear-gradient(90deg, rgba(255, 255, 255, 0.52) 2.85%, rgba(255, 255, 255, 0.34) 98.96%); */
    box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(2px);
    
}
  
.knowledge-data-detail-tabs .ant-tabs-content {
    border-radius: 0px 0px 8px 8px;
    height: calc(100vh - 126px);
    overflow-y: hidden;
}
  
.knowledge-add-file-modal-upload {
    display: inline-block;
    width: 100%;
}

/* upload */
.knowledge-add-file-modal-upload .ant-upload-drag .ant-upload{
  padding: 0;
}
.knowledge-add-file-modal-upload .ant-upload-drag{
  border: 0;
}
.knowledge-add-file-modal-upload .ant-upload-list .ant-upload-list-item{
  display: none;
}
.knowledge-add-file-modal-upload .ant-upload-select{
  display: block;
}
.knowledge-select {
  background-color: #fff;
  width: 100px;
  height: 30px;
  border-radius:6px 0 0 6px;
  box-sizing:'border-box';
}
  
.knowledge-index-input {
    border-radius: 0 6px 6px 0 !important;
}

.knowledge-drag .ant-upload .ant-upload-btn {
  padding: 0px !important;
}

.knowledge-step-file-list{
  .ant-table-container .ant-table-thead>tr>th, .ant-table-container .ant-table-tbody>tr>td{
    border-bottom-color: #EBECF0;
    padding: 9px 16px;
    height: 48px;
    
  }

  .ant-table-container .ant-table-thead>tr>th{
    // background: #F7F8FA;
    border-radius: 0 !important;
    font-weight: 400;
    color: #696B7A;
  }
}