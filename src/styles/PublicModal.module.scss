.publicItem{
    display: flex;
    margin-bottom: 28px;
}
.lastItem{
    margin-bottom: 0px;
}
.publicLabel{
    width: 42px;
    flex: 0 0 auto;
    margin-right: 17px;
    color: #1B2532;
    font-size: 14px;
}
.tagItem{
    background: #EEF6FF;
    color: #477FFF;
    padding: 5px 12px;
    margin-right: 17px;
    border-radius: 4px;
    margin-bottom: 17px;
    display: inline-block;
    cursor: pointer;
}
.tagItem img{
    display: inline-block;
    vertical-align: middle;
    margin-right: 3px;
    margin-top: -3px;
}
.tagCheckedItem{
    background: #477FFF;
    color: #fff;
}
.markDownModalBg{
    background: #fff;
    border-radius: 10px;
    padding-bottom: 0;
}
.deployTerms{
    padding-top: 50px;
}
.deployTermsTitle{
    padding-top: 16px;
}
.fuWuA{
    color: #477FFF;
    margin-left: -10px;
}
.modalImage {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center; 
    padding: 24px 0px;
    box-sizing: border-box;   

    img {
        width: 64px;
        height: 64px;
        border-radius: 8px;
        display: inline-block;
    }
}

.agentModalImage {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
        margin-bottom: 12px;
    }
}