.tiggerCommonWrapper {
    height: 355px;
}

.actionCommonWrapper, .tiggerCommonWrapper, .commonWrapper{
    background: #fff;
    border: 1px solid #fff;
    box-shadow: 0 5px 20px rgba(0,0,0,.04);
    border-radius: 8px;
    padding: 16px;
    position: relative;
    margin: 0 16px 16px;

    .contentWrapper {
        max-height: calc((100vh - 291px)*.63);
        min-height: 300px;
        overflow-y: hidden;
    }

    .actionContentWrapper, .contentWrapper{
        .actionLabel {
            display: flex;
            flex-direction: row;
            align-items: center;

            img {
                width: 16px;
                height: 16px;
                margin-left: 8px;
                cursor: pointer;
            }
        }

        :global {
            .ant-form-item .ant-form-item-label >label .ant-form-item-tooltip {
                width: 14px;
                height: 14px;
                margin-left: 8px;
            }

            .ant-input-group .ant-input-group-addon:first-child {
                color: #9EA7B8;
            }
        }
    }

    input{
        border-radius: 8px;
        border: 1px solid #D5D7DE;
    }
}
.actionContent {
    height: calc(100vh - 96px);
    overflow-y: auto;
}
.topWrapper, .contentWrapper, .contentIdeWrapper{
    display: flex;
    justify-content: flex-start;
}
.topWrapper{
    margin-bottom: 16px;
}
.autoCommonWrapper{
    height: auto;
}
.responseCommonWrapper{
    .contentWrapper{
        height: calc((100vh - 291px)*.37);
        overflow-y: hidden;
    }
    input{
        border-radius: 8px;
        border: 1px solid #D5D7DE;
    }
}
.leftWrapper{
    width: calc(100% - 416px);
    border-right: 1px solid #fff;
    padding-right: 16px;
    overflow-y: auto;
}
.ideTable {
    border-right: 1px solid #fff;
    padding-right: 16px;
    max-height: 290px;

    overflow-y: auto;
}
.ideLeftWrapper{
    width: calc(100% - 416px);
}
.tiggerLeftWrapper{
    width: calc(100% - 416px);
    border-right: 1px solid #fff;
    padding-right: 16px;
    overflow-y: auto;
}

.fullLeftWrapper{
    width: calc(100% - 416px);
}
.configTitle {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 13px;

    .operateBtn {
        margin-right: 10px;
    }
}
.configItem {
    margin-bottom: 24px;
    overflow-x: auto;
    min-width: 900px;
}
.tiggerConfigItem {
    margin-bottom: 24px;
    overflow-x: auto;
    min-width: 700px;
}
.configHeader {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}
.hideConfigItem{
    display: none;
}
.commonTitle{
    color: #1B2532;
    font-size: 16px;
    font-weight: 600;
    line-height: 32px;
}

.chooseMethod{
    width: 100%;
}
.rightWrapper{
    width: 400px;
    margin-top: 10px;
    margin-left: 16px;
    border-radius: 4px;
    background: #FFF;
}
.fullRightWrapper{
    width: 100%;
    margin-left: 0px;
}
.hideWrapper{
    display: none;
}
.testResult {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.resultTitle{
    display: flex;
    flex-direction: row;
    align-items: center;    
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 12px;

    img {
        width: 16px;
        height: 16px;
        margin: 0 8px 0 16px;
    }

    .resultLabel {
        color: #626F84;
    }

    .resultPass {
        color: #00B42A;
    }
}
.textAreaWrapper{
    background: #fff;
    height: calc(100% - 45px);
    border-radius: 8px;
    border: 1px solid #D5D7DE;
    padding-top: 2px;
}

/* table */
.tableTitle{
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    th{
        background: #F7F8FA;
        border-bottom: 1px solid #D5D7DE;
        border-top: 1px solid #D5D7DE;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 400;
        color: #626F84;
        text-align: left;
        padding:0 16px;
        border-right: 0;
        border-left: 0;
    }
    td{
        border-bottom: 1px solid #D5D7DE;
        opacity: 0.8;
        background: rgba(255, 255, 255, 0.70);
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 400;
        color: #1B2532;
        text-align: left;
        padding:0 16px;
    }
}
.pTable{
    overflow-x: auto;
}
.pTable table{
    min-width: 900px;
}
table.tableTitle tr:first-child th:first-child {
    border-top-left-radius: 4px;
}

table.tableTitle tr:first-child th:last-child {
    border-top-right-radius: 4px;
}
table.tableTitle tr:last-child td:first-child {
    border-bottom-left-radius: 4px;
}

table.tableTitle tr:last-child td:last-child {
    border-bottom-right-radius: 4px;
}
.tableTitle td.lastItem, .tableTitle th.lastItem{
    border-right: 1px solid #D5D7DE;
    width: 100px;
}
.tableTitle td.lastItemInModal, .tableTitle th.lastItemInModal{
    border-right: 1px solid #D5D7DE;
}
.tableTitle td.firstItem, .tableTitle th.firstItem{
    border-left: 1px solid #D5D7DE;
    min-width: 100px;
}
.tableTitle td.expandItem {
    position: relative;
    width: 180px;
}
.addBtn{
    color: #006BFF;
    margin-right: 10px;
    cursor: pointer;
}
.addApiBtn{
    cursor: pointer;
    display: inline-block;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 14px;
    &:hover{
        color: #4096ff;
        border-color: #4096ff;
    }
    span{
        margin-right: 4px;
        width: 12px;
    }
}
.addedApiBtn{
    color: #4EC01E;
    cursor: pointer;
    display: inline-block;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 14px;
    img{
        margin-right: 0px;
        vertical-align: middle;
        width: 13px;
        margin-top: -2px;
    }
}
.deleteApiBtn{
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    background: #EBECF0;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 14px;
    img{
        margin-right: 0px;
        vertical-align: middle;
        width: 13px;
        margin-top: -2px;
    }
}
.removeBtn{
    color: #006BFF;
    cursor: pointer;
}
.removeDisabledBtn{
    color: #ccc;
    cursor: not-allowed;
}
.nameInput{
    display: flex;
}
.nameInput span{
    color: #626F84;
    padding-right: 4px;
    cursor: pointer;
}
.nameInput input{
    display: inline-block;
    vertical-align: middle;
    height: 32px;
    margin-top: 0px;
}
.nameInput1 input{
    margin-top: 4px;
}
.gapItem{
    height: 40px;
}

.tabs{
    margin-bottom: 8px;
}
.bodyType{
    margin-top: 5px;
}
.btnWrapper{
    display: flex;
    justify-content: right;
}

.jsoneditor{
    height: 100%;
}
.requiredIcon{
    color: #ff4d4f;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
}
.correspondingInput{
    display: inline-block;
    vertical-align: middle;
    margin-top: -2px;
}

/* apiModal */
.apiModal{
    width: 896px!important;
}
.apiListModal, .fcApiListModal{
    max-height: 500px;
    min-height: 500px;
    overflow-y: auto;
    padding: 0 16px;
    margin-bottom: -65px;
}
.apiItemModal{
    position: relative;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    height: 160px;
    width: calc((100% - 32px) / 3);
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 4px;
    background: #F7F8FA;
    border: 1px solid transparent;
    &:hover{
        border: 1px solid #D5D7DE;
    }
}
.fcApiItemModal{
    position: relative;
    // cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    height: 160px;
    width: calc((100% - 32px) / 3);
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 4px;
    background: #F7F8FA;
    border: 1px solid transparent;
    &:hover{
        border: 1px solid #D5D7DE;
    }
}
.lastApiItemModal{
    margin-right: 0px;
}
.apiTitleModal{
    color: #262B33;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}
.apiTitleModal img, .apiTitleModal span{
    display: inline-block;
    vertical-align: middle;
}
.apiTitleModal span{
    margin-left: 8px;
    width: 80%;
}
.fcApiTitleModal span{
    width: 61%;
}
.apiDescModal {
    color: #8592A6;
    font-size: 12px;
    font-weight: 400;
    max-height: 86px;
    overflow-y: hidden;
}
.btnWrapperModal{
    width: 100%;
    padding: 16px;
    position: absolute;
    display: flex;
    justify-content: right;
    bottom: 0;
    right: 0;
    background: linear-gradient(180deg, rgba(247, 248, 250, 0.87) 22.66%, #F7F8FA 76.56%);
    opacity: 0;
    border-radius: 0 0 4px 4px;
}
.showBtnWrapperModal{
    animation: showBtn 1s;
    animation-fill-mode:forwards;
}
.hideBtnWrapperModal{
    /* animation: hideBtn 0.5s;
    animation-fill-mode:forwards; */
}
.fcCheckBox{
    position: absolute;
    right: 16px;
    top: 21px;
    height: 16px;
}

@keyframes showBtn
{
    from {opacity: 0;}
    to {opacity: 1;}
}
@keyframes hideBtn
{
    from {opacity: 1;}
    to {opacity: 0;}
}
.useButton{
    margin: 0;
}
.viewButton{
    border: 1px solid #D8E0E8!important;
}

.showInModal{
    padding: 16px 16px 0px;
    max-height: 600px;
    overflow-y: auto;
    .commonWrapper{
        margin: 0 0 16px;
    }
}
.expandInput {
    position: position;
    top: -20px;
    left: 0;
    width: 150px;
    height: 160px;
}
.apiModal input, .apiModal .commonWrapper{
    border: 1px solid #ddd;
    border-radius: 8px;
}
.apiModal .rightWrapper .textAreaWrapper{
    /* background: #F7F9FA; */
    border: 1px solid #EEF3FC;
}
.apiModal .leftWrapper{
    width: 100%;
}
.apiModal .tableTitle th{
    border-top: 1px solid #D5D7DE;
    border-bottom: 1px solid #D5D7DE;
}
.apiModal .tableTitle th:last-child{
    border-radius: 0 4px 0 0;
}
.apiModal .ant-select-selector{
    border: 1px solid #D5D7DE;
}
.apiModal table.tableTitle th:first-child, .apiModal table.tableTitle td:first-child {
    border-left: 1px solid #D5D7DE;
}
.apiModal .testButton{
    border: 1px solid #D8E0E8;
}
.footer{
    position: absolute;
    bottom: 0px;
    right: 0px;
    padding: 16px 16px 16px 0px;
    width: 100%;
    display: flex;
    background: #fff;
    justify-content: right;
    border-top: 1px solid #EFF0F2;
}
span.apiTitleNameModal{
    color: #1B2532;
    font-size: 16px!important;
    font-weight: 600;
    margin: 0 16px 0 0px;
}
.apiTitleDescModal{
    color: #626F84;
    font-size: 12px!important;;
    font-weight: 400;
}
.apiTitleIconModal{
    margin: 0 8px 0 16px;
}
.header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 16px;
    height: 96px;
    align-items: center;

    .headerTitle {
        display: flex;
        flex-direction: row;
        align-items: center;

        .leftBtn {
            width: 20px;
            height: 20px;
            cursor: pointer;
            margin-right: 16px;
        }

        .apiTitle {
            color: #1B2532;
            font-size: 18px;
            line-height: 41px;
            font-weight: 600;
            width: 150px;
            height: 100%;
        }
    }
}
.query {
    // color: #626F84;
    font-size: 13px;
    margin-bottom: 3px;
}

