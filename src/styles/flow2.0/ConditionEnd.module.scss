.opList{
    .opItem{
        border-radius: 8px;
        border: 1px solid #D8E0E8;
        background: #FFF;
        padding: 12px;
        margin-bottom: 12px;
        .op<PERSON>ey{
            display: flex;
            .deleteBtn{
                margin-left: 4px;
                width: 16px;
                cursor: pointer;
            }
        }
    }
}
.opBranch{
    .opBranchTitle{
        text-align: center;
        margin: 12px 0;
        position: relative;
        color: #9BA7BA;
        &::before{
            width: 38%;
            height: 1px;
            background: #D8E0E8;
            content: '';
            position: absolute;
            top: 10px;
            display: block;
        }
        &::after{
            width: 38%;
            height: 1px;
            background: #D8E0E8;
            content: '';
            position: absolute;
            top: 10px;
            right: 0;
            display: block;
        }
    }
    .opBranchList{
        .opBranchItem{
            display: flex;
            margin-bottom: 8px;
        }
    }
}
span.addBtn{
    margin-top: 0;
}
