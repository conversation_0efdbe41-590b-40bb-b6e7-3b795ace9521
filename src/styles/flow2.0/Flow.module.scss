.commonBorder{
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.696) 2.85%, rgba(255, 255, 255, 0.448) 98.96%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
}
.commonBorderActive{
    border: 1px solid #006BFF;
    box-shadow: 3px 3px 6px rgba(0, 107, 255, 0.18);    
}
.buttonWrapper{
    margin-right: 8px;
}
.hideWrapper{
    display: none!important;
}
.errorWrapper{
    background: #FFECE8;
    padding: 12px;
    line-height: 12px;
    margin-bottom: 12px;
    img{
        margin: 0px 8px 0 0;
        display: inline-block;
        vertical-align: top;
    }
    span{
        display: inline-block;
        vertical-align: top;
        width: calc(100% - 30px);
        word-break: break-all;
        line-height: 18px;
    }
}
/* 左侧 */
.container {
    display: flex;
    position: relative;
    background: #edf1f5;
    // border: 1px solid #FFFFFF;
    height: calc(100vh - 72px);
    overflow: hidden;
}
.left{ 
    flex: 1 0 auto;
    position: relative;
}
div.toolsOuterWrapper{
    position: absolute;
    left: 0px;
    top: 14%;
    padding-left: 24px;
    background: #edf1f5;
    max-height: 77%;
    z-index: 11;
}
div.toolsWrapper{
    padding: 5px 8px 4px;
    font-size: 12px!important;
    color: #626F84!important;
    height: 100%;
    overflow-y: auto;
    div{
        text-align: center;
    }
}
.addBlockWrapper{
    background: #fff;
    border-radius: 10px;
}
.addBlockDisabledWrapper{
    cursor: not-allowed;
}
.addBlockWrapper img, .addBlockWrapper span{
    display: inline-block;
    vertical-align: middle;
    color: #626F84;
}
.addBlockWrapper img.moreIcon{
    margin-right: 5px;
}
.addBlockWrapper .toolItem{
    margin-bottom: 14px;
    text-align: left;
    padding: 8px 12px;
    img{
        display: inline-block;
    }
}
.addBlockWrapper .llmToolItem{
    padding: 8px 12px;
    margin-bottom: 0px;
    text-align: left;
    img{
        display: inline-block;
    }
}
.addBlockWrapper .lastToolItem{
    margin-bottom: 0px;
    cursor: pointer;
    padding: 8px 12px;
}
.addBlockWrapper .toolDisabledItem, .toolsWrapper .toolDisabledItem{
    cursor: not-allowed;
}
.addBlockWrapper .disabledDeleteItem{
    cursor: not-allowed;
    opacity: 0.5;
}
.addBlockContent{
    width: 80px;
    height: 100%;
    left: calc(50% - 39px);
    
    position: absolute;
}
.toolItem{
    margin-bottom: 20px;
    cursor: pointer;
    padding: 6px 6px;
    margin: 0!important;
}
.onlyToolItem{
    border-radius: 4px;
}
.toolItem:hover, .llmToolItem:hover, .lastToolItem:hover, .pasteToolItem:hover{
    background: #EEF2F6;
    border-radius: 4px;
}
.pasteToolItem:hover{
    border-radius: 4px 4px 0 0;
}
.llmFirstToolItem:hover{
    border-radius: 4px 4px 0 0;
}
.toolItem:first-child:hover{
    border-radius: 4px 4px 0 0;
}
.lastToolItem:hover{
    border-radius: 0 0 10px 10px;
}
.onlyToolItem:hover{
    border-radius: 4px!important;
}
.pasteToolItem{
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    text-align: left;
}
.llmToolItem{
    padding: 5px 0;
    cursor: pointer;
    text-align: center;
    img{
        display: inline-block;
    }
}
.llmFirstToolItem{
    border-bottom: 1px solid #eee;
}
.flowsOuterWrapper{
    height: 100%;
    cursor: move;
    // transform-origin: 50% 50% !important;
}
.flowsWrapper{
    position: absolute;
    width: 100%;
    padding: 0 calc((100% - 320px)/2);
    left: 0;
    // top: 50%;
    // max-height: 95%;
    overflow-y: auto;
    overflow-x: hidden;
}
.flowItem{
    padding: 12px;
    color: #9BA7BA;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    width: 320px;
    z-index: 1;
    // background: #fff;
}
.flowFirstItem{
    padding: 8px 12px;
}
.flowIcon{
    // background: #6D9AFF;
    padding: 5px;
    border: 1px solid #FFFFFF;
    box-shadow: inset 0px 2px 8px 4px rgba(255, 255, 255, 0.25);
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
    width: 40px;
    height: 40px;
    margin-right: 12px;
}
// .inputFlow{
//     background: #A68CFF;
// }
// .llmFlow{
//     background: #33D47D;
// }
// .grabFlow{
//     background: #6D9AFF;
// }
// .searchFlow{
//     background: #6D9AFF;
// }
// .apiFlow{
//     background: #6D9AFF;
// }
// .codeFlow{
//     background: #6D9AFF;
// }
// .outputFlow{
//     background: #BCCAD6;
// }
// .knowledgeFlow{
//     background: #6D9AFF;
// }
.conditionFlow{
    // background: #FFBF75;
    padding: 8px;
    img{
        width: 22px;
    }
}
// .interactionFlow{
//     background: #FFB65D;
// }
// .guiFlow{
//     background: #37D4CF;
// }

.blockTitleLeftWrapper .conditionFlow.flowIcon{
    padding: 4px;
    img{
        width: 18px;
    }
}

.flowType{
    font-size: 12px;
    margin-bottom: 3px;
}
.flowContent{
    display: inline-block;
    vertical-align: middle;
}
.flowContent span{
    display: block;
    font-size: 12px;
}
span.flowName{
    font-size: 14px;
    color: #1B2532;
    margin-top: 2px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.flowNameInput{
    margin-left: -3px;
}
span.onlyFlowName{
    font-size: 14px;
}
.blockNumber{
    position: absolute;
    left: 3px;
    top: 3px;
    color: #BCCAD6;
    font-size: 12px;
}
.blockId{
    position: absolute;
    right: 5px;
    top: 3px;
    color: #BCCAD6;
    font-size: 12px;
    opacity: 0.5;
}
.errorBlockId {
    color: #FF0000;
}
span.moreBtn{
    position: absolute;
    right: 12px;
    top: 25px;
    padding: 2px;
    display: none;
    cursor: pointer;
}
span.moreBtnActive{
    display: block;
}
.moreBtn:hover{
    background: #DAE9FF;
    border-radius: 2px;
    color: #006BFF;
    padding: 2px;
}
.statusIcon{
    position: absolute;
    right: 12px;
    top: 25px;
}
.statusIconLeft{
    right: 26px;
}
.flowFirstItem .statusIcon{
    top: 19px;
}
.flowEdgeItem{
    height: 38px;
    width: 320px;
    margin-left: 0px;
    text-align: center;
    position: relative;
    z-index: 2;
    cursor: pointer;
}
.flowEdgeItem:hover .flowEdge, .flowEdgeItem:hover .flowEdgeEndDot, .flowEdgeItem:hover .flowEdgeStartDot,
.flowEdgeItem.dragOverHover .flowEdge, .flowEdgeItem.dragOverHover .flowEdgeEndDot, .flowEdgeItem.dragOverHover .flowEdgeStartDot{
    background: #398CFF;
}
.flowBranchEdgeItem{
    height: 38px;
    width: 320px;
    margin-left: 0px;
    text-align: center;
    position: relative;
    z-index: 2;
    // background: #EEF2F6;
}
.flowEdge{
    display: inline-block;
    width: 1px;
    height: 100%;
    background:#9EA7B8;
}
.flowEdgeStartDot, .flowEdgeEndDot{
    width: 6px;
    height: 6px;
    position: absolute;
    background:#9EA7B8;
    border-radius: 6px;
}
.flowEdgeStartDot{
    top: -3px;
    margin-left: -3px;
}
.flowEdgeEndDot{
    bottom: -3px;
    left: calc(50% - 3px);
}
.flowEdgeIcon{
    background: url(../../images/edgeAdd.png);
    background-size: 20px 20px;
    width: 20px;
    height: 20px;
    position: absolute;
    top: 9px;
    margin-left: -10.5px;
}
.copySuccess{
    display: inline-block;
    vertical-align: middle;
}
.flowEdgeItem:hover .flowEdgeIcon,
.flowEdgeItem.dragOverHover .flowEdgeIcon
{
    background: url(../../images/edgeAddHover.png);
    background-size: 20px 20px;
}
.rightOperationWrapper{
    position: absolute;
    right: calc(50% - 140px);
    bottom: 20px;
    // transform: translateY(50%);
    z-index: 111;
    background-color: #fff;
    padding: 8px;
    border-radius: 8px;
    display: flex;
    button{
        padding: 4px;
        min-width: 30px;
        &:hover{
            background-color: #f4f4f4!important;
        }
        &:last-child{
            padding: 4px 15px;
        }
    }
}
.rightOperationItem{
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.696) 2.85%, rgba(255, 255, 255, 0.448) 98.96%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    padding: 14px 15px 0;
}
.rightOtherOperation{
    margin-top: 20px;
    padding: 12px 0px 11px 0px;
    // display: flex;
    // justify-content: center;
    width: 38px;
}
.addBlockBtn{
    padding: 4px 13px;
    &:hover{
        background-color: #E8F5FF!important;
        color: #006BFF!important;
        path{
            fill: #006BFF!important;
        }
    }
}
.menubarPopover{
    z-index: 11;
}
.menubarPopover>div{
    top: -9px;
}
.flowBlockSearchIcon, .flowBlockResetIcon{
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin-left: 10px;
}
.flowBlockResetIcon{
    margin-top: 10px;
}
.zoomValueWrapper{
    padding: 10px;
}
.zoomValue{
    color: #b3bdcb;
    font-size: 12px;
    margin-left: 5px;
    margin-top: 5px;
    display: inline-block;
}
.zoomTips{
    margin-left: 5px;
}
.searchBlockWrapper{
    padding: 10px;
    width: 300px;
    position: relative;
}
.searchBlockWrapper .flowBlockSearchIcon{
    position: absolute;
    z-index: 1;
    top: 17px;
    left: 20px;
    margin-left: 0px;
}
.searchBlockInput{
    padding-left: 35px;
}
.statusWrapper{
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
}
.hideStatusWrapper{
    display: none;
}
.toTestStatusItem{
    width: 6px;
    height: 6px;
    background: #8ABBFF;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}
.successStatusItem{
    width: 6px;
    height: 6px;
    background: #4EC01E;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}
.errorStatusItem{
    width: 6px;
    height: 6px;
    background: #FF5E68;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}
.updateStatusItem{
    width: 6px;
    height: 6px;
    background: #FA9600;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}
.loadingStatusItem{
    width: 6px;
    height: 6px;
    background: #999;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}
.activedStatusItem{
    /* box-shadow: 0px 5px 20px rgba(244, 241, 241, 0.959); */
}
/* 右侧 */
.right{
    display: flex;
    position: absolute;
    margin: auto;
    top: 8px;
    bottom: 8px;
    right: 8px;
    z-index: 11;
}
.rightResizable{ 
    border-left: 1px solid #fff;
    width: 460px;
    height: calc(100vh - 80px);
    padding: 12px;
    position: relative;
    overflow-y: auto;
    background: #fff;
    border: 1px solid rgba(255, 255, 255, 1);
    box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10);
    border-radius: 8px;
}
.blockTitleWrapper{
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}
.blockTitleButton {
    display: flex;
    align-items: center;
    .blockTitleDebug {
        border-radius: 4px;
        &:hover {
            background: #EDF1F5;
        } 
    }
}
.blockTitleWrapper .flowIcon{
    width: 28px;
    height: 28px;
    padding: 0;
    margin-right: 8px;
}
.blockTitleLeftWrapper{
    display: flex;
    align-items: center;
}
input.blockTitle{
    font-size: 14px;
    font-weight: 600;
    color: #1B2532;
    display: inline-block;
    vertical-align: middle;
    width: 260px;
    border: 0px;
    background: transparent;
    padding: 0;
    margin-top: 4px;
}
.blockContentWrapper{
    height: calc(100% - 48px);
    overflow-y: auto;
    .common_tabs {
        & :global(.ant-tabs-nav) {
            margin: 0;
            :global(.ant-tabs-nav-list) {
                border-radius: 8px !important;
                background: #F2F5F8 !important;
            }
        } 
    }
}
.maskWrapper{
    position: relative;
    & :global(.ant-input), & :global(.ant-select-selector) {
        border-radius: 8px !important;
    }
}
.mask{
    position: absolute;
    height: 100%;
    width: 100%;
    opacity: 0.6;
    background: #F7F9FA;
    z-index: 111;
}
.inputMask{
    position: absolute;
    // height: 78%;
    height: 160px;
    width: 100%;
    opacity: 0.6;
    background: #F7F9FA;
    z-index: 1;
}
.commonWrapper{
    background: #EEF2F6;
    border-radius: 8px;
    margin-bottom: 12px;
    position: relative;
    border: 1px solid #D5D7DE;
    overflow: hidden;
    font-size: 14px;
    .resultTitle {
        padding-left: 0px;
        padding: 0 12px;
    }
    .resultTitleNew {
        padding: 0;
    }
    .resultTitleNewLine {
        padding: 0;
        display: flex;
        flex-direction: column;
        height: auto;
    }
}

.newCommonWrapper{
    background:#EEF2F6;
    border-radius: 8px;
    margin-bottom: 12px;
    position: relative;
    padding: 12px;
    .title{
        color: #626F84;
        margin-bottom: 12px;
        font-size: 14px;
    }
}
.cardWrapper{
    padding: 0 12px;
}
.showResultWrapper{
    display: flex;
    justify-content: space-between;
}
.resultJsonWrapper{
    background: #EEF2F6;
    padding: 12px;
    border: 0px;
    .cbTitle{
        display: inline-block;
        vertical-align: middle;
        color: #626F84;
    }
    .messageTitleWrapper{
        display: flex;
        justify-content: space-between;
    }
    .messageList{
        margin-top: 12px;
        .messageItem{
            display: flex;
            margin-bottom: 12px;
            .messageRequired{
                flex: 1 1 150px;
                line-height: 32px;
                span{
                    color: #626F84;
                }   
            }
            .messageValue{
                flex: 1 1 370px;
                span{
                    color: #626F84;
                }   
            }
        }
        &.displayNone{
            display: none;
        }
    }
    font-size: 14px;
}
.messageWrapper{
    position: relative;
    & :global(.ant-input), & :global(.ant-select-selector) {
        border-radius: 8px !important;
    }
    .cbTitle{
        padding: 12px 0;
        display: inline-block;
    }
    .resultTitle{
        padding: 0;
        margin: 2px 0;
    }
    .coveredStatus{
        margin: 6px 0 0px;
        button{
            margin-top: 5px;
        }
    }
}
.memoryWrapper{
    background: #EEF2F6;
    border: 0;
    padding-bottom: 1px;
}
.inputWrapper{
    overflow: auto;
}
.tokenContent{
    img,span{
        display: inline-block;
        vertical-align: middle;
    }
    .tokenTip{
        color: #657083;
        margin: 0 14px 0 8px;
    }
    .errorTokenTip{
        color: #F53F3F;
    }
    .tokenBtn{
        cursor: pointer;
        color: #006BFF;
    }
}
.paramTitle{
    background: #EEF2F6;
    border-bottom: 1px solid #fff;
    border-radius: 8px 8px 0px 0px;
    display: flex;
    justify-content: space-between;
    color: #626F84;
    padding: 12px 12px 4px;
}
.paramTitleItem{
    margin-top: -4px; 
}
.paramTitle img{
    height: 13px;
    position: relative;
    top: 2px;
}
.deleteBtn{
    cursor: pointer;
}
.displayNone{
    display: none;
}
.opTextAreaWrapper{
    height: 212px;
    background: #fff;
    border-radius: 0 0 4px 4px;
}
.tisTextAreaWrapper {
    height: 68px;
    background: #fff;
    border-radius: 0 0 4px 4px;
}
.tipsTextAreaWrapper{
    background: #fff;
    border-radius: 0 0 4px 4px;
}
.modelTextAreaWrapper {
    padding: 0 12px 0px 12px;
    border-radius: 0 0 4px 4px;
}
.collapseChildren {
    padding: 0px;
    box-sizing: border-box;
    margin-top: -30px;
}

.collapseChildrenOutPut {
    margin-top: 12px;
    margin-bottom: 12px;
}
.collapseChildrenPenTitle {
    color: #626F84;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-bottom: 4px;
    display: inline-block;
}
.collapseChildrenTopTitle {
    display: flex;
    flex-direction: row;
    allign-items: center;
    justify-content: space-between;
    color: #626F84;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
.apiAreaWrapper {
    width: 100%;
    height: 82px;
    border-radius: 0 0 4px 4px;
    padding: 0 12px 12px 12px;
}
.apiAreaWrapperCont {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: felx-start;
    gap: 8px;
    padding: 4px 0px 12px 12px;
}
.apiAreaWrapperItem {
    max-width: 48%;
    height: 24px;
    border-radius: 4px;
    background: #EBECF0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    color: #626F84;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    padding: 0 3px;
    .apiAreaWrapperItemRightContent{
        display: flex;
        justify-content: space-around;
        margin-left: 4px;
        .apiTokenBtn{
            color: #1D7CFF;
            cursor: pointer;
            margin: 0 5px;
            &:hover{
                opacity: 0.8;
            }
        }
    }
}
.apiAreaWrapperNoData {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 5px 12px;
    border-radius: 4px;
    border: 1px solid  #D1E3FF;
    background: #fff;
    margin: 2px 12px 12px;
    cursor: pointer;

    color: #006BFF;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
.resultTextAreaWrapper{
    height: 400px;
    border-radius: 8px;
}
.queryTextAreaWrapper{
    height: 120px;
    // margin: 0 12px 12px;
}
div.queryTextAreaWrapper textarea, div.queryTextAreaWrapper textarea:focus{
    border-radius: 4px;
    height: 108px;
}
.urlTextAreaWrapper{
    height: 85px;
}
.urlTextAreaWrapper textarea{
    height: 100%;
}
.codeTextAreaWrapper{
    height: 280px;
}

div.textAreaWrapper textarea, div.textAreaWrapper textarea:focus{
    border: 0px;
    box-shadow: none;
    height: 32px; 
    padding: 5px 12px;
}
div.textAreaWrapper textarea, div.textAreaWrapper textarea:focus{
    height: 100%;
    border-radius:8px;
}

div.promptTextAreaWrapper{
    border-radius: 8px;
    border: 1px solid #D5D7DE;
    background: #FFF;
    textarea,textarea:focus{
        border-radius: 8px;
    }
}
div.promptTextAreaFullScreenWrapper{
    textarea,textarea:focus{
        height: 100%!important;
    }
}
span.addBtn{
    margin-top: 12px;
    display: block;
    cursor: pointer;
    border: 1px solid #D5D7DE;
    border-radius: 8px;
    background: #FFF;
    display: inline-block;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 14px;
    &:hover{
        color: #4096ff;
        border-color: #4096ff;
    }
    span{
        margin-right: 4px;
        width: 10px;
    }
}
.promptWrapper{
    height: auto;
    margin-bottom: 12px;
    border-radius: 4px;
    border: 1px solid #D5D7DE;
    background:#FFF;
    .resultTitle {
        padding-left: 12px;
    }
    .fullscreenBtn {
        margin-right: 12px;
    }
}
.resultWrapper{
    border: 1px solid #D5D7DE;
    margin-bottom: 0;
    .resultTitle {
        padding-left: 12px;
    }
}
.inputsWrapper{
    padding: 12px;
    color: #626F84;
    border: 0px;
    .resultTitle{
        padding-left: 1px;
    }
}
.guiTitleWrapper{
    display: flex;
    justify-content: space-between;
    .guiLabelWrapper{
        margin-right: 10px;
    }
}
.addInput{
    color: #006BFF
}
.chooseApiBtn{
    width: 100%;
    border-radius: 4px;
    border: 1px solid #D1E3FF;
    background: #FFF;
    text-align: center;
    height: 32px;
    line-height: 32px;
    color: #006BFF;
    cursor: pointer;
    font-size: 14px;
}
.hasChooseApiBtn{
    width: 100%;
    border-radius: 8px;
    border: 1px solid #D5D7DE;
    background: #FFF;
    display: flex;
    justify-content: space-between;
    height: 32px;
    line-height: 31px;
    color: #006BFF;
    cursor: pointer;
    padding: 0 12px;
    font-size: 14px;
}
span.apiName{
    color: #1B2532;
    width: 80%;
}
.descBtn{
    margin-left: 8px;
    margin-top: 1px;
}

/* input */
.inputTextAreaWrapper textarea, .inputTextAreaWrapper textarea:focus{
    height: 32px;
    padding: 4px 12px;
}
.inputsWrapper input.disabledURLIp{
    background-color: #F7F9FA;
    border-color: #EBF0F5;
}
.paramListWrapper{
   position: relative;
   z-index: 0;
}
.paramWrapper{
    background: #EEF2F6;
    margin-top: 12px;
    margin-bottom: 0px;
    border: 1px solid #fff;
}
.paramContent{
    padding: 12px;
}
.paramItem{
    display: flex;
    margin-bottom: 8px;
}
.paramItem:last-child{
    margin-bottom: 0px;
}
.paramKey{
    width: 39px;
    margin-right: 12px;
    line-height: 32px;
}
.paramKey span{
    color: red;
    margin-right: 3px;
    width: 6px;
    display: inline-block;
    position: absolute;
    left: 6px;
}
.paramValue{
    width: calc(100% - 40px);
    line-height: 32px;
    & :global(.ant-input), & :global(.ant-select-selector), & :global(.ant-input-number) {
        border-radius: 8px !important;
    }
}
.paramValueInput{
    display: block;
    width: 100%;
}
.timePreviewWrapper{
    height: 100px;
    .timePreview{
        padding: 12px;
        overflow-y: auto;
        height: 100%;
        span{
            margin-right: 15px;
            display: inline-block;
            margin-bottom: 3px;
        }
    }
}
.webhookTitle{
    margin: 0px 0 8px;
    display: flex;
    justify-content: space-between;
}
.accessRadio{
    width: 100%;
}
.accessControl{
    display: block;
    >span{
        display: inline-block;
        vertical-align: top;
        margin-bottom: 18px;
        &:nth-child(2n) {
            width: 95%;
        }
    }
    .radioContent{
        margin-top: -3px;
    }
    .radioTitle{
        margin-bottom: 5px;
    }
    .whiteItem{
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        input{
            margin-right: 8px;
        }
    }
    span.addBtn{
        margin-top: 10px;
    }
}
.webhookParamItem{
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}
/* http */
.httpParamsWrapper{
    /* height: 510px; */
}
.httpParamsTabs{
    background: #EEF2F6;
    padding: 0 12px;
}
.httpParamsTextAreaWrapper{
    margin: 0 12px;
}
.httpParamsTextAreaWrapper textarea{
    height: 220px;
    background: #FAFCFF;
    border: 1px solid #D1E3FF;
    border-radius: 4px;
    margin-bottom: 12px;
}
.httpParamsType{
    margin: 12px;
}
.headersParam{
    padding: 12px;
    background: #fff;
    position: relative;
    left: -12px;
    width: calc(100% - 32px);
}
.headersParamItem{
    background: #FAFCFF;
    border: 1px solid #D1E3FF;
    border-radius: 4px;
    margin-bottom: 12px;
}
.headersParamItemKey{
    border-bottom: 1px solid #D1E3FF;
    position: relative;
}
.headersParamItemKey span, .headersParamItemValue span{
    width: 66px;
    display: inline-block;
    padding-left: 12px;
    color: #626F84;
}
.headersParamInput{
    display: inline-block;
    width: calc(100% - 100px);
    border: 0;
    background: #FAFCFF;
}
.deleteParamIcon{
    position: absolute;
    top: 7px;
    right: 9px;
    cursor: pointer;
}
.bodyWrapper{
    background: #fff;
    position: relative;
    left: -12px;
    width: calc(100% - 32px);
}
// crab
.crabResultWrapper{
    margin-bottom: 0;
}
// web-search
.searchEnginesWrapper {
    border: 0;
}
/* llm */
.llmWrapper{
    background: #EEF2F6;
    padding: 12px;
    border: 0px;
    margin-top: 12px;
}
.llmInnerWrapper{
    padding: 0 0px 12px;
}
.llmResultWrapper{
    padding: 12px;
    border: 1px solid #D5D7DE;
    margin-bottom: 0;
}
.llmResultWrapper .resultTitle{
    display: flex;
    justify-content: space-between;
}
.llmParamsWrapper{
    // background: rgba(255, 255, 255, 0.7);
}
.hideLlmParamsWrapper{
    display: none;
}
.aiModule{
    background: #EEF2F6;
}
.aiModuleLabel{
    font-size: 14px!important;
    margin-bottom: 0px!important;
    display: flex;
    justify-content: space-between;
}
.doubleArrowUp{
    background: #EEF2F6;
    text-align: center;
    border-radius: 0 0 4px 4px;
    padding: 3px 0 4px;
    cursor: pointer;
}
.doubleArrowDown{
    margin-top: -10px;
}
/* memory */
.memoryInnerWrapper{
    padding: 0 12px 10px;
}
.memoryResultTitle{
    margin: 5px 0 ;
    border-bottom: 0px!important;
    padding: 0 12px!important;
}
.promptResultTitle{
    display: flex;
    justify-content: space-between;
    
}
.queryWrapper{
    border: 1px solid #D5D7DE;
    margin-bottom: 12px;
    border-radius: 8px;
    background-color: #EEF2F6;
    .resultTitle {
        padding-left: 12px;
    }
    & :global(.ant-input) {
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
    }
}
.queryResultTitle{
    border-bottom: 0px!important;
}
/* result */
.opResultWrapper{
    margin-bottom: 0px;
}
.outputResultWrapper .markResultTitle{
    display: flex;
    justify-content: space-between;
}
.httpWrapper{
    background:#EEF2F6;
    padding: 5px 12px 11px;
}
.httpWrapper .apiSelector{
    margin-bottom: 0px;
}
.urlInput input{
    border-color: #fff;
}
.outputBtn{
    display: flex;
    justify-content: right;
}
.outputBtn button{
    margin-left: 12px;
}
.resultTitle{
    border-radius: 4px 4px 0px 0px;
    height: 32px;
    line-height: 32px;
    color: #626F84;
    font-size: 14px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
}
.fullscreenBtn{
    margin-top: 9px;
    cursor: pointer;
}
.upIcon, .downIcon{
    margin-top: 5px;
    margin-right: 6px;
    cursor: pointer;
    position: relative;
    top: 3px;

}
.upfcIcon, .downfcIcon{
    position: absolute;
    top: 5px;
    right: 0px;
}
.fcApiTitle {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background: #EEF2F6;
    border-radius: 4px 4px 0px 0px;
    height: 32px;
    line-height: 32px;
    color: #626F84;
    padding-left: 12px;
    padding-right: 12px;
    font-size: 14px;
    padding-top: 2px;
}
.fcApiTitleEdit {
    color: #006BFF;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    cursor: pointer;
}
.apiTooltip{
    margin-left: 5px;
}
.apiTitle{
    margin-top: 5px;
}
.resultTitle span.titleContent{
    margin: 0 8px 0 0px;
}
.resultTitleContent{
    margin-right: 12px;
}
.structuredBtn{
    line-height: 32px;
}
.selectParams{
    /* position: absolute; */
    background: #EEF2F6;
    border-bottom: 1px solid #F2F7FF;
    border-radius: 0px 0px 4px 4px;
    width: 100%;
    bottom: 0;
    padding: 12px 12px 0;
    color: #626F84;
}
.selectParamsList{
    margin-top: 12px;
}
.selectParamsItem{
    background: #FFFFFF;
    border-radius: 4px;
    display: inline-block;
    padding: 4px 8px;
    margin-right: 12px;
    margin-bottom: 12px;
}
.selectLlmItem{
    margin-bottom: 0px;
    cursor: pointer;
}
.activedSelectLlmItem{
    background: #006BFF;
    color: #fff;
}

.selectParamsItem span{
    color: #9BA7BA;
    margin-right: 3px;
}
.selectParamsValueList{
    max-height: 200px;
    overflow-y: auto;
}
.selectParamsValueItem{
    background: #FFFFFF;
    border: 1px solid #B5D4FF;
    border-radius: 4px;
    margin-bottom: 12px;
    cursor: pointer;
}
.selectParamsValue{
    padding: 6px 12px;
    min-height: 34px;
}
.selectParamsName{
    height: 32px;
    line-height: 32px;
    padding-left: 10px;
    background: #F2F7FF;
    border-radius: 0 0 4px 4px;
}

.settingLeftBtn, .settingRightBtn{
    width: 24px;
    height: 48px;
    padding: 11px 4px 10px 5px;
    background: #fff;
    border-width: 1px 0px 1px 1px;
    border-style: solid;
    border-color: #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 4px 0px 0px 4px;
    color: #006BFF;
    font-size: 14px;
    cursor: pointer;
    line-height: 22px;
}
.settingLeftBtn{
    position: absolute;
    right: 0px;
    top: 12px;
}
.settingRightBtn{
    margin-top: 12px;
}

.questionIcon{
    opacity: 0.5;
    margin-right: 10px;
}
.jsoneditor{
    height:100%;
}
.fullScreenBtnWrap{
    cursor: pointer;
    background-color: rgb(215, 234, 245);
    padding: 7px 7px 4px 7px;
    border-radius: 10px;
    margin-right: 20px;
    display: inline-block;
}
.fullScreenBtnIcon{
    width: 18px;
    height: 18px;
}
// fc
.fcResultContent {
    border-radius: 4px;
    margin: 0;
}
.fcResultTextarea {
    width: 100%;
    height: 212px;
    textarea{
        border-radius: 0 0 4px 4px;
    }
}
.fcResultWrapper{
    background: #EEF2F6;
    margin-bottom: 12px;
    border: 0;
}
.chooseApiWrapper{
    background: #EEF2F6;
    border: 0;
    padding-bottom: 12px;
}
.triggerWrapper{
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .triggerLabel{
        color: #626F84;
        margin-right: 13px;
        font-size: 14px;
        line-height: 32px;
    }
    .triggerSelector{
        flex: 1 1 330px;
        & :global(.ant-select-selector) {
            border-radius: 8px;
        }
    }
}
// block Interaction

.guiCardBox {
    border-radius: 6px;
    height: 242px;
    position: relative;
    border: 1px solid transparent;
    .guiCardBoxButton { 
        width: 80%;
        height: 32px;
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: none;
    }
    .guiCardBoxDelete { 
        position: absolute;
        right: 0px;
        display: none;
    }
    &:hover {
        cursor: pointer;
        border: 1px solid #1677ff;
        .guiCardBoxButton {
            display: block;
        }
        .guiCardBoxDelete {
            display: block;
        }
    }
}
.guiCardListBox {
    padding: 0 24px;
    width: 100%;
}
.guiCardAddParams {
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid #FFF;
    background: var(---g-10, #EEF2F6);
}
.ant-select-selector {
    border-radius: 8px !important;
}
