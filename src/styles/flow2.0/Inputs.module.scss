/* title */
.titleWrapper{
    height: 20px;
    margin-bottom: 9px;
    font-size: 14px;
}
.title{
    margin-right: 8px;
}
.knowBtn{
    color: #006BFF;
}
/* implementType */
.implementType{
    display: flex;
    margin: 5px 0 17px;

}
.implementLabel{
    margin-right: 28px;
    width: 56px;
    font-size: 14px;
}
/* inputs */
.inputList{
    max-height: 400px;
    overflow-y: auto;
}
.inputItem{
    display: flex;
    margin-bottom: 8px;
    // position: relative;
}
.keyItem{
    width: 180px;
}
.keyMask{
    position: absolute;
    width: 74px;
    height: 31px;
    left: 16px;
    top: 0;
    z-index: 111;
    border-radius: 8px;
}
.blockItem{
    width: 120px;
    margin: 0 5px;
}
.deleteBtn{
    margin-left: 4px;
    width: 16px;
    cursor: pointer;
}
.deleteDisabledBtn{
    opacity: 0.5;
    cursor: not-allowed;
}
/* add */
.addBtn{
    margin-top: 0px;
    display: block;
    cursor: pointer;
    border: 1px solid #D5D7DE;
    border-radius: 8px;
    background: #FFF;
    display: inline-block;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 14px;
    &:hover{
        color: #4096ff;
        border-color: #4096ff;
    }
    span{
        margin-right: 4px;
        width: 10px;
    }
}

.requiredIcon{
    color: #ff4d4f;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    line-height: 32px;
    position: absolute;
    left: 5px;
    width: 20px;
}