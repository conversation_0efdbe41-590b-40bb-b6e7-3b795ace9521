.container {
    padding: 16px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    border-radius: 8px;
    background: var(----, #FFF);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
    cursor: pointer;

    .header {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;

        .apiHeaderImg {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            // background: linear-gradient(180deg, #919FC3 0%, #525E7F 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            img {
                // width: 48px;
                // height: 48px;
                border-radius: 8px;
            }

            div {
                width: 48px;
                height: 48px;
                border-radius: 8px;
            }
        }

        .agentsHeaderImg {
            width: 68px;
            height: 68px;
            border-radius: 8px;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            // background: linear-gradient(180deg, #919FC3 0%, #525E7F 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            img {
                // width: 68px;
                // height: 68px;
                border-radius: 8px;
            }

            div {
                width: 68px;
                height: 68px;
                border-radius: 8px;
            }
        }

        .apiHeaderRight {
            // height: 96px;
            height: 68px;
            width: calc(100% - 60px);

            .apiUserName {
                color: #9BA7BA;
                font-size: 12px;
                line-height: 20px;
                margin-bottom: 8px;
            }
        }

        .agentsHeaderRight {
            height: 68px;
            width: calc(100% - 80px);
        }

        .apiHeaderRight, .agentsHeaderRight {
            // flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
           
            .headerTitle {
                width: 80%;
                color: var(---, #1B2532);
                font-feature-settings: 'clig' off, 'liga' off;
                /* 加粗/16px */
                font-family: PingFang SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px; /* 150% */
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                white-space: nowrap;

            }

            .headerDesc {
                width: 100%;
                min-height: 35px;
                color: var(---, #626F84);
                font-feature-settings: 'clig' off, 'liga' off;
            
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;

                /* 常规/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    .gapLine {
        height: 1px;
        width: 100%;
        margin: 16px 0;

        &::after {
            content: '';
            display: block;
            height: 1px;
            width: 100%;
            background: var(---g-20, #EBECF0);
        }
    }

    .apiFooter {
        margin-left: 60px;
    }

    .agentsFooter {
        margin-left: 80px;
    }

    .apiFooter, .agentsFooter {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-top: 16px;
        min-height: 20px;
    }
}

.itemMoreImg {
    position: absolute;
    display: inline-block;
    right: 30px;
    top: 15px;
   
    span {
        overflow: hidden;
        color: var(---, #9BA7BA);
        font-feature-settings: 'clig' off, 'liga' off;
        text-overflow: ellipsis;
        margin-left: 4px;

        /* 常规/12px */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
    }
}

.containerForYunPan {
    padding: 24px 24px 12px 24px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    border-radius: 8px;
    background: #F7F9FA;
    cursor: pointer;
    border: 1px solid #F7F9FA;
    position: relative;


    &:hover {
        border-radius: 8px;
        border: 1px solid #006BFF;
        background: #E8F5FF;
        box-shadow: 0px 2px 6px 0px rgba(170, 196, 221, 0.50);
    }

    .header {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;

        .apiHeaderImg {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            img {
                border-radius: 8px;
            }

            div {
                width: 48px;
                height: 48px;
                border-radius: 8px;
            }
        }

        .agentsHeaderImg {
            width: 68px;
            height: 68px;
            border-radius: 8px;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            // background: linear-gradient(180deg, #919FC3 0%, #525E7F 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            img {
                // width: 68px;
                // height: 68px;
                border-radius: 8px;
            }

            div {
                width: 68px;
                height: 68px;
                border-radius: 8px;
            }
        }

        .apiHeaderRight {
            // height: 96px;
            height: 68px;
            width: calc(100% - 60px);

            .apiUserName {
                color: #9BA7BA;
                font-size: 12px;
                line-height: 20px;
                margin-bottom: 8px;
            }
        }

        .agentsHeaderRight {
            height: 68px;
            width: calc(100% - 80px);
        }

        .apiHeaderRight, .agentsHeaderRight {
            // flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
           
            .headerTitle {
                width: 80%;
                color: #1D2531;
                /* 加粗/16px */
                font-family: PingFang SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px; /* 150% */
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                white-space: nowrap;

            }

            .headerDesc {
                width: 100%;
                min-height: 35px;
                color: #657083;
            
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;

                /* 常规/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    .gapLine {
        height: 1px;
        width: 100%;
        margin: 16px 0;

        &::after {
            content: '';
            display: block;
            height: 1px;
            width: 100%;
            background: #EBECF0;
        }
    }

    .apiFooter {
        margin-left: 60px;
    }

    .agentsFooter {
        margin-left: 80px;
    }

    .apiFooter, .agentsFooter {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-top: 16px;
        min-height: 20px;
    }

    .tag {
        border-radius: 2px;
        border: 1px solid #E1E7ED;
        color:  #657083 !important;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        background:transparent;
    }
}

.itemMoreImgForYunPan {
    position: absolute;
    display: inline-block;
    right: 24px;
    bottom: 12px;

    display: flex;
    align-items: center;
    justify-content: center;

   
    span {
        overflow: hidden;
        color: var(---, #9BA7BA);
        font-feature-settings: 'clig' off, 'liga' off;
        text-overflow: ellipsis;
        margin-left: 4px;

        /* 常规/12px */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
    }
}
