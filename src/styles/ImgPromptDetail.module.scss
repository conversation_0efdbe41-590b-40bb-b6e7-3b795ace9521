/* 左侧 */
.promptWrapper{
    position: relative;
}

.promptButton{
    position: absolute!important;
    right: 24px;
    bottom: 24px;
}
.demoBtn{
    position: absolute;
    bottom: 24px;
    left: 124px;
    color: #1D7CFF;
    cursor: pointer;
}
.negativeDemoBtn{
    position: absolute;
    top: 53px;
    right: 12px;
    color: #1D7CFF;
    cursor: pointer;
}
.promptButtonBottom{
    bottom: 18px;
}
.negativeTip{
    background: #B5D4FF;
    border-radius: 6px 0px;
    color: #006BFF;
    width: 40px;
    height: 20px;
    display: inline-block;
    text-align: center;
    position: absolute;
    top: 1px;
    left: 1px;
    z-index: 1;
}
.promptTextAreaWrapper{
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    height: 172px;
    border: 1px solid #FFFFFF;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 10px;
    position: relative;
}
.negativepromptTextAreaWrapper{
    padding: 0;
    height: 68px;
}
.promptTextAreaSmallWrapper{
    height: 80px;
    margin-bottom: 24px;
}
.promptTextAreaSmallWrapper textarea.promptTextArea{
    height: 38px;
}
.promptTextAreaWrapper:hover, .promptTextAreaSmallWrapper:hover,.negativeWrapper:hover{
    border-color: #4096ff;
    border-inline-end-width: 1px;
}
textarea.promptTextArea{
    width: 100%;
    display: block;
    background: transparent;
    padding: 0;
    border: 0px!important;
    color: #626F84;
    font-size: 16px;
    height: 91px!important;
}
textarea.negativePromptTextArea{
    height: 68px;
    width: 84%;
    margin-bottom: 10px;
    display: block;
    border-radius: 8px;
    border: 0px;
    padding: 22px;
    color: #626F84;
    font-size: 16px;
}

input.negativePromptInput{
    background: transparent;
    border: 0;
    width: 72%;
    padding: 10px;
    margin: 13px 0 10px 10px;
}
input.negativePromptInput:focus{
    border-color:transparent!important;
    box-shadow: none!important;
}
.promptTextArea:focus{
    border-color:transparent!important;
    box-shadow: none!important;
}
.edit{
    position: absolute;
    left: 24px;
    bottom: 24px;
    color: #006BFF;
    font-size: 14px;
    cursor: pointer;
}
.edit img{
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
}
.edit span{
    display: inline-block;
    vertical-align: middle;
}
.result{
    margin-top: 16px;
    padding: 16px;
    background: #fff;
    border: 1px solid #FFFFFF;
    border-radius: 8px;
}
.resultTitle{
    color: #1B2532;
    font-size: 16px;
    font-weight: 600;
}
.resultList{
    height: calc(100vh - 381px);
    overflow: auto;
    margin-top: 24px;
    min-width: 400px;
}
.resultItem{
    display: inline-block;
    position: relative;
    width: calc((100% - 72px)/4);
    margin-right: 24px;
    border-radius: 5px;
    cursor: pointer;
    border: 2px solid transparent;
    box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
    img{
        display: block!important;
    }
}
.resultItemActive{
    border: 2px solid #006BFF;
}
.resultItem:last-child{
    margin-right: 0px;
}
.resultItem img{
    width: 100%;
    border-radius: 4px;
    display: block;
}
.downImg{
    position: absolute;
    top: 8px;
    right: 8px;
    background: #006BFF;
    padding: 4px 17px;
    border-radius: 40px;
    cursor: pointer;
    display: none;
}
.downImgActive{
    display: block;
}
.downImg img{
    width: 13px;
    border-radius: 0px;
}


/* 右侧 */
.tabTitle{
    margin: 2px 0;
    font-size: 14px;
}
.aiModule{
    height: calc(100vh - 200px);
    overflow-y: auto;
    margin-top: 10px;
}
.aiModuleLabel{
    /* width: 150px; */
}
.dictionary{
    margin-top: 10px;
    /* padding: 10px; */
}
.tabs{
    height: 290px;
}
.dictionaryItems{
    overflow: auto;
    width: 100%;
    height: calc(100vh - 233px);
    margin-top: 16px;
}
.dictionaryItemsTitle{
    margin-bottom: 10px;
}
.dictionaryItem{
    display: inline-block;
    margin-right: 10px;
    border-radius: 10px;
    margin-bottom: 12px;
    cursor: pointer;
    height: 32px;
    line-height: 24px;
    color: #fff;
    font-size: 12px;
}
.cnTip{
    background: #9BA7BA;
    border-radius: 0px 4px 4px 0px;
    display: inline-block;
    padding: 4px;
}
.enTip{
    background: #BCCAD6;
    border-radius: 4px 0px 0px 4px;
    display: inline-block;
    padding: 4px;
}

.activeDictionaryItem{
    display: inline-block;
    margin-right: 10px;
    border-radius: 10px;
    margin-bottom: 12px;
    cursor: pointer;
    height: 32px;
    line-height: 24px;
    color: #fff;
    font-size: 12px;
}
.activeDictionaryItem .enTip{
    background: #8ABBFF;
}
.activeDictionaryItem .cnTip{
    background: #398CFF;
}
.foldWrapper{
    display: flex;
    justify-content: right;
    color: #3E74F8;
    font-size: 16px;
    cursor: pointer;
}
.foldIcon{
    margin-left: 12px;
}
.foldIconBox{
    margin-top: 10px;
}
.styleItem{
    display: inline-block;
    position: relative;
    margin: 0 10px 12px 0;
    cursor: pointer;
    height: 80px;
    width: 82px;
    vertical-align: middle;
    border-radius: 13px;
    border: 3px solid transparent;
}
.styleItem:last-child{
    margin: 0
}
.styleItemActive{
    border: 3px solid #006BFF;
}
.styleItemRight{
    margin: 0 0px 12px 0;
}
.styleItem img{
    width: 76px;
    border-radius: 10px;
    display: block;
}
.styleItemTextBg{
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0 0 9px 9px;
}
.styleItemText{
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
}
.sizeItem{
    display: inline-block;
    width: 82px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #D5D7DE;
    border-radius: 4px;
    text-align: center;
    margin-right: 12px;
    color: #1B2532;
    cursor: pointer;
    font-size: 12px;
}
.sizeItemActive{
    background: rgba(165, 192, 255, 0.4);
    color: #006BFF;
    border: 1px solid #006BFF;
}
.sizeItem:last-child{
    margin-right: 0px;
}

.numberItem{
    display: inline-block;
    width: 58px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #D5D7DE;
    border-radius: 4px;
    text-align: center;
    margin-right: 12px;
    color: #1B2532;
    cursor: pointer;
    font-size: 12px;
}
.numberItemActive{
    background: rgba(165, 192, 255, 0.4);
    color: #006BFF;
    border: 1px solid #006BFF;
}
.numberItem:last-child{
    margin-right: 0px;
}

@media screen and (max-width: 1300px) {
    .foldWrapper{
        font-size: 14px;
    }
    .tabTitle{
        font-size: 14px;
    }
}
