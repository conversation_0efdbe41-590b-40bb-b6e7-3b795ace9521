.outerContent {
  // flex: 1 0 auto;
  // width: calc(100% - 220px);
  padding: 16px;
  box-sizing: border-box;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  overflow: auto;
  min-width: 1028px;
  height: 100%;

  .header {
    width: 100%;
    padding: 8px 0 24px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .headerLeft {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;

      .headerLeftTitle {
        color: var(---, #1b2532);
        /* 加粗/24px */
        font-family: PingFang SC;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 36px;
        /* 150% */
        margin-right: 16px;
      }

      .headerLeftChannel {
        background: #fff;
        border-radius: 4px;
        display: flex;
        max-width: 200px;
        padding: 5px 12px;
        align-items: center;
        gap: 4px;

        .channelIcon {
          display: block;
          width: 24px;
          height: 24px;
          border-radius: 3.429px;
          background: linear-gradient(140deg, #fff 6.95%, #f0f4f7 93.62%);
        }

        .channelName {
          max-width: 132px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: block;
          color: #1b2532;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
        }
      }
    }

    .headerRight {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .createPropel {
        margin-right: 16px;
        padding: 2px 8px;
        cursor: pointer;
      }

      .createPropelForYunPan {
        margin-right: 16px;
        display: flex;
        width: 120px;
        height: 32px;
        padding: 5px 10px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        border: 1px solid  #E1E7ED;
        background: #FFF;
        cursor: pointer;
      }

      .createPropel:hover {
        border-radius: 4px;
        background:  #ebecf0;
      }

      .status {
        margin-right: 16px;
        padding: 2px 8px;
        cursor: pointer;
      }

      .statusForYunPan {
        margin-right: 16px;
        display: flex;
        width: 120px;
        height: 32px;
        padding: 5px 10px;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        border: 1px solid  #E1E7ED;
        background: #FFF;
        cursor: pointer;
      }

      .spaceForYunPan {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .status:hover {
        border-radius: 4px;
        background: var(---g-20, #ebecf0);
      }

      // .search {
      //     margin-right: 16px;
      // }
      .btn {
        margin-left: 16px;
      }

      .channelFormat {
        display: flex;
        width: 32px;
        height: 32px;
        margin-right: 16px;
        padding: 5px 8px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 4px;
        background: #fff;
      }

      .channelFormat:hover {
        img {
          filter: invert(43%) sepia(89%) saturate(7499%) hue-rotate(214deg)
            brightness(113%) contrast(101%);
        }
      }
    }

    .selectTitle {
      color: var(---, #9ba7ba);
      font-feature-settings: "clig" off, "liga" off;

      /* 常规/14px */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .selectSubtitle {
      color: var(---, #1b2532);
      font-feature-settings: "clig" off, "liga" off;

      /* 常规/14px */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .mainList {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }
}

.selectProItem {
  width: 100px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.tab {
  min-width: 152px;
  width:fit-content;
  display: flex;
  flex-direction: row;
  gap:4px;
  align-items: center;
  justify-content: space-around;
  height: 36px;
  border-radius: 8px;
  background: var(---g-20, #ebecf0);
  padding: 2px;
  box-sizing: border-box;
  color: var(---, #626f84);
  cursor: pointer;

  font-feature-settings: "clig" off, "liga" off;
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  &.templateTab {
    display: none;
  }

  .tabItem {
    width: fit-content;
    height: 100%;
    line-height: 30px;
    text-align: center;
    padding: 0 8px;
  }

  .tabItemActive {
    color: #1d7cff;
    font-weight: 600;
    background-color: #fff;
    border-radius: 8px;
  }
}

.templateTab {
  width: 116px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  height: 32px;
  border-radius: 8px;
  background: #fff;
  padding: 4px;
  box-sizing: border-box;
  color: var(---, #626f84);
  cursor: pointer;

  font-feature-settings: "clig" off, "liga" off;
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(2px);

  .templateTabItem {
    width: 50%;
    height: 100%;
    line-height: 24px;
    text-align: center;
  }

  .templateTabItemActive {
    color: #fff;
    font-weight: 600;
    background-color: #1d7cff;
    border-radius: 8px;
  }
}

.templateTabForYunPan {
  width: 92px;
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  border-radius: 4px;
  background:  #EDF1F5;
  padding: 4px;
  box-sizing: border-box;
  color:  #657083;
  cursor: pointer;

  font-feature-settings: "clig" off, "liga" off;
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;


  .templateTabItemForYunPan {
    width: 50%;
    height: 100%;
    line-height: 24px;
    text-align: center;
  }

  .templateTabItemActiveForYunPan {
    color: #1D2531;
    font-weight: 400;
    background-color: #fff;
    border-radius: 3px;
  }
}

.listContainer {
  width: 100%;
  height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: auto;
}

.promptListContainer {
  width: 100%;
  height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: hidden;
}

.templateContainer {
  width: 100%;
  height: calc(100vh - 100px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  .emptyListClass {
    width: 100%;
  }

  .filterContent {
    position: relative;
    margin-bottom: 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;

    .channelTag {
      display: flex;
      gap: 8px;

      .channelBtn {
        padding: 5px 16px;
        border-radius: 4px;
        background: #fff;
        color: #1b2532;
        font-size: 14px;
        cursor: pointer;

        &.actived {
          color: #fff;
          background: #1d7cff;
        }
      }

      .channelSelector {
        border-radius: 4px;
        background: #fff;
      }

      .actived {
        background: #1d7cff;

        span {
          color: #fff !important;
        }
      }
    }

    .filterItem {
      padding: 5px 16px;
      border-radius: 8px;
      background: #fff;
      color: #1b2532;
      font-feature-settings: "clig" off, "liga" off;
      /* 常规/14px */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      cursor: pointer;
    }

    .filterItemActive {
      background: var(----60-Hover, #1d7cff);
      color: #fff;
      font-weight: 600;
    }

    .filterArrow {
      position: absolute;
      cursor: pointer;
      right: 0;
      bottom: 0;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(----, #fff);
      box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
      backdrop-filter: blur(2px);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.templateListContainer {
  width: 100%;
  flex: 1;
  overflow-y: auto;
}

.backBtn {
  display: flex;
  width: 20px;
  height: 20px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  gap: 28px;
  margin-right: 28px;
}

.apiContent {
  display: flex;
  flex-direction: column;
  align-items: start;
  border-radius: 4px;
  padding: 12px;

  .contentFirstTab {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: left;
    opacity: 0.3;
    cursor: not-allowed;

    .versionTest {
      background: linear-gradient(
        8.45deg,
        #349aff 37.29%,
        #47cefe 120.47%,
        #5afffd 151.46%
      );
      padding: 4px 8px;
      font-size: 10px;
      line-height: 10px;
      color: #ffffff;
      border-radius: 4px 4px 4px 0;
      margin-left: 4px;
    }
  }

  .contentTab,
  .contentFirstTab {
    font-size: 14px;
    line-height: 22px;
    color: #1b2532;
    width: 156px;
    padding: 8px 12px;
  }

  .contentTab {
    margin-top: 8px;
    cursor: pointer;
  }

  .contentTab:hover,
  .contentFirstTab:hover {
    background-color: #f7f9fa;
  }
}

.dropMenuBox {
  box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12),
    0px 11px 25px 4px rgba(27, 37, 50, 0.07);
  border-radius: 8px;
  background: #fff;
}

.disableIde {
  color: #1b2532;
  font-weight: 600;
  padding: 8px 16px;
}

.tagContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 28px;
  width:100%;
  height: 32px;
  position: relative;

  .tagItem {
    padding: 4px;
    color: #1D2531;
    font-size: 14px;
    cursor: pointer;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; 
  }

  .tagItem:hover {
    color: #006BFF;
  }

  .tagItemActive {
    color: #006BFF;
  }
}

.indicator {
  width: 24px;
  height: 2px;
  border-radius: 100px;
  background:  #006BFF;
  position: absolute;
  left: 5px;
  bottom: 0;
}