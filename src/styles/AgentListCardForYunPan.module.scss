.container {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  border-radius: 8px;
  background: #f7f9fa;
  cursor: pointer;
  padding: 24px 24px 12px 24px;
  border: 1px solid #f7f9fa;
  position: relative;

  &:hover {
    border-radius: 8px;
    box-shadow: 0px 0px 10px 2px rgba(158, 205, 234, 0.3);
    backdrop-filter: unset;
    -webkit-filter: unset;
    box-sizing: border-box;

    background: linear-gradient(0deg, #f2f7fc99, #ebf4ff99);
    border: 1px solid #62a3f8;
    filter: drop-shadow(0px 2px 6px rgba(170, 196, 221, 0.5));
  }
  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .headerImg {
      width: 56px;
      height: 56px;
      border-radius: 12px;

      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      img {
        border-radius: 12px;
      }
    }

    .headerRight {
      height: 56px;
      width: 100%;
      // flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;

      .headerTitle {
        width: 80%;
        color: var(---, #1d2531);
        font-feature-settings: "clig" off, "liga" off;
        /* 加粗/16px */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        white-space: nowrap;
      }

      .headerDesc {
        width: 100%;
        // min-height: 35px;
        color: #657083;
        font-feature-settings: "clig" off, "liga" off;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;

        /* 常规/14px */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }

  .footer {
    width: 100%;
    margin-top: 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;

    .footerIcon {
      img {
        width: 16px;
        height: 16px;
        display: block;
      }
    }
  }
  .statusContent {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 22px;
    border-radius: 0px 8px;
    background: #e1e7ed;
    color: #657083;
    font-feature-settings: "liga" off, "clig" off;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 166.667% */
    text-align: center;
  }

  .statusContentPub {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 22px;
    border-radius: 0px 8px;
    color: #389e0d;
    background: #f6ffed;
    font-feature-settings: "liga" off, "clig" off;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 166.667% */
    text-align: center;
  }
}

.itemMoreImg {
  width: 20px;
  height: 20px;
  position: absolute;
  display: inline-block;
  right: 16px;
  top: 15px;
  border-radius: 2.857px;
  border: 0.714px solid #fff;
}

.dropdownItem {
  color: red;
  width: 50px;
  height: 30px;
  list-style: 30px;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
