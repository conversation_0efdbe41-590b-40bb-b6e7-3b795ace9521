.demoWrapper{
    display: flex;
}
.tagList{
    padding: 16px;
    width: 168px;
    border-right: 1px solid #EBECF0;
    .tagItem{
        padding: 10px 12px;
        cursor: pointer;
        &:hover, &.activeItem{
            border-radius: 4px;
            background: #F7F8FA;
            color: #1D7CFF;
        }
    }
}
div.demoName{
    margin-bottom: 12px;
    font-weight: 600;
}
.demoList{
    padding: 24px 16px 0;
    width: calc(100% - 168px);
    max-height: 500px;
    min-height: 500px;
    overflow-y: scroll;

    .itemModal{
        position: relative;
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        height: 160px;
        width: calc((100% - 32px) / 3);
        margin-right: 16px;
        margin-bottom: 16px;
        padding: 16px;
        border-radius: 4px;
        background: #F7F8FA;
        border: 1px solid transparent;
        &:hover{
            border: 1px solid #D5D7DE;
        }
    }
    .imgItemModal{
        position: relative;
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        width: calc((100% - 32px) / 3);
        margin-right: 16px;
        margin-bottom: 16px;
        border-radius: 4px;
        background: #F7F8FA;
        img{
            border-radius: 4px;
            width: calc(100% - 1px);
            display: inline-block;
            vertical-align: middle;
        }
        .imgLine{
            display: inline-block;
            vertical-align: middle;
            width: 1px;
            height: 100%;
        }
    }
    .lastItemModal{
        margin-right: 0px;
    }
    .titleModal{
        // color: #fff;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
    }
    .titleModal img, .titleModal span{
        display: inline-block;
        vertical-align: middle;
    }
    .titleModal span{
        // margin-left: 8px;
    }
    .descModal {
        // color: #fff;
        font-size: 12px;
        font-weight: 400;
        max-height: 90px;
        overflow-y: hidden;
    }
    .infoWrapper{
        width: 100%;
        height: 100%;
        padding: 16px;
        position: absolute;
        bottom: 0;
        right: 0;
        background: linear-gradient(0deg, rgba(19, 19, 26, 0.60) 0%, rgba(19, 19, 26, 0.60) 100%);
        opacity: 0;
        border-radius: 4px;
        color: #fff;
    }
    .textBtnWrapperModal{
        display: flex;
        justify-content: right;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 16px;
        opacity: 0;
        background: linear-gradient(180deg, rgba(247, 248, 250, 0.60) 6.25%, #F7F8FA 44.64%);
        border-radius: 0 0 4px 4px;
    }
    .btnWrapperModal{
        display: flex;
        justify-content: right;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 16px;
        opacity: 0;
        // background: linear-gradient(180deg, rgba(247, 248, 250, 0.60) 6.25%, #F7F8FA 44.64%);
        border-radius: 0 0 4px 4px;
    }
    .showBtnWrapperModal{
        animation: showBtn 1s;
        animation-fill-mode:forwards;
    }
    .showInfoWrapperModal{
        animation: showBtn 1s;
        animation-fill-mode:forwards;
        .btnWrapperModal{
            opacity: 1;
        }
        button,button:hover{
            border: 0;
        }
    }
    
    @keyframes showBtn
    {
        from {opacity: 0;}
        to {opacity: 1;}
    }
    @keyframes hideBtn
    {
        from {opacity: 1;}
        to {opacity: 0;}
    }
}
.demoDetail{
    padding: 24px;
    color: #626F84;
    .demoImage{
        width: 300px;
        height: 400px;
        line-height: 400px;
        img{
            border-radius: 4px;
            width: 100%;
        }
    }
    .demoText{
        width: calc(100% - 348px);
        margin-left: 48px;
        position: relative;
        .demoTitle{
            padding: 5px 8px 5px 0;
            height: 32px;
            display: inline-block;
        }
        .promptContent{
            margin-bottom: 12px;
        }
        .pLine{
            display: flex;
            margin-bottom: 12px;
            .pLineItem{
                width: 50%;
                color: #1B2532;
                .pLineTitle{
                    color: #626F84;
                }
            }
        }
        .useButton{
            display: flex;
            justify-content: right;
            position: absolute;
            bottom: 0;
            right: 0;
        }
    }
    .demoDetailTitle{
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        line-height: 32px;
        font-weight: 600;
        color: #1B2532;
    }
}
.imgDemoDetail{
    display: flex;
}
.demoDetailName{
    margin-left: 8px;
}