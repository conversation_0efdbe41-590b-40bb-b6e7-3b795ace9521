.header{
    height: 56px;
    border-bottom: 1px solid #E1E7ED;
    background: #FFF;
    display: flex;
    justify-content: space-between;
    .logo{
        margin-left: 20px;
        margin-top: 12px;
    }
    .logoTitle{
        position: relative;
        height: 32px;
        margin-top: 12px;
        margin-left: 12px;
    }
    .loginBtn{
        margin: 12px 20px 0 0;
    }
    .userInfo{
        margin: 16px 20px 0 0;
        font-size: 14px;
    }
    .logoutBtn{
        color: #006BFB;
        cursor: pointer;
    }
}
.flowList{
    border-right: 1px solid #E1E7ED;
    background: #FFF;
    flex: 0 0 80px;
    text-align: center;
    padding: 16px 8px 0;
    position: relative;

    .flowItem{
        color: #1B2532;
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        cursor: pointer;
        padding: 12px 0;
        
        &:hover {
            background: rgba(0, 40, 120, 0.06);
            border-radius: 8px;
        }
    }
    .activedFlowItem{
        // color: #006BFF;
        // border-left: 2px solid #006BFF;
        // background: linear-gradient(90deg, #E8F5FF 0%, rgba(232, 245, 255, 0.00) 94.44%);
        background: rgba(0, 40, 120, 0.06);
        border-radius: 8px;
        color: #003ffb; // #006BFB;
    }
    .redFlowItem{
        background: rgba(0, 40, 120, 0.06);
        border-radius: 8px;
        color: red;
    }
    .flowListUserItem {
        position: absolute;
        bottom: 12px;
        left: 0;
        width: 100%;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .redFlowItem{
        background: rgba(0, 40, 120, 0.06);
        border-radius: 8px;
        color: red;
    }
}
.left{
    border-left: 1px solid #EEEFF2;
    // background: #F4F6F9;
    // box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(2px);
    flex: 0 0 220px;
    .nameWrapper{
        font-size: 16px;
    }
    .top{
        padding: 32px 0px;
        margin: 0 18px;
        border-bottom: 1px solid #E1E7ED;
        overflow: hidden;
    }
    .flowsWrapper{
        padding: 24px 18px 24px 16px;
        .flowTitle{
            color: #505355;
            font-family: "PingFang SC";
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-bottom: 16px;
        }
        .flowEdgeItem{
            width: 186px;
            height: 17px;
            margin: 4px 0 2px;
            position: static;
            cursor: default;
        }
        .flowEdge{
            background: #BFC9D5;
            height: 15px;
            display: block;
            margin: 0 auto;
        }
        .flowEdgeEnd{
            position: absolute;
            bottom: 0;
            left: 33px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 10px solid #BFC9D5;
        }
        .statusIcon{
            position: absolute;
            right: 12px;
            top: 12px;
        }
        .flowItem{
            padding: 9px 12px;
            color: #888D93;
            font-size: 14px;
            position: relative;
            cursor: pointer;
            width: 320px;
            z-index: 1;
            background: #fff;
            border: 2px solid transparent;
            &:hover{
                box-shadow: 0px 1px 8px -3px rgba(0, 0, 0, 0.06);
            }
        }
        .commonBorderActive{
            border-radius: 12px;
            border: 2px solid #003FFB; // #2E8EFC;
            background: #fff;
            // box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.16), 0px 4px 16px -6px rgba(0, 0, 0, 0.12), 0px 6px 20px 2px rgba(0, 0, 0, 0.08);
            box-shadow: 0px 1px 8px -3px rgba(0, 0, 0, 0.06);
        }
        .redBorderActive {
            border-radius: 12px;
            border: 2px solid red;
            background: #fff;
            box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.16), 0px 4px 16px -6px rgba(0, 0, 0, 0.12), 0px 6px 20px 2px rgba(0, 0, 0, 0.08);
        }
        .messageItemWrap {
            padding: 2px;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            .messageItem {
              background: #fff;
              font-size: 13px;
              color: #505355;
              border-radius: 16px;
              box-shadow: 0px 2px 12px -3px rgba(0, 0, 0, 0.16);
              z-index: 20;
              min-height: 28px;
              display: flex;
              align-items: center;
              padding: 0 12px;
            }
            .messageItemLayer {
                background: linear-gradient(
                    to right,
                    #b9e0fb,
                    #b9e0fb 25%,
                    #0047fb 45%,
                    #0047fb 55%,
                    #b9e0fb 75%,
                    #b9e0fb 100%
                );
                position: absolute;
                left: 50%;
                top: 50%;
                width: 370px;
                height: 370px;
                margin-left: -185px;
                margin-top: -185px;
                background-size: 400%;
                animation: animate 4s linear infinite;
            }
            @keyframes animate {
                0% {
                  background-position: 0 0;
                }
                100% {
                  background-position: -300% 0;
                }
              }
          }
        .addBlockContent{
            width: 100%;
            height: 100%;
        }
    }
    .addNewBox {
        width: 100%;
        position: absolute;
        bottom: 16px;
        padding: 0 18px 0 16px;
    }
    .addNew{
        width: 100%;
        padding: 9px 0px;
        color: #888D93;
        font-size: 13px;
        font-weight: 400;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4px;

        &:hover{
            border-radius: 8px;
            background: #fff;
        }
    }
}
.rightResizable{
    box-shadow: none;
}

.renewButton{
    color: #505355;
    font-size: 14px;
    // line-height: 32px;
    cursor: pointer;
    img{
        display: inline-block;
        vertical-align: middle;
        position: relative;
        top: -2px;
    }
}

// codeWrapper
.codeWrapper{
    margin: 12px;
    border: 0;
    .resultTitle{
        padding-left: 0;
        margin-bottom: 2px;
    }
}

.operationWrapper{
    padding: 16px;
    background: #FFFFFF;

    .operationContent{
        background: #F7F9FA;
        padding: 16px;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        .operationItem:first-child{
            margin-bottom: 16px;
        }
        .operationItemLabel{
            color: #657083;
            width: 80px;
            display: inline-block;
        }
        .operationItemContent{
            color: #1B2532;
            display: inline-block;
        }
    }

    .operationContentTwo {
        margin-bottom: 16px;
        width: 100%;
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        align-self: stretch;
        .contentHeader {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .contentHeaderTitle {
            color:#1B2532;
            text-align: right;
            font-feature-settings: 'liga' off, 'clig' off;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;  
        }
        .contentHeaderBtns {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            gap: 8px;
        }
        .contentParams {
            display: flex;
            padding: 16px 16px 16px 0;
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
            align-self: stretch;
            border-radius: 8px;
            background:#F7F9FA;
        }
        .contentParamsItemFirst {
            display: flex;
            align-items: flex-start;
            gap: 40px;
            align-self: stretch;
        }
        .contentParamsItem {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 32px;
        }
        .contentParamsItemLabel {
            width: 78px;
            color: #657083;
            text-align: right;
            font-feature-settings: 'liga' off, 'clig' off;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            span {
                display: inline-block;
                color: #F53F3F;
                font-feature-settings: 'liga' off, 'clig' off;
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                width: 8px;
                margin-right: 2px;
            }
        }
        .contentParamsItemDetail {
            flex: 1;
            color: #1D2531;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .contentParamsItemDetailAddress {
            color: #006BFF;
        }
        .contentParamsItemFirstStatic {
            .contentParamsItem {
                width: 30%;
            }
        }
    }
}
.primaryButton{
    width: 76px;
    height: 32px;
    display: flex;
    line-height: 18px;
    padding: 5px 26px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    // background: #1D7CFF;
    color: #fff;
    border: 1px solid transparent;
    box-shadow: none;
    img{
      display: inline-block;
      vertical-align: middle;
      margin-right: 8px;
    }
    &:hover{
      opacity: 0.8;
    }
    &:disabled{
      border: 1px solid transparent;;
    }
    &.smallPrimaryButton {
      padding: 5px 16px;
    }
    &.ant-btn-primary:disabled:hover{
      opacity: 1;
    }
  }

.normalBtn{
    background-color: #1d7cff;
    &:hover{
        background-color: #1d7cff !important;
    }
}
.redBtn{
    background-color: red;
    &:hover{
        opacity: 0.8 !important;
        background-color: red !important;
        color: #fff !important;
        border: 1px solid transparent !important;
    }
}
.flowBaoAppHomeHeader {
    padding-left: 24px;
    
}
.flowBaoAppHomeHeaderLeft {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .flowBaoAppHomeHeaderLogo {
        img {
            width: 28px;
            height: 28px;
        }
    }
    .flowBaoAppHomeHeaderTitle {
        color: var(---text-color-primary, #202224);
        font-family: "360shouhu Type";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 28px; /* 140% */
    }
}

.flowBaoAppHomeContent {
    display: flex;
    padding: 64px 0px;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--comp-margin-xxl, 32px);
    border-radius: var(--radius-rightangle, 0px);
    background: linear-gradient(33deg, #FFF 45.75%, #F0F5FF 80.67%), var(---bg-color-primary, #FFF);
    box-shadow: 0px 2px 12px -3px var(---shadow-color-base, rgba(0, 0, 0, 0.16));
    position: relative;
    height: calc(100vh - 56px);
    overflow-y: auto;
    .flowBaoAppHomeContentHeader {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--comp-margin-xs, 8px);
        align-self: stretch;
    }
    .flowBaoAppHomeContentHeaderTitle {
        color: var(---text-color-primary, #202224);
        text-align: center;

        /* 运营标题/display-m */
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: 600;
        line-height: 56px; /* 116.667% */
    }
    .flowBaoAppHomeContentHeaderDesc {
        color: var(---text-color-placeholder, #888D93);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    .flowBaoAppHomeContentMain {
        display: flex;
        // height: 1008px;
        padding: 24px;
        flex-direction: column;
        align-items: center;
        align-self: stretch;
        border-radius: 8px;
    }
    .flowBaoAppHomeContentMainContainer {
        display: flex;
        width: var(--height-1200, 1200px);
        min-width: var(--width-924, 924px);
        max-width: var(--height-1200, 1200px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 32px;
    }
    .flowBaoAppHomeContentMainRow {
        display: flex;
        align-items: center;
        gap: 24px;
    }
    .flowBaoAppHomeContentMainImage {
        display: flex;
        width: 384px;
        height: 240px;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
.headerContentRight {
    display: flex;
    padding-right: 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    span {
        color: #888D93;
        text-align: right;
        font-family: "PingFang SC";
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        cursor: pointer;
        &:hover {
            color: #505355;
        }
    }
}
.userPolicyContent {
    margin-top: 25px;
    max-height: 550px;
    overflow-y: auto;
    .userPolicyTitle {
        color: #000;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 28px;
        margin-bottom: 32px;
    }
}
.userInfoWrapper{
    display: flex;
    width: 240px;
    padding: 16px 12px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 12px;
    .userNameBg{
        display: flex;
        align-items: center;
        gap: 12px;
        
        img {
            width: 40px;
            height: 40px;
        }
        span {
            color: var(---text-color-primary, #202224);
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; 
        }
    }
    .userLine {
        height: 1px;
        align-self: stretch;
        background-color: rgba(0, 0, 0, 0.12);
    }
    .userLogin{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        align-self: stretch;
        cursor: pointer;
        
        img{
            width: 16px;
            height: 16px;
        }
         
        span{
            color: var(---error-color-default, #F22933);
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
        }
    }
}
.btnDefault,.btnPrimary,.btnPrev,.btnNext {
    border-radius: 8px !important;
    &:hover {
        // background-color: #2E68FC !important;
    }
    &:active {
        background-color: #002DCF !important;
    }
    &:disabled {
        // background-color: #8AB3FD !important;
    }
}
.btnPrev,.btnNext {
    width: 96px;
}
.btnPrimary,.btnNext {
    background-color: #003FFB;
}

