
span.addBtn{
    margin-top: 0;
}
span.disabledAddBtn{
    cursor: not-allowed;
    opacity: 0.5;
    &:hover{
        border-color: #D5D7DE;
        color: #1B2532;
    }
}
.statusList{
    margin-bottom: 12px;
    .statusItem{
        display: flex;
        margin-bottom: 8px;
    }
    .deleteBtn{
        cursor: pointer;
    }
}
.branchesWrapper{
    margin-bottom: 12px;
    .title{
        color: #1B2532;
        margin-top: 13px;
        margin-bottom: 12px;
    }
    .bItem{
        background-color: #F7F8FA;
        border-radius: 4px;
        padding: 12px;
        color: #9BA7BA;
        margin-top: 8px;
        font-size: 14px;
    }
    .branchList{
        .branchItem{
            background-color: #F7F8FA;
            border-radius: 4px;
            padding: 12px;
            color: #626F84;
            margin-bottom: 8px;
            .branchTitle{
                margin-bottom: 12px;
                display: flex;
                justify-content: space-between;
                font-size: 14px;
            }
            .branchItemWrapper{
                display: flex;
                .addBranchWrapper{
                    flex: 0 0 24px;
                    margin-right: 8px;
                    position: relative;
                    text-align: center;
                    .addBranchLine{
                        width: 1px;
                        height: 100%;
                        background: #D8E0E8;
                        position: absolute;
                        left: 13px;
                    }
                    .addBranchBtn{
                        border-radius: 4px;
                        border: 1px solid #D8E0E8;
                        background: #FFF;
                        padding: 2px;
                        position: absolute;
                        z-index: 11;
                        top: 50%;
                        left: 3px;
                        width: 22px;
                        margin-top: -12px;
                        cursor: pointer;
                        font-size: 13px;
                    }

                    .addBranchBtn1{
                        background: #FFF;
                        border-radius: 4px;
                        border: 1px solid #D8E0E8;
                        position: absolute;
                        z-index: 11;
                        top: 50%;
                        left: 3px;
                        width: 22px;
                        margin-top: -21px;
                        cursor: pointer;
                        font-size: 13px;
                        span{
                            padding: 3px;
                            margin-top: 3px;
                            font-size: 13px;
                        }
                        .orSpan{
                            font-size: 13px;
                            border-bottom: 1px solid #D8E0E8;
                        }
                    }
                }
            }
            .conditionList{
                flex: 1 1 auto;
                .conditionGroupItem{
                    padding: 12px;
                    background: #FFF;
                    border: 1px solid #D8E0E8;
                    border-radius: 4px;
                    margin-bottom: 8px;
                }
                .conditionTitle{
                    margin-bottom: 12px;
                    font-size: 14px;
                }
                .conditionWrapper{
                    display: flex;
                    .addConditionWrapper{
                        flex: 0 0 24px;
                        margin-right: 8px;
                        position: relative;
                        text-align: center;
                        .addConditionLine{
                            width: 1px;
                            height: 100%;
                            background: #D8E0E8;
                            position: absolute;
                            left: 13px;
                        }
                        .addConditionBtn{
                            border-radius: 4px;
                            width: 22px;
                            border: 1px solid #D8E0E8;
                            background: #FFF;
                            padding: 2px;
                            position: absolute;
                            z-index: 11;
                            top: 50%;
                            left: 3px;
                            margin-top: -12px;
                            cursor: pointer;
                            font-size: 13px;
                        }
                        .addConditionBtn1{
                            border-radius: 4px;
                            width: 22px;
                            border: 1px solid #D8E0E8;
                            background: #FFF;
                            position: absolute;
                            z-index: 11;
                            top: 50%;
                            left: 3px;
                            margin-top: -21px;
                            cursor: pointer;
                            font-size: 13px;
                            span{
                                padding: 3px;
                                margin-top: 3px;
                                font-size: 13px;
                            }
                            .andSpan{
                                font-size: 13px;
                                border-bottom: 1px solid #D8E0E8;
                            }
                        }
                    }
                    .conditions{
                        flex: 1 1 auto;
                        .conditionItem{
                            display: flex;
                            margin-bottom: 12px;
                            &.conditionLastItem{
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
            .deleteBtn{
                cursor: pointer;
            }
        }
    }
    
}
.resultWrapper{
    .title{
        color: #1B2532;
        margin-top: 13px;
        margin-bottom: 12px;
    }
    .bItem{
        background-color: #F7F8FA;
        border-radius: 4px;
        padding: 12px;
        color: #9BA7BA;
        margin-top: 12px;
        font-size: 14px;
        span{
            color: #1B2532;
            margin-left: 8px;
        }
    }
}