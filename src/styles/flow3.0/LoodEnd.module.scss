.condition {
    // background-color: #EEF2F6;
    // padding: 12px;
    border-radius: 8px;
    color: #626F84;
    margin-bottom: 8px;
		

    .condTitle {
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
    }

    .condList {
        display: flex;
        flex-direction: row;
        width: 100%;

        .condAllLeft {
            position: relative;
            width: 34px;

            .condAllLine {
                width: 1px;
                height: 100%;
                background: #D8E0E8;
                position: absolute;
                left: 13px;
            }

            .condAllText {
                border-radius: 4px 4px 0 0;
                border: 1px solid #D8E0E8;
                background: #FFF;
                padding: 2px 4px;
                position: absolute;
                z-index: 11;
                top: calc(50% - 20px);
                left: 3px;
                width: 22px;
                cursor: pointer;
                font-size: 12px;
            }

            .condAllIcon {
                top: 50%;
            }

            .condAllIconOne {
                top: calc(50% - 11px);
            }

            .condAllIcon, .condAllIconOne {
                border-radius: 0 0 4px 4px;
                border: 1px solid #D8E0E8;
                background: #FFF;
                padding: 2px 4px;
                position: absolute;
                z-index: 11;
                left: 3px;
                width: 22px;
                cursor: pointer;
                font-size: 13px;
            }
        }

        .condOuter {
            display: flex;
            flex-direction: column;
            width: 100%;

            .condContent:not(:first-child){
                margin-top: 8px;
            }

            .condContent {
                display: flex;
                flex-direction: column;
                width: 100%;

                .condItem {
                    padding: 12px;
                    background: #F7F9FA;
                    border: 1px solid #D8E0E8;
                    border-radius: 8px;
                    display: flex;
                    flex-direction: column;
                    width: 100%;

                    .condItemTitle {
                        margin-bottom: 12px;
                        font-size: 14px;
                    }

                    .condItemCon {
                        display: flex;
                        flex-direction: row;
                        width: 100%;

                        .condItemConLeft {
                            position: relative;
                            width: 38px;

                            .condAllLine {
                                width: 1px;
                                height: 100%;
                                background: #D8E0E8;
                                position: absolute;
                                left: 13px;
                            }

                            .condAllText {
                                border-radius: 4px 4px 0 0;
                                border: 1px solid #D8E0E8;
                                background: #FFF;
                                padding: 2px 4px;
                                position: absolute;
                                z-index: 11;
                                top: calc(50% - 20px);
                                left: 3px;
                                width: 22px;
                                cursor: pointer;
                                font-size: 12px;
                            }

                            .condAllIcon {
                                top: 50%;
                            }

                            .condAllIconOne {
                                top: calc(50% - 11px);
                            }

                            .condAllIcon, .condAllIconOne {
                                border-radius: 0 0 4px 4px;
                                border: 1px solid #D8E0E8;
                                background: #FFF;
                                padding: 2px 4px;
                                position: absolute;
                                z-index: 11;
                                left: 3px;
                                width: 22px;
                                cursor: pointer;
                                font-size: 13px;
                            }
                        }

                        .condItemOuer {
                            display: flex;
                            flex-direction: column;
                            width: 100%;

                            .condItemInner:not(:first-child) {
                                margin-top: 8px;
                            }

                            .condItemInner {
                                display: flex;
                                flex-direction: row;
                                justify-content: space-between;
                                width: 100%;
                                height: 32px;

                                .deleteIcon {
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.collapseLabel {
	display: flex;
	align-items: center;
	justify-content: space-between;

	&Title {
		color: #1D2531;
		font-size: 14px;
		line-height: 22px;
		font-weight: 600;
	}

	&Right {
		.addBtn {
			margin-left: 10px;
				color: #333;
				cursor: pointer;
				background: #F7F9FA;
				border-radius: 8px;
				display: inline-block;
				width: 24px;
				height: 24px;
				text-align: center;
				line-height: 23px;

				&:hover{
					color: #4096ff;
					border-color: #4096ff;
				}

				span {
					width: 10px;
				}
		}
	}
}