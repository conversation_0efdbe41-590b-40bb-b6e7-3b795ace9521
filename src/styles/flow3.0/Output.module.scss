.output {
	padding: 0px;
	color: #626F84;

	&Content {
		padding-top: 7px;
	}

    .header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .headerLeft {
            img {
                width: 16px;
                height: 16px;
								line-height: 24px;
                margin-right: 8px;
                cursor: pointer;
            }

            color: #1D2531;
            font-size: 14px;
            line-height: 22px;
            // font-weight: 600;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;

						&Title {
							font-weight: 600;
						}
        }

				.headerRight {
					.addBtn {
						margin-left: 10px;
						color: #333;
						cursor: pointer;
						background: #F7F9FA;
						border-radius: 8px;
						display: inline-block;
						width: 24px;
						height: 24px;
						text-align: center;
						line-height: 23px;

						&:hover{
							color: #4096ff;
							border-color: #4096ff;
					}

						span {
							width: 10px;
						}
					}

				}
    }

		.formTitle {
			display: flex;
			color: #9EA7B8;
			font-size: 12px;
			line-height: 20px;
			gap: 5px;

			&Name {
				flex: 1;
			}
			
			&Type {
				width: 72px; 
			}

			&Stream {
				width: 120px;
			}
		}

    .itemLine {
        width: 100%;
        padding: 4px 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        font-family: 'PingFang SC';
        margin-bottom: 4px;
        // margin-left: 8px;
        position: relative;

        .topExpand {
            position: relative;
            width: 16px;
            height: 16px;
            cursor: pointer;

            .arrowSvg {
                position: absolute;
                top: 7px;
            }

						.arrowSvgClose {
							top: 5px;
						}
        }

        .itemValue {
            font-size: 14px;
            line-height: 22px;
            color: #1B2532;
						padding-left: 3px;
        }

        .itemType {
            display: inline-block;
            font-size: 12px;
            line-height: 20px;
            padding: 1px 3px;
            background: #EDF1F5;
            border-radius: 4px;
            margin-left: 8px;
            color: #657083;
        }

				.formItem {
					width: 100%;
					display: flex;
					justify-content: flex-end;
					align-items: center;
					gap: 5px;

					&Add {
						width: 24px;
						text-align: center;
						position: relative;
						top: 2px;
						left: 4px;

						&Icon {
							color: #657083;
							cursor: pointer;
							&.disabled {
								color: #dbdcde;
							}
						}
					}

					&Del {
						width: 24px;
						text-align: center;
						position: relative;
						top: 2px;

						&Icon {
							cursor: pointer;
							color: #6E6E6E;
							&.disabled {
								cursor: not-allowed;
								color: #dbdcde;
							}
						}
					}

					&Stream {
						width: 120px;
					}
				}
    }

		.empty {
			color: #626F84;
		}
}