.toolContainer{
    position: absolute;
    top: -20px;
    padding: 8px;
    border-radius: 12px;
    border: 1px solid #EDF1F5;
    background: #FFF;
    box-shadow: 0px 6px 30px -3px rgba(75, 85, 105, 0.10);
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 215px;
    box-sizing: border-box;
    display: none;
    &.toolContainerLoop {
        left: unset !important;
        right: 0 !important;
        transform: unset !important;
        z-index: 99999;
    }
    span{
        padding: 4px 8px;
        font-size: 14px;
        color: #1D2531;
        font-weight: 400;
        line-height: 22px;

        &:first-child::after{
            content: '';
            position: absolute;
            background: #EDF1F5;
            width: 1px;
            height: 16px;  
            left: 75px;
            top: 12px;
        }
        &:last-child{
            // margin-left: 12px;
        }
        &:last-child::before{
            content: '';
            position: absolute;
            background: #EDF1F5;
            width: 1px;
            height: 16px;  
            right: 71px;
            top: 12px;
        }
        img{
            position: relative;
            top: 3px;
            margin-right: 4px;
            width: 16px;
            height: 16px;
        }
        &.activedBtn{
            color: rgba(0, 107, 255, 1);
            background: rgba(230, 240, 255, 1);
            border-radius: 8px;
        }
        &:hover {
            background: rgba(230, 240, 255, 1);
            border-radius: 8px;
        }
        &.disabledSpan:hover{
            background: transparent;
        }
    }
}