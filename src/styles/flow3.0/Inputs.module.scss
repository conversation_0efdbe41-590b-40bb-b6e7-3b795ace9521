/* title */
.titleWrapper{
    display: flex;
    justify-content: space-between;
    color: #1D2531;
    font-size: 14px;
    font-weight: 600;
}
.title{
    margin-right: 8px;
}
.knowBtn{
    color: #006BFF;
}
.implementType{
    display: flex;

    .radioGroup {
        display: flex;
        flex-direction: row;
        margin: 12px 16px 20px;
        background: #F7F9FA;
        border-radius: 8px;
        padding: 2px;
        font-size: 14px;
        line-height: 22px;
        width: 100%;

        .activeRadio {
            background: #FFFFFF;
            color: #006BFF;
            width: 50%;
            text-align: center;
            border-radius: 8px;
            padding: 4px 0;
            cursor: pointer;
        }

        .defaultRadio {
            background: #F7F9FA;
            color: #626F84;
            width: 50%;
            text-align: center;
            padding: 4px 0;
            cursor: pointer;
        }
    }
}
.implementLabel{
    margin-right: 28px;
    width: 56px;
    font-size: 14px;
}
/* inputs */
.inputList, .paramsInputList{
    max-height: 400px;
    overflow-y: auto;
    .inputTitle{
        display: flex;
        color: #9EA7B8;
        font-size: 12px;
    }
}
.inputList{
    margin-top: 10px;
}
.paramsInputList {
    font-size: 12px;
    line-height: 22px;
    color: #9EA7B8;

    .paramsFisrtKey {
        width: 28%;
        display: inline-block;
    }
}

.inputItem{
    display: flex;
    margin-bottom: 4px;
    // position: relative;
}
.selectItem{
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    &:hover{
        background: rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        cursor: pointer;
    }
}
.keyItem{
    width: 180px;
}
.keyMask{
    position: absolute;
    width: 74px;
    height: 31px;
    left: 16px;
    top: 0;
    z-index: 111;
    border-radius: 8px;
}
.deleteBtn{
    margin-left: 4px;
    width: 16px;
    cursor: pointer;
}
.deleteDisabledBtn{
    opacity: 0.5;
    cursor: not-allowed;
}
/* add */
.addBtn{
    margin-top: 0px;
    display: block;
    cursor: pointer;
    background: #F7F9FA;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    height: 24px;
    padding: 5px 6px;
    text-align: center;
    line-height: 23px;
    &:hover{
        color: #4096ff;
        border-color: #4096ff;
    }
    span{
        width: 10px;
    }
}

.requiredIcon{
    color: #ff4d4f;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    line-height: 32px;
    position: absolute;
    left: 5px;
    width: 20px;
}
.itemSelector{
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    &:hover{
        background: rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        cursor: pointer;
    }
}
.activedSelector{
    background: rgba(0, 0, 0, 0.04);
}
.inputsWrapper {
    padding: 16px;
    // border-top: 1px solid #EDF1F5;
}