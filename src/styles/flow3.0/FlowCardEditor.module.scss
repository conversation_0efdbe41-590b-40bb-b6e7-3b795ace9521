.cardEditorWrapper{
    border-radius: 16px;
    background: #FFF;
    position: absolute;
    padding: 24px 24px 12px;
    top: 113%;
    width: 432px;
    left: -35%;
    display: none;

    /* 投影/下/弹出投影（4级） */
    box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10), 0px 15px 45px 7px rgba(27, 37, 50, 0.06);
    .title{
        color: #1D2531;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
    }
    .inputWrapper{
        margin-top: 10px;
        .btn{
            display: none;
            width: 0px;
            vertical-align: middle;
            position: relative;
            top: 2.5px;
            span{
                &:first-child{
                    margin: 0 10px;
                }
            }
        }
        .activedBtn{
            display: inline-block;
            width: 76px;
        }
        .input{
            width: calc(100% - 0px);
        }
        .activedInput{
            width: calc(100% - 76px);
        }
        &:last-child{
            margin-bottom: 20px;
        }
        .preFix{
            color: #657083;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            span{
                color: #EDF1F5;
                margin: 0 10px;
            }
        }
    }
    .colorList{
        padding: 12px 0 0;
        .colorItem{
            padding: 3px 4px 4px 3px;
            border: 1px solid transparent;
            display: inline-block;
            width: 36px;
            height: 36px;
            margin-right: 22px;
            margin-bottom: 17px;
            &:nth-child(7n){
                margin-right: 0px;
            }
            span{
                display: inline-block;
                width: 28px;
                height: 28px;
                border-radius: 32px;
            }
        }
        .activeColorItem{
            border: 1px solid #006BFF;
            border-radius: 18px;
        }
    }
    .iconList{
        padding: 12px 0 0;
        .iconItem{
            margin-right: 8px;
            display: inline-block;
            margin-bottom: 11.5px;
            .imageBorderItem{
                width: 48px;
                height: 48px;
                padding: 5px 5px 3px 3px;
                display: inline-block;
                border: 1px solid transparent;
            }
            .imageItem{
                background: #4DD0E1;
                border-radius: 12px;
                padding: 8px;
                text-align: center;
                img{
                    display: inline!important;
                    position: relative;
                    top: 6px;
                }
            }
            .iconName{
                display: block;
                color: #657083;
                font-size: 14px;
                font-weight: 400;
                margin-top: 0px;
                text-align: center;
            }
            &:nth-child(7n){
                margin-right: 0px;
            }
        }
        .activeIconItem{
            .imageBorderItem{
                border: 1px solid #006BFF;
                border-radius: 16px;
            }
        }
    }
}