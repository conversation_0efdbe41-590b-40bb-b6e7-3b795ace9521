.header{
    height: 56px;
    border-bottom: 1px solid #E1E7ED;
    background: #FFF;
    display: flex;
    justify-content: space-between;
    .logo{
        margin-left: 20px;
        margin-top: 12px;
    }
    .logoTitle{
        position: relative;
        height: 32px;
        margin-top: 12px;
        margin-left: 12px;
    }
    .loginBtn{
        margin: 12px 20px 0 0;
    }
    .userInfo{
        margin: 16px 20px 0 0;
        font-size: 14px;
    }
    .logoutBtn{
        color: #006BFB;
        cursor: pointer;
    }
}
.flowList{
    border-right: 1px solid #E1E7ED;
    background: #FFF;
    flex: 0 0 80px;
    text-align: center;
    padding: 16px 8px 0;

    .flowItem{
        color: #1B2532;
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        cursor: pointer;
        padding: 12px 0;
        img{
            // margin-bottom: 4px;
        }
    }
    .activedFlowItem{
        // color: #006BFF;
        // border-left: 2px solid #006BFF;
        // background: linear-gradient(90deg, #E8F5FF 0%, rgba(232, 245, 255, 0.00) 94.44%);
        background: rgba(0, 40, 120, 0.06);
        border-radius: 8px;
        color: #006BFB;
    }
}
.left{
    border-left: 1px solid #EEEFF2;
    background: #F4F6F9;
    // box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(2px);
    flex: 0 0 256px;
    .nameWrapper{
        font-size: 16px;
    }
    .top{
        padding: 32px 0px;
        margin: 0 24px;
        border-bottom: 1px solid #E1E7ED;
    }
    .flowsWrapper{
        padding: 24px calc((100% - 208px) / 2);
        .flowTitle{
            color: #1B2532;

            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; 
            margin-bottom: 16px;
        }
        .flowEdgeItem{
            width: 208px;
            height: 19px;
            margin: 4px 0 2px;
            position: static;
            cursor: default;
        }
        .flowEdge{
            background: #BFC9D5;
            height: 19px;
            display: block;
            margin: 0 auto;
        }
        .flowEdgeEnd{
            position: absolute;
            bottom: 0;
            left: 33px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 10px solid #BFC9D5;
        }
        .statusIcon{
            position: absolute;
            right: 16px;
            top: 14px;
        }
        .flowItem{
            padding: 12px;
            color: #9BA7BA;
            font-size: 14px;
            position: relative;
            cursor: pointer;
            width: 320px;
            z-index: 1;
            background: #fff;
            border: 2px solid transparent;
        }
        .commonBorderActive{
            border-radius: 12px;
            border: 2px solid #2E8EFC;
            background: #fff;
            box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.16), 0px 4px 16px -6px rgba(0, 0, 0, 0.12), 0px 6px 20px 2px rgba(0, 0, 0, 0.08);
        }
        .messageItem{
            background: #fff;
            font-size: 13px;
            color: #505355;
            padding: 4px 12px;
            border-radius: 6px;
            box-shadow: 0px 2px 12px -3px rgba(0, 0, 0, 0.16);
        }
        .addBlockContent{
            width: 100%;
            height: 100%;
        }
    }
}
.rightResizable{
    box-shadow: none;
}

.renewButton{
    color: #505355;
    font-size: 14px;
    // line-height: 32px;
    cursor: pointer;
    img{
        display: inline-block;
        vertical-align: middle;
        position: relative;
        top: -2px;
    }
}

// codeWrapper
.codeWrapper{
    margin: 12px;
    border: 0;
    .resultTitle{
        padding-left: 0;
        margin-bottom: 2px;
    }
}

// operationWrapper
.operationWrapper{
    padding: 16px;
    border-top: 1px solid #E1E7ED;

    .operationContent{
        background: #F7F9FA;
        padding: 16px;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        .operationItem:first-child{
            margin-bottom: 16px;
        }
        .operationItemLabel{
            color: #657083;
            width: 80px;
            display: inline-block;
        }
        .operationItemContent{
            color: #1B2532;
            display: inline-block;
        }
    }
}