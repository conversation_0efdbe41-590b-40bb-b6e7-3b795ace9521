.customRadioWrapper {
  padding: 0 16px 16px;
  // background-color: #ccc;
  :global .ant-radio-group.ant-radio-group-block {
    background-color: #f7f9fa;
    padding: 2px;
    border-radius: 8px;
    .ant-radio-button-wrapper {
      height: 28px;
      border: 0 none;
      border-inline-start: 0 none;
      background-color: #f7f9fa;
      color: #626f84;
      border-radius: 6px;
      &::before {
        display: none;
      }
    }
    .ant-radio-button-wrapper-checked {
      background-color: #fff;
      color: #006bff;
      font-weight: 600;
    }
  }
  label {
    border-color: #ccc !important;
  }
}
