.commonBorder {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.696) 2.85%, rgba(255, 255, 255, 0.448) 98.96%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
}

.commonBorderActive {
    border: 1px solid #006BFF;
    box-shadow: 3px 3px 6px rgba(0, 107, 255, 0.18);
}

.buttonWrapper {
    margin-right: 8px;
}

.hideWrapper {
    display: none !important;
}

.errorWrapper {
    background: #FFECE8;
    padding: 12px;
    line-height: 12px;
    margin-bottom: 12px;

    img {
        margin: 0px 8px 0 0;
        display: inline-block;
        vertical-align: top;
    }

    span {
        display: inline-block;
        vertical-align: top;
        width: calc(100% - 30px);
        word-break: break-all;
        line-height: 18px;
    }
}

/* 左侧 */
.container {
    display: flex;
    position: relative;
    background: #edf1f5;
    // border: 1px solid #FFFFFF;
    height: calc(100vh - 72px);
    overflow: hidden;
}

.left {
    flex: 1 0 auto;
    position: relative;
}

div.toolsOuterWrapper {
    position: absolute;
    left: 0px;
    top: 14%;
    padding-left: 24px;
    background: #edf1f5;
    max-height: 77%;
    z-index: 11;
}

div.toolsWrapper {
    padding: 5px 8px 4px;
    font-size: 12px !important;
    color: #626F84 !important;
    height: 100%;
    overflow-y: auto;

    div {
        text-align: center;
    }
}

.addBlockWrapper {
    background: #fff;
    border-radius: 10px;
}

.addBlockDisabledWrapper {
    cursor: not-allowed;
}

.addBlockWrapper img,
.addBlockWrapper span {
    display: inline-block;
    vertical-align: middle;
    color: #626F84;
}

.addBlockWrapper img.moreIcon {
    margin-right: 5px;
}

.addBlockWrapper .toolItem {
    margin-bottom: 14px;
    text-align: left;
    padding: 8px 12px;

    img {
        display: inline-block;
    }
}

.addBlockWrapper .llmToolItem {
    padding: 8px 12px;
    margin-bottom: 0px;
    text-align: left;

    img {
        display: inline-block;
    }
}

.addBlockWrapper .lastToolItem {
    margin-bottom: 0px;
    cursor: pointer;
    padding: 8px 12px;
}

.addBlockWrapper .toolDisabledItem,
.toolsWrapper .toolDisabledItem {
    cursor: not-allowed;
}

.addBlockWrapper .disabledDeleteItem {
    cursor: not-allowed;
    opacity: 0.5;
}

.addBlockContent {
    width: 80px;
    height: 100%;
    left: calc(50% - 39px);

    position: absolute;
}

.toolItem {
    margin-bottom: 20px;
    cursor: pointer;
    padding: 6px 6px;
    margin: 0 !important;
}

.onlyToolItem {
    border-radius: 4px;
}

.toolItem:hover,
.llmToolItem:hover,
.lastToolItem:hover,
.pasteToolItem:hover {
    background: #EEF2F6;
    border-radius: 4px;
}

.pasteToolItem:hover {
    border-radius: 4px 4px 0 0;
}

.llmFirstToolItem:hover {
    border-radius: 4px 4px 0 0;
}

.toolItem:first-child:hover {
    border-radius: 4px 4px 0 0;
}

.lastToolItem:hover {
    border-radius: 0 0 10px 10px;
}

.onlyToolItem:hover {
    border-radius: 4px !important;
}

.pasteToolItem {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    text-align: left;
}

.llmToolItem {
    padding: 5px 0;
    cursor: pointer;
    text-align: center;

    img {
        display: inline-block;
    }
}

.llmFirstToolItem {
    border-bottom: 1px solid #eee;
}

.flowsOuterWrapper {
    height: 100%;
    cursor: move;
    // transform-origin: 50% 50% !important;
}

.flowsWrapper {
    position: absolute;
    width: 100%;
    padding: 0 calc((100% - 320px)/2);
    left: 0;
    // top: 50%;
    // max-height: 95%;
    overflow-y: auto;
    overflow-x: hidden;
}

.flowItem {
    padding: 12px;
    color: #9BA7BA;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    width: 320px;
    z-index: 1;
    // background: #fff;
}

.flowFirstItem {
    padding: 8px 12px;
}

.flowIcon {
    // background: #6D9AFF;
    padding: 5px;
    border: 1px solid #FFFFFF;
    box-shadow: inset 0px 2px 8px 4px rgba(255, 255, 255, 0.25);
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
    width: 40px;
    height: 40px;
    margin-right: 12px;
}

// .inputFlow{
//     background: #A68CFF;
// }
// .llmFlow{
//     background: #33D47D;
// }
// .grabFlow{
//     background: #6D9AFF;
// }
// .searchFlow{
//     background: #6D9AFF;
// }
// .apiFlow{
//     background: #6D9AFF;
// }
// .codeFlow{
//     background: #6D9AFF;
// }
// .outputFlow{
//     background: #BCCAD6;
// }
// .knowledgeFlow{
//     background: #6D9AFF;
// }
.conditionFlow {
    // background: #FFBF75;
    padding: 8px;

    img {
        width: 22px;
    }
}

// .interactionFlow{
//     background: #FFB65D;
// }
// .guiFlow{
//     background: #37D4CF;
// }

.blockTitleLeftWrapper .conditionFlow.flowIcon {
    padding: 4px;

    img {
        width: 18px;
    }
}

.flowType {
    font-size: 12px;
    margin-bottom: 3px;
}

.flowContent {
    display: inline-block;
    vertical-align: middle;
}

.flowContent span {
    display: block;
    font-size: 12px;
}

span.flowName {
    font-size: 14px;
    color: #1B2532;
    margin-top: 2px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.flowNameInput {
    margin-left: -3px;
}

span.onlyFlowName {
    font-size: 14px;
}

.blockNumber {
    position: absolute;
    left: 3px;
    top: 3px;
    color: #BCCAD6;
    font-size: 12px;
}

.blockId {
    position: absolute;
    right: 5px;
    top: 3px;
    color: #BCCAD6;
    font-size: 12px;
    opacity: 0.5;
}

.errorBlockId {
    color: #FF0000;
}

span.moreBtn {
    position: absolute;
    right: 12px;
    top: 25px;
    padding: 2px;
    display: none;
    cursor: pointer;
}

span.moreBtnActive {
    display: block;
}

.moreBtn:hover {
    background: #DAE9FF;
    border-radius: 2px;
    color: #006BFF;
    padding: 2px;
}

.statusIcon {
    position: absolute;
    right: 12px;
    top: 25px;
}

.statusIconLeft {
    right: 26px;
}

.flowFirstItem .statusIcon {
    top: 19px;
}

.flowEdgeItem {
    height: 38px;
    width: 320px;
    margin-left: 0px;
    text-align: center;
    position: relative;
    z-index: 2;
    cursor: pointer;
}

.flowEdgeItem:hover .flowEdge,
.flowEdgeItem:hover .flowEdgeEndDot,
.flowEdgeItem:hover .flowEdgeStartDot,
.flowEdgeItem.dragOverHover .flowEdge,
.flowEdgeItem.dragOverHover .flowEdgeEndDot,
.flowEdgeItem.dragOverHover .flowEdgeStartDot {
    background: #398CFF;
}

.flowBranchEdgeItem {
    height: 38px;
    width: 320px;
    margin-left: 0px;
    text-align: center;
    position: relative;
    z-index: 2;
    // background: #EEF2F6;
}

.flowEdge {
    display: inline-block;
    width: 1px;
    height: 100%;
    background: #9EA7B8;
}

.flowEdgeStartDot,
.flowEdgeEndDot {
    width: 6px;
    height: 6px;
    position: absolute;
    background: #9EA7B8;
    border-radius: 6px;
}

.flowEdgeStartDot {
    top: -3px;
    margin-left: -3px;
}

.flowEdgeEndDot {
    bottom: -3px;
    left: calc(50% - 3px);
}

.flowEdgeIcon {
    background: url(../../images/edgeAdd.png);
    background-size: 20px 20px;
    width: 20px;
    height: 20px;
    position: absolute;
    top: 9px;
    margin-left: -10.5px;
}

.copySuccess {
    display: inline-block;
    vertical-align: middle;
}

.flowEdgeItem:hover .flowEdgeIcon,
.flowEdgeItem.dragOverHover .flowEdgeIcon {
    background: url(../../images/edgeAddHover.png);
    background-size: 20px 20px;
}

.rightOperationWrapper {
    position: absolute;
    // right: calc(50% - 140px);
    left: 50%;
    transform: translateX(-50%);
    bottom: 20px;
    // transform: translateY(50%);
    z-index: 111;
    background-color: #fff;
    padding: 8px;
    border-radius: 8px;
    display: flex;

    button {
        padding: 4px;
        min-width: 30px;

        span {
            color: #1D2531 !important;
        }

        &:hover {
            background-color: #f4f4f4 !important;
        }

        &:last-child {
            padding: 4px;
        }
    }

    .addBlockBtn {
        span {
            color: #006BFF !important;
        }

        &:hover {
            background-color: rgba(1, 106, 252, 0.10) !important;

            span {
                opacity: 0.8;
            }
        }
    }

    .gapLine {
        color: #EDF1F5;
        padding: 0 6px;
        display: inline-block;
        vertical-align: middle;
        line-height: 32px;
    }
}

.rightOperationWrapper.withRightMenu {
    left: calc(50% - 220px);
}

.rightOperationWrapper.withFullRightMenu {
    left: calc((100% - 824px)/2);
}

.rightOperationItem {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.696) 2.85%, rgba(255, 255, 255, 0.448) 98.96%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    padding: 14px 15px 0;
}

.rightOtherOperation {
    margin-top: 20px;
    padding: 12px 0px 11px 0px;
    // display: flex;
    // justify-content: center;
    width: 38px;
}

.addBlockBtn {
    padding: 4px 12px !important;
    border-radius: 8px;
    background: rgba(1, 106, 252, 0.10);

    span {
        color: #006BFF !important;
    }

    color: #006BFF !important;

    path {
        fill: #006BFF !important;
    }

    &:hover {
        span {
            color: #006BFF !important;
        }

        color: #006BFF !important;

        path {
            fill: #006BFF !important;
        }
    }
}

.menubarPopover {
    z-index: 11;
}

.menubarPopover>div {
    top: -9px;
}

.flowBlockSearchIcon,
.flowBlockResetIcon {
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin-left: 10px;
}

.flowBlockResetIcon {
    margin-top: 10px;
}

.zoomValueWrapper {
    padding: 10px;
}

.zoomValue {
    color: #b3bdcb;
    font-size: 12px;
    margin-left: 5px;
    margin-top: 5px;
    display: inline-block;
}

.zoomTips {
    margin-left: 5px;
}

.searchBlockWrapper {
    padding: 10px;
    width: 300px;
    position: relative;
}

.searchBlockWrapper .flowBlockSearchIcon {
    position: absolute;
    z-index: 1;
    top: 17px;
    left: 20px;
    margin-left: 0px;
}

.searchBlockInput {
    padding-left: 35px;
}

.statusWrapper {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
}

.hideStatusWrapper {
    display: none;
}

.toTestStatusItem {
    width: 6px;
    height: 6px;
    background: #8ABBFF;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}

.successStatusItem {
    width: 6px;
    height: 6px;
    background: #4EC01E;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}

.errorStatusItem {
    width: 6px;
    height: 6px;
    background: #FF5E68;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}

.updateStatusItem {
    width: 6px;
    height: 6px;
    background: #FA9600;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}

.loadingStatusItem {
    width: 6px;
    height: 6px;
    background: #999;
    border-radius: 6px;
    margin-bottom: 13px;
    cursor: pointer;
}

.activedStatusItem {
    /* box-shadow: 0px 5px 20px rgba(244, 241, 241, 0.959); */
}

/* 右侧 */
.right {
    display: flex;
    position: absolute;
    margin: auto;
    top: 8px;
    bottom: 8px;
    right: 8px;
    z-index: 500;
}

.rightResizable {
    border-left: 1px solid #fff;
    width: 460px;
    height: calc(100vh - 80px);
    padding: 12px 0px;
    position: relative;
    // overflow-y: auto; // code节点ai生成代码左侧弹框不显示，需要隐藏
    background: #fff;
    border: 1px solid rgba(255, 255, 255, 1);
    box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10);
    border-radius: 8px;
    overflow: hidden;
}

.blockTitleWrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 0 16px;
}

.blockTitleButton {
    display: flex;
    align-items: center;

    .blockTitleDebug {
        border-radius: 4px;

        &:hover {
            background: #EDF1F5;
        }
    }
}

.blockTitleWrapper .flowIcon {
    width: 28px;
    height: 28px;
    padding: 0;
    margin-right: 8px;
}

.blockTitleLeftWrapper {
    display: flex;
    align-items: center;
}

.editTitleIcon {
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
        path {
            fill: #006BFF !important;
            stroke: #006BFF !important;
        }
    }
}

input.blockTitle {
    font-size: 14px;
    font-weight: 600;
    color: #1B2532;
    display: inline-block;
    vertical-align: middle;
    width: 260px;
    border: 0px;
    background: transparent;
    padding: 0;
    margin-top: 4px;
}

.blockTitleContent {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
}

.blockContentWrapper {
    height: calc(100% - 48px);
    padding-bottom: 70px;
    overflow-y: auto;

    .common_tabs {
        & :global(.ant-tabs-nav) {
            margin: 0;

            :global(.ant-tabs-nav-list) {
                border-radius: 8px !important;
                background: #F7F9FA !important;
            }
        }
    }

    .common_tabs_nobg {
        & :global(.ant-tabs-nav) {
            margin: 0;

            :global(.ant-tabs-nav-list) {
                border-radius: 8px !important;
                background: red !important;
            }
        }
    }
}

.formInteractionWrapper {
    height: 264px;
    border-radius: 8px;
    background: #F7F9FA;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #EDF1F5;
    border-radius: 8px;

    .formcardcontainer {
        background: #fff;
        height: 240px;
        max-height: 240px;
        overflow-y: auto;
        border-radius: 6px;
        border: 1px solid #F7F9FA;
        position: relative;

        &::before {
            display: block;
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            height: 100%;
            z-index: 1;
            // background-color: pink;
        }
    }
}

.maskWrapper {
    position: relative;

    & :global(.ant-input),
    & :global(.ant-select-selector) {
        border-radius: 8px !important;
    }
}

.maskAiCodeWrapper {
    position: absolute;
    left: calc(-100% - 23px);
    top: 20px;
    border-left: 1px solid #fff;
    width: 460px;
    height: 640px;
    padding: 16px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid rgba(255, 255, 255, 1);
    box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10);
    border-radius: 8px;

    .maskAiCodetitle {
        font-size: 14px;
        font-weight: bold;
    }

    .maskAiCodeContainer {
        margin-top: 8px;
        height: 480px;
        position: relative;
        background: #fff;
        border-radius: 4px;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-start;
        border-radius: 8px;
        border: 1px solid #E1E7ED;

        &.maskAiCodeContainerEmp {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #9EA7B8;
            font-size: 12px;
        }

        .maskUseAiResultBtn {
            display: flex;
            width: 64px;
            height: 24px;
            padding: 5px 8px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 8px;
            position: absolute;
            right: 8px;
            bottom: 8px;
        }
    }

    .maskUseAiChatInput {
        margin-top: 16px;
        padding-right: 4px;
    }
}

.mask {
    position: absolute;
    height: 100%;
    width: 100%;
    opacity: 0.6;
    background: #F7F9FA;
    z-index: 111;
}

.inputMask {
    position: absolute;
    // height: 78%;
    height: 160px;
    width: 100%;
    opacity: 0.6;
    background: #F7F9FA;
    z-index: 1;
}

.fullscreenBtnWrapper {
    position: relative;
    top: -5px;
}

.commonWrapper {
    // background: #EEF2F6;
    border-radius: 8px;
    // margin-bottom: 12px;
    position: relative;
    overflow: hidden;
    font-size: 14px;

    .resultTitle {
        padding-left: 0px;
        padding: 0 12px;
    }

    .resultTitleNew {
        padding: 0;
    }

    .resultTitleNewLine {
        padding: 0;
        display: flex;
        flex-direction: column;
        height: auto;
    }

    .paramsHeader {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        svg {
            cursor: pointer;
        }
    }
}

.newCommonWrapper {
    background: #EEF2F6;
    border-radius: 8px;
    margin-bottom: 12px;
    position: relative;
    padding: 12px;

    .title {
        color: #626F84;
        margin-bottom: 12px;
        font-size: 14px;
    }
}

.cardWrapper {
    padding: 0 12px;
}

.showResultWrapper {
    display: flex;
    justify-content: space-between;
}

.resultJsonWrapper {
    background: #F7F9FA;
    padding: 12px;
    border: 0px;

    .cbTitle {
        display: inline-block;
        vertical-align: middle;
        color: #626F84;
    }

    .messageTitleWrapper {
        display: flex;
        justify-content: space-between;
    }

    .messageList {
        margin-top: 12px;

        .messageItem {
            display: flex;
            margin-bottom: 12px;

            .messageRequired {
                flex: 1 1 150px;
                line-height: 32px;

                span {
                    color: #626F84;
                }
            }

            .messageValue {
                flex: 1 1 370px;

                span {
                    color: #626F84;
                }
            }
        }

        &.displayNone {
            display: none;
        }
    }

    font-size: 14px;
}

.messageWrapper {
    position: relative;

    & :global(.ant-input),
    & :global(.ant-select-selector) {
        border-radius: 8px !important;
    }

    .cbTitle {
        padding: 12px 0;
        display: inline-block;
    }

    .resultTitle {
        padding: 0;
        margin: 2px 0;
    }

    .coveredStatus {
        margin: 6px 0 0px;

        button {
            margin-top: 5px;
        }
    }
}

.memoryWrapper {
    background: #EEF2F6;
    border: 0;
    padding-bottom: 1px;
}

.inputWrapper {
    overflow: auto;
    padding: 0 16px;
}

.tokenContent {

    img,
    span {
        display: inline-block;
        vertical-align: middle;
    }

    .tokenTip {
        color: #657083;
        margin: 0 14px 0 8px;
    }

    .errorTokenTip {
        color: #F53F3F;
    }

    .tokenBtn {
        cursor: pointer;
        color: #006BFF;
    }
}

.paramTitle {
    background: #fff;
    border-radius: 8px 8px 0px 0px;
    display: flex;
    justify-content: space-between;
    color: #626F84;
    padding: 4px;
}

.paramTitleActive {
    background: #F7F9FA;
}

.paramTitleItem {
    margin-top: -4px;
}

.paramTitle img {
    height: 16px;
    position: relative;
    top: 9px;
    margin-right: 0px;
    margin-left: 4px;
}

.paramTitle .expandBtn {
    cursor: pointer;
    padding: 4px;
    height: 22px;
    width: 22px;
    margin-right: 4px;
    margin-top: 5px;
    background: url(../../images/flow3.0/expand.svg) no-repeat 3px 4px;

    &:hover {
        background-color: #EDF1F5;
        background: url(../../images/flow3.0/expand_hover.svg) no-repeat 3px 4px;
    }
}

.paramTitle .activedExpandBtn {
    background-color: #EDF1F5;
    background: url(../../images/flow3.0/expand_hover.svg) no-repeat 3px 4px;
}

.deleteBtn {
    cursor: pointer;
}

.displayNone {
    display: none;
}

.opTextAreaWrapper {
    height: 212px;
    background: #fff;
    border-radius: 0 0 4px 4px;
}

.tisTextAreaWrapper {
    height: 68px;
    background: #fff;
    border-radius: 0 0 4px 4px;
}

.tipsTextAreaWrapper {
    background: #fff;
    border-radius: 0 0 4px 4px;
}

.modelTextAreaWrapper {
    padding: 0 12px 0px 12px;
    border-radius: 0 0 4px 4px;
}

.collapseTitle {
    font-weight: bold;
}

.collapseMaskTitle {
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .fullscreenBtn {
        margin-top: 0;
    }

    .collapseBtnWrapper {
        display: flex;
        align-items: center;
    }

    .codeaiBtn {
        margin-right: 10px;
    }
}

.collapseChildren {
    padding: 0px;
    box-sizing: border-box;
    margin-top: -30px;
}

.collapseChildrenOutPut {
    margin-top: 12px;
    margin-bottom: 12px;
}

.collapseChildrenPenTitle {
    color: #626F84;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    margin-bottom: 4px;
    display: inline-block;
}

.collapseChildrenTopTitle {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    color: #626F84;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.titleWrapper {
    display: flex;
    justify-content: space-between
}

/* add */
.addBtnTitle {
    margin-top: 0px;
    display: block;
    cursor: pointer;
    background: #F7F9FA;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    height: 24px;
    padding: 5px 6px;
    text-align: center;
    line-height: 23px;

    &:hover {
        color: #4096ff;
        border-color: #4096ff;
    }

    span {
        width: 10px;
    }
}

.apiAreaWrapper {
    width: 100%;
    min-height: 82px;
    max-height: 120px;
    margin-bottom: 16px;
}

.apiAreaWrapperCont {
    width: 100%;
    min-height: 82px;
    background: #fff;
    border-radius: 8px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: flex-start;
    gap: 8px;
    padding: 12px;
    border: 1px solid #E1E7ED;
    max-height: 120px;
}

.apiAreaWrapperItem {
    height: 24px;
    width: 48%;
    border-radius: 6px;
    background: rgba(237, 241, 245, 0.80);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    color: #1B2532;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;

    .apiName {
        max-width: 60%;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .apiAreaWrapperItemRightContent {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-left: 4px;
        min-width: 35%;
        white-space: nowrap;

        .apiTokenBtn {
            color: #1D7CFF;
            cursor: pointer;
            margin: 0 5px;
            white-space: nowrap;

            &:hover {
                opacity: 0.8;
            }
        }
    }
}

.apiIconWrapper {
    display: flex;
    align-items: center;
    margin-right: 4px;
}

.apiIcon {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid #EDF1F5;
    background: #FFF;
    object-fit: cover;
    overflow: hidden;
}

.apiAreaWrapperNoData {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 5px 12px;
    border-radius: 8px;
    border: 1px solid #D1E3FF;
    background: #fff;
    margin: 0px 1px 0px 1px;
    cursor: pointer;

    color: #006BFF;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.resultTextAreaWrapper {
    height: 400px;
    border-radius: 8px;
}

.queryTextAreaWrapper {
    height: 120px;
    // margin: 0 12px 12px;
}

div.queryTextAreaWrapper textarea,
div.queryTextAreaWrapper textarea:focus {
    border-radius: 4px;
    height: 108px;
}

.urlTextAreaWrapper {
    height: 85px;
}

.urlTextAreaWrapper textarea {
    height: 100%;
}

.codeTextAreaWrapper {
    height: 280px;
}

div.textAreaWrapper textarea,
div.textAreaWrapper textarea:focus {
    // border: 0px;
    box-shadow: none;
    height: 32px;
    padding: 5px 12px;
}

div.textAreaWrapper textarea,
div.textAreaWrapper textarea:focus {
    height: 100%;
    border-radius: 8px;
}

div.promptTextAreaWrapper {
    border-radius: 8px;
    // border: 1px solid #D5D7DE;
    background: #FFF;

    textarea,
    textarea:focus {
        border-radius: 8px;
    }

    textarea::placeholder {
        color: #9BA7BA;
    }
}

div.promptTextAreaFullScreenWrapper {

    textarea,
    textarea:focus {
        height: 100% !important;
    }
}

span.addBtn {
    display: block;
    cursor: pointer;
    border-radius: 8px;
    background: #F7F9FA;
    display: inline-block;
    padding: 5px 3px 5px 7px;
    font-size: 12px;
    line-height: 14px;

    &:hover {
        color: #4096ff;
        border-color: #4096ff;
    }

    span {
        margin-right: 4px;
        width: 10px;
    }
}

.promptWrapper {
    height: auto;
    margin-bottom: 12px;
    border-radius: 4px;
    border: 1px solid #D5D7DE;
    background: #FFF;

    .resultTitle {
        padding-left: 12px;
    }

    .fullscreenBtn {
        margin-right: 12px;
    }
}

.resultWrapper {
    border: 1px solid #D5D7DE;
    margin-bottom: 0;

    .resultTitle {
        padding-left: 12px;
    }
}

.inputsWrapper {
    padding: 0px;
    color: #626F84;

    .resultTitle {
        padding-left: 1px;
    }

    .tabsWrapper {
        margin: 12px 0 0px;
        padding: 0 16px 16px;
    }
}

.guiTitleWrapper {
    display: flex;
    justify-content: space-between;

    .guiLabelWrapper {
        margin-right: 10px;
    }
}

.addInput {
    color: #006BFF
}

.chooseApiBtn {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #D1E3FF;
    background: #FFF;
    text-align: center;
    height: 32px;
    line-height: 32px;
    color: #006BFF;
    cursor: pointer;
    font-size: 14px;
}

.hasChooseApiBtn {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #E1E7ED;
    background: #FFFFFF;
    display: flex;
    justify-content: space-between;
    height: 32px;
    line-height: 31px;
    color: #006BFF;
    cursor: pointer;
    padding: 0 12px;
    font-size: 14px;

    .apiChoose {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        color: #1D2531;

        img {
            margin-right: 5px;
        }
    }
}

span.apiName {
    color: #1B2532;
    width: 80%;
}

.descBtn {
    margin-left: 8px;
    margin-top: 1px;
}

/* input */
.inputTextAreaWrapper textarea,
.inputTextAreaWrapper textarea:focus {
    height: 32px;
    padding: 4px 12px;
}

.inputsWrapper input.disabledURLIp {
    background-color: #F7F9FA;
    border-color: #EBF0F5;
}

.paramListWrapper {
    position: relative;
    z-index: 0;
    margin-top: -8px;

    .inputTitle {
        display: flex;
        color: #9EA7B8;
        font-size: 12px;
    }
}

.paramWrapper {
    background: #F7F9FA;
    margin-bottom: 4px;
    border: 1px solid #fff;
}

.paramContent {
    padding: 4px;
}

.paramItem {
    margin-bottom: 8px;
}

.paramItem:last-child {
    margin-bottom: 0px;
}

.paramKey {
    padding: 2px;
    color: #9EA7B8;
    font-size: 12px;
}

.paramKey span {
    color: red;
    margin-right: 3px;
    width: 6px;
    display: inline-block;
    position: absolute;
    left: 6px;
}

.paramValue {
    width: 100%;
    line-height: 32px;

    & :global(.ant-input),
    & :global(.ant-select-selector),
    & :global(.ant-input-number) {
        border-radius: 8px !important;
    }
}

.paramValueInput {
    display: block;
    width: 100%;
}

.timePreviewWrapper {
    height: 100px;
    border: 1px solid #E1E7ED;
    border-radius: 8px;

    .timePreview {
        padding: 12px;
        overflow-y: auto;
        height: 100%;

        span {
            margin-right: 15px;
            display: inline-block;
            margin-bottom: 3px;
        }
    }
}

.webhookTitle {
    width: 100%;
    margin: 0px 0 8px;
    display: flex;
    justify-content: space-between;
}

.accessRadio {
    width: 100%;
}

.accessControl {
    display: block;

    >span {
        display: inline-block;
        vertical-align: top;
        margin-bottom: 18px;

        &:nth-child(2n) {
            width: 95%;
        }
    }

    .radioContent {
        margin-top: -3px;
    }

    .radioTitle {
        margin-bottom: 5px;
    }

    .whiteItem {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        input {
            margin-right: 8px;
        }
    }

    span.addBtn {
        margin-top: 10px;
    }
}

.webhookParamItem {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

/* http */
.httpParamsWrapper {
    /* height: 510px; */
}

.httpParamsTabs {
    background: #EEF2F6;
    padding: 0 12px;
}

.httpParamsTextAreaWrapper {
    margin: 0 12px;
}

.httpParamsTextAreaWrapper textarea {
    height: 220px;
    background: #FAFCFF;
    border: 1px solid #D1E3FF;
    border-radius: 4px;
    margin-bottom: 12px;
}

.httpParamsType {
    margin: 12px;
}

.headersParam {
    padding: 12px;
    background: #fff;
    position: relative;
    left: -12px;
    width: calc(100% - 32px);
}

.headersParamItem {
    background: #FAFCFF;
    border: 1px solid #D1E3FF;
    border-radius: 4px;
    margin-bottom: 12px;
}

.headersParamItemKey {
    border-bottom: 1px solid #D1E3FF;
    position: relative;
}

.headersParamItemKey span,
.headersParamItemValue span {
    width: 66px;
    display: inline-block;
    padding-left: 12px;
    color: #626F84;
}

.headersParamInput {
    display: inline-block;
    width: calc(100% - 100px);
    border: 0;
    background: #FAFCFF;
}

.deleteParamIcon {
    position: absolute;
    top: 7px;
    right: 9px;
    cursor: pointer;
}

.bodyWrapper {
    background: #fff;
    position: relative;
    left: -12px;
    width: calc(100% - 32px);
}

// crab
.crabResultWrapper {
    margin-bottom: 0;
}

// web-search
.searchEnginesWrapper {
    border: 0;
}

/* llm */
.llmWrapper {
    // background: #EEF2F6;
    // padding: 12px;

    // margin-top: 12px;
    border-top: 1px solid #EDF1F5;
    border-bottom: 0px solid transparent !important;
    border-left: 0px solid transparent !important;
    border-right: 0px solid transparent !important;
    border-radius: 0px !important;
}

.llmInnerWrapper {
    padding: 0 0px 12px;
}

.llmResultWrapper {
    padding: 12px;
    border: 1px solid #D5D7DE;
    margin-bottom: 0;
}

.llmResultWrapper .resultTitle {
    display: flex;
    justify-content: space-between;
}

.llmParamsWrapper {
    // background: rgba(255, 255, 255, 0.7);
}

.hideLlmParamsWrapper {
    display: none;
}

.llmConfigWrapper {
    position: fixed;
    top: 99px;
    right: 453px;
    z-index: 111;
    width: 400px;
    height: auto;
    display: flex;
    padding: 16px 16px 24px 16px;
    flex-direction: column;
    box-sizing: border-box;
    gap: 24px;
    border-radius: 8px;
    background: #FFF;
    box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
}

.aiModule {
    // background: #EEF2F6;
}

.aiModuleLabel {
    font-size: 14px !important;
    margin-bottom: 0px !important;
    display: flex;
    justify-content: space-between;
}

.modelSelectWrapper {
    margin-top: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 4px;

    .moreIcon {
        cursor: pointer;
        border-radius: 8px;
        padding: 4px;
        background: #FFFFFF;
        width: 24px;
        height: 24px;

        &:hover {
            background: #F7F9FA;
        }

        .moreIconSvg {
            width: 16px;
            height: 16px;

            path {
                fill: #657083;
            }

            &:hover path {
                fill: #006BFF;
            }
        }
    }
}

.forminteractionbtn {
    display: flex;
    height: 32px;
    padding: 5px 12px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    gap: 8px;
    flex: 1 0 0;
    border-radius: 8px;
    border: 1px solid #E1E7ED;
    background: #FFF;
    color: #006BFF;
    font-size: 14px;
}

.collapsePromptWrapper,
.collapseMaskWrapper {
    // border-bottom: 1px solid #EDF1F5;
    border-top: 0px solid transparent !important;
    border-left: 0px solid transparent !important;
    border-right: 0px solid transparent !important;
    border-radius: 0px !important;
    box-sizing: border-box;

    :global(.ant-collapse-item) {
        border-top: 1px solid #EDF1F5;
        border-radius: 0 !important;
    }

    :global(.ant-collapse-item > .ant-collapse-header) {
        padding: 12px 16px 6px 16px !important;
    }

    :global(.ant-collapse-content) {
        padding-bottom: 16px;
    }
}



.collapseHeader {
    :global(.ant-collapse-item) {
        padding: 12px 16px 12px 16px !important;
        border-top: 1px solid #EDF1F5;
        border-radius: 0 !important;
    }

    :global(.ant-collapse-item > .ant-collapse-header) {
        padding: 0 !important;
    }

    :global(.ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
        padding: 0 !important;
    }
}

.foldHeader {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .foldHeaderLeft {
        img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            cursor: pointer;
        }

        color: #1D2531;
        font-size: 14px;
        line-height: 22px;
        font-weight: 600;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
    }

    .foldHeaderApiRight {
        color: #006BFF;
        font-size: 14px;
        line-height: 22px;
        cursor: pointer;
    }

    .fullScreenImg {
        width: 14px;
        height: 14px;
        cursor: pointer;
    }
}



.modelTypeIcon {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;

}

.modelTypeIcon:hover {
    border-radius: 8px;
    background: #F7F9FA;
}

.doubleArrowUp {
    background: #F7F9FA;
    text-align: center;
    border-radius: 0 0 4px 4px;
    padding: 3px 0 4px;
    cursor: pointer;
}

.doubleArrowDown {
    margin-top: -10px;
}

/* memory */
.memoryInnerWrapper {
    // padding: 0 12px 10px;
}

.memoryResultTitle {
    margin: 5px 0;
    border-bottom: 0px !important;
    padding: 0 12px !important;
}

.promptResultTitle {
    display: flex;
    justify-content: space-between;

}

.queryWrapper {
    border: 1px solid #D5D7DE;
    margin-bottom: 12px;
    border-radius: 8px;
    background-color: #EEF2F6;

    .resultTitle {
        padding-left: 12px;
    }

    & :global(.ant-input) {
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
    }
}

.queryResultTitle {
    border-bottom: 0px !important;
}

/* result */
.opResultWrapper {
    margin-bottom: 0px;
}

.outputResultWrapper {
    border: 1px solid #E1E7ED;
}

.outputResultWrapper .markResultTitle {
    display: flex;
    justify-content: space-between;
}

.httpWrapper {
    background: #EEF2F6;
    padding: 5px 12px 11px;
}

.httpWrapper .apiSelector {
    margin-bottom: 0px;
}

.urlInput input {
    border-color: #fff;
}

.outputBtn {
    display: flex;
    justify-content: right;
}

.outputBtn button {
    margin-left: 12px;
}

.resultTitle {
    border-radius: 4px 4px 0px 0px;
    height: 32px;
    line-height: 32px;
    color: #626F84;
    font-size: 14px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
}

.fullscreenBtn {
    margin-top: 9px;
    cursor: pointer;
}

.upIcon,
.downIcon {
    margin-top: 5px;
    margin-right: 6px;
    cursor: pointer;
    position: relative;
    top: 3px;

}

.upfcIcon,
.downfcIcon {
    position: absolute;
    top: 5px;
    right: 0px;
}

.fcApiTitle {
    display: flex;
    width: 24px;
    height: 24px;
    padding: 5px 8px;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: #F7F9FA;
}

.fcApiTitleEdit {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    svg path {
        transition: fill 0.3s;
    }

    &:hover svg path {
        fill: #006BFF;
    }
}

.apiTooltip {
    margin-left: 5px;
}

.apiTitle {
    margin-top: 5px;
}

.resultTitle span.titleContent {
    margin: 0 8px 0 0px;
}

.resultTitleContent {
    margin-right: 12px;
    color: #1D2531;
    font-size: 14px;
    font-weight: 600;
}

.structuredBtn {
    line-height: 32px;
}

.selectParams {
    /* position: absolute; */
    background: #EEF2F6;
    border-bottom: 1px solid #F2F7FF;
    border-radius: 0px 0px 4px 4px;
    width: 100%;
    bottom: 0;
    padding: 12px 12px 0;
    color: #626F84;
}

.selectParamsList {
    margin-top: 12px;
}

.selectParamsItem {
    background: #FFFFFF;
    border-radius: 4px;
    display: inline-block;
    padding: 4px 8px;
    margin-right: 12px;
    margin-bottom: 12px;
}

.selectLlmItem {
    margin-bottom: 0px;
    cursor: pointer;
}

.activedSelectLlmItem {
    background: #006BFF;
    color: #fff;
}

.selectParamsItem span {
    color: #9BA7BA;
    margin-right: 3px;
}

.selectParamsValueList {
    max-height: 200px;
    overflow-y: auto;
}

.selectParamsValueItem {
    background: #FFFFFF;
    border: 1px solid #B5D4FF;
    border-radius: 4px;
    margin-bottom: 12px;
    cursor: pointer;
}

.selectParamsValue {
    padding: 6px 12px;
    min-height: 34px;
}

.selectParamsName {
    height: 32px;
    line-height: 32px;
    padding-left: 10px;
    background: #F2F7FF;
    border-radius: 0 0 4px 4px;
}

.settingLeftBtn,
.settingRightBtn {
    width: 24px;
    height: 48px;
    padding: 11px 4px 10px 5px;
    background: #fff;
    border-width: 1px 0px 1px 1px;
    border-style: solid;
    border-color: #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 4px 0px 0px 4px;
    color: #006BFF;
    font-size: 14px;
    cursor: pointer;
    line-height: 22px;
}

.settingLeftBtn {
    position: absolute;
    right: 0px;
    top: 12px;
}

.settingRightBtn {
    margin-top: 12px;
}

.questionIcon {
    opacity: 0.5;
    margin-right: 10px;
}

.jsoneditor {
    height: 100%;
}

.fullScreenBtnWrap {
    cursor: pointer;
    background-color: rgb(215, 234, 245);
    padding: 7px 7px 4px 7px;
    border-radius: 10px;
    margin-right: 20px;
    display: inline-block;
}

.fullScreenBtnIcon {
    width: 18px;
    height: 18px;
}

// fc
.fcResultContent {
    border-radius: 4px;
    margin: 0;
}

.fcResultTextarea {
    width: 100%;
    height: 212px;

    textarea {
        border-radius: 0 0 4px 4px;
    }
}

.fcResultWrapper {
    background: #EEF2F6;
    margin-bottom: 12px;
    border: 0;
}

.chooseApiWrapper {
    // background: #EEF2F6;
    border: 0;
    padding-bottom: 12px;
}

.triggerWrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 0 16px;

    .triggerLabel {
        color: #626F84;
        margin-right: 13px;
        font-size: 14px;
        line-height: 32px;
    }

    .triggerSelector {
        flex: 1 1 330px;

        & :global(.ant-select-selector) {
            border-radius: 8px;
        }
    }
}

.tabsWrapper {
    padding: 0px;
}

// block Interaction

.guiCardBox {
    border-radius: 6px;
    height: 242px;
    position: relative;
    border: 1px solid transparent;

    .guiCardBoxButton {
        width: 80%;
        height: 32px;
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: none;
    }

    .guiCardBoxDelete {
        position: absolute;
        right: 0px;
        display: none;
    }

    &:hover {
        cursor: pointer;
        border: 1px solid #1677ff;

        .guiCardBoxButton {
            display: block;
        }

        .guiCardBoxDelete {
            display: block;
        }
    }
}

.guiCardListBox {
    padding: 0 24px;
    width: 100%;
}

.guiCardAddParams {
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid #FFF;
    background: var(---g-10, #EEF2F6);
}

.ant-select-selector {
    border-radius: 8px !important;
}

.modelSelectModal {
    :global {
        .ant-modal-content {
            width: 400px !important;
            padding: 16px !important;
        }
    }

    .modelSelectTitle {
        color: #1B2532;
        font-size: 14px;
        font-weight: 600;
        line-height: 32px;
    }

    .modelSelectHeader {
        padding-bottom: 24px;
        border-bottom: 1px solid #EEEFF2;
    }
}

.mcpCollapseHeader {
    :global(.ant-collapse-header) {
        height: 47px;
    }
}

.modelSelectModal {
    :global {
        .ant-modal-content {
            width: 400px !important;
            padding: 16px !important;
        }
    }

    .modelSelectTitle {
        color: #1B2532;
        font-size: 14px;
        font-weight: 600;
        line-height: 32px;
    }

    .modelSelectHeader {
        padding-bottom: 24px;
        border-bottom: 1px solid #EEEFF2;
    }
}

.collapseBoxBottom {
    border-bottom: 1px solid #EDF1F5
}

.collapseKeyLabel {
    color: #1D2531;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
}

.agentStrategyPanel {}

.aiAgentStrategyWrapper {
    :global(.ant-collapse-content-box) {
        padding-top: 5px !important;
        padding-bottom: 16px !important;
    }
}

.agentStrategyContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    // margin-top: 8px;
}

.strategySelect {
    width: calc(100% - 32px);
}

.settingIconWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
        background-color: #EEF2F6;
    }
}

.settingsPopover {
    width: 400px;
    padding: 16px;
}

.settingsPopoverTitle {
    font-weight: 600;
    font-size: 14px;
    color: #1B2532;
    margin-bottom: 12px;
}

.settingsPopoverContent {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sliderWrapper {
    flex: 1;
}

.stepsInputNumber {
    width: 80px;
}

.strategySettingsPopover {
    :global(.ant-popover-inner) {
        padding: 16px;
        border-radius: 8px;
    }
}

.aiAgentStrategyCollapse {
    border-top: 1px solid #EDF1F5;
    border-bottom: 0px solid transparent !important;
    border-left: 0px solid transparent !important;
    border-right: 0px solid transparent !important;
    border-radius: 0px !important;
    box-sizing: border-box;
    // :global(.ant-collapse-item > .ant-collapse-header) {
    //     padding: 12px 16px 16px 16px !important;
    // }
}

.aiModuleSlider {
    width: 176px;

    .collapseBoxBottom {
        border-bottom: 1px solid #EDF1F5
    }

    .collapseKeyLabel {
        color: #1D2531;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
    }
}

/* BlockItemWrap 样式 start */
.blockItemWrap {
    padding: 5px 0;
    background-color: #fff;

    :global(.ant-collapse-header-text) {
        font-weight: bold !important;
    }
}

.borderTop {
    border-top: 1px solid #EDF1F5;
}

.borderBottom {
    border-bottom: 1px solid #EDF1F5;
}

/* BlockItemWrap 样式 end */
/* BlockRandomValue 样式 start */

.blockItemContent {
    padding-bottom: 12px;
}

/* BlockRandomValue 样式 end */

/* BlockMergeVar 样式 start */
.groupItem {
    display: flex;
    align-items: center;
    padding: 4px 4px;
    gap: 4px;
    transition-property: box-shadow;

    &Handle {
        color: #BFC9D5;
        font-size: 18px;
        line-height: 28px;
        display: flex;
        align-items: center;
        font-weight: bold;

        &.cursorMove {
            // cursor-move
            cursor: move;
        }

        &.opacity60 {
            // opacity-60
            opacity: 0.6;
        }

        &Move {
            cursor: move;
        }

        &Icon {
            font-size: 15px;
        }
    }

    &Content {
        flex: 1 1 0%;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    &DeleteBtn {
        padding: 0 !important;

        &:hover {
            background-color: transparent !important;
        }
    }
}

.groupCard {
    background-color: #fff;
    border: 1px solid #E1E7ED;
    border-radius: 8px;
    width: 100%;
    padding-top: 8px;
    padding-bottom: 8px;
    margin-bottom: 12px;

    &Header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 12px;
        padding-right: 12px;
    }

    &Title {
        color: #626F84; // text-[#626F84]
        font-size: 14px; // text-sm
        line-height: 20px; // leading-5
    }

    &Content {
        background-color: #F7F9FA;
        border: 1px solid #D8E0E8;
        border-radius: 8px;
        margin-left: 8px;
        margin-right: 8px;
        padding: 4px;
    }

    &Empty {
        text-align: center;
        color: #9CA3AF;
        padding-top: 16px;
        padding-bottom: 16px;
    }

    &AddBtnWrapper {
        display: flex;
        justify-content: flex-start;
        padding-left: 28px;
        padding-bottom: 8px;
        padding-top: 8px;
    }

    &AddBtn {
        display: flex;
        align-items: center;
        gap: 4px; // gap-1
        border: 1px solid #E1E7ED;
        border-radius: 8px;
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 4px;
        padding-bottom: 4px;
        font-size: 12px;
        line-height: 16px;
        height: 24px;

        &:hover {
            background-color: transparent !important;
        }
    }

    &DeleteBtn {
        padding: 0 !important;

        &:hover {
            background-color: transparent !important;
        }
    }
}

.groupCardList {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    row-gap: 16px;
}

/* BlockMergeVar 样式 end */

.addbtnLine {
    .addLineBoxNewImg {
        display: none !important;
    }

    .addLineBoxNew {
        display: none !important;
    }

    &:hover {
        .addLineBoxNewImg {
            display: inline-block !important;
        }

        // .addLineBoxNew {
        //     display: flex !important;
        // }
    }
}

.addLineActive {
    .addLineBoxNewImg {
        display: inline-block !important;
    }

    &:hover {
        .addLineBoxNewImg {
            display: inline-block !important;
        }
    }
}

.addbtnLine:hover~svg {
    // path {
    //     stroke: #04b4e5;
    // }
}

.llmSelectIcon {
    width: 16px;
    height: 16px;
    aspect-ratio: 1/1;
    margin-right: 8px;
}

.llmSelectText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 320px;
}

.namimcpWrapper {
    display: flex;
    height: 32px;
    padding: 5px 12px;
    align-items: center;
    gap: 8px;
    flex: 1 0 0;
    border-radius: 8px;
    border: 1px solid #E1E7ED;
    background: #FFF;
}

.namiResetSelect {
    width: 76px;
    color: #006BFF;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
}

.namiAiStrategyCrapper {
    width: 440px;
    box-sizing: border-box;
    left: calc(-100% - 7px);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.namiAiModule {
    margin: 0 !important;
}

.namiAiContent {
    height: 500px;
}

.namillmConfigWrapper {
    width: 408px;
    top: 189px;
    right: 468px;
}

.collapseExtra {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;

    &Tip {
        padding: 4px 4px;
        display: flex;
        align-items: center;

        &Icon {
            width: 14px;
            height: 14px;
            color: #657083;
        }

        &Text {
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            color: #1B2532;
            margin-left: 4px;
        }

        &:hover {
            color: #006BFF;
            border-radius: 4px;
            background: #006BFF0A;

            &Text {
                color: #006BFF;
            }
        }
    }

    &Btn {
        padding: 3px 12px;
        border-radius: 8px;
        border: 1px solid #2A76FF;
        background: #F8F9FF;
        font-size: 14px;
        height: 28px;
        margin-left: 10px;
        gap: 4px;
        box-shadow: none;

        &Icon {
            margin-right: 0px;
        }
    }
}

.textAreaFooter {
    display: flex;
    gap: 10px;
    margin-top: 8px;

    &Btn {
        border-radius: 8px;
        border: 1px solid #2A76FF;
        background: #F8F9FF;
        font-size: 14px;
        line-height: 16px;
        box-shadow: none;
        color: #1B2532;
        height: 28px;
    }
}

.addLineBoxNew {
    position: absolute;
    cursor: pointer;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -60%);
    display: flex;
    align-items: center;
    justify-content: center;
    // gap: 8px;
    // padding: 8px 12px;
    border-radius: 640px;
    border: 1px solid #FFFFFF;
    background: linear-gradient(92deg, #4FCCDE -1.64%, #63B7F5 112.02%);
    box-shadow: 0px 8.889px 26.667px -2.667px rgba(75, 85, 105, 0.10), 0px 13.333px 40px 6.222px rgba(27, 37, 50, 0.06);
}

.addLineBoxNewImg {
    position: absolute;
    cursor: pointer;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -16px);
    text-align: center;
    width: 28px;
    height: 28px;
    // transition: width .1s ease, height .1s ease, transform .1s ease;

    // span {
    //     display: inline-block;
    //     border: 2px solid #ffffff;
    //     width: 18px;
    //     height: 18px;
    //     line-height: 10px;
    //     border-radius: 50%;
    //     background: linear-gradient(92deg, #4FCCDE -1.64%, #63B7F5 112.02%);
    //     transition: width .15s ease, height .15s ease, line-height .15s ease;

    //     img {
    //         width: 8px;
    //         height: 8px;
    //         transition: all .15s ease;
    //     }
    // }

    // &:hover,
    &.addLineBoxNewImgActive {
        transform: translate(-50%, -17px);
        width: 60px;
        height: 60px;

        span {
            width: 30px;
            height: 30px;
            line-height: 27px;
        }

        img {
            width: 12px;
            height: 12px;
        }
    }
}

.addLineBoxNewImgWindows {
    transform: translate(-50%, -18px);

    span {
        line-height: 9px;
    }

    &:hover,
    &.addLineBoxNewImgActive {
        transform: translate(-50%, -18px);

        span {
            line-height: 25px;
        }
    }
}

.modelHandleBox {
    width: 344px;
    height: 268px;
    box-sizing: border-box;
    display: flex;
    padding: 16px;
    flex-direction: column;
    gap: 8px;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10), 0px 15px 45px 7px rgba(27, 37, 50, 0.06);

    // position: relative;
    .modelHandleTitle {
        color: #1D2531;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
    }

    .modelSelectWrapper {
        margin: 0 !important;
        position: relative;
    }

    .modelTypeIcon {
        position: absolute;
        right: 50px;
        top: 50%;
        transform: translateY(-50%);
    }

    .llmSelectText {
        max-width: 192px;
    }

    .modelHandleBtn {
        position: absolute;
        bottom: 30px;
        right: 30px;
    }

    .llmConfigWrapper {
        position: absolute;
        bottom: unset;
        right: unset;
        top: 86px;
        left: 15px;
    }
}

.blockContainerWrapper {
    padding: 16px;
    // background: pink;

    &.borderTop {
        border-top: 1px solid #EDF1F5;
    }

    &.borderBottom {
        border-bottom: 1px solid #EDF1F5;
    }

    .collapseMaskWrapper {
        // border-bottom: 1px solid #EDF1F5;
        border-top: 0px solid transparent !important;
        border-left: 0px solid transparent !important;
        border-right: 0px solid transparent !important;
        border-radius: 0px !important;
        box-sizing: border-box;

        :global(.ant-collapse-item) {
            border-top: none;
            border-radius: 0 !important;
        }

        :global(.ant-collapse-item > .ant-collapse-header) {
            padding: 0px !important;
        }

        :global(.ant-collapse-content) {
            padding-bottom: 0px;
            margin-top: 10px !important;
        }

        :global(.ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
            padding: 0 !important;
        }
    }

    .collapseTitle {
        color: #1D2531;
        font-size: 14px;
    }
}

.runDebugBox {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    box-sizing: border-box;
    display: flex;
    padding: 24px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    background: #ffffff;
    box-shadow: 0px -10px 30px -3px rgba(75, 85, 105, 0.06);
    z-index: 499;

    .runDebugBoxContent {
        display: flex;
        height: 32px;
        padding: 5px 16px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        border-radius: 8px;
        border: 1px solid #006BFF;
        color: #006BFF;
        background: rgba(0, 107, 255, 0.04);
        cursor: pointer;
        font-size: 14px;

        img {
            margin-top: 1px;
        }
    }
}

.collapseAddItemBtnWrap {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 4px;

    .collapseAddItemBtn {
        padding: 5px 16px;
        border-radius: 8px;
        border: 1px solid #2A76FF;
        background: #F8F9FF;
        gap: 4px;
        height: 28px;
        color: #006BFF;

        &:hover {
            color: #2E8CFF !important;
            border: 1px solid #2E8CFF;
            background: #F8F9FF !important;

            path {
                fill: #2E8CFF !important;
            }
        }

        path {
            fill: #006BFF !important;
        }
    }

    & :global(.ant-btn-variant-text:disabled) {
        color: #BFC9D5 !important;
        border: 1px solid #EDF1F5 !important;
        background: #F7F9FA !important;

        path {
            fill: #BFC9D5 !important;
        }
    }

    svg {
        cursor: pointer;
    }
}



.fullScreenMask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 399;
}