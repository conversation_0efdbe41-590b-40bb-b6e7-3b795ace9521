.detailContent {
    width: 100%;
    height: calc(100% - 96px);
}
.knowledgeStyleDataShow {
    display: block; 
}

.knowledgeStyleDataHidden {
    display: none;
}
.stepOne {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    .stepOneHead {
        padding: 16px;
        width: 100%;
        background-color: #fff;
        // border-radius: 8px 8px 0px 0px;
        
        .stepOneHeadTitle {
            color: var(---, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 加粗/16px */
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
            margin-right: 12px;
        }
        .stepOneHeadSubTitle {
            color: var(---, #626F84);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;

            /* 常规/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
        }
    }
    .stepOneContent {
        width: 100%;
        background-image: url(../images/knowledge_local_bg.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        border-radius: 0px 0px 8px 8px;
        flex: 1;
        min-height: 400px;
        overflow-y: auto;
        margin-bottom: 16px;
        padding-bottom: 60px;
        position: relative;

        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        .centerContent {
            width: 70%;
            max-height: 80%;
            overflow-y: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            // background-color: #4EC01E;

            .localContent {
                flex: 1;
                width: 100%;
                background: #fff;
                // background-color: #006BFF;
                overflow-y: auto;
                border-radius: 8px;
                padding: 32px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;

                .localContentFile {
                    margin-top: 16px;
                    flex: 1;
                    width: 100%;
                    border-radius: 4px;
                    // border: 1px dashed var(---Cold-Gray-30, #D8E0E8);
                    background: var(---Gray-10, #F7F9FA);
                    margin-bottom: 8px;
                }

                .localContentFooter {
                    margin-top: 8px;
                    align-self: flex-start;
                    color: var(---, #626F84);
                    text-align: center;
                    font-feature-settings: 'clig' off, 'liga' off;
                    // background-color: yellow;

                    /* 常规/12px */
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                }
            }

            .onLineContent {
                flex: 1;
                width: 100%;
                background: #fff;
                border-radius: 8px;
                padding: 32px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
                overflow-y: auto;
                min-height: 238px;

                .onLineContentInput {
                    margin-top: 16px;
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    // margin-bottom: 16px;

                    :global .ant-input-affix-wrapper{
                        border-radius: 8px !important;
                    }
                }

                .updateRateDiv {
                    margin-top: 16px;
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                }

                .updateRateDivTitle {
                    color: var(---, #1B2532);
                    font-feature-settings: 'clig' off, 'liga' off;
                    /* 加粗/14px */
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    line-height: 22px; /* 157.143% */
                }

                .uploadOnlineFileWarper {
                    width: 100%;
                    max-height: 200px;
                    min-height: 80px;
                    // background-color: #006BFF;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: flex-start;
                    overflow-y: auto;
                    margin-top: 16px;

                    .uploadFileItem {
                        width: 100%;
                        padding: 16px;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: space-between;
                        border-radius: 4px;
                        background: #F7F9FA;
                        margin-bottom: 8px;

                        .uploadFileItemName {
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            justify-content: flex-start;
                            width: 89%;

                            .uploadFileItemNameUrl {
                                color: var(---, #1B2532);
                                text-align: center;
                                font-feature-settings: 'clig' off, 'liga' off;
                                /* 加粗/12px */
                                font-family: "PingFang SC";
                                font-size: 12px;
                                font-style: normal;
                                font-weight: 600;
                                line-height: 20px; /* 166.667% */
                                margin-right: 16px;
                                max-width: 75%;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .uploadFileItemNameSuccess {
                                color: #00B42A;
                                font-feature-settings: 'clig' off, 'liga' off;
                                /* 常规/14px */
                                font-family: "PingFang SC";
                                font-size: 14px;
                                font-style: normal;
                                font-weight: 400;
                                line-height: 22px; /* 157.143% */
                                margin-left: 16px;
                            }
                        }
                    }
                }

                .onLineContentFooter {
                    margin-top: 8px;
                    align-self: flex-start;
                    color: var(---, #626F84);
                    text-align: center;
                    font-feature-settings: 'clig' off, 'liga' off;

                    /* 常规/12px */
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                }
               
            }
            .centerContentTap {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                .centerContentTapTitle {
                    cursor: pointer;
                    padding: 5px 16px;
                    color: var(---, #626F84);
                    font-feature-settings: 'clig' off, 'liga' off;
                    /* 常规/14px */
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
                .centerContentTapTitleActive {
                    color: var(----70-Normal, #006BFF);
                    background-color: #fff;
                    border-radius: 8px 8px 0px 0px;
                    padding: 5px 16px;
                }
            }
        }

        .centerContentFooter {
            background: #FFF;
            padding: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0px 0px 8px 8px;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;

            .footerBtnCancel {
                margin-right: 12px;
            }

            .footerBtnNext{
                color: #fff;
            }
            .footerBtnNextDisabled{
                display: flex;
                height: 32px;
                padding:  4px  16px;
                justify-content: center;
                align-items: center;
                gap:  4px;
                border-radius:  8px;
                background: #EDF1F5;
                color:  #BFC9D5;
                border-color: transparent !important;
            }
        }
    }
    .stepOneBottom {
        padding: 16px;
        width: 100%;
        // border-radius: 8px;
        border: 1px solid var(---Gray-30-, #D8E0E8);
        background: var(---Gray-20, #EBF0F5);
        .stepOneBottomTitle {
            color: var(---, #BCCAD6);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/16px */
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
            margin-right: 12px;
        }
        .stepOneBottomSubTitle {
            color: var(---, #BCCAD6);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
        }
    }
}

.dragContent {
    width: 100%;
    height: 100px;
    // height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .dragTitle {
        color: var(----70-Normal, #006BFF);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    .dragSubTitle {
        color: var(---Auxiliary-Text, #9BA7BA);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        
        /* 常规/12px */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
    }
}
.stepTwoContent {
    width: 100%;
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    overflow-y: auto !important;
  

    .stepTwoContentOne {
        padding: 16px;
        width: 100%;
        background-color: #fff;
        &Title {
            color: var(---, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            
            /* 加粗/16px */
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
            margin-right: 12px;
        }

        &SubTitle {
            color: var(---, #626F84);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;
            
            /* 常规/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
        }
    }

    .stepTwoContentDetail {
        flex: 1;
        width: 100%;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;

        .stepTwoContentDetailRadio {
            padding: 24px 16px;
            box-sizing: border-box;
            flex: 1;
            width: 100%;

            .stepTwoFragment {
                padding: 24px;
                box-sizing: border-box;
                background: var(---Gray-10, #F7F9FA);
                border-radius: 4px;

                &Title {
                    color: var(---Main-Text, #1B2532);
                    font-feature-settings: 'clig' off, 'liga' off;

                    /* 加粗/16px */
                    font-family: "PingFang SC";
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 150% */
                }

                &SubTitle {
                    color: var(---Auxiliary-Text, #9BA7BA);
                    font-feature-settings: 'clig' off, 'liga' off;
                    
                    /* 常规/14px */
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                    margin-left: 24px;
                }

                .stepTwoFragmentWap {
                    margin-top: 24px;
                    width: 70%;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    justify-content: center;

                    .stepTwoFragmentWapFirst {
                        width: 96%;
                        margin-bottom: 16px;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: flex-start;
                    }

                    .stepTwoFragmentWapFirstTitle {
                        color: var(---, #626F84);
                        text-align: right;
                        font-feature-settings: 'clig' off, 'liga' off;
                        width: 100px;
                        /* 常规/14px */
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                        margin-right: 16px;
                    }

                    .stepTwoFragmentWapSec {
                        width: 96%;
                        display: flex;
                        flex-direction: row;
                        align-items: space-between;
                        justify-content: flex-start;
                    }
                }
                &Active {
                    border: 1px solid var(----70-Normal, #006BFF);
                }
            }

            .stepTwoQa {
                margin-top: 16px;
                padding: 24px;
                box-sizing: border-box;
                background: var(---Gray-10, #F7F9FA);
                border-radius: 4px;
                &Active {
                    border: 1px solid var(----70-Normal, #006BFF);
                }
            }
        }


        .stepTwoContentDetailFooter {
            width: 100%;
            padding: 16px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-end;
            border-top: 1px solid var(---Gray-30-, #D8E0E8);
        }
    }
}
.InputNumberTis {
    color: var(---Auxiliary-Text, #9BA7BA);
    font-feature-settings: 'clig' off, 'liga' off;

    /* 常规/12px */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    margin-top: 2px;
}

.content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    position: relative;

    .contentHeader {
        width: 100%;
        padding: 12px 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        
        .contentHeaderTitle {
            color: var(---, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
        }
    }

    .contentDetail {
        width: 100%;
        flex: 1;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
    }
}

.knowledgeListStyle {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    height: calc(100vh - 100px);
    background-color: #fff;
    border-radius: 8px 8px 0 0;
    // background-color: green;
    // position: absolute;
    // left: 0;
    // top: 0;
    // z-index: 100;
    // margin: 0px -16px;

    .knowledgeListBottom {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;    
        color: #626F84;
        /* 常规/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */

        .knowledgeListBottomTitle {
            margin-left: 12px;
        }
    }
}

.stateSuccess {
    &::before {
        content: "•";
        display: inline-block;
        margin-right: 5px;
        color: #4EC01E;
    }
}

.stateFail {
    &::before {
        content: "•";
        display: inline-block;
        margin-right: 5px;
        color: #BCCAD6;
    }
}

.stateTraining {
    &::before {
        content: "•";
        display: inline-block;
        margin-right: 5px;
        color: #006BFF;
    }
}

.tableNormalText {
    color: var(---, #1B2532);
    font-feature-settings: 'clig' off, 'liga' off;
    text-overflow: ellipsis;
    // overflow: hidden;
    // white-space: nowrap;

    /* 常规/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
.tableActiveText {
    color: var(----70-Normal, #006BFF);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
}

.tableDisableText {
    color: var(---, #BCCAD6);
    font-feature-settings: 'clig' off, 'liga' off;
    text-overflow: ellipsis;

    /* 常规/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}

.tableFileRow {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}

.tableFileRowImage {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 8px;
}

.streamWarper {
    width: 40%;
    height: 100%;
    position: absolute;
    right: -50%;
    bottom: 0px;
    z-index: 10;
    box-shadow: -15px 0px 45px 7px rgba(27, 37, 50, 0.06), -10px 0px 30px -3px rgba(75, 85, 105, 0.10);
    transition: all 0.3s ease-in-out;
}

.knowledgeChatDetailTestPreviewWrapper {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    background: #fff;

    .knowledgeChatDetailTestPreviewWrapperHeader {
        width: 100%;
        display: flex;
        justify-content:space-between;  
        align-items: center;  
        background: #F7F8FA;
        padding: 16px;
        
        .headerTitle {
            color: #1B2532;         
            /* 加粗/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
        }

        .headerSubTitle {
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
        }
    }

    .knowledgeChatDetailTestPreviewMessageWrapper {
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 16px 16px 5px 16px;
        height: calc(100% - 130px);
        overflow: auto;
        background-color: #fff;

        .knowledgeChatDetailTestPreviewMessageWrapperTis {
            padding: 16px;
            border-radius: 4px;
            background: var(---g-10, #F7F8FA);
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;

            /* 常规/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        .knowledgeChatDetailTestPreviewMessageItem {
            display: flex;
            gap: 8px;

            .knowledgeChatDetailTestPreviewMessageItemAvatar {
                width: 24px;
                height: 24px;
                flex-shrink: 0;

                .knowledgeChatDetailTestPreviewMessageItemAvatarImg {
                    width: 100%;
                    height: 100%;
                }
            }

            .knowledgeChatDetailTestPreviewMessageItemContent {
                // width: 100%;
                padding: 4px 10px;
                border-radius: 4px;
                user-select: text;
                max-width: 100%;
                word-break: break-word;
                color: var(---, #1B2532);
                font-feature-settings: 'clig' off, 'liga' off;
                margin-left: 32px;
                background: var(---g-10, #F7F8FA);


                /* 常规/14px */
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */

                .knowledgeChatDetailTestPreviewMessageItemContentValueSys{
                    white-space: pre-wrap;
                    padding: 12px 6px;
                }

                .knowledgeChatDetailTestPreviewMessageItemContentSource{
                    margin-top: 8px;
                    margin-bottom: 16px;
                    display: flex;
                    flex-direction: column;
                    color: #626F84;

                    .knowledgeChatDetailTestPreviewMessageItemContentSourceHeader {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: flex-start;

                        img {
                            margin-right: 8px;
                        }
                    }

                    .knowledgeChatDetailTestPreviewMessageItemContentSourceTitle {
                        width: 88px;
                    }

                    .knowledgeChatDetailTestPreviewMessageItemContentSourceFileWrapper{
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;

                        .knowledgeChatDetailTestPreviewMessageItemContentSourceFileInner {
                            display: flex;
                            flex-direction: row;
                            align-items: center;

                            .knowledgeChatDetailTestPreviewMessageItemContentValueSysScore {
                                color: #626F84;
                                font-size: 12px;
                                line-height: 20px;
                                border-radius: 11px;
                                background: rgba(0, 0, 0, 0.05);
                                padding: 0 8px;
                                margin-top: 2px;
                                display: inline-block;
                                margin-left: 8px;
                                margin-bottom: 2px;
                            }
    
                            .knowledgeChatDetailTestPreviewMessageItemContentSourceFileName{
                                margin-left: 2px;
                                color: var(---, #1B2532);
                                text-decoration: underline; // 添加这一行
                                cursor: pointer;
    
                                &:hover {
                                    color: #006BFF;
                                }
                            }
                        }
                    }
                }
            }
        }

        .knowledgeChatDetailTestPreviewMessageItemRight {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            margin-bottom: 16px;
            // .knowledgeChatDetailTestPreviewMessageItemContent {
            //     border-radius: 4px;
            //     background: #1D7CFF;
            //     // background-color: #4EC01E;
            //     color: #FFF;
            //     font-feature-settings: 'clig' off, 'liga' off;

            //     /* 加粗/14px */
            //     font-family: "PingFang SC";
            //     font-size: 14px;
            //     font-style: normal;
            //     font-weight: 600;
            //     line-height: 22px; /* 157.143% */
            // }
        }
    }

    .knowledgeChatDetailTestPreviewInputWrapper {
        display: flex;
        position: absolute;
        width: 100%;
        left: 0;
        padding: 16px;
        margin-top: 16px;
        background: #fff;
        box-shadow: 0px -11px 25px 4px rgba(27, 37, 50, 0.07);
        input{
            margin-right: 12px;
        }
    }

}
.knowledgeStopBtn {
    position: absolute;
    left: 45%;
    bottom: 60px;
}
.knowledgeChatAgentContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    // margin-top: 16px;
}
.knowledgeChatItemHolder {
   padding:4px;
   box-sizing: border-box;
   border: 1px solid transparent;
   position: relative;
   .knowledgeChatItemDelete {
    position: absolute;
    top: -7px;
    right: -7px;
     img {
        width: 18px;
        height: 18px;
     }
   }
}


.knowledgeChatItemHolderHover {
    border: 1px solid #D5D7DE;
    border-radius: 4px;
    cursor: pointer;
    box-sizing: border-box;
}
.knowledgeChatUserAnswer {
    // background: #1D7CFF;

    .userContent {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;

        .userContentImg {
          border-radius: 12px;
          width: 24px;
          height: 24px;
        }
        .userContentName {
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;
            margin-left: 8px;            
            /* 常规/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.knowledgeChatUserAnswerMessage {
    padding: 2px 10px;
    border-radius: 4px;
    background: var(----70-, #006BFF);
    color: #FFF;
    margin-left: 32px;
    /* 加粗/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    // margin-top: -10px;
}

.uploadFileWarper {
    width: 100%;
    max-height: 200px;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    // background-color: cyan;
    overflow-y: auto;

    .isUploadFileStyle {
        width: 100%;
        padding: 12px 16px 16px 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        border-radius: 4px;
        background: #F7F9FA;
        margin-bottom: 8px;

        .uploadFileItemName {
            color: var(---, #1B2532);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;
            /* 加粗/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px; /* 166.667% */
            margin-right: 16px;
        }
    }

    .uploadFileItem {
        width: 100%;
        padding: 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-radius: 4px;
        background: #F7F9FA;
        margin-bottom: 8px;

        .uploadFileItemDetail {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
        }

        .uploadFileItemName {
            color: var(---, #1B2532);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;
            /* 加粗/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px; /* 166.667% */
            margin-right: 16px;
        }

        .uploadFileItemNameSize {
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/12px */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
        }
    }
}

.addFileProperContent {
    padding: 16px;

    .addFileProperContentHeader {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: flex-start;
    }

    .addFileContentFooter {
        margin-top: 24px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .addFileContentFooterTitle {
            color: var(----70-, #006BFF);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;

            /* 常规/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            cursor: pointer;
        }
    }

    .addFileProperContentTitle {
        color: var(---, #1B2532);
        font-feature-settings: 'clig' off, 'liga' off;
        /* 常规/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        vertical-align: baseline;
    }
}

.popoverContent {
    padding:  0 24px 24px 24px;
    border-radius: 8px;
    box-sizing: border-box;
    width: 400px;
    height: 400px;
    .popoverContentHeader {
        padding: 16px 8px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        span {
            color: var(---Main-Text, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;

            /* 加粗/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
        }

        img {
            cursor: pointer;
        }
    }
    .popoverContentBody {
        height: calc(100% - 50px);
        border-radius: 4px;
        background: var(---g-10, #F7F8FA);
        padding: 16px;
        box-sizing: border-box;
        overflow-y: auto;
    }
}

.catchContentTitle {
    color: var(---, #626F84);
    font-feature-settings: 'clig' off, 'liga' off;
    
    /* 常规/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
.catchContentUrl {
    overflow: hidden;
    color: var(---, #1B2532);
    font-feature-settings: 'clig' off, 'liga' off;
    cursor: pointer;
    
    /* 常规/12px */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    margin-bottom: 24px;
    max-width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    a {
      color: #1B2532;
    }

    a:default {
        color: #1B2532;
    }

    a:hover {
        color: #006BFF;
    }
    a:active {
        color: #1B2532;
    }
}