.login{
  text-align: center;
}
.homeBg {
  background: url(https://p0.ssl.qhimg.com/t110b9a9301cd39c931f340d3d0.png) #000;
  background-repeat: no-repeat;
  background-position: 0 0px;
  background-size: 100%;
  overflow: auto;
  min-height: 100vh;
}
.videoModal {
  display: none;
  position: fixed;
  background: rgba(0, 0, 0, 0.80);
  z-index: 100;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  &.vedioActive{
    display: flex;
  }
  .modelContent {
    position: relative;
    width: 1200px;
    display: flex;
    padding: 8px;
    border-radius: 16px;
    border: 1px solid #2B263D;
    background: rgba(24, 20, 39, 0.50);
    backdrop-filter: blur(5px);
  }
  .videoClose {
    position: absolute;
    display: flex;
    width: 32px;
    height: 32px;
    padding: 10px;
    justify-content: center;
    align-items: center;
    aspect-ratio: 1/1;
    border-radius: 100%;
    border: 1px solid #2B263D;
    background: rgba(24, 20, 39, 0.50);
    top: -48px;
    right: 0;
    cursor: pointer;
    &::before {
      content: ' ';
      display: block;
      width: 12px;
      height: 12px;
      background-image: url(https://s2.ssl.qhres2.com/static/961ac6eff0e3497d.svg);
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
    }
    &:hover {
      background: rgba(57, 49, 87, 0.50);
    }
  }
  .video {
    width: 100%;
    border-radius: 8px;
  }
}

.homeBody {
  padding-top: 80px;
  width: 100%;
  text-align: left;

  .homeBodyContent {
    position: relative;
    padding: 80px calc(50% - 600px);
    width: 100%;
    &:last-child {
      padding: 0;
    }
    &:nth-child(3) {
      padding: 80px 0;
    }
    &:nth-child(2){
      &::before {
        content: ' ';
        position: absolute;
        background-image: url(https://p2.ssl.qhimg.com/t110b9a93017a6fb1dc9bcf6fae.png);
        width: 730px;
        height: 570px;
        left: 0;
        background-size: contain;
        background-repeat: no-repeat;
        top: 40%;
        background-position: left center;
      }
    }
    &:nth-child(4) {
      &::before {
        content: ' ';
        display: block;
        position: absolute;
        background-image: url('https://p1.ssl.qhimg.com/t110b9a93013d3f8764a41a1e42.png');
        width: 729px;
        height: 570px;
        right: 0;
        background-size: contain;
        background-repeat: no-repeat;
        bottom: 0;
        background-position: right center;
      }
    }
    &:nth-child(5) {
      &::before {
        content: ' ';
        display: block;
        position: absolute;
        background-image: url('https://p1.ssl.qhimg.com/t110b9a93013d3f8764a41a1e42.png');
        opacity: 0.6;
        width: 729px;
        height: 570px;
        right: 0;
        background-size: contain;
        background-repeat: no-repeat;
        bottom: 0;
        background-position: right center;
      }
    }
    &:nth-child(6) {
      &::before {
        content: ' ';
        display: block;
        position: absolute;
        background-image: url('https://p1.ssl.qhimg.com/t110b9a93014d82908dff64f9ae.png');
        width: 729px;
        height: 570px;
        left: 0;
        background-size: contain;
        background-repeat: no-repeat;
        bottom: 0;
        background-position: left center;
      }
    }
    // position: absolute;
    // top: calc(calc(100% - 424px) / 2);
    // left: 20%;
    // width: 504px;

    .bodyTitle {
      // color: var(---, #1B2532);
      // font-family: "360shouhuType-Bold";
      // font-size: 60px;
      // font-style: normal;
      // font-weight: 700;
      height: 74px; /* 173.333% */
    }

    .bodySubTitle {
      color: #fff;
      /* 常规/24px */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      // opacity: 0.6;
      // line-height: 36px; /* 150% */
      width: 998px;
      display: inline-block;
      margin-top: 16px;
      max-width: 90%;
      opacity: 0.6;
      line-height: 28px;
    }
    .bodyBtnWrapper {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-top: 40px;
      .videoBtn {
        display: flex;
        width: 168px;
        height: 56px;
        padding: 6px 16px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        background-image: url(https://p0.ssl.qhimg.com/t110b9a9301e5dd974824ee8db8.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        border-radius: 130.769px;
        // border: 0.5px solid rgb(196, 221, 255, .5);
        background-color: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(2px);
        color: #FFF;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.50);
        font-size: 20px;
        line-height: 22px;
        cursor: pointer;
        &::before {
          content: ' ';
          width: 28px;
          height: 28px;
          background-image: url('https://s2.ssl.qhres2.com/static/14065e2a5115caaf.svg');
          background-size: 100%;
          background-repeat: no-repeat;
          background-position: center;
        }
      }
    }

    .bodyBtn {
      // margin-top: 40px;
      position: relative;
      z-index: 0;
      cursor: pointer;
    }
    .contentWrapper{
      background: url(https://p5.ssl.qhimg.com/t110b9a93014e16147b30ee0599.png) no-repeat 97% -55px;
      background-size: 38%;
      img{
        margin-top: -18.5%;
        width: 126%;
        margin-left: -22%;
        margin-bottom: -65px;
      }
    }

    .contentTitle {
      text-align: center;
      font-family: "360shouhu Type";
      font-size: 48px;
      font-weight: 700;
      position: relative;
      color: #fff;
      margin-bottom: 60px;
      .titleStar {
        position: absolute;
        top: -8px;
        width: 44px;
        height: 44px;   
      }
    }

    .contentImg {
      height: 560px;
      width: 100%;
      background-image: url('https://p1.ssl.qhimg.com/t110b9a930199e80ec4d403f4b3.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center bottom;
    }


    .homeContentSecond {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
      .contentFirst {
        display: flex;
        height: 340px;
        padding: 40px;
        align-items: center;
        align-self: stretch;
        gap: 16px;
        background-image: url('https://p2.ssl.qhimg.com/t110b9a93016c367223616cb7df.png');
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: 50%;
      }
      .contentSecond {
        display: flex;
        height: 360px;
        gap: 24px;
        .contentSecondItem {
          display: flex;
          padding: 40px;
          flex-direction: column;
          gap: 24px;
          flex: 1 0 0;
          align-self: stretch;
          background-image: url('https://p0.ssl.qhimg.com/t110b9a93012a5a2d15a2cd4bbb.png');
          background-size: 100%;
          background-repeat: no-repeat;
          background-position: 50%;
          &:nth-child(2) {
            background-image: url('https://p5.ssl.qhimg.com/t110b9a930112cc54240a9591f1.png');
          }
          &:nth-child(3) {
            background-image: url('https://p1.ssl.qhimg.com/t110b9a9301787e05f3072b8455.png');
          }
        }
      }
      .contentThird {
        display: flex;
        height: 360px;
        align-items: flex-start;
        gap: 24px;
        .contentThirdItem {
          display: flex;
          padding: 40px;
          flex-direction: column;
          align-items: center;
          gap: 16px;
          flex: 1 0 0;
          align-self: stretch;
          background-image: url('https://p2.ssl.qhimg.com/t110b9a930136bb27050bfa9668.png');
          background-size: 100%;
          background-repeat: no-repeat;
          background-position: 50%;
          &:nth-child(2) {
            background-image: url('https://p0.ssl.qhimg.com/t110b9a93019ac1c811e60299c0.png');
          }
        }
      }
      .contentFourth {
        display: flex;
        height: 353px;
        padding: 40px;
        align-items: center;
        align-self: stretch;
        gap: 16px;
        background-image: url('https://p4.ssl.qhimg.com/t110b9a9301ce10013def24419d.png');
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: 50%;
        .contentLeft {
          gap: 24px;
          width: 550px;
          justify-content: unset;
          .contentLeftBtns {
            margin-top: auto;
          }
        }
      }
      .contentLeft {
        display: flex;
        width: 600px;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        align-self: stretch;
        .contentLeftBtns {
          display: flex;
          align-items: center;
          gap: 14px;
          .contentLeftBtn {
            display: flex;
            padding: 6px 18px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            border-radius: 222px;
            border: 0.5px solid rgba(255, 255, 255, 0.50);
            background: rgba(255, 255, 255, 0.08);
            color: var(--85, rgba(255, 255, 255, 0.85));
            font-feature-settings: 'liga' off, 'clig' off;

            /* 常规/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
      }
      .contentLeftTitle {
        color: var(--100, #FFF);
        font-feature-settings: 'liga' off, 'clig' off;

        /* 加粗/28px */
        font-family: "PingFang SC";
        font-size: 28px;
        font-style: normal;
        font-weight: 600;
        line-height: 42px; /* 150% */
      }
      .contentLeftDesc {
        color: #FFF;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 28px; /* 175% */
        opacity: 0.8;
      }
    }

    .homeContentThird {
      display: flex;
      width: 100%;
      height: 606px;
      align-items: center;
      gap: 60px;
      position: relative;
      
      .homeContentLeft {
        display: flex;
        flex-direction: column;
        gap: 48px;
        width: calc(100% - 620px);
        .contentLeftItem {
          display: flex;
          padding: 5px 0;
          flex-direction: column;
          gap: 8px;
          .contentLeftTitle {
            span {
              background: linear-gradient(90deg,#A879FF, #4461FF);
              color: transparent;
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
              font-size: 28px;
              font-weight: 600;
              line-height: 42px; /* 150% */
            }
           
          }
          .contentLeftDesc {
            color: #FFF;
            font-size: 16px;
            line-height: 24px; /* 150% */
            opacity: 0.6;
          }
        }
      }

      .homeContentRight {
        display: block;
        position: absolute;
        width: 938px;
        height: 755px;
        background-image: url('https://p3.ssl.qhimg.com/t110b9a9301834d9078bf2a8ece.png');
        background-size: 938px 755px;
        background-repeat: no-repeat;
        background-position: -60px 0;
        left: calc(100% - 590px);
        top: -155px;
      }
    }

    .homeContentFourth {
      display: flex;
      width: 100%;
      height: 606px;
      justify-content: flex-end;
      align-items: center;
      position: relative;
      .homeContentLeft {
        display: block;
        position: absolute;
        width: 1083px;
        height: 833px;
        background-image: url('https://p5.ssl.qhimg.com/t110b9a9301cdd146b6dd8e5455.png');
        background-size: 1083px 833px;
        background-repeat: no-repeat;
        background-position: 35px -70px;
        right: 465px;
        top: 0;
      }
      .homeContentRight {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 32px;
        .contentRightTitle {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          span {
            color: #FFF;
            font-size: 28px;
            font-weight: 600;
            line-height: 42px; /* 150% */
            &:last-child {
              background: linear-gradient(90deg, #A879FF, #4461FF);
              color: transparent;
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
        .contentRightList {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 24px;
          align-self: stretch;
          .contentRightItem {
            display: flex;
            width: 252px;
            padding: 12px 24px;
            align-items: center;
            gap: 8px;
            border-radius: 222px;
            border: 1px solid rgba(92, 119, 230, 0.20);
            background: linear-gradient(93deg, rgba(40, 98, 255, 0.40) 1.04%, rgba(169, 71, 255, 0.08) 99.3%);
            span {
              color: var(--100, #FFF);
              font-feature-settings: 'liga' off, 'clig' off;

              /* 常规/16px */
              font-family: "PingFang SC";
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 24px; /* 150% */
            }
          }
        }
      }
    }

    .homeContentFiveth {
      display: flex;
      width: 100%;
      flex-direction: column;
      gap: 60px;
      .homeContentTop {
        .contentTopList {
          display: flex;;
          .contentTopItem {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1 0 0;
            div {
              display: flex;
              align-items: center;
              gap: 12px;
              align-self: stretch;
              height: 44px;
              span {
                color: #FFF;
                font-size: 14px;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
              }
              &:first-child {
                margin-bottom: 24px;
                span {
                  font-size: 28px;
                  font-weight: 600;
                  line-height: 42px; /* 150% */
                }
              }
            }
          }
        }
      }
      .homeContentBottom {
        display: block;
        height: 485px;
        background-image: url(https://p1.ssl.qhimg.com/t110b9a93016168f65ecfc326da.png);
        background-size: cover;
        background-repeat: no-repeat;
      }
    }

    .homeContentSixth {
      display: flex;
      height: 320px;
      align-items: flex-start;
      gap: 24px;
      .homeContentLeft, .homeContentRight {
        display: flex;
        padding: 40px;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 24px;
        flex: 1 0 0;
        align-self: stretch;
        background-image: url(https://p4.ssl.qhimg.com/t110b9a9301dc775d5ebe28ddce.png);
        background-size: 100%;
        background-repeat: no-repeat;
        transition: background-image 0.3s ease;
        .homeContentLeftTitle {
          display: flex;
          align-items: center;
          gap: 24px;
          align-self: stretch;
          img {
            display: flex;
            width: 44px;
            height: 44px;
            justify-content: center;
            align-items: center;
            gap: 8px;
          }
          span {
            color: #FFF;
            font-size: 28px;
            font-weight: 600;
            line-height: 42px; /* 150% */
          }
        }
        .homeContentLeftContent {
          color: #FFF;
          font-size: 16px;
          font-weight: 400;
          line-height: 40px; /* 250% */
        }
        &:hover {
          background-image: url(https://p3.ssl.qhimg.com/t110b9a9301619fac881b9aab42.png);
        }
      }

    }

    .homeContentLast {
      display: flex;
      height: 380px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 40px;
      background: url(https://p5.ssl.qhimg.com/t110b9a93014002b341f529bb0e.png) #000;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: bottom;
      .homeContentLastTitle {
        color: #FFF;
        text-align: center;
        font-size: 48px;
        font-weight: 700;
      }
      .homeContentLastBtns {
        display: flex;
        align-items: center;
        gap: 24px;
        .homeContentLastBtn {
          display: flex;
          width: 160px;
          height: 40px;
          padding: 10px 24px;
          justify-content: center;
          align-items: center;
          gap: 8px;
          border-radius: 12px;
          border: 0.5px solid #FFF;
          color: #FFf;
          cursor: pointer;
          &:last-child {
            border: none;
            background: linear-gradient(92deg, #6358FF 5.84%, #3354FF 98.31%);
          }
        }
      }
    }
  }
}
.header{
  height: 80px;
  width: 100%;
  position: fixed;
  z-index: 1;
  &.headerActive {
    background: rgba(2, 6, 19, 0.80);
    backdrop-filter: blur(20px);
  }
  .headerInner{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    height: 100%;
    width: 62.5%;
    margin: 0 auto;
    padding: 13px 0;
  }
  .title{
  }
}
.myAgent{
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .userContent {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    font-weight: normal;
    font-size: 16px;
  }
}
.login{
  // margin-right: 48px;
  color: #fff;
}
.login .loginBtn{
  line-height: 55px;
  border-radius: 12px;
  background: linear-gradient(92deg, #6358FF 5.84%, #3354FF 98.31%);
  padding: 10px 24px;
  color: #F9FAFB;

  text-align: center;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  cursor: pointer;
}
.noLogin{
  display: inline-block;
  margin-top: 17px;
}

.filterWrapper{
  padding: 0 24px 2px;
  display: flex;
  justify-content: space-between;
}
.sortType{
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
  color: #9BA7BA;
  font-size: 14px;
  height: 32px;
}
.activeSortTypeItem{
  background: linear-gradient(90deg, #6E7CF8 3.33%, #3B73F7 100%);
  border-radius: 30px;
  color: #FFFFFF;
}
.sortTypeItem{
  padding: 5px 16px;
  display: inline-block;
  vertical-align: middle;
  height: 32px;
  cursor: pointer;
}
.modelType{
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
}
.tagList{
  padding: 16px 24px 6px;
  display: flex;
}
.tagItems{
  width: calc(100% - 164px);
  height: 32px;
  overflow-x: hidden;
  margin: 0 16px;
  position: relative;
}
.tagInnerItems{
  width: 1825px;
  height: 32px;
  position: absolute;
  left: 0px;
}
.tagItem{
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  /* box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04); */
  color: #626F84;
  border-radius: 20px;
  font-size: 12px;
  padding: 6px 16px;
  margin-right: 10px;
  height: 32px;
  line-height: 20px;
  cursor: pointer;
  float: left;
}
.tagActiveItem{
  color: #F2F7FF;
  background: linear-gradient(90deg, #6F7DF9 2.85%, #3D74F8 98.96%);
}
.tagBtn{
  display: inline-block;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.435) 2.85%, rgba(255, 255, 255, 0.28) 98.96%);
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
  width: 32px;
  height: 32px;
  text-align: center;
  cursor: pointer;
  line-height: 31px;
  color: #818284;
  font-size: 12px;
}

.promptList{
  height: calc(100vh - 164px);
  padding: 10px 24px 24px;
  overflow-y: scroll;
}
.waterFallItem{
  position: relative;
  background: #B5D4FF;
  border-radius: 8px;
  cursor: pointer;
}
.waterFallItem img{
  border-radius: 8px;
  width: 100%;
  display: block;
}
.promptInfo{
  background: rgba(43, 54, 88, 0.8);
  border-radius: 0 0 8px 8px;
  position: absolute;
  width: 100%;
  bottom: 0px;
  color: #fff;
  padding: 12px;
  font-size: 14px;
  display: none;
}
.activePromptInfo{
  display: block;
}
.promptTitle{
  color: #1B2532;
  font-size: 16px;
  padding: 12px 13px;
}
.promptDesc{
  color: #1B2532;
  font-size: 13px;
  padding: 0px 13px 12px;
}
.promptResult{
  color: #1B2532;
  font-size: 12px;
  padding: 0 13px 15px;
}
.promptInfo img{
  width: 20px;
  vertical-align: bottom;
  margin-right: 4px;
  display: inline-block;
}
img.starIcon{
  width: 13px;    
  position: relative;
  top: -4px;
  border-radius: 0px;
}
img.starActiveIcon{
  width: 14px;    
}
.starActiveNum{
  color: #006BFF;
}
.userInfoWrapper{
  background: #fff;
  border-radius: 8px;
  padding: 14px;
  color: #626F84;
  font-size: 14px;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  right: -55px;
  top: -10px;
  z-index: 1;

  .userNameBg,.userInfoInner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;

    img {
      margin-right: 12px;
      width: 20px;
      height: 20px;
    }
  }

  .userInfoInner {
    margin-top: 4px;
    cursor: pointer;
    color: #1B2532;
    img {
      margin-right: 14px;
      width: 17px;
      height: 17px;
    }
  }
}
.userBox{
  padding: 0px;
  display: flex;
  align-items: center;
  a{
    color: #FFF;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-right: 32px;
  }
}
.userWrapper{
  padding: 12px;
}
.userName{
  margin-bottom: 7px;
}
.doc{
  padding: 5px 0;
}
.doc a{
  margin-left: 10px;
}
.userName img{
  margin-right: 8px;
  vertical-align: middle;    
  margin-top: -2px;
}
.logout{
  padding: 5px 0;
  cursor: pointer;
}
.logout:hover{
  opacity: 0.7;
  border-radius: 4px;
}
.logoutBtn{
  margin-left: 10px;
}
.showUserInfoWrapper{
  display: block;
}
.userInfoTitle{
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.userInfoDesc{
  font-size: 12px;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.userInfo{
  display: flex;
  justify-content: space-between;
  line-height: 20px;
  font-size: 12px;
}
.loadMore{
  text-align: center;
  font-size: 12px;
  color: rgba(82, 78, 78, 0.664);
  margin: 10px 0;
  cursor: pointer;
}

/* openDetail */
.openDetail{
  margin-top: 18px;
}
.openDetailContent{
  display: flex;
}
.openDetailLeftContent{
  width: 580px;
  height: 556px;
  border: 1px solid #BCCAD6;
  margin-right: 16px;
  border-radius: 8px;
  text-align: center;
  position: relative;
}
.openTextDetailLeftContent{
  width: 580px;
  height: 556px;
  margin-right: 16px;
  border-radius: 8px;
  text-align: center;
  position: relative;
}
div.promptWrapper{
  height: 240px;
}
div.result{
  height: 296px;
}
.openDetailLeftContent img{
  display: inline-block;
  vertical-align: middle;
  border-radius: 8px;
}
.vline{
  width: 1px;
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.leftImgIcon{
  position: absolute;
  left: 10px;
  top: 261px;
  background: #fff;
  padding: 10px;
  border-radius: 20px;
  cursor: pointer;
}
.rightImgIcon{
  right: 10px;
  position: absolute;
  top: 261px;
  background: #fff;
  padding: 10px;
  border-radius: 20px;
  cursor: pointer;
}
.pageIcon{
  position: absolute;
  bottom: 0px;
  left: 280px;
  color: #aaa;
  font-size: 12px;
}
img.hideImg{
  display: none;
}

.openDetailRightContent{
  width: 356px;
  font-size: 12px;
  position: relative;
}
.detailTitleWrapper{
  /* border-bottom: 1px solid #BCCAD6; */
  margin-bottom: 16px;
}
.detailTitle{
  color: #1B2532;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}
.detailDesc{
  color: #1B2532;
  font-weight: 400;
  padding-bottom: 16px;
  border-bottom: 1px solid #BCCAD6;
}
.detailPrompt{
  color: #1B2532;
  font-weight: 400;
  border-bottom: 1px solid #BCCAD6;
  padding-bottom: 16px;
}
.detailPromptTitle{
  color: #626F84;
  margin-bottom: 3px;
}
.inputWrapper{
  padding: 10px 16px;
}
.paramItem{
  width: 50%;
  display: inline-block;
  margin-top: 16px;
}
.paramItemTitle{
  color: #626F84;margin-bottom: 3px;
}
.copyBtn{
  position: absolute;
  right: 0;
  bottom: -50px;
  margin: 0;
}

.openDetailFoot{
  width: 580px;
  color: #626F84;
  display: flex;
  font-size: 12px;
  justify-content: space-between;
  margin-top: 20px;
}
.openDetailFootLeft{
  width: 300px;
  display: flex;
  justify-content: flex-start;
  margin-top: 6px;
}
.openDetailTime{
  margin-right: 10px;
}
.modalBg{
  background: url(../images/common/bg.png);
  background-size: cover;
  width: 1000px!important;
  height: 690px;
  border-radius: 10px;
}

// footer
.homeFooter{
  display: flex;
  padding-top: 80px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 80px;
  background-color: #000;
  background-image: url(https://p1.ssl.qhimg.com/t110b9a9301511a33ffca6d20a7.png);
  background-size: 468px 195px;
  background-repeat: no-repeat;
  background-position: left bottom;
  position: relative;
  .footer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 40px;
    width: 1200px;
    .footerLogo {
      display: flex;
      align-items: center;
      gap: 8px;
      &::before {
        content: ' ';
        display: block;
        background-image: url(https://s3.ssl.qhres2.com/static/b22fc388ca9ce846.svg);
        background-size: 100%;
        background-repeat: no-repeat;
        width: 36px;
        height: 36px;
      }
      div {
        background-image: url(https://p0.ssl.qhimg.com/t110b9a9301cbe45950d6fb7816.png);
        background-size: 100%;
        background-repeat: no-repeat;
        height: 25px;
        width: 240px;
      }
    }
    .footerLine {
      border-bottom: 1px solid #ffffff;
      width: 100%;
      opacity: 0.1;
    }
    .footerContent {
      display: flex;
      align-items: flex-start;
      gap: 20px;
      width: 100%;
      .footerContentLeft {
        display: flex;
        align-items: flex-start;
        gap: 289px;
        flex: 1 0 0;
        .footerContentLeftItem {
          display: flex;
          width: 320px;
          flex-direction: column;
          align-items: flex-start;
          gap: 24px;
          color: #fff;
          flex: 1 0 0;
          div {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px; /* 150% */
          }
          .footerContentLeftItemLink {
              display: flex;
              align-items: flex-start;
              align-content: flex-start;
              gap: 24px;
              align-self: stretch;
              flex-wrap: wrap;
              a {
                display: flex;
                width: 180px;
                align-items: center;
                gap: 8px;
                img {
                  display: flex;
                  width: 24px;
                  height: 24px;
                  padding: 3.333px;
                  justify-content: center;
                  align-items: center;
                  gap: 3.333px;
                  flex-shrink: 0;
                  aspect-ratio: 1/1;
                  border-radius: 5.333px;
                  background: #FFF;
                }
                span {
                  font-size: 14px;
                  line-height: 32px; /* 228.571% */
                  opacity: .8;
                  font-weight: 400;
                }
                &:nth-child(4) {
                  img {
                    height: auto;
                    padding: 4.508px 2px 2.508px 3.333px
                  }
                }
              }
          }
        }
        .footerContentRightItem {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 24px;
          color: #fff;
          div {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px; /* 150% */
          }
          .footerContentLeftItemDesc {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
            span {
              font-size: 14px;
              line-height: 22px; /* 157.143% */
              font-weight: 400;
              opacity: .8;
            }
          }
        }
      }
    }
  }
  .footerCopyright {
    width: 100%;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    line-height: 60px;
    color: #FFF;
    font-size: 12px;
    opacity: 0.4;
  }
}

.mobileHomeBg{
  background: #000;
}