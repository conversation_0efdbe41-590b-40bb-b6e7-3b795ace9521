/* operationsList */
.teamMembersFilter{
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}
.teamMembersLeftFilter{
    display: flex;
    color: #626F84;
    font-size: 14px;
}
.teamMembersFilterLabel{
    margin-right: 8px;
    margin-left: 16px;
}
.teamMembersFilterItem{
    margin-right: 32px;
}
.teamMembersRightFilter button{
    margin-right: 16px;
}
.tableWrapper{
    height: calc(100% - 48px);
}
.deploySuccessStatus{
    background: #F1FEE1;
    color: #4EC01E;
    padding: 5px 8px;
    border-radius: 3px;
}
.deployErrorStatus{
    background: #FFF4F2;
    color: #FF5E68;
    padding: 5px 8px;
    border-radius: 3px;
}
.fileSuccessStatus{
    width: 6px;
    height: 6px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 6px;
    background: #4EC01E;
    margin-right: 5px;
    margin-top: -2px;
}
.fileErrorStatus{
    width: 6px;
    height: 6px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 6px;
    background: #626F85;
    margin-right: 5px;
    margin-top: -2px;
}

.cancelDeployBtn, .detailBtn{
    color: #006BFF;
    margin-right: 10px;
    cursor: pointer;
}
.fileName{
    color: #006BFF;
}
/* operationDetail */
.operationDetailStatus{
    padding: 16px 32px 24px;
    color: #1b2532;
    position: relative;
    display: flex;
    justify-content: space-between;
    background: #FFF;
}
.publicWrapper{
    line-height: 32px;
    .publicTitle{
        font-weight: 600;
        margin-right: 40px;
        font-size: 14px;
    }
}
.statusTitle{
    color: #1B2532;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 16px;
}
.status{
    font-weight: 400;
    font-size: 16px;
    color: #1B2532;
}
.statusWrapper {
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
.successStatus{
    display: inline-block;
    background: #4EC01E;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    vertical-align: middle;
    margin: -3px 8px 0 0px;
}
.errorStatus{
    display: inline-block;
    background: #626F85;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    vertical-align: middle;
    margin: -3px 8px 0 0px;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
.cancelBtn{
    margin-left: 32px;
    margin-right: 24px;
}
.cancelBtn, .monitorBtn{
    color: #006BFF;
    cursor: pointer;
     /* 常规/14px */
     font-family: PingFang SC;
     font-size: 14px;
     font-style: normal;
     font-weight: 400;
     line-height: 22px; /* 157.143% */
}
.rightFooter {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
}
.rightFooterApi {
    display: flex;
    width: 337px;
    flex-direction: row;
    padding: 5px 12px;
    align-items: flex-start;
    box-sizing: border-box;
    border-radius: 4px;
    background: #F7F8FA;
    cursor: pointer;
}
.rightFooterScr {
    display: flex;
    flex-direction: row;
    padding: 5px 12px;
    align-items: center;
    box-sizing: border-box;
    border-radius: 4px;
    background: #F7F8FA;
    margin-left: 16px;
    cursor: pointer;
}
.rightFooterApiTis {
    color:#626F84;
    margin-right: 12px;
}
.rightFooterApiSrc {
    margin-left: 8px;
}
.rightFooterApiUrl{
    color: #1B2532;
    overflow: hidden; 
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 240px;
}
.teamMembersDetail{
    border-top:1px solid #D5D7DE ;
    padding: 0 0 0 32px;
    width: 100%;
    height: calc(100vh - 240px);
    color: #1B2532;
    font-size: 15px;
    background: #FFF;
}
.teamMembersDetail a{
    color: #006BFF;
}
.gapWrapper{
    padding: 16px 0;
    border-bottom: 1px solid #fff;
}
.iframeWrapper{
    height: calc(100% - 115px);
    border-radius: 8px;
    margin-top: 20px;
}
.iframeWrapper iframe{
    border-radius: 8px;
}
/* monitor */
.promptTitle{
    font-size: 24px;
    font-weight: 600;
    color: #1B2532;
}
.monitorChart{
    padding: 16px;
    margin: 0px 16px 16px;
    border-radius: 8px;
    border: 1px solid #FFF;
    background: #fff;
    // width: calc(100vw - 252px);
    height: 400px;
    .monitorChartTitle{
        display: flex;
        justify-content: space-between;
        line-height: 32px;
        .monitorChartTitleContent{
            font-weight: 600;
        }
    }
}
.monitorChartLabel{
    color: #626F84;
    font-size: 14px;
    margin: 0 8px 0 16px;
    display: inline-block;
}
.monitorLog{
    color: #626F84;
    font-size: 14px;
    border-radius: 8px;
    border: 1px solid #FFF;
    background: #fff;
    height: 500px;
    overflow-y: auto;
}
.monitorLogTitle{
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}
.monitorLogTable{
    padding: 16px 16px 10px;
    margin: 0px 16px 16px;
    border-radius: 8px;
    border: 1px solid #FFF;
    background: #fff;
    min-height: calc(100vh - 512px);
    .monitorLogTitle{
        font-weight: 600;
        line-height: 32px;
        display: flex;
        justify-content: space-between;
    }
}
.monitorTable{
    margin-top: 16px;
    height: calc(100% - 37px);
}
.container{
    margin-top: 10px;
    height: 330px;
}
.emptyWrapper{
    margin-top: 100px;
}
.barEmptyWrapper{
    position: relative;
    margin-top: -250px;
}
.logWrapper{
    height: calc(100% - 40px);
    overflow-y: auto;
}
.logItem{
    margin-top: 6px;
}
.logTitle{
    margin-bottom: 3px;
    font-size: 14px;
    font-weight: 600;
}
.logBtn, .oDetailBtn{
    color: #006BFF;
    cursor: pointer;
}
.oDetailDisabledBtn{
    color: #ccc;
    cursor: not-allowed;
}
.logBtn{
    margin-right: 10px;
}
.logModal{
    width: 1200px!important;
}
.detailModal{
    width: 1200px!important;
}
.dropMenuList {
    box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
    border-radius: var(---m, 4px);
    background: #FFFFFF;
    padding: 12px;

    .modeLabel {
        color: #1B2532;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
    }

    :global {
        .ant-dropdown-menu-submenu-title {
            position: relative;
        }

        .ant-dropdown-menu-submenu-expand-icon {
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.selectMode {
    color: #1B2532;
    background-color: #EDF1F5;
    font-size: 14px;
    line-height: 22px;
    margin-right: 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 8px;
    padding: 5px 12px;
    cursor: pointer;

    svg {
        margin-left: 8px;
    }
}