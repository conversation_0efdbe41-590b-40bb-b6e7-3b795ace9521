/* table */
.addMember{
    margin-left: 8px;
}
.deleteBtn{
    cursor: pointer;
    padding: 4px 10px;
}
.firstName{
    border-radius: 12px;
    background: #3872F6;
    width: 24px;
    height: 24px;
    line-height: 24px;
    display: inline-block;
    color: #fff;
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    margin-right: 10px;
}
.selectRole svg{
    font-size: 10px!important;
}
.addModal{
    height: 430px;
}
/* add-member */
.searchList{
    margin-top: 24px;
    height: 280px;
    overflow-y: auto;
}
.searchItem{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    width: 100%;
}

.searchItem .email {
    white-space: nowrap; 
    overflow: hidden;
    text-overflow: ellipsis; 
    margin-left: 10px; 
    min-width: 235px; 
    flex-shrink: 0; 
}
.nameWrapper{
    display: flex;
    align-items: center;
    flex-grow: 1;
}



.addMemberInput{
    margin: 0px 0 20px;
    width: 100%;
}
button.addBtn{
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #006BFF;
    border-radius: 4px;
    color: #006BFF;
    height: 24px;
    width: 58px;
    font-size: 12px;
    padding: 5px 10px 5px 6px;
    margin-right: 20px;
}
.addBtn:hover{
    opacity: 0.8;
}
.addBtn span{
    vertical-align: middle;
    display: inline-block;
    /* margin-top: -7px; */
}
