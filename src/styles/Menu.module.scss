// common
.menuBg {
    position: relative;
    width: 220px;
    height: 100vh;
    padding: 14px 12px;
    box-sizing: border-box;
    background: var(----, #FFF);
    border-right: 1px solid var(---g-20, #EBECF0);
}

// 企业智脑,页面高度兼容
.menuBgZhiNao {
    height: calc(100vh - 56px);
}

// logo
.logoWrapper {
    padding: 0px 10px;
    margin-top: 12px;
}

.logo {
    display: inline-block;
    vertical-align: top;
    margin-right: 3px;
}

.agent {
    display: inline-block;
    vertical-align: top;
}

.teamImg {
    margin-right: 8px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 4px;
}

.teamName {
    display: inline-block;
    vertical-align: middle;
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 加粗/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 800;
}

// projectWrapper
.projectWrapper {
    margin: 10px 0px;
    background-color: #fff;

    .projectSelector {
        width: 100%;
        height: 44px;
    }

    .projectInfo {
        // background: var(----60-Hover, #1D7CFF);
        border-radius: 4px;
        text-align: center;
        padding-top: 4px;
        border-radius: 4px;
    }
}

// menuList
.menuList {
    width: 100%;
    margin-top: 4px;

    // background-color: #006BFF;
    .menuItem {
        line-height: 24px;
        padding: 10px 10px;
        cursor: pointer;

        img {
            margin-right: 9px;
        }

        svg {
            margin-right: 9px;
            display: inline-block;
            vertical-align: middle;
        }

        img,
        span {
            display: inline-block;
            vertical-align: middle;
        }

        &:hover,
        &.menuActiveItem {
            color: #1B2532;
            font-weight: 600;
            font-family: PingFang SC;
            border-radius: 8px;
            background: var(---g-10, #F7F8FA);

            path {
                fill: rgb(29, 124, 255)
            }
        }
    }

    .menuGap {
        width: 100%;
        height: 1px;
        border-bottom: 1px solid var(---g-20, #EBECF0);
        margin: 8px 0px;

    }
}

// userWrapper
.userWrapper {
    width: 90%;
    padding: 16px 0px 16px 0;
    line-height: 32px;
    height: 56px;

    img {
        display: inline-block;
        vertical-align: middle;
    }

    .userName {
        margin-left: 8px;
        display: inline-block;
        vertical-align: middle;
    }
}

.menuGapLine {
    width: 100%;
    height: 1px;
    border-bottom: 1px solid var(---g-20, #EBECF0);
    // position: absolute;
    bottom: 64px;
}

.userInfoWrapper {
    width: 200px;
    position: relative;
    padding: 16px;
    box-sizing: border-box;

    .userNameBg {
        height: 32px;
        margin-bottom: 12px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;

        .headSculpture {
            border-radius: 12px;
            margin-right: 12px;
        }

        span {
            color: var(---Main-Text, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 加粗/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
            /* 157.143% */
        }
    }

    .userInfoInner {
        .userInfoList {
            border-bottom: 1px solid #EBF0F5;

            .userInfoItem {
                margin-bottom: 8px;

                .userInfoLabelItem {
                    width: 43px;
                    color: #626F84;
                    display: inline-block;
                }
            }
        }

        .userInfoList1 {
            padding: 12px 0 0 0;
            margin-bottom: 12px;
        }

        .userInfoItem1 {
            img {
                margin-right: 8px;
                display: inline-block;
                vertical-align: middle;
            }

            a,
            span {
                display: inline-block;
                vertical-align: middle;
            }

            span {
                cursor: pointer;
            }
        }
    }
}

.flodIcon {
    width: 24px;
    height: 24px;
    border-radius: 90px;
    border: 1px solid var(---g-30, #D5D7DE);
    background: var(----, #FFF);
    position: absolute;
    right: -12px;
    line-height: 24px;
    top: 48px;
    // display: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;

}

.show {
    opacity: 1;
}

.templateLabel {
    color: #9ba7ba;
    margin: 15px 10px 10px;
}

// isFlod 
.isFlod {
    width: 80px;
    text-align: center;

    .logoWrapper {
        box-sizing: border-box;
        padding: 0 0px;

        .logo {
            margin: 0;
        }
    }

    .projectWrapper {
        padding: 0 0px;
    }

    .menuList {
        .menuItem {
            padding: 0px;
            margin-top: 22px;
            margin-bottom: 20px;
            box-sizing: border-box;

            img,
            svg {
                margin-right: 0;
            }
        }
    }

    .docWrapper {
        .docWrapperItem {
            padding: 0px 0px 0px 3px;
            box-sizing: border-box;

            img {
                margin-right: 0;
            }
        }
    }

    .userWrapper {
        padding: 16px 0 0 0;
    }
}

.menuAgentIcon {
    background: linear-gradient(270deg, #3872F6 0%, #707CF7 100%);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    flex-shrink: 0;
}

.docWrapper {
    // width: 100%;
    // display: flex;
    // flex-direction: column;
    // align-items: flex-start;
    // justify-content: center;
    // box-sizing: border-box;
    // cursor: pointer;

    width: 100%;
    box-sizing: border-box;
    cursor: pointer;


    .docWrapperItem {
        // display: flex;
        // height: 44px;
        // width: 100%;
        // flex-direction: row;
        // align-items: center;
        // justify-content: flex-start;
        // // padding-left: 10px;
        // box-sizing: border-box;
        // line-height: 24px;
        // padding: 10px 10px 10px 12px;
        // cursor: pointer;

        display: flex;
        height: 44px;
        // width: 33%;
        align-items: center;
        width: 100%;
        box-sizing: border-box;
        line-height: 24px;
        gap: 8px;
        padding: 10px 10px 18px 10px;
        cursor: pointer;
        color: #1B2532;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        // text-align: center;

        // &:hover{
        //     color: #1B2532;
        //     font-weight: 600;
        //     font-family: PingFang SC;
        //     border-radius: 4px;
        //     background: #F7F8FA;
        // }
        .docWrapperItemIcon {
            // margin-right: 10px;
        }

        .docText {
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
        }
    }
}

.docWrapper1 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    box-sizing: border-box;
    cursor: pointer;

    .docWrapperFoldItem {
        height: 44px;
        width: 100%;
        box-sizing: border-box;
        line-height: 24px;
        padding: 10px 10px 10px 12px;
        cursor: pointer;
    }
}

.dropdownContent {
    width: 194px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    cursor: pointer;
    overflow-y: auto;

    &:hover {
        color: #1B2532;
        font-weight: 600;
        font-family: PingFang SC;
        border-radius: 4px;
        background: var(---g-10, #F7F8FA);
        // width: 194px;
    }

    .dropdownContentLeft {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        img {
            margin-right: 8px;
        }

        .dropdownContentLeftTitle {
            color: var(---, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 加粗/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
            /* 157.143% */
            width: 80%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.dropDownIcon {
    position: relative;
    right: 8px;
}

.selectProItem {
    width: 190px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0px 10px 0px;
    box-sizing: border-box;
    cursor: pointer;

    .selectProItemTrue {
        position: relative;
        right: 8px;
    }

    img {
        margin-right: 8px;
    }

    .selectProItemContent {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        width: 100%;


        .selectProItemTitle {
            color: var(---, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            /* 常规/14px */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            width: 80%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

}

.dropdownWrapper {
    ul {
        max-height: inherit !important;
    }
}