  .deploy-detail-right-top-select .ant-select-selector{
    color: #006BFF !important;
    font-size: 16px !important;
  }
  
  .deploy-detail-right-content-editor {
    width: 100%;
    height: 98%;
    overflow: hidden;
    background: #fff;
  }
  .deploy-detail-right-content-editor .monaco-editor .overflow-guard .monaco-scrollable-element {
    left: -1px !important;
  }
  
  .deploy-detail-right-content-editor .monaco-editor .overflow-guard .monaco-scrollable-element .decorationsOverviewRuler {
    width: 0 !important;
  }
  
  .deploy-detail-left-content-editor {
    /* height: 270px; */
    overflow: hidden;
    background: #fff;
    margin-top: 10px;
    border-radius: 8px;
  }
  .deploy-detail-left-content-editor .monaco-editor .overflow-guard .monaco-scrollable-element .decorationsOverviewRuler {
    width: 0 !important;
  }