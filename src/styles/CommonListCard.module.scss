.container {

    padding: 16px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 8px;
    background: var(----, #FFF);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    height: 165px;
    position: relative;

    .topIcon {
        position: absolute;
        display: flex;
        padding: 4px 8px;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0px;
        top: 0;
        border-radius: 12px 0px;
        background: #006BFF;
        color: #fff;
        font-size: 10px;
    }

    .commonHeader {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;

        .commonHeaderTop {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 8px;

            .commonHeaderImg {
                width: 32px;
                height: 32px;
                // padding-top: 3px;
                border-radius: 4px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                margin-right: 12px;

                img {
                    width: 32px;
                    height: 32px;
                    border-radius: 4px;
                }
            }

            .commonHeaderTitle {
                width: 76%;
                color: var(---, #1B2532);
                font-feature-settings: 'clig' off, 'liga' off;
                /* 加粗/16px */
                font-family: PingFang SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

            }
        }

        .commonHeaderDesc {
            width: 100%;
            min-height: 35px;
            color: var(---, #626F84);
            font-feature-settings: 'clig' off, 'liga' off;

            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;

            /* 常规/14px */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
        }
    }

    .gapLine {
        height: 1px;
        width: 100%;
        margin: 16px 0;

        &::after {
            content: '';
            display: block;
            height: 1px;
            width: 100%;
            background: var(---g-20, #EBECF0);
        }
    }

    .footer {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .footerUser {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;

            img {
                margin-right: 8px;
            }

            span {
                color: var(---, #9BA7BA);
                font-feature-settings: 'clig' off, 'liga' off;

                /* 常规/12px */
                font-family: PingFang SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    .itemMoreImg {
        width: 20px;
        height: 20px;
        display: inline-block;
        position: absolute;
        right: 16px;
        top: 15px;
        border-radius: 2.857px;
        // border: 0.714px solid #FFF;
        z-index: 1;
    }
    
    .itemMoreImgFlow {
        right: 16px;
        top: 20px;
    }
    
    .dropMenuBox {
        box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
        border-radius: 8px;
        background: #FFF;
    }
}


.containerForYunPan {
    width: 100%;
    padding: 24px 24px 12px 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    border-radius: 8px;
    background: #F7F9FA;
    border: 1px solid #F7F9FA;
    cursor: pointer;
    height: 144px;
    position: relative;
    gap: 16px;

    &:hover {
        border: 1px solid  #006BFF;
        background:  #E8F5FF;
        box-shadow: 0px 2px 6px 0px rgba(170, 196, 221, 0.50);
    }

    .topIcon {
        position: absolute;
        display: flex;
        padding: 4px 8px;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0px;
        top: 0;
        border-radius: 8px 0px;
        background: #006BFF;
        color: #fff;
        font-size: 10px;
    }

    .commonHeader {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;

        .commonHeaderTop {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            // margin-bottom: 8px;

            .commonHeaderImg {
                width: 56px;
                height: 56px;
                border-radius: 12px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                margin-right: 12px;

                img {
                    width: 56px;
                    height: 56px;
                    border-radius: 12px;
                }
            }

            .commonHeaderTitle {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;

            }

            .commonHeaderTitleSpan {
                max-width: 80%;
                color: #1D2531;
                font-feature-settings: 'clig' off, 'liga' off;
                /* 加粗/16px */
                font-family: PingFang SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-right: 8px;
            }

            .commonHeaderDesc {
                width: 100%;
                min-height: 35px;
                color: #657083;
                font-feature-settings: 'clig' off, 'liga' off;
    
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
    
                /* 常规/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    .footer {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .footerUser {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;

            img {
                margin-right: 8px;
            }

            span {
                color: var(---, #9BA7BA);
                font-feature-settings: 'clig' off, 'liga' off;

                /* 常规/12px */
                font-family: PingFang SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    .itemMoreImg {
        width: 20px;
        height: 20px;
        display: inline-block;
        position: absolute;
        right: 24px;
        bottom: 15px;
        border-radius: 2.857px;
        z-index: 1;
    }

    .itemMoreImg:hover {
        background: #eee;
        border-radius: 2.857px;
    }

    .yunPanCardStatus {
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 0px 8px;
        background:  #E1E7ED;
        text-align: center;
        width: 52px;
        height: 22px;
        line-height: 22px;
        padding: 1px 8px;

        color:  #657083;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
    }
    
    .dropMenuBox {
        box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
        border-radius: 8px;
        background: #FFF;
    }
}
