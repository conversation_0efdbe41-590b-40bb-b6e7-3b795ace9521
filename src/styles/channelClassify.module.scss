.listEmpty {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

    :global {
        .ant-empty .ant-empty-description {
            color: #626F84;
        }
    }
}

.commonSearch {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    font-family: "PingFang SC";
    font-size: 14px;

    .selectTitle {
        color: #9BA7BA;
    }

    .selectSubtitle {
        color: #1B2532;
    }

    .cutUp {
        width: 1px;
        height: 24px;
        background: #E1E7ED;
    }

    .commonSearchInput {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 4px;
    }

    .commonSearchInput input {
        background: rgba(255, 255, 255, 0.80);

        &:focus {
            outline: 0;
            box-shadow: none;
        }
    }
}

.modalContent {
    display: flex;
    padding: 0px 8px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;

    .modalSwitch{
        display: flex;
        padding: 16px;
        justify-content: space-between;
        align-items: center;
        border-radius: 8px;
        background: #FFF;
        height: 80px;
        width: 100%;
        margin-bottom: 24px;
        .switchItem{
            width: 297px;
            height: 48px;
        }

        .itemTop{
            color:  #1B2532;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px; /* 150% */
            margin-bottom: 4px;
        }
        .itemBottom{
            color:  #626F84;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
        }
    }

    .modalSearch {
        padding-bottom: 16px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1 0 0;

        .modalNumTitle {
            color: var(---, #1B2532);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 600;
            line-height: 22px;
        }

        .modalNum {
            color: var(---, #9EA7B8);
            font-family: "PingFang SC";
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }
    }

    .modalList {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        align-self: stretch;
        max-height: 400px;
        overflow-y: scroll;
        overflow-x: hidden;
        height: 400px;

        .modalItem {
            width: 100%;
            border-radius: 4px;
            background: #FFF;

            .agentItem {
                display: flex;
                padding: 16px;
                justify-content: space-between;
                align-items: flex-end;
                gap: 16px;
                align-self: stretch;

                .itemInfo {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    flex: 1 0 0;
                }

                .itemName {
                    color: #1B2532;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: 600;
                }

                .itemDetail {
                    display: flex;
                    align-items: center;
                    align-self: stretch;
                    gap: 8px;
                    font-size: 12px;
                    line-height: 20px;
                    font-family: "PingFang SC";
                }

                .breakUp {
                    width: 1px;
                    height: 13px;
                    background: #E1E7ED;
                }

                .itemStatus {
                    color: #4EC01E;
                    display: flex;
                    height: 22px;
                    // padding: 5px 8px;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    border-radius: 4px;
                    background: #F1FEE1;
                }

                .itemUnpublished {
                    color: #626F84;
                    display: flex;
                    height: 22px;
                    padding: 5px 8px;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    border-radius: 4px;
                    background: #EBF0F5;
                }

                .modalButton {
                    display: flex;
                    align-items: flex-start;
                    gap: 16px;

                    :global {
                        .ant-btn {
                            padding: 4px 16px;
                            border-radius: 4px;
                        }
                    }
                }

            }
        }
    }

    .modalEmpty {
        height: 400px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.commonList {
    :global {
        .ant-modal-content {
            background-color: #F7F8FA !important;
        }
    }
}

.tableExpand {
    :global {
        .ant-table-expanded-row {
            display: none;
        }

        .ant-table-row-level-1 {
            background: #F7F9FA;
        }
    }
}

.urlLoading {
    width: 93px;
    height: 93px;
    position: absolute;
    top: 16px;
    left: 40%;
    background: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.urlMark {
    color: #FFF;
    font-size: 12px;
    font-family: "PingFang SC";
    opacity: 0;
}

.urlMark:hover {
    opacity: 1;
}