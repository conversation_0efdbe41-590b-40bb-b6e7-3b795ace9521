.container {
    padding: 16px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 8px;
    background: var(----, #FFF);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    position: relative;

    .topIcon {
        position: absolute;
        display: flex;
        padding: 4px 8px;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0px;
        top: 0;
        border-radius: 12px 0px !important;
        background: #006BFF;
        color: #fff;
        font-size: 10px;
    }

    .header {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .headerImg {
            width: 68px;
            height: 68px;
            border-radius: 8px;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            // background: linear-gradient(180deg, #919FC3 0%, #525E7F 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            img {
                //    width: 68px;
                //    height: 68px;
                border-radius: 8px;
            }

        }

        .headerRight {
            height: 68px;
            width: calc(100% - 80px);
            // flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;

            .headerTitle {
                width: 80%;
                color: var(---, #1B2532);
                font-feature-settings: 'clig' off, 'liga' off;
                /* 加粗/16px */
                font-family: PingFang SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                white-space: nowrap;
            }

            .headerDesc {
                width: 100%;
                min-height: 35px;
                color: var(---, #626F84);
                font-feature-settings: 'clig' off, 'liga' off;

                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;

                /* 常规/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    .gapLine {
        height: 1px;
        width: 100%;
        margin: 16px 0;

        &::after {
            content: '';
            display: block;
            height: 1px;
            width: 100%;
            background: var(---g-20, #EBECF0);
        }
    }

    .footer {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .footerUser {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;

            img {
                margin-right: 8px;
            }

            span {
                color: var(---, #9BA7BA);
                font-feature-settings: 'clig' off, 'liga' off;

                /* 常规/12px */
                font-family: PingFang SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }
}

.itemMoreImg {
    width: 20px;
    height: 20px;
    position: absolute;
    display: inline-block;
    right: 16px;
    top: 15px;
    border-radius: 2.857px;
    // border: 0.714px solid #FFF;
}