.rootContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    background: #F7F8FA;
    width: 100%;
    overflow-y: hidden;
    min-width: 1028px;
}

.headerContainer {
    display: flex;
    padding: 24px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-bottom: 1px solid #EBECF0;

    .headerLeft {
        display: flex;
        align-items: center;
        gap: 28px;
        flex: 1;
        overflow: hidden;

        .backBtn {
            display: flex;
            width: 20px;
            height: 20px;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .agentInfo {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            gap: 12px;
            flex: 1;
            overflow: hidden;

            .agentRight {
                margin-right: 12px;
                display: flex;
                flex-direction: column;
                flex: 1;
                overflow: hidden;
            }

            .agentDigitalIcon{
                width: 40px;
                height: 40px;
                border-radius: 8px;
                display: flex;
                justify-content: center;

                img {
                    border-radius: 8px;
                }
            }

            .agentIcon {
                width: 40px;
                height: 40px;
                border-radius: 8px;
            }

            .infos {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;

                .title {
                    color: #1B2532;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 28px;
                }

                .discribe {
                    color: #626F84;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                }
            }

        }
    }

    .headerRight {
        display: flex;
        align-items: center;
        gap: 8px;

        .historyIcon {
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            background: url('https://p3.ssl.qhimg.com/t11098f6bcd32c67dd3d39c2925.png');
            background-size: contain;
            cursor: pointer;
            text-indent: -1000px;
            overflow: hidden;
            background-color: #FFFFFF;
            border-radius: 8px;

            &:hover {
                background: url('https://p3.ssl.qhimg.com/t11098f6bcd31ee38075c7527a8.png');
                background-size: contain;
            }
        }

        .beadBtn {
            display: flex;
            width: 88px;
            height: 32px;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
            border-radius: 8px;
        }

        .profileTitleSelectBtn {
            display: flex;
            height: 24px;
            padding: 5px 8px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            margin-right: 48px;
            position: relative;

            &::after {
                display: block;
                content: '';
                width: 1px;
                background: #D5D7DE;
                position: absolute;
                top: 0;
                bottom: 0;
                right: -26px;
            }

            .selectBtnText {
                color: #1B2532;
                font-size: 12px;
                line-height: 20px;
            }

            &:hover {
                border-radius: 4px;
                background: #EBECF0;
            }
        }

    }
}

.contentContainer {
    display: flex;
    width: 100%;
    height: calc(100vh - 96px);
    justify-content: space-between;
    align-items: flex-start;
}

.contentChatContainer {
    width: 100%;
    min-width: 800px;
    padding: 16px 16px 152px;
    height: calc(100vh - 96px);
    background: #fff;
    overflow: auto;

    .chatFooter {
        width: 800px;
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
        padding: 48px 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.profileContainer {
    width: 66%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.profileTitle {
    display: flex;
    height: 54px;
    padding: 16px;
    align-items: center;
    justify-content: space-between;
    border-right: 1px solid #EBECF0;
    border-bottom: 1px solid #EBECF0;
    background: #F7F8FA;

    .profileTitleText {
        color: #1B2532;
        font-size: 14px;
        font-weight: 600;
    }

    .profileTitleSelectBtn {
        display: flex;
        height: 24px;
        padding: 5px 8px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        cursor: pointer;

        .selectBtnText {
            color: #1B2532;
            font-size: 12px;
            line-height: 20px;
        }

        &:hover {
            border-radius: 4px;
            background: #EBECF0;
        }
    }
}

.profileContent {
    display: flex;
    width: 100%;
    height: calc(100% - 54px);
    height: calc(100%);

    .prompConfigtTitle {
        height: 54px;
        line-height: 54px;
        background: #F7F8FA;
        font-size: 16px;
        color: #1B2532;
        font-weight: 600;
        position: sticky;
        top: 0;
        left: 0;
        right: 0;
        padding-left: 16px;
        padding-right:16px ;
        z-index: 1;
        border-bottom: 1px solid #EBECF0;

        .toolTitle{
            display: inline-flex;
            float: right;
            margin-left: 20px;
            cursor: pointer;
            font-weight: 400 !important;
            font-size: 14px !important;
        }
        .disabled{
            pointer-events: none; /* 禁止所有的鼠标事件 */
            opacity: 0.6; /* 改变透明度来显示禁用状态 */
            display: inline-flex;
            float: right;
            margin-left: 20px;
            cursor: pointer;
            font-weight: 400 !important;
            font-size: 14px !important;
        }
    }
}

.profileLeftContent {
    width: 50%;
    // height: calc(100% - 54px);
    border-right: 1px solid #EBECF0;
    background: #FFF;
    position: relative;
}

.promptTitle {
    display: flex;
    height: 54px;
    padding: 16px;
    justify-content: space-between;
    align-items: center;

    .promptTitleText {
        display: flex;
        align-items: center;
        color: #1B2532;
        font-size: 14px;
        font-weight: 600;
    }

    .optimizeBtn {
        display: flex;
        height: 24px;
        padding: 5px 8px;
        justify-content: center;
        align-items: center;
        cursor: not-allowed;
        color: #1B2532;
        font-size: 12px;
        border-radius: 4px;
        border: 1px solid #D5D7DE;
        color: #1677ff;

        &:hover {
            background: #F7F9FA;
        }

        &.active {
            cursor: pointer;
        }
    }
}

.textWrapper {
    width: 100%;
    // height: calc(100% - 54px);
    // overflow-y: auto;
    // display: flex;
    // align-items: flex-start;
    // padding: 0px 0px 46px;

    .textLoading {
        width: 100%;
        color: #1677ff;
        position: absolute;
        left: 0;
        right: 0;
        top: 44%;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .pd {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }

    .textarea {
        width: 100% !important;
        color: #626F84;
        color: #000;
        line-height: 1.7;
        font-size: 14px;
        height: 286px !important;
        height: 340px !important;
        overflow-y: auto;
        overflow-wrap: break-word;
        padding: 0;
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
        // white-space: pre-wrap;
        background-color: rgba(0, 0, 0, 0);
        border: 0 solid rgba(0, 0, 0, 0);
        outline: none;
        cursor: text;
        resize: none;
        margin-bottom: 18px;

        &::-webkit-scrollbar {
            display: none;
            /* 隐藏滚动条 */
        }
    }

    textarea::-webkit-input-placeholder {
        color: #9BA7BA;
    }

    textarea::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #9BA7BA;
    }

    textarea:-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #9BA7BA;
    }

    textarea:-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #9BA7BA;
    }

    .hr {
        display: block;
        width: 100%;
        height: 1px;
        background-color: #D8E0E8;
    }
}

.sampleWrapper {
    width: 100%;
    position: absolute;
    display: flex;
    padding: 0 16px;
    bottom: 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    cursor: pointer;
    background: #FFF;

    :global {

        // collapse
        :where(.css-dev-only-do-not-override-qgg3xn).ant-collapse .ant-collapse-content>.ant-collapse-content-box {
            padding: 0;
        }

        :where(.css-dev-only-do-not-override-qgg3xn).ant-collapse>.ant-collapse-item>.ant-collapse-header {
            padding: 16px 0
        }
    }
}

.collapseContent {
    height: 240px;
    padding: 12px;
    // background: rgb(247, 248, 250);
    gap: 4px;
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
    border-radius: 8px;
    background: #F7F8FA;
    margin-bottom: 12px;


    .contentTitle {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 24px;
        line-height: 24px;
    }

    .collapseBtn {
        height: 24px;
        width: 40px;
        font-size: 12px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}





.profileRightContent {
    width: 50%;
    height: 100%;
    border-right: 1px solid #EBECF0;
    background: #F7F8FA;
    position: relative;
}

.dialogueContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    height: 100%;
    position: relative;
    min-width: 0;
}

.agentOptimizeModal {

    .optimizeTextContent {
        display: flex;
        height: 400px;
        padding: 5px 12px;
        align-items: flex-start;
        border-radius: 4px;
        border: 1px solid #D5D7DE;
        background: #F7F8FA;
        overflow-y: auto;

        .optimizeText {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #1B2532;
            font-size: 14px;
            gap: 8px;
            background: transparent;
            border: none;
            outline: none;
        }

        .loadingWrapper {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #9BA7BA;
            font-size: 14px;
            gap: 8px;

            .rotating {
                animation: rotateLoading 2s linear infinite;
            }
        }

    }

    .footerContent {
        position: relative;

        .retryWrapper {
            display: flex;
            position: absolute;
            left: 0;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

    }



    :global {

        .ant-modal .ant-modal-content {
            padding: 0;
        }

        .ant-modal .ant-modal-header {
            padding: 16px 24px;
            border-radius: 8px 8px 0px 0px;
            border-bottom: 1px solid #EBF0F5;
            background: #F7F8FA;
        }

        .ant-modal .ant-modal-body {
            padding: 24px;
        }

        .ant-modal .ant-modal-footer {
            padding: 12px 24px;
            border-radius: 0px 0px 4px 4px;
            border-top: 1px solid #EBECF0;
            background: #FFF;
        }

        .ant-btn-link {
            padding: 0;
            margin: 0 4px;
        }
    }
}

.createCopyModal {

    .footerContent {
        position: relative;

        .retryWrapper {
            display: flex;
            position: absolute;
            left: 0;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }
    }

    .contentWrapper {
        display: flex;
        flex-direction: column;
    }

    .label {
        display: flex;
        height: 32px;
        align-items: center;
        color: #626F84;
        font-size: 14px;
    }

    .projectWrapper {
        margin: 10px 0px;
        background-color: #fff;

        .projectSelector {
            width: 100%;
            height: 44px;
        }

        .projectInfo {
            border-radius: 4px;
            text-align: center;
            padding-top: 4px;
            border-radius: 4px;
        }
    }



    :global {

        .ant-modal .ant-modal-content {
            padding: 0;
        }

        .ant-modal .ant-modal-header {
            padding: 16px 24px;
            border-radius: 8px 8px 0px 0px;
            border-bottom: 1px solid #EBF0F5;
            background: #F7F8FA;
        }

        .ant-modal .ant-modal-body {
            padding: 24px;
        }

        .ant-modal .ant-modal-footer {
            padding: 12px 24px;
            border-radius: 0px 0px 4px 4px;
            border-top: 1px solid #EBECF0;
            background: #FFF;
        }

        .ant-btn-link {
            padding: 0;
            margin: 0 4px;
        }
    }
}


@keyframes rotateLoading {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.agentModelSettingModal {

    .agentModelSettingContainer {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 28px;
        padding: 20px;
        width: 400px;
        height: 400px;
        overflow-y: scroll;
        margin-top: -15px;

        .header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            width: 100%;

            .title {
                color: #1B2532;
                font-size: 14px;
                font-weight: 600;
            }

            .selectWrapper {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                align-self: stretch;
                width: 100%;

                .title {
                    color: #626F84;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 22px;
                }
            }
        }

        .line {
            width: 100%;
            border: 0;
            border-top: 1px solid #EBECF0;
        }

        .parameterContent {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .title {
                color: #1B2532;
                font-size: 14px;
                font-weight: 600;
            }

            .discribe {
                width: 100%;
                height: 32px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                color: #626F84;
                text-align: right;
                font-size: 14px;
                line-height: 22px;
            }

            .sliderWrapper {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;

                .slider {
                    width: 100%;
                }
            }

            .radioWrapper {
                width: 100%;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                .radioGroup {
                    width: 240px;
                    display: flex;
                    flex-direction: row;
                }
            }

            .inputWrapper {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                width: 100%;
                gap: 5px;

                .input {
                    width: 100%;
                }
            }
        }
    }
}

// 兼容智脑
.chatContainerCompanyBrain {
  height: calc(100vh - 154px);
}

.agentToolSettingModal{
    // position: relative;
    .agentToolSettingContainer{
        display: flex;
        width: 440px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
        // position: absolute;
        // right: 492px;
        // top: 144px;
        box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10), 0px 15px 45px 7px rgba(27, 37, 50, 0.06); 

        .header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            width: 100%;
            padding: 12px 16px;
            color: #1B2532;
            font-size: 14px;
            font-weight: 600;
        }
        .toolContent{
            display: flex;
            padding: 4px 16px 16px 16px;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 16px;
            width: 100%;
            // height: 180px;
        }
        .radioItem{
            display: flex;
            padding: 12px 16px;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
            width:408px;
            border-radius: 4px;
            border: 1px solid #E1E7ED;
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }
        }
        .radioItem:hover{
            background:#F7F8FA; ;
            cursor: pointer;
        }
        .description{
            width:100%;
            color: #657083;
            background-color: transparent;
            line-height: 1.5;
            font-size: 14px;
        }

    }
    
}
.imgBox{
    height: 22px;
    width: 22px;
    padding: 6px;
    cursor: pointer;

}