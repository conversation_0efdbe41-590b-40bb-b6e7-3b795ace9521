.commonList{
    // flex: 1 0 auto;
    // width: calc(100% - 220px);
    padding: 16px;
    box-sizing: border-box;
    min-width: 1028px;

    .titleWrapper{
        display: flex;
        justify-content: space-between;
        padding: 8px 0 16px 0;
        margin-bottom: 8px;

        .titleWrapperLeft {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }
        .title{
            color: #1B2532;
            font-family: PingFang SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 36px;
            margin-right: 24px;
        }

        .titleForYunPan{
            color:  #1D2531;
            font-feature-settings: 'liga' off, 'clig' off;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
            margin-right: 24px;
        }

        :global .ant-select .ant-select-arrow .anticon {
            margin-left: -18px;
        }

    }
    .titleLayoutWrapper{
        display: flex;
        justify-content: space-between;
    }
    .list{
        height: calc(100vh - 76px);
        overflow-y: auto;
        border-radius: 8px;
        border: 1px solid #FFF;
        background: linear-gradient(90deg, #ffffff42 2.85%, rgba(255, 255, 255, 0.17) 98.96%);
        box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
        padding: 16px 16px 0;
    }
    .checkTableList {
        height: calc(100vh - 202px);
    }
    .tableList {
        height: calc(100vh - 102px);
    }
    .tableList, .checkTableList {
        // min-width: 1028px;
        overflow-y: hidden;
        border-radius: 8px;
        border: 1px solid #FFF;
        background: linear-gradient(90deg, #ffffff42 2.85%, rgba(255, 255, 255, 0.17) 98.96%);
        box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
        padding: 0px 0px 0;

        :global .ant-table-thead > tr > th {
            text-align: left !important;
        }

        :global .ant-table-tbody > tr > td {
            text-align: left !important;
        }
    }
}
.item{
    position: relative;
    border-radius: 8px;
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.60);
    padding: 16px;
    height: 120px;
    width: calc(25% - 12px);
    display: inline-block;
    vertical-align: top;
    cursor: pointer;
    margin: 0 16px 16px 0;
    img{
        margin-right: 16px;
        display: inline-block;
        vertical-align: top;
    }
    .itemContent{
        display: inline-block;
        vertical-align: top;
        width: calc(100% - 40px);
        .itemTitle{
            color: #262B33;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            .title{
                max-width: calc(100% - 100px);
                display: inline-block;
                vertical-align: middle;
            }
        }
        .status{
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 4px;
            margin-left: 8px;
            display: inline-block;
            vertical-align: middle;
        }
        .successDeploy{
            color: #4EC01E;
            background: #F1FEE1;
        }
        .publicing{
            background: #FFFAD6;
            color: #FA9600;
        }
        .successPublic{
            background: #E0EEFF;
            color: #1D7CFF;
        }
        .itemDesc{
            color: #8592A6;
            font-size: 12px;
            max-height: 60px;
            line-height: 19px;
        }
    }
    .moreBtn{
        border-radius: 2.857px;
        border: 0.714px solid #FFF;
        background: #F3F7FF;
        width: 20px;
        height: 20px;
        padding: 2px;
        position: absolute;
        top: 16px;
        right: 16px;
        display: none;
    }
    &:hover{
        border: 1px solid #3872F6;
        box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.10);
        background: url(../images/commonList/hoverBg.svg) no-repeat;
        background-position: 0 100%;
        .moreBtn{
            display: inline-block;
        }
    }
}
.lastItem{
    margin-right: 0;
}
.addItem{
    color: #626F84;
    text-align: center;
    img{
        margin: 14px 0 12px;
    }
    span{
        display: inline-block;
    }
}
.imgItem{
    height: 272px;
    width: calc(20% - 12.8px);
    .promptImgWrapper{
        height: 180px;
        margin-bottom: 12px;
        text-align: center;
        .promptImg{
            width: auto;
            max-width: 100%;
            max-height: 100%;
            display: inline-block;
            border-radius: 4px;
            margin: 0;
        }
    }
    .itemContent{
        .imgItemTitle{
            color: #1B2532;
            font-size: 14px;
            .title{
                max-width: 100%;
            }
        }
        .status{
            margin: 2px 0 0 0;
            width: 52px;
            display: block;
        }
    }
    &.addItem{
        img{
            margin: 82px 0 12px;
        }
    }
}

// addItemlist
.addItemlist{
    .item{
        border-radius: 4px;
        background: #F7F9FA;
        width: calc(33% - 8px);
        &:hover{
            border: 1px solid #006BFF;
            background: #F2F7FF;
        }
    }
    .imgItem{
        width: calc(25% - 12px);
        height: 262px;
        .promptImgWrapper{
            height: 170px;
        }
    }
    .addItem{
        margin: 0;
    }
    .activeItem{
        border: 1px solid #006BFF;
        background: #F2F7FF;
    }
}
.addTitle{
    margin: 12px 0;
}
.toolWrapper{
    .toolItem{
        height: 34px;
        padding: 6px 16px;
        color: #626F84;
        cursor: pointer;
        img{
            margin-right: 8px;
            display: inline-block;
            vertical-align: middle;
        }
        &:first-child:hover{
            border-radius: 8px 8px 0 0;
        }
        &:last-child:hover{
            border-radius: 0px 0px 8px 8px;
        }
        &:hover{
            background: #f2f7ff;
        }
    }
}