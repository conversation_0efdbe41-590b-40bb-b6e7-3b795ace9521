.bg {
    background: url(../images/common/bg.png);
    background-size: cover;
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
}

.rightContent {
    flex: 1 0 auto;
    height: calc(100vh);
    overflow: auto;
    min-width: 1028px;
}

.apiRightContent {
    flex: 1 0 auto;
    min-width: 1028px;
}

.commonContent {
    width: 100%;
    height: calc(100vh);
    min-width: 1028px;
}

.fullRightContent {
    width: 100%;
}

/*   头部   */
.top {
    display: flex;
    justify-content: space-between;
    padding: 24px 16px;
    height: 72px;
    // border-bottom: 1px solid #EBECF0;
}

.leftBtn {
    width: 19px;
    height: 19px;
    margin-right: 28px;
    cursor: pointer;
    width: 19px;
}

.imageWrapper {
    margin-right: 12px;
    margin-top: 4px;
    border-radius: 8px;

}

.title {
    display: flex;
    flex-direction: row;
    align-items: center;
}

input.nameInput {
    color: #1B2532 !important;
    // display: block;
    font-weight: 600;
    line-height: 28px;
    font-size: 18px;
    width: auto;
    margin-right: 5px;

    &[disabled] {
        background-color: transparent;

        &:hover {
            background-color: transparent;
        }
    }
}

.nameWrapper {
    color: #1B2532 !important;
    font-weight: 600;
    line-height: 28px;
    font-size: 18px;
    width: auto;
    margin-right: 5px;
}

.editBtn {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}

.editDisBtn {
    display: inline-block;
    vertical-align: middle;
    cursor: not-allowed;
}
.saveTime {
    font-size: 12px;
    color: #9EA7B8;
    margin-left: 22px;
}

input.descInput {
    display: block;
    color: #626F84 !important;
    font-weight: 400;
    line-height: 20px;
    font-size: 12px;
    width: 420px;

    &[disabled] {
        background-color: transparent;

        &:hover {
            background-color: transparent;
        }
    }
}

.descWrapper {
    display: block;
    color: #626F84 !important;
    font-weight: 400;
    line-height: 20px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    max-width: 800px;
}

.buttonWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
        margin-left: 8px;
        margin-top: 0px;
    }
}

.addBtn {
    margin-top: 16px;
    display: block;
    cursor: pointer;
    border: 1px solid #D5D7DE;
    border-radius: 4px;
    background: #FFF;
    display: inline-block;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 14px;

    &:hover {
        color: #4096ff;
        border-color: #4096ff;
    }

    span {
        margin-right: 4px;
        width: 10px;
    }
}

/* 左侧 */
.container {
    display: flex;
    margin-left: 16px;
    position: relative;
}

.showLeftContainer {
    display: block;
    position: relative;
}

.left {
    flex: 1 0 auto;
}

.showLeftSet {
    margin-right: 46px;
}

/* 右侧 */
.right {
    padding: 24px;
    background: #fff;
    border: 1px solid #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    margin-left: 16px;
    width: 320px;
    margin-right: 37px;
    position: relative;
}

.aiModuleItem {
    line-height: 32px;
    margin-bottom: 16px;
}

.aiModuleItem1 {
    margin-bottom: 39px;
}

.aiModuleItem:first-child {
    margin-bottom: 16px;
}

.aiModuleSlider {
    width: calc(100% - 100px);
}

.aiModuleLabel {
    font-size: 14px;
    color: #626F84;
    /* margin-bottom: 8px; */
}

.settingBtn {
    position: absolute;
    right: -32px;
    top: 28px;
    width: 32px;
    height: 78px;
    color: #626F84;
    border-radius: 0px 8px 8px 0px;
    padding: 8px 8px 10px;
    line-height: 22px;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    background: #fff;
    background-size: 38px 160px;
    cursor: pointer;
}

.settingLeftBtn {
    position: absolute;
    right: 0px;
    top: 28px;
    width: 32px;
    height: 78px;
    padding: 8px 8px 10px;
    background: #fff;
    border-width: 1px 0px 1px 1px;
    border-style: solid;
    border-color: #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 8px 0px 0px 8px;
    color: #626F84;
    font-size: 14px;
    cursor: pointer;
    line-height: 22px;
}

.history {
    background: #fff;
    border: 1px solid #FFFFFF;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    padding: 12px;
    margin-left: 16px;
    width: 300px;
    font-size: 12px;
    color: #626F84;
    height: calc(100vh - 85px);
    overflow: auto;
    margin-right: 48px;
}

.historyItem {
    background: #F7F8FA;
    border: 1px solid #FFFFFF;
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
}

.historyItemActive {
    border: 1px solid #006BFF;
}

.historyTime {
    border-bottom: 1px solid #EBECF0;
    border-radius: 8px 8px 0px 0px;
    height: 40px;
    line-height: 40px;
    padding: 0 16px;
    position: relative;
}

.historyTime img {
    position: absolute;
    right: 16px;
    top: 13px;
    cursor: pointer;
}

.historyPrompt {
    border-bottom: 1px solid #F2F7FF;
    border-radius: 0px;
    padding: 12px 16px;
}

.historyPromptContent {
    color: #1B2532;
    display: inline-block;
    vertical-align: top;
    width: 182px;
    max-height: 90px;
    overflow: auto;
}

.historyResult {
    padding: 4px 16px 16px;
    border-radius: 0 0 8px 8px;
}

.historyLabel {
    display: inline-block;
    vertical-align: top;
    margin-right: 8px;
    color: #626F84;
}

.historyResultImg {
    display: inline-block;
    vertical-align: middle;
    position: relative;
}

.historyResultImg img {
    margin-right: 8px;
    border-radius: 4px;
}

.moreMask {
    display: inline-block;
    color: #F2F7FF;
    font-size: 14px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));
    border-radius: 4px;
    width: 56px;
    height: 56px;
    text-align: center;
    line-height: 56px;
    position: absolute;
    right: 8px;
}

.llmWrapper {
    padding: 0 16px 16px;
}

.llmItem {
    display: inline-block;
    vertical-align: middle;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #D5D7DE;
    border-radius: 4px;
    padding: 2px 10px;
    margin: 0px 4px 4px 0;
    color: #1B2532
}

.llmItemLabel {
    color: #626F84;
    margin-right: 8px;
}


@media screen and (max-width: 1300px) {}