/* 左侧 */
.promptWrapper{
    display: flex;
    height: calc((100vh - 128px)*0.53);
    overflow-y: hidden;
    background: #fff;
    border: 1px solid #FFFFFF;
    border-radius: 4px;
    padding: 16px 16px 16px 5px;
    position: relative;
}
.promptText{
    width: calc(100% - 240px);
    color: #1B2532;
    margin-right: 20px
}
.promptTextArea{
    height: 90%!important;
}
.promptTextAll{
    width: calc(100% - 0px);
    margin-right: 0px
}
.slotsInput{
    width: 240px;
    position: relative;
}
.slotsInputHide{
    display: none;
}
.slotsInputList{
    max-height: 84%;
    overflow-y: auto;
    border-radius: 4px;
    border: 1px solid #D5D7DE;
}
.slotsInputItem{
    border: 1px solid #fff;
    border-bottom: 0;
    text-align: left;
}
.slotsInputItem:last-child{
    border: 1px solid #fff;
    border-radius: 0 0 4px 4px;
}
.slotsInputItem:first-child{
    border-radius: 4px 4px 0px 0px;
}
.slotsInputItem:first-child:last-child{
    border-radius: 4px;
}
.testBtn{
    position: absolute!important;
    bottom: 24px;
    right: 24px;
}
.demoBtn{
    position: absolute;
    bottom: 24px;
    left: 24px;
    color: #1D7CFF;
    cursor: pointer;
}
.inputName{
    border-bottom: 1px solid #fff;
    padding: 0 16px;
    height: 45px;
    line-height: 45px;
    color: #626F84;
    background: #F7F8FA;
    font-size: 14px;
}
.inputWrapper{
    padding: 10px 16px;
}
.inputWrapper textarea{
    padding: 0;
    outline: 0;
    border: 0;
}
.inputWrapper textarea:focus{
    box-shadow: none;
}
/* 右侧 */
.testDesc{
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding: 5px 0;
}
.testDesc span{
    margin: 10px;
}
.result{
    padding: 16px;
    background: #fff;
    border: 1px solid #FFFFFF;
    border-radius: 4px;
    margin-top: 16px;
    height: calc((100vh - 128px)*0.47);
    overflow-y: auto;
}
.resultButton{
    display: flex;
    justify-content: space-between;
    line-height: 24px;
    color: #1B2532;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 16px;
}
.resultTextArea{
    border: 0;
    background: transparent;
    color: #1B2532;
    font-weight: 400;
    font-size: 14px;
    padding: 0;
}
.resultTextArea:focus{
    box-shadow: none;
}
.slotsDemo{
    border-top: 1px solid #ccc;
    padding: 10px 20px;
}
.foldWrapper{
    display: flex;
    justify-content: right;
    color: #3E74F8;
    font-size: 16px;
    cursor: pointer;
}
.foldIcon{
    margin-left: 12px;
}
.foldIconBox{
    margin-top: 10px;
}

@media screen and (max-width: 1300px) {
    .foldWrapper{
        font-size: 14px;
    }
}