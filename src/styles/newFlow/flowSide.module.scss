
.btnWrapper{
    padding: 16px 24px;
    text-align: center;
    button{
        background: rgba(0, 107, 255, 0.04)!important;
    }
}
.flowSideList {
    padding:  0px 0px 0px;
    border-radius: 16px;
    width: 448px;
    box-sizing: border-box;
    .flowSideItem {
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 16px 24px;
        cursor: pointer;
        border-bottom: 1px solid #EDF1F5;
        &:first-child{
            border-radius: 8px 8px 0 0;
        }
        &:hover {
            background: rgba(237,241,245,.5);
            // border-radius: 8px;
            // border-bottom: 1px solid transparent;
        }
        img{
            margin-right: 16px;
        }
        .title{
            color: #1D2531;
            font-size: 14px;
            font-weight: 600;
        }
        .desc{
            color: #657083;
            font-size: 12px;
            font-weight: 400;
        }
    }
}
.flowSideListMask{
    position: absolute;
    top: 0px;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ddd;
    opacity: 0.4;
    border-radius: 8px;
    cursor: not-allowed;
}

