
.groupBox {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    // align-items: center;
    // justify-content: center;
    // gap: 12px;
    flex-shrink: 0;
    // padding: 12px;
    // min-width: 500px;
    // min-height: 240px;
    background: transparent;
    border-radius: 12px;
    // 周围加透明边框，会导致连接点偏左
    // border: 2px solid transparent;
    // box-shadow: rgba(75, 85, 105, 0.14) 0px 10px 30px -3px;
}
.groupHeader {
    width: 100%;
    height: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
    img {
        width: 24px;
        height: 24px;
        vertical-align: middle;
    }
}
.groupTitle {
    color: #1D2531;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
}
// 结构：groupBox > groupWrapper > groupOuterWrapper
.groupWrapper{
    width: 100%;
    height: 100%;
    border-radius: 18px;
    // 加padding，会导致连接点偏左
    // padding: 4.5px;
}
.groupOuterWrapper{
    background-color: #fff;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    border: 1.5px solid #9C89B8;
    // box-shadow: 0px 13.5px 60px -4.05px rgba(75, 85, 105, 0.08);
}
.groupContent {
    width: 100%;
    height: 100%;
    flex: 1;
    border-radius: 16px;
    background: #fff;
    padding: 16px;
    // border: 16px solid #F5F3F7;
}
.groupAdd {
    display: none;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -15px -57px;
    border: 0!important;
    // background-color: rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
        .groupAddContent {
            display: flex;
        }
        .groupAddPlus {
            cursor: pointer;
        }
    }
    .groupAddContent {
        display: none;
        // display: flex;
        align-items: center;
        width: 94%;
    }
    .groupAddPlus{
        width: 5%;
    }
    .groupAddLine {
        width: 49%;
        display: flex;
        align-items: center;
        img {
            width: 100%;
            height: auto;
            object-fit: contain;
        }
        .groupAddLineRight {
            transform: rotate(180deg);
        }
    }
}
.addAgentBtn{
    padding: 4px 12px;
    border: 1px solid;
    border-radius: 8px;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    cursor: pointer;
    background: rgba(255,255,255, 0.6);
    svg{
        margin-right: 2px;
        display: inline-block;
        vertical-align: middle;
        margin-top: -1px;
    }
    span{
        display: inline-block;
        vertical-align: middle;

    }
}
