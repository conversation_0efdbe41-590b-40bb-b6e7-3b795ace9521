.listContainer {
  display: flex;
  width: 200px;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  border-radius: 12px;
  background: #fff;

  /* 投影/下/弹出投影（4级） */
  box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.1),
    0px 15px 45px 7px rgba(27, 37, 50, 0.06);
    cursor: pointer;
  height: 564px;
  overflow-y: scroll;
}
.item {
  display: flex;
  height: 44px;
  padding: 12px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 8px;
}
.item:hover {
  // border-radius: 8px;
  background-color:  #EDF1F5;
}
.itemAvatar {
  margin-right: 8px;
}
.disabled {
  cursor: not-allowed;
}

