.flow { 
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    .flowHeader {
        background-color: #fff;
        border-bottom: 1px solid var(--light-usage-border-color-border, #1d1c2314);
        justify-content: space-between;
        height: 96px;
        padding: 16px;
    }
    .flowContent {
        position: relative;
    }
}
.flowSider {
    background: #F7F8FA !important;
    border-right: 1px solid var(--light-usage-border-color-border, rgba(29, 28, 35, .08));
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 16px 0;
    position: relative;
    transition: width .1s linear, padding .1s linear;
    width: 330px;
}
.flowSiderIcon {
    position: absolute;
    top: 15px;
    left: 0;
    height: 36px;
    width: 36px;
    background-color: #fff;
    z-index: 999;
    border-radius: 0px 8px 8px 0px;
    box-shadow: 0px 15px 45px 7px rgba(75, 85, 105, 0.10);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
