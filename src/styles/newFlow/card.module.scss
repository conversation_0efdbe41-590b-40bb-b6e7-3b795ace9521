.cardWrapper{
  border-radius: 14px;
  // border: 4px solid transparent;
  // 设置了会影响连接点偏左
  // padding: 4px;
}
.cardInnerWrapper{
  border-radius: 16px;
  background: #fff;
}
.cardContainer {
  position: relative;
  border-radius: 12px;
  border: 1.5px solid #4DD0E1;
  background: #EFFAFC;
  box-shadow: 0px 13.5px 60px -4.05px rgba(75, 85, 105, 0.08);

  box-sizing: content-box;
  // overflow: hidden;
  display: flex;
  flex-direction: column;
  // width: 240px;
  box-sizing: border-box;
  max-width: 360px;
  min-width: 240px;
  padding: 1px;
}

.cardContainer:hover .handle {
  width: 16px;
  height: 16px;
  &:hover {
    width: 20px;
    height: 20px;
  }
}

.handle {
  background-color: #006BFF;
  width: 8px;
  height: 8px;
  transition: width 0.25s linear, height 0.25s linear;
}
// 由于flowgram ai插件计算节点居中的时候，包含头部；这里设置绝对定位，让元素离开文档流
.groupHeader{
  // width: calc(100% - 180px);
  width: 100%;
  position: absolute;
  left: 0;
  top: -5px;
  transform: translateY(-100%);
}
.groupHeader .headerContainer{
  padding: 0;
}
.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  min-height: 76px;
  height: 100%;
  .titleContainer {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    .iconWrapper{
        margin-right: 12px;
        width: 40px;
        background: #4DD0E1;
        text-align: center;
        height: 40px;
        line-height: 40px;
        border-radius: 12px;
        padding: 7px 8px 9px;
        .icon {
          width: 24px;
          height: 24px;
        }
    }

    // 专家智能体真实图标样式
    .iconWrapperWithUrl{
        margin-right: 12px;
        width: 40px;
        height: 40px;
        border-radius: 100%;
        overflow: hidden;
        padding: 0; // 确保没有padding
        box-sizing: border-box; // 确保尺寸计算正确
        flex-shrink: 0; // 防止被压缩！！
        .icon {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
    }
    .subTitle {
      color: #9ea7b8;
      font-weight: 400;
    }
    .renameInput {
      border-radius: 4px;
      border: 1px solid #006bff;
      background: #fff;
      box-shadow: 0px 0px 0px 2px rgba(0, 107, 255, 0.2);
      color:  #1B2532;
      font-size: 14px;
      font-weight: 600;
      outline: none; /* 移除默认轮廓 */
    }
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #1D2531;
      max-width: 155px;
      display: inline-block;
      span.name{
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        vertical-align: middle;
      }
      span.agent{
        width: 40px;
        text-align: center;
        border-radius: 8px;
        border: 1px solid rgba(77, 208, 225, 0.40);
        background: #FFF;
        overflow: hidden;
        color: #05A5B9;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: 15px;
        display: inline-block;
        vertical-align: middle;
        // height: 17px;
        position: relative;
        top: 0px;
        margin-left: 4px;
      }
    }
    .desc{
      max-width: 155px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
      font-size: 14px;
      font-weight: 400;
      color: #657083;
    }
  }
  .nodeInfoContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    .nodeInfoId {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      text-align: left;
      color: #9ea7b8;
    }
    .itemMoreImg {
      width: 20px;
      height: 20px;
      display: inline-block;
    }
  }
}

.paramContainer {
  border-radius: 8px;
  padding: 0 12px 12px 12px;
  display: grid;
  row-gap: 16px;
  grid-template-columns: auto minmax(0, 1fr);
  width: 100%;
  align-items: baseline;
  .title {
    font-size: 14px;
    font-weight: 400;
    color: #1d2531;
    min-width: 28px;
    text-align: right;
    margin-right: 16px;
  }
}
.paramTagContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 64px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  .paramText {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item {
    height: 28px;
    border-radius: 8px;
    background-color: #eef2f6;
    text-align: center;
    padding: 3px 12px;
    display: flex;
    align-items: center;
    gap: 2px;
    overflow: hidden;
    max-width: 100%;
  }
  .tagItemWrapper {
    box-sizing: border-box;
    max-width: 100%;
  }
  .moreContainer {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: #fff;
    cursor: pointer;
    width: 36px;
    height: 28px;
    .more {
      // padding: 4px;
      margin-left: 8px;
      background-color: #eef2f6;
      border-radius: 8px;
      width: 28px;
      height: 28px;
    }
  }
  .normalItem {
    @extend .item;

    .parameterType {
      @extend .paramText;
      color: #9ea7b8;
    }
    .parameterValue {
      @extend .paramText;
      color: #1d2531;
    }
  }
  .undefinedItem {
    @extend .item;
    background-color: #FEFAD9;
    color: #ff9a2e;
    .parameterType {
      @extend .paramText;
      color: #ff9a2e;
    }
    .parameterValue {
      @extend .paramText;
      color: #ff9a2e;
    }
  }
}
.inputContainer {
  @extend .paramContainer;
}
.outputContainer {
  @extend .paramContainer;
}
.modelContainer {
  @extend .paramContainer;
  // align-items: center;
  .model {
    display: flex;
    align-items: center;
    gap: 10px;
    .modelIcon {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      border: 1px solid #edf1f5;
      background: #fff;
      -o-object-fit: cover;
      object-fit: cover;
      overflow: hidden;
    }
    .modelText {
      color: #1d2531;
      font-size: 14px;
      font-weight: 400;
    }
  }
  .alignCenter {
    align-self: center; /* 覆盖默认的居中对齐，使用基线对齐 */
  }
}
.infoContainer {
  @extend .paramContainer;
  .info {
    font-size: 14px;
    font-weight: 400;
    color: #1d2531;
  }
}
.moreListContainer {
  display: flex;
  width: 306px;
  flex-wrap: wrap;
  padding: 12px;
  max-height: unset;
}

.alignCenter {
  align-items: center;
}
.cardItemDropdown  {
/* 投影/下/弹出投影（4级） */
border-radius: 8px;
box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10), 0px 15px 45px 7px rgba(27, 37, 50, 0.06) !important;
}
.cardItemDropdown .ant-dropdown-menu {
  padding: 8px 2px;
}
.cardItemDropdown .ant-dropdown-menu .ant-dropdown-menu-item {
  color: #1b2532 !important;
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.statusContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  background-color: #EDF1F5;
  height: 22px;
  box-sizing: border-box;
  .statusIcon {
    width: 20px;
    height: 20px;
  }
  .rotate {
    animation: circle 2s linear infinite;
  }
  .statusTitle {
    color: #657083;
    font-size: 14px;
    display: flex;
    align-items: center;
  }
}
@keyframes circle {
  0% {
     transform: rotate(0deg);
  }
  100% {
     transform: rotate(360deg);
  }
}

.flowCardResponse {
  border: 1px solid #E1E7ED;
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 12px;
  box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.14);
  padding: 8px;
  position: absolute;
  top: 180px;
  left: 0;
  width: 100%;
  &:hover {
    cursor: pointer;
  }
  .statusContainer {
    background-color: #ffffff !important;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  .flowCardResponseContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .flowCardResponseStatus {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}