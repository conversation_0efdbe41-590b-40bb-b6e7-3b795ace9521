.container {
    width: 100%;
    height: 100%;
}

.left {
    width: 50%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    border-right: 1px solid #D5D7DE;
    padding-top: 16px;
    float: left;
}

.leftContent {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: 12px;
}

.leftContent:last-child {
    margin-bottom: 0;
}

.leftTitle {
    color: #1B2532;
    /* 加粗/20px */
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    margin-left: -10px;
}

.leftUrl {
    width: 100%;
    height: 40px;
    display: flex;
    felx-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin: 8px 0;
}

.leftFunc {
    margin-right: 12px;
}

.leftUrlTis {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.leftSubTitle {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.leftContentReq {
    margin-top: 24px;
    width: 85%;
    /* background: red; */
}

.leftContentRes {
    margin-top: 12px;
    width: 85%;
}

.leftContentCode {
    width: 100%;
    margin-top: 24px;
    margin-bottom: 12px;
}

.leftContentReqTitle {
    color: #1B2532;
    /* 加粗/20px */
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    /* 150% */
}

.right {
    width: 50%;
    height: 100%;
    box-sizing: border-box;
    float: left;
}

.rightTop {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 44px;
    background: #F7F8FA;
    padding: 0 16px;
    position: relative;
}

.rightTopTitle {
    color: #1B2532;
    /* 加粗/20px */
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    /* 150% */
}

.copyTis {
    color: #006BFF;
    /* 正常/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    /* 150% */
    cursor: pointer;
    position: absolute;
    right: 16px;
    top: 14px;
}

.rightContent {
    width: 100%;
    height: calc(100% - 44px);
    padding: 0px 0 0 16px;
}

.locationItem {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: felx-start;
}

.locationItemName {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 16px;
    margin-top: 16px;
}

.reqItemName {
    border: 1px solid #9BA7BA;
    background: rgba(240, 245, 255, 0.60);
    text-align: center;
    border-radius: 4px;
    box-sizing: border-box;
    height: 30px;
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
    padding: 4px 8px;
    margin-right: 12px;
}

.reqItemType {
    color: var(--auxiliary-text, #9BA7BA);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.reqItemReq {
    margin-left: 12px;
}

.reqItemDesc {
    color: var(--main-text, #1B2532);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    margin-top: 12px;
    margin-bottom: 12px;
    /* margin-top: 12px;
    margin-bottom: 12px; */
}

.childrenContent {
    margin-top: 12px;
}

.contentTitle {
    color: #1B2532;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    padding: 8px 16px;
    height: 64px;
    line-height: 48px;
}

.agentContent {
    display: flex;
    flex-direction: column;
    padding-top: 24px;
    padding-left: 32px;
}

.contentWrapper {
    background: #EFF0F2;
    height: calc(100% - 72px);
    min-height: calc(100vh - 72px);
    padding: 16px;
    position: relative;

    :global {
        .ant-tabs-nav .ant-tabs-nav-wrap {
            border-bottom: 1px solid #EFF0F2;
        }
    }

    .messageWrapper {
        padding: 16px 24px 16px;
        background: #F1FEE1;
        margin: 0 16px;
        border-radius: 4px;

        img {
            vertical-align: middle;
            margin-top: -3px;
        }

        .successTips {
            font-size: 16px;
            font-weight: 600;
            margin: 0 16px 0 8px;
        }
    }

    .publicContent {
        padding: 24px 32px;
        background: #FFFFFF;
    }

    .publicItem {
        width: 100%;
        display: flex;
        margin-bottom: 46px;
    }

    .publishStatusItem {
        justify-content: space-between;
    }

    .publicLabel {
        display: flex;

        .publicLabelTitle {
            margin-right: 40px;
            color: #1B2532;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 16px;
            width: 100px;

            img {
                width: 16px;
                height: 16px;
                margin-left: 8px;
                cursor: pointer;
            }

            min-width: 100px;
        }

        .publishStatus {
            margin-right: 28px;

            .circleIcon {
                margin-right: 8px;
            }
        }

        .publishStatusTip {
            color: #626F84;
            font-size: 12px;
            margin-top: 3px;
        }
    }

    .tagList {
        width: calc(100vw - 450px);
    }

    .tagItem {
        padding: 8px 24px;
        margin-right: 16px;
        border-radius: 4px;
        margin-bottom: 16px;
        cursor: pointer;
        border: 1px solid #D5D7DE;
        background: #FFF;
        display: inline-block;
    }

    .agentTagItem {
        padding: 8px 24px;
        margin-right: 16px;
        border-radius: 4px;
        cursor: pointer;
        border: 1px solid #D5D7DE;
        background: #FFF;
        display: inline-block;
    }

    .tagItem img,
    .agentTagItem img {
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
        margin-top: -3px;
    }

    .tagCheckedItem {
        border-color: #477FFF;
        color: #1D7CFF;
    }

    .markDownModalBg {
        background: #fff;
        border-radius: 10px;
        padding-bottom: 0;
    }

    .deployTerms {
        padding-top: 50px;
    }

    .deployTermsTitle {
        padding-top: 16px;
    }

    .fuWuA {
        color: #477FFF;
        margin-left: -10px;
    }

    .footer {
        position: absolute;
        right: 0;
        bottom: 0;
        width: calc(100% - 64px);
        margin: 16px 32px;
        border-radius: 4px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
    }
}

.agentContentWrapper {
    height: calc(100% - 96px);
    min-height: calc(100vh - 96px);
    padding-top: 16px;
    position: relative;
    padding-left: 65px;
    padding-right: 65px;

}

.circleIcon {
    width: 8px;
    height: 8px;
    display: inline-block;
    background: #BCCAD6;
    border-radius: 4px;
    margin-right: 4px;
}

.reviewCircleIcon {
    background: #1D7CFF;
}

.publishedCircleIcon {
    background: #4EC01E;
}

.publishedBtn {
    border-radius: 4px;
    background: #DFFAC7;
    color: #4EC01E;
    padding: 5px 8px;
    font-size: 12px;
    display: inline-block;
    vertical-align: top;
}

.unPublishedBtn {
    color: #626F84;
    font-size: 12px;
    display: inline-flex;
    height: 22px;
    padding: 5px 8px;
    gap: 10px;
    border-radius: 4px;
    background: #EBF0F5;
    text-align: center;
    align-items: center;
    justify-items: center;
}

.agentPublishedBtn {
    font-size: 12px;
    display: inline-flex;
    height: 22px;
    padding: 5px 8px;
    gap: 10px;
    border-radius: 4px;
    background: #F1FEE1;
    color: #4EC01E;
    text-align: center;
    align-items: center;
    justify-items: center;
}

.unpubishItemContent {
    margin-top: 18px;
}

.publishItems {
    display: flex;
    padding: 16px;
    // justify-content: center;
    // align-items: flex-start;
    align-self: stretch;
    background: #FFF;
    justify-content: space-between;
    margin-bottom: 16px;

    .checkedContent {
        flex: 0 0 auto;
        /* 不伸缩，固定宽度 */
        min-width: 0;
    }

    .publishedConetnt {
        flex: 1;
        /* 伸缩占据剩余空间 */
        min-width: 0;
        margin-left: 16px;
    }

    .itemInfo {
        display: flex;
        align-items: center;
    }

    .channelName {
        font-size: 14px;
        font-weight: 600;
        margin: 0 8px;
        cursor: pointer;
    }

    .publishItemContent {
        margin-top: 8px;
    }

    .line {
        height: 13px;
        width: 1px;
        background: #E1E7ED;
        display: inline-block;
        margin: 0px 4px;
    }

    .publishInfo {
        color: #626F84;
        font-size: 12px;
    }

    .publishInfoBack {
        color: var(---, #1D2531);
        font-size: 14px;
        margin: 0 8px;
    }

    .channelTag {
        margin-left: 32px;
        font-size: 14px;
        color: #626F84;

    }

    .btnList {
        display: inline-block;
        float: right;
        cursor: pointer;
    }

    .operateBtn {
        display: inline-block;
        height: 32px;
        padding: 4px 16px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        border: 1px solid #E1E7ED;
        background: #FFF;
        margin: 0 16px;
        color: #1D2531;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px
    }

    .chooseTagList {
        display: inline-block;
        font-size: 14px;
        color: var(---, #1B2532);
        text-align: center;
        cursor: pointer;

        img,
        span {
            display: inline-block;
            vertical-align: middle;
        }
    }


}

.cancelModal {
    padding: 24px;

    .ant-modal-body {
        display: flex;
        align-items: center;

        span {
            margin-left: 8px;
        }
    }
}

.menuBox {
    width: 180px;
    display: flex;
    // padding: 12px;
    // flex-direction: column;
    // align-items: flex-start;
    border-radius: 4px;
    background: #FFF;
    margin-bottom: -5px;

    .ant-dropdown-menu-submenu-expand-icon {
        // margin-top: -20px;
    }

    overflow-y: hidden;
}

// 版本管理页面样式

.versionManageBox {
    background: #FFFFFF;
    padding: 0 16px;

    .versionManageCreate {
        width: 100%;
        height: 156px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        align-self: stretch;

    }

    .versionManageCreateTop {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .versionManageCreateTopTitle {
        color: var(---, #1B2532);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 加粗/16px */
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        /* 150% */
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .versionManageCreateBottom {
        width: 100%;
    }

    .versionManageSort {
        padding: 16px;
        display: flex;
        align-items: center;
    }

    .versionManageSortLeft {
        display: flex;
        align-items: center;
    }

    .versionManageTable {
        padding: 0 16px;
    }

    .versionListLiLeftStatus {
        display: flex;
        height: 22px;
        padding: 5px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 4px;
        background: var(---Green-Green-10, #F1FEE1);
        color: var(---Green-Green-60, #4EC01E);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 常规/12px */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
    }

    .versionListLiLeftStatusNo {
        display: flex;
        height: 22px;
        padding: var(---spacing-1, 4px) var(---spacing-2, 8px);
        justify-content: center;
        align-items: center;
        gap: var(---spacing-1, 4px);
        border-radius: var(---m, 4px);
        background: var(---04, #E1E7ED);

        color: var(---, #657083);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 常规/12px */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
    }

    .versionListLiRightBtnEdit {
        color: var(---, #006BFF);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 常规/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        display: flex;
        height: 32px;
        padding: var(---spacing-1, 4px) var(---spacing-2, 8px);
        justify-content: center;
        align-items: center;
        gap: var(---spacing-1, 4px);
        border-radius: var(---m, 4px);
        background: var(--100, #FFF);

        &:hover {
            cursor: pointer;
        }
    }

    .versionListLiRightBtnNo {
        display: flex;
        height: 32px;
        padding: var(---spacing-1, 4px) var(---spacing-2, 8px);
        justify-content: center;
        align-items: center;
        gap: var(---spacing-1, 4px);
        border-radius: var(---m, 4px);
        background: var(--100, #FFF);
        color: var(---, #BFC9D5);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 常规/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;

        /* 157.143% */
        &:hover {
            cursor: not-allowed;
        }
    }

    .versionMangeCreateBtn {
        border: 1px solid var(----70-Normal, #006BFF);
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        border-color: #006bff;
        color: #006bff;
        background-color: #fff;
        border-radius: 8px;
    }

    .versionManagePublishDetail {
        display: flex;
        margin-left: 10px;
        gap: 10px;

        .versionManagePublishDetailKey {
            font-size: 14px;
            font-weight: 700;
        }

        .versionManagePublishDetailNoValue {
            color: #657083;
        }

        .versionManagePublishDetailValue {
            color: #4EC01E;
        }
    }

    .versionManageDescBox {
        display: flex;
        justify-content: space-between;
    }

    .versionManageDesc {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 260px;
    }

    .versionManageDescImg {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .versionMangeColumnCom {
        padding: 10px !important;
    }
}