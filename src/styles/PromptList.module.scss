.promptList {
    height: calc(100vh - 100px);
    overflow-y: auto;
    width: calc(100% + 10px);
    overflow-x: hidden;
}

.promptListContainer {
    // background-color: aqua;
}

.promptImageListContainer {
    // background-color: green;
}

.promptShow {
    display: block;
}

.promptHide {
    display: none;
}

.waterFallItem {
    position: relative;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);

    .topIcon {
        position: absolute;
        display: flex;
        padding: 4px 8px;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0px;
        top: 0;
        border-radius: 12px 0px;
        background: #006BFF;
        color: #fff;
        font-size: 10px;
    }

    .footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 16px;

        .user {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;

            span {
                margin-left: 8px;
                color: var(---, #9BA7BA);
                font-feature-settings: 'clig' off, 'liga' off;
                /* 常规/12px */
                font-family: PingFang SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
                /* 166.667% */
            }
        }
    }

    .hoverBox {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 24px;
        box-sizing: border-box;
        background: linear-gradient(0deg, rgba(19, 19, 26, 0.60) 0%, rgba(19, 19, 26, 0.60) 100%);
        border-radius: 8px;


        .hoverBoxContent {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-start;
            width: 100%;
            height: 100%;

            .hoverBoxTitle {
                overflow: hidden;
                width: 100%;
                color: var(---Basic-White, #FFF);
                font-feature-settings: 'clig' off, 'liga' off;
                text-overflow: ellipsis;
                white-space: nowrap;

                /* 加粗/16px */
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
            }

            .hoverBoxText {
                color: var(---Basic-White, #FFF);
                font-feature-settings: 'clig' off, 'liga' off;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 12;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;

                /* 常规/14px */
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                /* 157.143% */
            }
        }

        .moreIcon {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 24px;
            height: 24px;
            cursor: pointer;

            &:hover {
                border-radius: 2px;
                background: rgba(216, 224, 232, 0.20);
            }
        }

    }
}

.waterFallItem img {
    border-radius: 8px;
    display: block;
}