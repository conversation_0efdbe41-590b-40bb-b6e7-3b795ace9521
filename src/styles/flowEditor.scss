.gedit-flow-activity-node>div{
  height: 100%!important;
}
// workflow
.gedit-flow-activity-node .selectedNodeWrapper .cardContainer{
    border-width: 2.5px;
    padding: 0;
}
// 卡片（active样式）设置border，会影响连接点计算的x位置在border左侧
.gedit-flow-activity-node .selectedNodeWrapper .cardWrapper{
  // border: 4px solid rgba(77, 208, 225, 0.10);
  outline:  4px solid rgba(77, 208, 225, 0.10);
  padding: 0px;
}
// todo
// .gedit-flow-activity-node .selected .groupOuterWrapper, 
.gedit-flow-activity-node .selectedNodeWrapper .groupOuterWrapper{
  border-width: 2.5px;
  padding: 0;
}
// 迭代节点（active样式）设置border，会影响连接点计算的x位置在border左侧
// todo
// .gedit-flow-activity-node .selected .groupWrapper, .gedit-flow-activity-node .selectedNodeWrapper .groupWrapper
.gedit-flow-activity-node .selectedNodeWrapper .groupWrapper{
  // border: 4px solid;
  // padding: 0;
  outline: 4px solid;
}
//  todo
// .gedit-flow-activity-node .selected .toolContainer
.gedit-flow-activity-node .selectedNodeWrapper .toolContainer{
    display: block;
} 

// todo
// .gedit-flow-activity-node .selected .cardEditorWrapper, 
.gedit-flow-activity-node .selectedNodeWrapper .cardEditorWrapper{
  display: block;
}
// todo
// .gedit-flow-activity-node .selected .loopNodeWrapper .cardEditorWrapper
.gedit-flow-activity-node .selectedNodeWrapper.loopNodeWrapper .cardEditorWrapper{
  z-index: 1;
  left: auto;
  right: -435px;
  top: 2px;
}
// todo
// 选中状态的group header，为右侧展示按钮区域
.gedit-flow-activity-node .selectedNodeWrapper .loopNodeWrapper .groupHeader{
  width: calc(100% - 180px);
}

.gedit-flow-activity-node:has(.selectedNodeWrapper) {
    // z-index: 1111!important;
}
// 循环节点，由于头部高度影响节点居中，需要将头部内容和tools设置绝对定位，浮动出去
// .loopNodeWrapper .toolContainer{
//   left: calc(100% - 184px);
//   right: 0;
//   height: 43px;
//   width: 171px;
//   top: -5px;
//   transform: translateY(-100%);
// }

.loopNodeWrapper .headerContainer{
  min-height: 50px;
}

// div.sub-canvas-border, div.sub-canvas-background
// 子画布
div.sub-canvas-render{
  height: 100%!important;
  // min-height: 170px;
  // background: transparent;
}
div.sub-canvas-background{
  background: #fff;
}
div.sub-canvas-border{
  border: 0px;
  border-radius: 16px;
  overflow: hidden;
}
div.sub-canvas-border::before{
  border: 0px;
}
// 端口样式
div[class^="sc-"][data-port-entity-id]{
    // 这里使用默认的宽高20*20，增大hover区域
    // width: 5px;
    // height: 16px;
    border-radius: 0px;
    display: none;
    .bg-circle{
      border-radius: 0;
      width: auto;
      height: auto;
      transform: none;
    }
    .bg{
      width: 5px;
      height: 16px;
      margin: 0 auto;
      transform: translate(0.5px, 2px);
      border-radius: 0;
      // background: #4DD0E1;
      transition: none;
      .symbol{
        width: 100%;
        height: 100%;
      }
    }
}
.nodeWrapper{
  border-radius: 18px;
  // background: #fff;
}
// 节点连接点的样式（这是最外层）
.nodeWrapper div[class^="sc-"][data-port-entity-id]{
  display: block;
}

// 默认隐藏输出端口
.nodeWrapper div[class^="sc-"][data-port-entity-type="output"] .bg {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

// 当hover节点时，显示所有输出端口
.nodeWrapper:hover div[class^="sc-"][data-port-entity-type="output"] .bg {
  opacity: 1;
}

// .nodeWrapper{
//   .gedit-flow-activity-node > div[class^="sc-"][data-port-entity-type='input'] {
//     top: calc(50% + 30px) !important; // 这里的50%是包含头部，计算时要去掉头部高度50px+margin4px 
//   }
//   // 输出
//   .gedit-flow-activity-node > div[class^="sc-"][data-port-entity-type='output'] {
//     top: calc(50% + 28px) !important; // 这里的50%是包含头部，计算时要去掉头部高度50px+margin4px 
//   }
// }
// div[class^="sc-"][data-port-entity-type='input'] {
//   top: calc(50% + 2px) !important; // 这里的50%是包含头部，计算时要去掉头部高度50px+margin4px 
// }
// 输出
div[class^="sc-"][data-port-entity-type='output'] {
  // top: calc(50% - 1px) !important; // 这里的50%是包含头部，计算时要去掉头部高度50px+margin4px 
  &.linked .bg:not(.hasError){
    background: transparent;
  }
  // &.my-hovered .bg:not(.hasError){
  //   transform: translateX(-3px);
  // }
  .bg{
    // transform: translateX(-3px)!important;
    width: 20px;
    height: 20px;
    border-radius: 0;
    background: transparent!important;
    transform: translate(0.5px, 0px);
    .symbol{
      // width: 5px;
      // height: 16px;
      .cross-hair{
        left: 0!important;
        &::before, &::after {
          display: none;
        }
      }
    }
  }
}
div[class^="sc-"][data-port-entity-id].linked .bg:not(.hasError){
  // background: #4DD0E1;
}
div[class^="sc-"][data-port-entity-id].output-port .bg:not(.hasError)>.symbol{
  opacity: 1!important;
}
div[class^="sc-"][data-port-entity-id].output-port {
  .bg:not(.hasError){
    background-color: transparent;
    transform: none;
    .cross-hair{
      // #4DD0E1
      margin-top: -1px;
      background-image: url(../images/flow3.0/rightArrow.svg);
      background-repeat: no-repeat;
      background-position: 2px 2px;
      background-size: 12px 12px;
      width: 18px;
      height: 18px;
      border: 1.25px solid #fff;
      border-radius: 9px;
      text-align: center;
    }
  }
}
.condition-if-port  div[class^="sc-"][data-port-entity-type='output']{
  margin-left: -12px;
}

// .loopNodeWrapper{
//   div[class^="sc-"][data-port-entity-id]{
//     .bg{
//       transform: translateX(0.5px);
//     }
//   }

// 定制port
div.portStyle0 {
  .bg{
    background: #8080FE;
  }
}
div.portStyle0.linked .bg:not(.hasError){
  background: #8080FE;
}
div.portStyle0 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #8080FE;
    }
  }
}


div.portStyle1 {
  .bg{
    background: #9C89B8;
  }
}
div.portStyle1.linked .bg:not(.hasError){
  background: #9C89B8;
}
div.portStyle1 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #9C89B8;
    }
  }
}


div.portStyle2 {
  .bg{
    background: #B8BEDD;
  }
}
div.portStyle2.linked .bg:not(.hasError){
  background: #B8BEDD;
}
div.portStyle2 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #B8BEDD;
    }
  }
}


div.portStyle3 {
  .bg{
    background: #E4B7FF;
  }
}
div.portStyle3.linked .bg:not(.hasError){
  background: #E4B7FF;
}
div.portStyle3 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #E4B7FF;
    }
  }
}


div.portStyle4 {
  .bg{
    background: #64B5F6;
  }
}
div.portStyle4.linked .bg:not(.hasError){
  background: #64B5F6;
}
div.portStyle4 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #64B5F6;
    }
  }
}


div.portStyle5 {
  .bg{
    background: #4DD0E1;
  }
}
div.portStyle5.linked .bg:not(.hasError){
  background: #4DD0E1;
}
div.portStyle5 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #4DD0E1;
    }
  }
}
div.portStyle5 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #4DD0E1;
    }
  }
}


div.portStyle6 {
  .bg{
    background: #4DDBBB;
  }
}
div.portStyle6.linked .bg:not(.hasError){
  background: #4DDBBB;
}
div.portStyle6 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #4DDBBB;
    }
  }
}


div.portStyle7 {
  .bg{
    background: #7EE787;
  }
}
div.portStyle7.linked .bg:not(.hasError){
  background: #7EE787;
}
div.portStyle7 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #7EE787;
    }
  }
}


div.portStyle8 {
  .bg{
    background: #A3B18A;
  }
}
div.portStyle8.linked .bg:not(.hasError){
  background: #A3B18A;
}
div.portStyle8 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #A3B18A;
    }
  }
}


div.portStyle9 {
  .bg{
    background: #E8D45A;
  }
}
div.portStyle9.linked .bg:not(.hasError){
  background: #E8D45A;
}
div.portStyle9 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #E8D45A;
    }
  }
}

div.portStyle10 {
  .bg{
    background: #FFA07A;
  }
}
div.portStyle10.linked .bg:not(.hasError){
  background: #FFA07A;
}
div.portStyle10 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #FFA07A;
    }
  }
}



div.portStyle11 {
  .bg{
    background: #FF7F7F;
  }
}
div.portStyle11.linked .bg:not(.hasError){
  background: #FF7F7F;
}
div.portStyle11 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #FF7F7F;
    }
  }
}

div.portStyle12 {
  .bg{
    background: #FF8FAB;
  }
}
div.portStyle12.linked .bg:not(.hasError){
  background: #FF8FAB;
}
div.portStyle12 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #FF8FAB;
    }
  }
}


div.portStyle13 {
  .bg{
    background: #D4A373;
  }
}
div.portStyle13.linked .bg:not(.hasError){
  background: #D4A373;
}
div.portStyle13 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #D4A373;
    }
  }
}


div.portStyle14 {
  .bg{
    background: #E3684D;
  }
}
div.portStyle14.linked .bg:not(.hasError){
  background: #E3684D;
}
div.portStyle14 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #E3684D;
    }
  }
}


div.portStyle15 {
  .bg{
    background: #D86E77;
  }
}
div.portStyle15.linked .bg:not(.hasError){
  background: #D86E77;
}
div.portStyle15 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #D86E77;
    }
  }
}


div.portStyle16 {
  .bg{
    background: #FF9C50;
  }
}
div.portStyle16.linked .bg:not(.hasError){
  background: #FF9C50;
}
div.portStyle16 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #FF9C50;
    }
  }
}


div.portStyle17 {
  .bg{
    background: #C17E9C;
  }
}
div.portStyle17.linked .bg:not(.hasError){
  background: #C17E9C;
}
div.portStyle17 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #C17E9C;
    }
  }
}


div.portStyle18 {
  .bg{
    background: #CF9CD5;
  }
}
div.portStyle18.linked .bg:not(.hasError){
  background: #CF9CD5;
}
div.portStyle18 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #CF9CD5;
    }
  }
}


div.portStyle19 {
  .bg{
    background: #B16EC7;
  }
}
div.portStyle19.linked .bg:not(.hasError){
  background: #B16EC7;
}
div.portStyle19 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #B16EC7;
    }
  }
}


div.portStyle20 {
  .bg{
    background: #8D5C86;
  }
}
div.portStyle20.linked .bg:not(.hasError){
  background: #8D5C86;
}
div.portStyle20 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #8D5C86;
    }
  }
}


div.portStyle21 {
  .bg{
    background: #F3BC53;
  }
}
div.portStyle21.linked .bg:not(.hasError){
  background: #F3BC53;
}
div.portStyle21 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #F3BC53;
    }
  }
}


div.portStyle22 {
  .bg{
    background: #6AB693;
  }
}
div.portStyle22.linked .bg:not(.hasError){
  background: #6AB693;
}
div.portStyle22 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #6AB693;
    }
  }
}


div.portStyle23 {
  .bg{
    background: #81ADED;
  }
}
div.portStyle23.linked .bg:not(.hasError){
  background: #81ADED;
}
div.portStyle23 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #81ADED;
    }
  }
}


div.portStyle24 {
  .bg{
    background: #5876BF;
  }
}
div.portStyle24.linked .bg:not(.hasError){
  background: #5876BF;
}
div.portStyle24 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #5876BF;
    }
  }
}


div.portStyle25 {
  .bg{
    background: #D58043;
  }
}
div.portStyle25.linked .bg:not(.hasError){
  background: #D58043;
}
div.portStyle25 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #D58043;
    }
  }
}


div.portStyle26 {
  .bg{
    background: #AD98FF;
  }
}
div.portStyle26.linked .bg:not(.hasError){
  background: #AD98FF;
}
div.portStyle26 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #AD98FF;
    }
  }
}


div.portStyle27 {
  .bg{
    background: #FD7462;
  }
}
div.portStyle27.linked .bg:not(.hasError){
  background: #FD7462;
}
div.portStyle27 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #FD7462;
    }
  }
}


div.portStyle28 {
  .bg{
    background: #3B8096;
  }
}
div.portStyle28.linked .bg:not(.hasError){
  background: #3B8096;
}
div.portStyle28 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #3B8096;
    }
  }
}


div.portStyle29 {
  .bg{
    background: #609749;
  }
}
div.portStyle29.linked .bg:not(.hasError){
  background: #609749;
}
div.portStyle29 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #609749;
    }
  }
}


div.portStyle30 {
  .bg{
    background: #52B0B2;
  }
}
div.portStyle30.linked .bg:not(.hasError){
  background: #52B0B2;
}
div.portStyle30 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #52B0B2;
    }
  }
}


div.portStyle31 {
  .bg{
    background: #88D6A3;
  }
}
div.portStyle31.linked .bg:not(.hasError){
  background: #88D6A3;
}
div.portStyle31 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #88D6A3;
    }
  }
}


div.portStyle32 {
  .bg{
    background: #B2DF7A;
  }
}
div.portStyle32.linked .bg:not(.hasError){
  background: #B2DF7A;
}
div.portStyle32 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #B2DF7A;
    }
  }
}


div.portStyle33 {
  .bg{
    background: #EDD900;
  }
}
div.portStyle33.linked .bg:not(.hasError){
  background: #EDD900;
}
div.portStyle33 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #EDD900;
    }
  }
}


div.portStyle34 {
  .bg{
    background: #DADB64;
  }
}
div.portStyle34.linked .bg:not(.hasError){
  background: #DADB64;
}
div.portStyle34 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #DADB64;
    }
  }
}


div.portStyle35 {
  .bg{
    background: #468CFF;
  }
}
div.portStyle35.linked .bg:not(.hasError){
  background: #468CFF;
}
div.portStyle35 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #468CFF;
    }
  }
}


div.portStyle36 {
  .bg{
    background: #4A74DE;
  }
}
div.portStyle36.linked .bg:not(.hasError){
  background: #4A74DE;
}
div.portStyle36 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #4A74DE;
    }
  }
}


div.portStyle37 {
  .bg{
    background: #118358;
  }
}
div.portStyle37.linked .bg:not(.hasError){
  background: #118358;
}
div.portStyle37 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #118358;
    }
  }
}


div.portStyle38 {
  .bg{
    background: #56A0D3;
  }
}
div.portStyle38.linked .bg:not(.hasError){
  background: #56A0D3;
}
div.portStyle38 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #56A0D3;
    }
  }
}


div.portStyle39 {
  .bg{
    background: #80D6E4;
  }
}
div.portStyle39.linked .bg:not(.hasError){
  background: #80D6E4;
}
div.portStyle39 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #80D6E4;
    }
  }
}


div.portStyle40 {
  .bg{
    background: #A0D6A0;
  }
}
div.portStyle40.linked .bg:not(.hasError){
  background: #A0D6A0;
}
div.portStyle40 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #A0D6A0;
    }
  }
}


div.portStyle41 {
  .bg{
    background: #B9C7BC;
  }
}
div.portStyle41.linked .bg:not(.hasError){
  background: #B9C7BC;
}
div.portStyle41 {
  .bg:not(.hasError){
    .cross-hair{
      background-color: #B9C7BC;
    }
  }
}