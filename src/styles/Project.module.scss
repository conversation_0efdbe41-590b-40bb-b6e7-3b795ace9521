.headerRight {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;



  .status {
    margin-right: 16px;
    padding: 2px 8px;
    display: inline-block;
    cursor: pointer;

  }
  .statusForYunPan {
    margin-right: 16px;
    display: flex;
    width: 80px;
    height: 32px;
    padding: 5px 12px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-radius:  4px;
    border: 1px solid #E1E7ED;
    background:  #FFF;
  }

  .spaceForYunPan {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .status:hover {
    border-radius: 4px;
    background: var(---g-20, #EBECF0);
  }

  // .search {
  //     margin-right: 16px;
  // }
  .btn {
    margin-left: 16px;
  }
}

.selectTitle {
  color: var(---, #9BA7BA);
  font-feature-settings: 'clig' off, 'liga' off;

  /* 常规/14px */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.selectSubtitle {
  color: var(---, #1B2532);
  font-feature-settings: 'clig' off, 'liga' off;

  /* 常规/14px */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.selectProItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.selectForYunPan {
  border-radius:  4px;
  border: 1px solid  #E1E7ED;
  background:  #FFF;
  margin-right: 16px;
}