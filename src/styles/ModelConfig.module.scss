.container {
    width: 100%;
    height: 100%;
    background-color: #F7F8FA;
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;

    .addedContainer {
        min-height: 148px;
        width: 100%;
        border-radius: 4px;
        background-color: #fff;
        padding: 16px;
        box-sizing: border-box;

        .addedContainerTitle {
            color: var(---, #1B2532);
            font-feature-settings: 'clig' off, 'liga' off;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
            margin-bottom: 4px;
        }

        .addedItem {
            width: 100%;
            border-radius: 4px;

            .addItemTop {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                padding: 16px;

                .addItemTopLeft {
                    display: flex;
                    img {
                        width: 200px;
                        height: 36px;
                        object-fit: contain;
                        text-align: left;
                        vertical-align:baseline;
                        object-position: left;
                    }
                }

                .addItemTopRight {
                    .addItemTopRightBtn {
                        color: var(---, #1D2531);
                        font-feature-settings: 'clig' off, 'liga' off;

                        /* 常规/14px */
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                    }
                }
            }

            .addItemTag {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;
                padding: 0 16px 16px 16px;
            }
        }
    }
}

.itemChildren {
    background-color: #FFF;
    overflow: hidden;
    border-radius: 4px;
}
.unfold {
    width: 100%;
    background:rgba(255, 255, 255, 0.45);
    padding-top: 9px;

}
.unfoldTitleTop {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-left: 16px;
    margin-right: 16px;
    cursor: pointer;
    img {
        vertical-align: baseline;
        margin-right: 2px;
    }
}
.modelDownArrowUnfoldCommon {
    margin-left: 4px;
    transition: transform 0.3s ease-in-out;
    cursor: pointer;
    width: 16px;
}
.modelDownArrowUnfold {
   // 旋转 180度
    transform: rotate(180deg);
    transition: transform 0.3s ease-in-out;
}

.modelSubItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;

    .modelSubItemLeft {
        color: var(---, #1D2531);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 常规/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    &:hover {
        background: var(---02, #F7F9FA);
    }
}

.addingContainer {
    background-color: #F7F8FA;

    .addingContainerTitle {
        color: var(---, #1B2532);
        font-feature-settings: 'clig' off, 'liga' off;
        /* 加粗/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-bottom: 16px;
    }
}

.itemAddedCardContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    border-radius: 4px;
    padding: 16px 16px 0 16px;
    box-sizing: border-box;
    cursor: pointer;
    height: 148px;
    width: 100%;
    position: relative;

    img {
        width: 150px;
        height: 24px;
        object-fit: contain;
        text-align: left;
        vertical-align:baseline;
        object-position: left;
    }

    .itemCardContentTag {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        align-content:flex-end;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: 16px;

        .itemCardSet {
            display: flex;
            flex-direction: row;
            width: 100%;
            align-items: center;
            justify-content: space-around;
        }
    }

    .itemCardBottom {
        background: var(--45, rgba(255, 255, 255, 0.45));
        width: 100%;
        height: 40px;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding-left: 24px;
        padding-right: 24px;
        box-sizing: border-box;

        .itemCardBottomShow {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;

            .itemCardBottomShowLeft {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;

                color: #1D2531;
                font-feature-settings: 'clig' off, 'liga' off;
                /* 常规/14px */
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 4px;
                }
            }
        }
        .itemCardBottomTis{
            color: var(---, #1D2531);
            font-feature-settings: 'clig' off, 'liga' off;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
}
.itemCardContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
    padding: 16px;
    box-sizing: border-box;
    cursor: pointer;

    img {
        width: 150px;
        height: 24px;
        object-fit: contain;
        text-align: left;
        vertical-align:baseline;
        object-position: left;
    }
    .itemCardContentTitle {
        color: var(---, #657083);
        font-feature-settings: 'clig' off, 'liga' off;

        /* 常规/12px */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        min-height: 42px;
        // 设置文字行数
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin: 8px 0;
    }
    .itemCardContentTag {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        align-content:flex-end;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 4px;

        .itemCardSet {
            display: flex;
            flex-direction: row;
            width: 100%;
            align-items: center;
            justify-content: space-around;
        }
    }
}

.providerModalForm {
    overflow-y: auto;
    max-height: calc(100vh - 400px);
    overflow-x: hidden;
}
.modelFooter {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.btn {
    margin-left: 8px;
}
.linkBtn {
    overflow: hidden;
    color: var(---, #006BFF);
    font-feature-settings: 'clig' off, 'liga' off;
    text-overflow: ellipsis;
    padding: 0;

    /* 常规/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #00B42A;
    margin-left: 8px;
}
.addedItemTop {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    width: 100%;
}
.addModelBtn:hover {
    color: #006BFF !important;
}
.addedItemTopRightBtn {
    border-radius: 4px;
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.40);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}
.popoverContent {
    padding: 12px 0 12px 0;
    width: 452px;
    max-height: 388px;
    overflow-y: auto;
    box-sizing: border-box;

    .popoverContentTitle {
        padding-left: 16px;
        margin-bottom: 4px;
        color: var(---, #1D2531);
        font-feature-settings: 'clig' off, 'liga' off;
        /* 加粗/16px */
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
    }
}

.modelSubItemRight {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
}
