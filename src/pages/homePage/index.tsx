import { RecoilRoot } from 'recoil'
import React, { useState, useEffect, useMemo } from 'react'
import { Row, Col } from 'antd'
import TopText from '@/components/homePage/TopText'
import TitleText from '@/components/homePage/TitleText'
import CourseCard from '@/components/homePage/CourseCard'
import NavTabs from '@/components/homePage/NavTabs'
import SearchBox from '@/components/homePage/SearchBox'
import AgentCard from '@/components/homePage/AgentCard'
import BackgroundPic from '@/components/homePage/BackgroundPic'
import { getNamiAgentList, AgentItem, AgentCategory } from '@/service/homePage'
import styles from './index.module.scss'

function HomePageContent() {
  const [activeTab, setActiveTab] = useState('all');
  const [allAgentData, setAllAgentData] = useState<AgentCategory[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟课程卡片数据
  const courseCards = [
    {
      id: 1,
      // leftTopIcon: <div style={{ width: 40, height: 40, background: '#1c53e0', borderRadius: 8 }} />,
      // bigText: '产品功能速览',
      // smallText: '快速了解产品核心功能',
      backgroundPic: 'https://p1.ssl.qhimg.com/t110b9a9301fa55f62722b7080a.png'
    },
    {
      id: 2,
      // leftTopIcon: <div style={{ width: 40, height: 40, background: '#c258f7', borderRadius: 8 }} />,
      // bigText: '多智能体蜂群能力精讲',
      // smallText: '深入理解多智能体协作机制',
      backgroundPic: 'https://p2.ssl.qhimg.com/t110b9a9301b80c87761143816f.png'
    },
    {
      id: 3,
      // leftTopIcon: <div style={{ width: 40, height: 40, background: '#52c41a', borderRadius: 8 }} />,
      // bigText: '多智能体蜂群快速搭建',
      // smallText: '从零开始构建智能体系统',
      backgroundPic: 'https://p1.ssl.qhimg.com/t110b9a930121bf10149d6dcf4d.png'
    }
  ];

  // 根据选中的tab过滤智能体数据
  const filteredAgentCards = useMemo(() => {
    if (!allAgentData.length) return [];

    if (activeTab === 'all') {
      // 返回所有智能体
      return allAgentData.reduce((acc, category) => {
        return acc.concat(category.list);
      }, [] as AgentItem[]);
    } else {
      // 返回指定分类的智能体
      const targetCategory = allAgentData.find(cat => cat.category === activeTab);
      return targetCategory ? targetCategory.list : [];
    }
  }, [allAgentData, activeTab]);

  // 获取智能体数据（只请求一次）
  const fetchAllAgentData = async () => {
    setLoading(true);
    try {
      const response = await getNamiAgentList();
      console.log('获取到的完整数据:', response);

      if (response && response.list) {
        setAllAgentData(response.list);
      }
    } catch (error) {
      console.error('获取智能体数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取全部数据
  useEffect(() => {
    fetchAllAgentData();
  }, []);

  return (
    <div className={styles.homePageContainer}>
      {/* 背景图片 */}
      <BackgroundPic />

      {/* 顶部文字 */}
      <TopText
        title="智筑未来 一键搭建超级智能体"
        subtitle="模块化零代码搭建，让智能能力触手可及"
      />

      {/* 新手教程部分 */}
      <div className={styles.tutorialSection}>
        <TitleText
          tabs={['新手教程']}
          activeTab="新手教程"
        />
        <Row gutter={[24, 24]} className={styles.courseSection}>
          {courseCards.map(card => (
            <Col span={8} key={card.id}>
              <CourseCard
                // leftTopIcon={card?.leftTopIcon}
                // bigText={card?.bigText}
                // smallText={card?.smallText}
                backgroundPic={card?.backgroundPic}
              />
            </Col>
          ))}
        </Row>
      </div>

      {/* 智能体广场部分 */}
      <div className={styles.agentSection}>
        <TitleText
          tabs={['智能体广场']}
          activeTab="智能体广场"
        />

        {/* 导航和搜索 */}
        <div className={styles.navSection}>
          <NavTabs
            activeTab={activeTab}
            updateActiveTab={setActiveTab}
          />
          <SearchBox
            placeholder="请输入关键词搜索"
            onSearch={(value) => console.log('搜索:', value)}
          />
        </div>

        {/* 智能体卡片列表 */}
        <Row gutter={[16, 16]} className={styles.agentList}>
          {loading ? (
            <div style={{ width: '100%', textAlign: 'center', padding: '40px' }}>
              加载中...
            </div>
          ) : (
            filteredAgentCards.map(card => (
              <Col span={6} key={card.id}>
                <AgentCard
                  icon={card.icon}
                  title={card.title}
                  desc={card.greeting || card.intro}
                  tag={card.limited_free ? '限免' : undefined}
                  creator={card.creator}
                  onClick={() => console.log('点击卡片:', card.title)}
                />
              </Col>
            ))
          )}
        </Row>
      </div>
    </div>
  )
}

export default function Root() {
  return (
    <RecoilRoot>
      <HomePageContent />
    </RecoilRoot>
  )
}
