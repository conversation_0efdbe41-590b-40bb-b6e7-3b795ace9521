import { RecoilRoot } from 'recoil'
import CommonPage from '@/components/common/CommonPage'
import Flow3 from '@/components/flow3.0/Flow'
import { useState } from 'react'

export default function Root() {
  const [flowVersion, setFlowVersion] = useState<string>('new');
  return (
    <RecoilRoot>
      <CommonPage
        pageName='flow'
        isFullScreen={true}
        children={<Flow3 setFlowVersion={setFlowVersion} />}
      />
    </RecoilRoot>
  )
}
