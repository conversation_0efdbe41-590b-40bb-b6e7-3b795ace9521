/*
 * @Author: yh
 * @Date: 2025-02-13 18:18:59
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-09 15:45:45
 * @FilePath: \prompt-web\src\pages\_document.tsx
 */
import React from 'react';
import { createCache, extractStyle, StyleProvider } from '@ant-design/cssinjs';
import Document, { Head, Html, Main, NextScript } from 'next/document';
import type { DocumentContext } from 'next/document';

const MyDocument = () => (
  <Html lang="en">
    <Head>
      <title>360智能体工厂</title>
      <meta
        name="viewport"
        content="initial-scale=1.0, width=device-width, maximum-scale=1, user-scalable=no"
      />
      <meta http-equiv="Permissions-Policy" content="clipboard-read=(), clipboard-write=(self)"></meta>
			<script src="//lib.baomitu.com/jquery/1.12.4/jquery.js"></script>
    </Head>
    <body>
      <Main />
      <NextScript />
    </body>
  </Html>
);

MyDocument.getInitialProps = async (ctx: DocumentContext) => {
  const cache = createCache();
  const originalRenderPage = ctx.renderPage;
  ctx.renderPage = () =>
    originalRenderPage({
      enhanceApp: (App) => (props) => (
        <StyleProvider cache={cache}>
          <App {...props} />
        </StyleProvider>
      ),
    });

  const initialProps = await Document.getInitialProps(ctx);
  const style = extractStyle(cache, true);
  return {
    ...initialProps,
    styles: (
      <>
        {initialProps.styles}
        <style dangerouslySetInnerHTML={{ __html: style }} />
      </>
    ),
  };
};

export default MyDocument;