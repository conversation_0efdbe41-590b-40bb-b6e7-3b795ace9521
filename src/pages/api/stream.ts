import { NextApiRequest, NextApiResponse } from 'next';
import http from 'http';
import https from 'https';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log("🚀 ~ req:", req.headers)

  // 确保在客户端断开时关闭响应
  req.on('close', () => {
    res.end(); // 客户端断开时清理响应
  });

  try {
    // 定义请求选项
    const options = {
      hostname: 'nami.agent.360.cn',
      path: '/api/v2/nami_agent_flow/prompt_optimization_gen', // 远程接口路径
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Accept': 'application/octet-stream',
        'Connection': 'keep-alive',
        "teamId": req.headers.teamid,
        'authorization': req.headers.authorization,
      },
    };

    // 发起请求
    // const reqPost = https.request(options, (response) => {
    const reqPost = https.request(options, (response) => {

      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Cache-Control', 'no-cache');


      if (response.statusCode !== 200) {
        res.status(response.statusCode!).json({ error: 'Failed to fetch stream data' });
        return;
      }

      // 逐块读取并传输流数据
      response.on('data', (chunk) => {
        console.log("🚀 ~ chunk:", chunk)
        res.write(chunk); // 将块数据写入响应
      });

      // 处理流结束
      response.on('end', () => {
        res.end(); // 完成响应
      });

      // 错误处理
      response.on('error', (err) => {
        console.error('Stream error:', err);
        res.status(500).json({ error: 'Stream error' });
      });
    });

    // 发送请求体数据 (POST 数据)
    const requestBody = JSON.stringify(req.body);
    reqPost.write(requestBody); // 写入 POST 请求体

    // 请求结束
    reqPost.end();

    // 监听客户端连接关闭
    req.on('close', () => {
      res.end();
    });

    // 错误处理
    reqPost.on('error', (err) => {
      console.error('Request error:', err);
      res.status(500).json({ error: 'Request error' });
    });

  } catch (error) {
    console.error('Error in handler:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}