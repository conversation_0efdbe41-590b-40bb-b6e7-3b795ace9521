// useCurrentProjectRole.ts
import { getRole, setRole, subscribeRole } from '@/utils/curProjectRoleStore';
import { useEffect, useState } from 'react';

const useCurrentProjectRole = () => {
  const [role, setRoleState] = useState(getRole());

  useEffect(() => {
    const unsubscribe = subscribeRole(setRoleState);
    return unsubscribe;
  }, []);

  return {
    userRole: role,
    updateRole: setRole,
  };
};

export default useCurrentProjectRole;
