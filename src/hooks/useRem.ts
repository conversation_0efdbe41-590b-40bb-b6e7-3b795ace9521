/*
 * @Author: yh
 * @Date: 2025-02-19 21:24:26
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-02-19 21:25:02
 * @FilePath: \prompt-web\src\hooks\useRem.ts
 */
import { useEffect } from 'react';

const useRem = (): void => {
  useEffect(() => {
    const setRem = (): void => {
      const html = document.documentElement;
      const width = html.clientWidth || window.innerWidth;
      // 设置根元素 font-size 为设备宽度的 1/10
      html.style.fontSize = (width / 375) * 100 + 'px'; // 设计稿宽度假设为375px
    };

    setRem(); // 初始化时设置

    window.addEventListener('resize', setRem); // 监听窗口大小变化

    // 清理函数，防止内存泄漏
    return () => {
      window.removeEventListener('resize', setRem);
    };
  }, []); // 空依赖数组，表示只在组件挂载和卸载时执行一次
};

export default useRem;
