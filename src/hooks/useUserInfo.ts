import { reqUserInfo } from '@/service/common';
import { isNullOrEmpty } from '@/utils/common';
import { useEffect, useState } from 'react';
import * as constants from "@/constants/appConstants";
import { UserInfo, userInfoAtom } from '@/atoms/userInfoAtoms';
import { useRecoilState } from 'recoil';

let fetchPromise: Promise<UserInfo | undefined> | null = null;
const fetchUserInfo = async (): Promise<UserInfo | undefined> => {
  if (fetchPromise) return fetchPromise;

  fetchPromise = (async () => {
    try {
      const res = await reqUserInfo({});
      if (isNullOrEmpty(res)) {
        throw new Error('用户信息初始化失败');
      }
      localStorage.setItem(constants.prompt_userId, res.user_id);
      localStorage.setItem(constants.prompt_userName, res.username);
      localStorage.setItem(constants.prompt_src, res.src);
      localStorage.setItem(constants.prompt_enable_canvas, res.enable_canvas)
      const resUserName = res.username;
      const resSrc = res.src;
      console.log('useUserInfoHooks', resSrc)
      let localSystemRole = localStorage.getItem(constants.prompt_systemRole)
      let system_role = String(res.system_role)
      if (localSystemRole !== system_role) {
        localStorage.setItem(constants.prompt_systemRole, res.system_role);
        // if (res.system_role == 0 || res.system_role == 2) {
        //   setBackGroundRouterList(localStorage.getItem(constants.prompt_src) ==  'agent' ? systemManagerRouters: IsNotAgentsystemManagerRouters)
        // } else {
        //   setBackGroundRouterList(localStorage.getItem(constants.prompt_src) ==  'agent' ? systemSuperManagerRouters: IsNotAgentsystemSuperManagerRouters)
        // }
      }
      return {
        ...res,
        user_id: res.user_id,
        username: resUserName || localStorage.getItem(constants.prompt_userName),
        email: res.email,
        phone_number: res.phone_number,
        system_role: res.system_role,
        src: resSrc || localStorage.getItem(constants.prompt_src),
      };
    } catch (e) {
      console.error('用户信息获取失败:', e);
      return undefined;
    } finally {
      fetchPromise = null; // 下次还能重新发
    }
  })();

  return fetchPromise;
};



const useUserInfo = () => {
  const [userInfo, setUserInfo] = useRecoilState(userInfoAtom);

  useEffect(() => {
    if (userInfo === undefined) {
      fetchUserInfo().then((data) => {
        if (data) setUserInfo(data);
      });
    }
  }, []);


  return {
    userInfo,
  }
};

export default useUserInfo;
