import { useEffect, RefObject } from 'react';

/**
 * 点击元素外部时触发回调的Hook
 * 当前的应用场景：利用事件冒泡机制，实现点击外部关闭组件
 * @param ref 需要监听的元素引用
 * @param callback 点击外部时触发的回调函数
 * @param excludeSelectors 排除的选择器列表，点击这些元素时不会触发回调
 */
const useClickOutside = (
  ref: RefObject<HTMLElement>,
  callback: () => void,
  excludeSelectors: string[] = []
): void => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击的元素是否在排除列表中
      const isExcluded = excludeSelectors.some(selector => {
        const elements = document.querySelectorAll(selector);
        return Array.from(elements).some(el => el.contains(event.target as Node));
      });

      // 检查是否点击了antd的下拉菜单（通常在body下的弹出层中），如果是其他组件库，需要注意添加
      const isAntdDropdown = 
        (event.target as Element).closest('.ant-select-dropdown') || 
        (event.target as Element).closest('.ant-dropdown') ||
        (event.target as Element).closest('.ant-picker-dropdown') ||
        (event.target as Element).closest('.ant-cascader-dropdown');

      // 如果点击的不是目标元素内部且不在排除列表中且不是antd下拉菜单，则触发回调
      if (ref.current && !ref.current.contains(event.target as Node) && !isExcluded && !isAntdDropdown) {
        callback();
      }
    };

    // 添加事件监听
    document.addEventListener('mousedown', handleClickOutside);
    
    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, callback, excludeSelectors]);
};

export default useClickOutside; 