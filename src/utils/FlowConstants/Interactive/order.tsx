export const pyCodeOrderPre = `
import json
import requests


def authentication(params: dict, url: str):
    """
    校验并获取token
    """
    response = requests.get(
        url=f"{url}/app_service/get_token",
        params=params
    ).json()
    return response["data"]["access_token"]


def get_flow_info(inputs: dict, url: str, id: str, token):
    """
    获取flow对应的回调id和block节点的索引列表
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    response = requests.post(
        url=f"{url}/chain_service/interaction/run/{id}",
        headers=headers,
        json=inputs
    ).json()
    return response["data"]["call_back_id"], response["data"].get("block_keys")

def get_flow_all_block_info(template_id: int, url: str, token: str):
    """
    获取flow各个节点的基础信息
    :param template_id: template_id
    :return: 返回结果
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    # 获取子流(PlayBook)block节点结果的接口和非子流flow的接口不一样
    response = requests.get(
        url=f"{url}/chain_service/interaction/flow_info/{template_id}",
        headers=headers
    ).json()
    return response

def get_flow_input_card_info(template_id: int, url: str, token: str):
    """
    获取flow各个节点的基础信息
    :param template_id: template_id
    :return: 返回结果
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    # 获取子流(PlayBook)block节点结果的接口和非子流flow的接口不一样
    response = requests.get(
        url=f"{url}/chain_service/interaction/input_card/{template_id}",
        headers=headers
    ).json()
    return response


def continue_run(call_back_id: str, block_key: str, outputs: dict, url: str, token: str):
    """
    获取flow各个节点的基础信息
    :param outputs: 当前节点的输出
    :param call_back_id: 回调id
    :param block_index: 节点id
    :return: 返回结果
    """
    params = {
        "outputs": outputs,
        "call_back_id": call_back_id,
        "block_index": block_key,
    }
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    # 获取子流(PlayBook)block节点结果的接口和非子流flow的接口不一样
    response = requests.post(
        url=f"{url}/chain_service/interaction/continue_run",
        json=params,
        headers=headers
    ).json()
    return response

def get_sse_result(call_back_id: str, block_key: str, url: str, token: str, trace_id: str = None):
    """
    获取flow各个block节点的结果，如果遇到子流递归到子流中将子流的结果也返回
    :param call_back_id: flow回调id
    :param block_key: flow流除开始节点的第一个节点索引
    :param url: 服务请求url
    :param token: 鉴权使用
    :param trace_id: 业务方生成的排查问题追踪的唯一ID 非必填
    :return: 返回结果
 """
    interaction_result = {}
    params = {
        "call_back_id": call_back_id,
        "block_key": block_key,
    }
    if trace_id:
        params["trace_id"] = trace_id
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    with requests.get(url + '/chain_service/streams/flow', headers=headers, params=params, stream=True) as response:
        if response.status_code != 200:
            print(f"Request failed with status code {response.status_code}")
            return

        # 逐行读取响应内容
        for line in response.iter_lines():
            if line:
                # 由于每一行都是以 "data: " 开头，去掉这个部分
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith("data: "):
                    # 获取真正的 JSON 数据部分
                    json_data = decoded_line[len("data: "):]

                    # 如果是结束标志 done，则退出
                    if json_data == "done":
                        print("Received done, stopping stream.")
                        break

                    # 解析 JSON 数据并打印
                    try:
                        data = json.loads(json_data)
                        interaction_result = data
                        print(data)  # 使用方可以在这里处理数据
                    except json.JSONDecodeError:
                        print(f"Error decoding JSON: {json_data}")
    return interaction_result`;

export const pyCodeOrderPro = `
    # 【第一步】 鉴权
    auth_param = {
        "app_id": request_inputs.get("app_id"),
        "app_secret": request_inputs.get("app_secret")
    }
    access_token = authentication(auth_param, base_url)

    # 【第二步】 获取flow必要的基础信息，包括block信息、开始节点卡片数据
    all_block_info = get_flow_all_block_info(int(request_inputs.get("template_id")), base_url, access_token)
    print(f"flow基础信息：{all_block_info}")
    input_card_info = get_flow_input_card_info(int(request_inputs.get("template_id")), base_url, access_token)
    print(f"初始卡片信息：{input_card_info}")

    # 【第三步】 回去flow对应的回调id和节点索引列表
    chain_inputs = {"chain_inputs": request_inputs.get("chain_inputs")}
    call_back_id, block_keys = get_flow_info(chain_inputs, base_url, template_id, access_token)

    # 【第四步】建立长链接获取SSE结果
    cur_block_index = block_keys[0]
    end_flag = False
    while not end_flag:
        result = get_sse_result(call_back_id, cur_block_index, base_url, access_token)
        block_type = result.get("message", {}).get("block_type", "")
        if block_type == "interaction":
            #交互式节点输出，用于用户交互式修改调整
            output = result.get("message", {}).get("content", {}).get("result", {}).get("output", {})
            #交互式节点下一个节点索引，用于后续查询
            next_block_index = result.get("message", {}).get("content", {}).get("next_block_index", "")
            # 当前节点索引，用于继续执行
            cur_block_index = result.get("block_key", "")
            # 继续执行
            continue_run(call_back_id, cur_block_index, output, base_url, access_token)
            cur_block_index = next_block_index
            interaction_result = f"block_id:{cur_block_index}当前节点为交互式节点，需用户进行交互后进行处理。当前脚本将直接调用接口继续执行"
            print(interaction_result)
        else:
            end_flag = True`;

export const curlCodeOrder = ``;

export const InterOrder = [
    {
        title: '【第一步】鉴权',
        func: 'GET',
        url: '/app_service/get_token',
        subTitles: '通过接口获取访问token',
        req: [
            {
                name: 'app_id',
                type: 'string',
                required: true,
                desc: 'app_id',
                location: 'query'
            },
            {
                name: 'app_secret',
                type: 'string',
                required: true,
                desc: '密钥',
                location: 'query'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>access_token</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token</div>
                            </div>
                        ),
                        type: 'string',
                        desc: 'token',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>expire</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token失效时间</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: 'token失效时间',
                        key: '0-0-1',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>当前请求时间戳</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '当前请求时间戳',
                        key: '0-0-2',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div style={{ marginRight: '16px' }}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "275px",
        resCode: `{   
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiJwcm9waGV0IiwiZXhwIjoxNzAwOTg2NzAwLCJkYXRhIjp7ImFwcF9pZCI6IkZsb3czXzU2MDUiLCJhcHBfc2VjcmV0IjoiMXVVSW5jVmtWanN2VXYxVE80UTdnSnJ2c2dHbjU4MW8iLCJjaGFpbl9ubyI6ImFjUEozRnc1MjMxMTE1VkhKclFwIn19.NwadYraaAEqhb3v3LxqY5V2r47uGBTPuIdPGrOMK7Kc",
        "expire": 1700986700,
        "timestamp": 1700186700
        },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129899
    }
}`,
    },
    {
        title: '【第二步】服务运行',
        func: 'POST',
        url: '/chain_service/interaction/run/{template_id}',
        subTitles: '运行服务',
        req: [
            {
                name: 'template_id',
                type:'string',
                required: true,
                desc: '实例编号',
                location: 'path'
            },
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                location: 'header',
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
            },
            {
                name: 'body',
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>body</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必须</span>
                    </div>
                ),
                key: '0-0',
                location: 'body',
                type: 'object',
                required: true,
                desc: '',
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>chain_inputs</span>
                                <span style={{ marginRight: '12px' }}>json</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>对应文生文中配置的槽位，json格式输入</div>
                            </div>
                        ),
                        key: '0-0-0',
                        location: 'body',
                        type: 'json',
                        required: true,
                        desc: '对应文生文中配置的槽位，json格式输入',
                        children: [
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>inputs</span>
                                        <span style={{ marginRight: '12px' }}>json</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>对应开始节点中的变量，以json格式输入</div>
                                    </div>
                                ),
                                key: '0-0-0-0',
                                location: 'body',
                                type: 'json',
                                required: true,
                                desc: '对应开始节点中的变量，以json格式输入',
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>sys_vars</span>
                                        <span style={{ marginRight: '12px' }}>json</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>系统变量</div>
                                    </div>
                                ),
                                key: '0-0-0-1',
                                location: 'body',
                                type: 'json',
                                required: true,
                                desc: '系统变量',
                            }
                        ]
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>trace_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>方便联调排查问题，建议随机生成</div>
                            </div>
                        ),
                        key: '0-0-1',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '方便联调排查问题，建议随机生成'
                    }
                ]
            }
        ],
        reqHeight: "260px",
        reqCode: `{
    "chain_inputs": {
        "inputs": {
            "key1":"v1",
            "key2":"v2"
        },
        "sys_vars": {
            "sys_uid": "1",
            "sys_source": "agent"
        }
    },
    "trace_id":"xxxxxxxxxx"
}`,
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>call_back_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div style={{ marginRight: '16px' }}>回调接口请求标识,获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '回调接口请求标识,获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>block_keys</span>
                                <span style={{ marginRight: '12px' }}>[string]</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div style={{ marginRight: '16px' }}>节点数组</div>
                            </div>
                        ),
                        type: '[string]',
                        desc: '节点数组',
                        key: '0-0-1',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "240px",
        resCode: `{
    "data": {
        "call_back_id": "xxxx-xxxx-xxx",
        "block_keys":["2776720", "2776721", "2597571", "2597570"]
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1690012160
    }
}`
    },
    {
        title: '【第三步】继续执行',
        func: 'POST',
        url: '/chain_service/interaction/continue_run',
        subTitles: '继续执行',
        req: [
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                location: 'header'
            },
            {
                name: 'outputs',
                type: 'object',
                required: true,
                desc: '输出',
                location: 'body'
            },
            {
                name: 'call_back_id',
                type: 'string',
                required: true,
                desc: 'call_back_id',
                location: 'body'
            },
            {
                name: 'block_index',
                type:'string',
                required: true,
                desc: '模块id',
                location: 'body'
            }
        ],
        reqHeight: "160px",
        reqCode: `{
    "outputs": {
        "output": "在茂密的森林里，猴子和松鼠是最好的朋友。一天，它们决定举办一场运动会。猴子擅长攀爬，松鼠擅长跳跃。它们比赛谁能最快到达树顶，谁就能赢得金牌。森林里的其他小动物们纷纷前来观赛，为它们加油助威。最终，猴子凭借灵活的身手赢得了比赛，而松鼠也因为敏捷的跳跃获得了大家的赞赏。赛后，它们一起分享了胜利的果实，成为了更好的朋友。"
    },
    "call_back_id": "6736-83f4b964-607b-11ef-96fd-dc4546d8ad4b",
    "block_index": "952980"      
}`,
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>call_back_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div style={{ marginRight: '16px' }}>获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>block_keys</span>
                                <span style={{ marginRight: '12px' }}>[string]</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div style={{ marginRight: '16px' }}>模块IDS 第一个和最后一个分别为 flow 输入和输出</div>
                            </div>
                        ),
                        type: '[string]',
                        desc: '模块IDS 第一个和最后一个分别为 flow 输入和输出',
                        key: '0-0-1',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "240px",
        resCode: `{
    "data": {
        "call_back_id": "xxxx-xxxx-xxx",
        "block_index": "2776721"
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1690012160
    }
}`
    },
    {
        title: '【第四步】Flow详情',
        func: 'POST',
        url: '/chain_service/interaction/flow_info/{template_id}',
        subTitles: 'Flow详情',
        req: [
            {
                name: 'template_id',
                type:'string',
                required: true,
                desc: '实例编号',
                location: 'path'
            },
            {
                name: 'Authorization',
                type:'string',
                required: true,
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                location: 'header'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "1600px",
        resCode: `{
    "data": {
        "template_id": 6736,
        "title": "测试交互技能（勿动）",
        "desc": "测试交互技能（勿动）",
        "images": "https://p4.ssl.qhimg.com/d/inn/e9fa4561f90e/flowIcon9.png",
        "deploy_id": 359,
        "team_id": 400,
        "version": "20240822152407-v2",
        "flow_config": {
            "blocks": {
                "952980": {
                    "id": "952980",
                    "name": "用户确认剧情",
                    "type": "interaction",
                    "block_version": 1,
                    "branch_id": "",
                    "inputs_key_mapping": [
                        {
                            "source": [
                                "721972",
                                "result",
                                "output"
                            ],
                            "target": "output"
                        }
                    ],
                    "inputs": {
                        "output": "在茂密的森林里，猴子、松鼠和兔子决定举办一场运动会。猴子灵活地攀爬树木，松鼠迅速地跳跃，兔子飞快地奔跑。最终，大家通过比赛更加团结友爱。"
                    },
                    "inputs_mark": {},
                    "inputs_desc": {
                        "output": "请修改相关剧情"
                    },
                    "block_output_key_mapping": [],
                    "execution_status": 0,
                    "block_output": {},
                    "display_message": [
                        {
                            "content": "",
                            "required": true,
                            "stage": "Execution"
                        },
                        {
                            "content": "",
                            "required": true,
                            "stage": "Completion"
                        }
                    ],
                    "result": "",
                    "show_block_result": true,
                    "status": 2
                },
                ...
            },
            "branches": {},
            "run_block_keys": [
                "802961",
                "465316",
                "589110",
                "721972",
                "952980",
                "637030",
                "530396"
            ],
            "block_keys": [
                "802961",
                "465316",
                "589110",
                "721972",
                "952980",
                "637030",
                "530396"
            ]
        }
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1724312527
    }
}`
    },
    {
        title: '【第五步】开始卡片',
        func: 'POST',
        url: '/chain_service/interaction/input_card/{template_id}',
        subTitles: '开始卡片',
        req: [
            {
                name: 'template_id',
                type: 'string',
                required: true,
                desc: '部署的文件名',
                location: 'path'
            },
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                location: 'header',
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "295px",
        resCode: `{
    "data": {
        "name": "123333333",
        "alist": [
            "8u8",
            "67i",
            "y5r"
        ]
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1739266055
    }                
}`
    },
    {
        title: '【第六步】有序SSE接口',
        func: 'GET',
        url: '/chain_service/streams/flow',
        subTitles: '有序SSE接口',
        req: [
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                location: 'header'
            },
            {
                name: 'call_back_id',
                type: 'string',
                required: true,
                desc: '轮询标识ID',
                location: 'query'
            },
            {
                name: 'block_key',
                type: 'string',
                required: true,
                desc: '模块ID 整个流除开始节点的第一个节点',
                location: 'query'
            },
            {
                name: 'trace_id',
                type: 'string',
                required: true,
                desc: '业务方生成的唯一ID，方便联调排查问题',
                location: 'query'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>object</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回数据结果</div>
                            </div>
                        ),
                        type: 'object',
                        desc: '返回数据结果',
                        key: '0-0-0',
                        required: true,
                        children: [
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>block_type</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>节点类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '节点类型',
                                key: '0-0-0-0',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>shard_id</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>分片ID</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '分片ID',
                                key: '0-0-0-1',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>shard_seq_id</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>分片序号</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '分片序号',
                                key: '0-0-0-2',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>content</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>具体内容</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '具体内容',
                                key: '0-0-0-3',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>errmsg</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>错误信息</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '错误信息',
                                key: '0-0-0-4',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>content_type</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>文本类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '文本类型',
                                key: '0-0-0-5',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>p_block_key</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>对应主流的Playbook节点</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '对应主流的Playbook节点',
                                key: '0-0-0-6',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>role</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>角色</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '角色',
                                key: '0-0-0-7',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>type</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '类型',
                                key: '0-0-0-8',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>status</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>状态</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '状态',
                                key: '0-0-0-9',
                                required: true,
                            }
                        ]
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>is_finish</span>
                                <span style={{ marginRight: '12px' }}>boolean</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>是否结束</div>
                            </div>
                        ),
                        type: 'boolean',
                        desc: '是否结束',
                        key: '0-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>call_back_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>轮询标识</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '轮询标识',
                        key: '0-0-2',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>block_key</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>模块ID</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块ID',
                        key: '0-0-3',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>seq_id</span>
                                <span style={{ marginRight: '12px' }}>int</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>序列号</div>
                            </div>
                        ),
                        type: 'int',
                        desc: '序列号',
                        key: '0-0-4',
                        required: true,
                    }
                ]
            }
        ],
        resHeight: "350px",
        resCode: `{
  "data": {
    "message": {
      "role": "assistant",
      "type": "error",
      "block_type": "llm",
      "content": "",
      "errmsg": "参数不合法",
      "content_type": "text",
      "status": "1"
    },
    "is_finish": true,
    "call_back_id": "7035-20200713f9ab14d9aecdc6d6f6fa1604",
    "block_key": "299205",
    "seq_id": 0
  }
}`
    }
]