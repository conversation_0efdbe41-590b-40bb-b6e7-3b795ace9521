/*
 * @Author: yh
 * @Date: 2025-04-23 15:03:49
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-05-08 15:03:37
 * @FilePath: \prompt-web\src\utils\FlowConstants\flowExportJsonFile.tsx
 */
import { reqOutputFlow, reqOutputVersionFlow } from "@/service/flow";
import { reqOutputV3Flow } from "@/service/flow3.0";
import { notify } from "@/utils/messageManager";

export default async function flowExportJsonFile(id: string, fileName: string, version?: string, flowVerison?: any) {
  try {
    let res: any;
    if (version) {
      res = await reqOutputVersionFlow({ template_id: id, version })
    } else {
      if(flowVerison == 5) {
        res = await reqOutputV3Flow({ template_id: id });
      }else {
        res = await reqOutputFlow({ template_id: id });
      }
    }
    if (res.status !== 200) {
      notify.error('下载失败')
      return
    }
    const blob = await res.blob()
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName.endsWith('.json') ? fileName : `${fileName}.json`; // 确保扩展名正确
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    notify.success("导出成功");
  } catch (e) {
    notify.error('下载失败')
  }
}