export const pyCodeFlowOrderPre = `
import json
import requests


def authentication(params: dict, url: str):
    """
    校验并获取token
    """
    response = requests.get(
        url=f"{url}/app_service/get_token",
        params=params
    ).json()
    return response["data"]["access_token"]


def get_flow_info(inputs: dict, url: str, id: str, token):
    """
    获取flow对应的回调id和block节点的索引列表
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    response = requests.post(
        url=f"{url}/chain_service/flow/{id}",
        headers=headers,
        json=inputs
    ).json()
    return response["data"]["call_back_id"], response["data"].get("block_keys")


def get_sse_result(call_back_id: str, block_key: str, url: str, token: str, trace_id: str = None):
    """
    获取flow各个block节点的结果，如果遇到子流递归到子流中将子流的结果也返回
    :param call_back_id: flow回调id
    :param block_key: flow流除开始节点的第一个节点索引
    :param url: 服务请求url
    :param token: 鉴权使用
    :param trace_id: 业务方生成的排查问题追踪的唯一ID 非必填
    :return: 返回结果
 """
    params = {
        "call_back_id": call_back_id,
        "block_key": block_key,
    }
    if trace_id:
        params["trace_id"] = trace_id
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    with requests.get(url + '/chain_service/streams/flow', headers=headers, params=params, stream=True) as response:
        if response.status_code != 200:
            print(f"Request failed with status code {response.status_code}")
            return

        # 逐行读取响应内容
        for line in response.iter_lines():
            if line:
                # 由于每一行都是以 "data: " 开头，去掉这个部分
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith("data: "):
                    # 获取真正的 JSON 数据部分
                    json_data = decoded_line[len("data: "):]

                    # 如果是结束标志 done，则退出
                    if json_data == "done":
                        print("Received done, stopping stream.")
                        break

                    # 解析 JSON 数据并打印
                    try:
                        data = json.loads(json_data)
                        print(data)  # 使用方可以在这里处理数据
                    except json.JSONDecodeError:
                        print(f"Error decoding JSON: {json_data}")`;

export const pyCodeFlowOrderPro = `
    # 【第一步】 鉴权
    auth_param = {
        "app_id": request_inputs.get("app_id"),
        "app_secret": request_inputs.get("app_secret")
    }
    access_token = authentication(auth_param, base_url)

    # 【第二步】 回去flow对应的回调id和节点索引列表
    chain_inputs = {"chain_inputs": request_inputs.get("chain_inputs")}
    call_back_id, block_keys = get_flow_info(chain_inputs, base_url, template_id, access_token)

    # 【第三步】建立长链接获取SSE结果
    get_sse_result(call_back_id, block_keys[0], base_url, access_token)`;

export const curlCodeFlowOrder = `
get_sse_result() {
    # 参数
    local call_back_id="$1"
    local block_key="$2"
    local url="$3"
    local token="$4"

    # 设置请求参数
    local params="call_back_id=\${call_back_id}&block_key=\${block_key}"

    # 请求的URL
    local request_url="\${url}/chain_service/streams/flow"

    # 打印请求的URL和参数
    echo "Request URL: \${request_url}?\${params}"

    # 发起curl请求，并打印curl的错误信息
    curl -s -N -L "\${request_url}?\${params}" \
    -H "Authorization: Bearer \${token}" \
    -H "Content-Type: application/json" | while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            decoded_line="\${line#data:}"
            echo "$decoded_line"
        fi
    done
}



# 【第一步】鉴权，获取 access_token
export access_token=$(curl -s -L -X GET "\${base_url}/app_service/get_token?app_id=\${app_id}&app_secret=\${app_secret}" | jq -r ".data.access_token")
echo "Access token: $access_token"

# 【第二步】 获取flow对应的回调id和节点索引列表
response=$(curl -s -L -X POST -H "Authorization: Bearer \${access_token}" -H "Content-Type: application/json" "\${base_url}/chain_service/flow/\${template_id}" -d "\${chain_inputs}")
echo "Service response: $response"

# 获取回调 ID
export call_back_id=$(echo "$response" | jq -r '.data.call_back_id')
echo "Callback ID: $call_back_id"
export block_key=$(echo "$response" | jq -r '.data.block_keys | .[1]')
echo "block_key: $block_key"

# 【第三步】获取SSE有序结果
get_sse_result "$call_back_id" "$block_key" "$base_url" "$access_token"`;

export const flowOrder = [
    {
        title: '【第一步】鉴权',
        func: 'GET',
        url: '/app_service/get_token',
        subTitles: '通过接口获取访问token',
        req: [
            {
                name: 'app_id',
                type: 'string',
                required: true,
                desc: 'app_id',
                location: 'query'
            },
            {
                name: 'app_secret',
                type: 'string',
                required: true,
                desc: '密钥',
                location: 'query'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>access_token</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token</div>
                            </div>
                        ),
                        type: 'string',
                        desc: 'token',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>expire</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token失效时间</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: 'token失效时间',
                        key: '0-0-1',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>当前请求时间戳</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '当前请求时间戳',
                        key: '0-0-2',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div style={{ marginRight: '16px' }}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "275px",
        resCode: `{   
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiJwcm9waGV0IiwiZXhwIjoxNzAwOTg2NzAwLCJkYXRhIjp7ImFwcF9pZCI6IkZsb3czXzU2MDUiLCJhcHBfc2VjcmV0IjoiMXVVSW5jVmtWanN2VXYxVE80UTdnSnJ2c2dHbjU4MW8iLCJjaGFpbl9ubyI6ImFjUEozRnc1MjMxMTE1VkhKclFwIn19.NwadYraaAEqhb3v3LxqY5V2r47uGBTPuIdPGrOMK7Kc",
        "expire": 1700986700,
        "timestamp": 1700186700
        },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129899
    }
}`,
    },
    {
        title: '【第二步】请求服务',
        func: 'POST',
        url: '/chain_service/flow/{template_id}',
        subTitles: '获取flow对应的回调id和节点索引列表',
        req: [
            {
                name: 'template_id',
                type: 'string',
                required: true,
                desc: '服务标识',
                location: 'path'
            },
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                location: 'header',
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
            },
            {
                name: 'body',
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>body</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必须</span>
                    </div>
                ),
                key: '0-0',
                location: 'body',
                type: 'object',
                required: false,
                desc: '',
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>chain_inputs</span>
                                <span style={{ marginRight: '12px' }}>json</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>对应文生文中配置的槽位，json格式输入</div>
                            </div>
                        ),
                        key: '0-0-0',
                        location: 'body',
                        type: 'json',
                        required: true,
                        desc: '对应文生文中配置的槽位，json格式输入',
                        children: [
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>inputs</span>
                                        <span style={{ marginRight: '12px' }}>json</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>对应开始节点中的变量，以json格式输入</div>
                                    </div>
                                ),
                                key: '0-0-0-0',
                                location: 'body',
                                type: 'json',
                                required: true,
                                desc: '对应开始节点中的变量，以json格式输入',
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>sys_vars</span>
                                        <span style={{ marginRight: '12px' }}>json</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>系统变量</div>
                                    </div>
                                ),
                                key: '0-0-0-1',
                                location: 'body',
                                type: 'json',
                                required: true,
                                desc: '系统变量',
                            }
                        ]
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>trace_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>方便联调排查问题，建议随机生成</div>
                            </div>
                        ),
                        key: '0-0-1',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '方便联调排查问题，建议随机生成'
                    }
                ]
            }
        ],
        reqHeight: "260px",
        reqCode: `{
    "chain_inputs": {
        "inputs": {
            "key1":"v1",
            "key2":"v2"
        },
        "sys_vars": {
            "sys_uid": "1",
            "sys_source": "agent"
        }
    },
    "trace_id":"xxxxxxxxxx"
}`,
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>call_back_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div style={{ marginRight: '16px' }}>回调接口请求标识,获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '回调接口请求标识,获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>block_keys</span>
                                <span style={{ marginRight: '12px' }}>[string]</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div style={{ marginRight: '16px' }}>节点数组</div>
                            </div>
                        ),
                        type: '[string]',
                        desc: '节点数组',
                        key: '0-0-1',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "240px",
        resCode: `{
    "data": {
        "call_back_id": "xxxx-xxxx-xxx",
        "block_keys":["2776720", "2776721", "2597571", "2597570"]
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1690012160
    }
}`
    },
    {
        title: '【第三步】Flow整体SSE接口',
        func: 'GET',
        url: '/chain_service/streams/flow',
        subTitles: '获取flow执行流式输出有序结果',
        req: [
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                location: 'header'
            },
            {
                name: 'call_back_id',
                type: 'string',
                required: true,
                desc: '服务接口中返回的轮询标识',
                location: 'query'
            },
            {
                name: 'block_key',
                type: 'string',
                required: true,
                desc: '模块标识ID',
                location: 'query'
            },
            {
                name: 'trace_id',
                type: 'string',
                required: false,
                desc: '业务方生成的唯一ID，方便联调排查问题，建议随机生成',
                location: 'query'
            },
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>object</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回数据结果</div>
                            </div>
                        ),
                        type: 'object',
                        desc: '返回数据结果',
                        key: '0-0-0',
                        required: true,
                        children: [
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>block_type</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>节点类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '节点类型',
                                key: '0-0-0-0',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>shard_id</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>分片ID</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '分片ID',
                                key: '0-0-0-1',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>shard_seq_id</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>分片序号</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '分片序号',
                                key: '0-0-0-2',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>content</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>具体内容</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '具体内容',
                                key: '0-0-0-3',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>errmsg</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>错误信息</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '错误信息',
                                key: '0-0-0-4',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>content_type</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>文本类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '文本类型',
                                key: '0-0-0-5',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>p_block_key</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>对应主流的Playbook节点</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '对应主流的Playbook节点',
                                key: '0-0-0-6',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>role</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>角色</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '角色',
                                key: '0-0-0-7',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>type</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '类型',
                                key: '0-0-0-8',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>status</span>
                                        <span style={{ marginRight: '12px' }}>string</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>状态</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '状态',
                                key: '0-0-0-9',
                                required: true,
                            }
                        ]
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>is_finish</span>
                                <span style={{ marginRight: '12px' }}>boolean</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>是否结束</div>
                            </div>
                        ),
                        type: 'boolean',
                        desc: '是否结束',
                        key: '0-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>call_back_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>轮询标识</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '轮询标识',
                        key: '0-0-2',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>block_key</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>模块ID</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块ID',
                        key: '0-0-3',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>seq_id</span>
                                <span style={{ marginRight: '12px' }}>int</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>序列号</div>
                            </div>
                        ),
                        type: 'int',
                        desc: '序列号',
                        key: '0-0-4',
                        required: true,
                    }
                ]
            }
        ],
        resHeight: "340px",
        resCode: `{
    data: {
        "message": {
            "role": "assistant",
            "type": "answer",
            "block_type": "llm",
            "content": "是",
            "errmsg": "",
            "content_type": "text",
            "status": "1"
        },
        "is_finish": false,
        "call_back_id": "7035-e0684750-95d8-11ef-aa77-dc4546d8ad4b",
        "block_key": "299205",
        "seq_id": 2
    }
}`
    }
]

