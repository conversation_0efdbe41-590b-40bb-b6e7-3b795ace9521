export const pyCodeFlowSamePre = `
import json
import requests


def authentication(params: dict, url: str):
    """
    校验并获取token
    """
    response = requests.get(
        url=f"{url}/app_service/get_token",
        params=params
    ).json()
    return response["data"]["access_token"]

def get_final_result(inputs: dict, url: str, token: str, id: str):
    """
    获取flow调用的最终结果
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    response = requests.post(
        url=f"{url}/chain_service/flow/sync/{id}",
        headers=headers,
        json=inputs
    )
    return response`;

export const pyCodeFlowSamePro = `
    # 【第一步】 鉴权
    auth_param = {
        "app_id": request_inputs.get("app_id"),
        "app_secret": request_inputs.get("app_secret")
    }
    access_token = authentication(auth_param, base_url)

    # 【第二步】 同步获取flow执行的最终结果
    chain_inputs = {"chain_inputs": request_inputs.get("chain_inputs")}
    res = get_final_result(chain_inputs, base_url, access_token, template_id)
    if res.status_code != 200:
        print(f"Request failed with status code {res.status_code}")
    else:
        # 结果调用方可自行处理
        res = res.json()
        if res['data']['code'] == 0:
            print(res['data']['data'])
        else:
            print(f"结果获取失败，失败原因为{res['data']['message']}")`;

export const curlCodeFlowSame = `
# 【第一步】鉴权，获取 access_token
export access_token=$(curl -s -L -X GET "\${base_url}/app_service/get_token?app_id=\${app_id}&app_secret=\${app_secret}" | jq -r ".data.access_token")
echo "Access token: $access_token"

# 【第二步】同步获取flow执行的结果
response=$(curl -s -L -X POST -H "Authorization: Bearer \${access_token}" -H "Content-Type: application/json" "\${base_url}/chain_service/flow/sync/\${template_id}" -d "\${chain_inputs}")
export res=$(echo "$response" | jq -r '.data.data')
echo "res: $res"
`;

export const flowSame = [
    {
        title: '【第一步】鉴权',
        func: 'GET',
        url: '/app_service/get_token',
        subTitles: '通过接口获取访问token',
        req: [
            {
                name: 'app_id',
                type: 'string',
                required: true,
                desc: 'app_id',
                location: 'query'
            },
            {
                name: 'app_secret',
                type: 'string',
                required: true,
                desc: '密钥',
                location: 'query'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>access_token</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token</div>
                            </div>
                        ),
                        type: 'string',
                        desc: 'token',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>expire</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token失效时间</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: 'token失效时间',
                        key: '0-0-1',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>当前请求时间戳</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '当前请求时间戳',
                        key: '0-0-2',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div style={{ marginRight: '16px' }}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "275px",
        resCode: `{   
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiJwcm9waGV0IiwiZXhwIjoxNzAwOTg2NzAwLCJkYXRhIjp7ImFwcF9pZCI6IkZsb3czXzU2MDUiLCJhcHBfc2VjcmV0IjoiMXVVSW5jVmtWanN2VXYxVE80UTdnSnJ2c2dHbjU4MW8iLCJjaGFpbl9ubyI6ImFjUEozRnc1MjMxMTE1VkhKclFwIn19.NwadYraaAEqhb3v3LxqY5V2r47uGBTPuIdPGrOMK7Kc",
        "expire": 1700986700,
        "timestamp": 1700186700
        },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129899
    }
}`,
    },
    {
        title: '【第二步】获取数据结果',
        func: 'POST',
        url: '/chain_service/flow/sync/{template_id}',
        subTitles: '同步获取flow执行的最终结果',
        req: [
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '单独的token鉴权，使用QT Cookie去换取token，紧急上线暂时放行 如 "Bearer token"',
                location: 'header'
            },
            {
                name: 'chain_inputs',
                type: 'json',
                required: true,
                desc: '请求参数',
                location: 'body'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{marginRight: '12px'}}>data</span>
                        <span style={{marginRight: '12px'}}>object</span>
                        <span style={{marginRight: '16px'}}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc:'',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>code</span>
                                <span style={{marginRight: '12px'}}>integer</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>code码</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: 'code码',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>message</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>提示文本</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '提示文本',
                        key: '0-0-1',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>data</span>
                                <span style={{marginRight: '12px'}}>object</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>返回数据</div>
                            </div>
                        ),
                        type: 'object',
                        desc: '返回数据',
                        key: '0-0-2',
                        required: false,
                    }
                ]
               },
               {
                title: (
                    <div>
                        <span style={{marginRight: '12px'}}>context</span>
                        <span style={{marginRight: '12px'}}>object</span>
                        <span style={{marginRight: '16px'}}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc:'',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>message</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>code</span>
                                <span style={{marginRight: '12px'}}>integer</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div style={{marginRight: '16px'}}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>timestamp</span>
                                <span style={{marginRight: '12px'}}>integer</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "430px",
        resCode: `{
    "data": {
        "code": 0,
        "message": "success",
        "data": {
            "list": [
                {
                    "title": "小红书家居改造案例，提升生活品质",
                    "xiaohongshu": "小红书家居改造案例，提升生活品质",
                    "shipinhao": "小红书家居改造案例，提升生活品质",
                    "douyin": "小红书家居改造案例，提升生活品质"
                },
                ...
            ]
        },
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1730481552
    }
}`
    }
]