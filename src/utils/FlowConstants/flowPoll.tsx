export const pyCodeFlowPollPre = `
import json
import time
import requests
from typing import Any

def authentication(params: dict, url: str):
    """
    校验并获取token
    """
    response = requests.get(
        url=f"{url}/app_service/get_token",
        params=params
    ).json()
    return response["data"]["access_token"]


def get_flow_info(inputs: dict, url: str, id: str, token):
    """
    获取flow对应的回调id和block节点的索引列表
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    response = requests.post(
        url=f"{url}/chain_service/flow/{id}",
        headers=headers,
        json=inputs
    ).json()
    return response["data"]["call_back_id"], response["data"].get("block_keys")

def get_llm_sse_result(call_back_id: str, block_key: str, url: str, token: str):
    """
     获取llm节点的流式输出结果
    :param call_back_id: flow回调id
    :param block_key: flow对应的具体block节点的索引值
    :param url: 服务请求url
    :param token: 鉴权使用
    :return: 返回结果
    """
    params = {
        "call_back_id": call_back_id,
        "block_key": block_key,
    }
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    with requests.post(url + '/chain_service/streams', headers=headers, json=params, stream=True) as response:
        if response.status_code != 200:
            yield {"block_key": block_key, "result": f"Request failed: {response.text}", "status": False}
            return
        # 逐行读取响应内容
        for line in response.iter_lines():
            if line:
                # 由于每一行都是以 "data: " 开头，去掉这个部分
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith("data: "):
                    # 获取真正的 JSON 数据部分
                    json_data = decoded_line[len("data: "):]

                    # 如果是结束标志 done，则退出
                    if json_data == "done":
                        yield {"block_key": block_key, "result": "Received done, stopping stream.", "status": True}
                        break

                    # 解析 JSON 数据并打印
                    data = json.loads(json_data)
                    yield {"block_key": block_key, "result": data, "status": True}

def get_block_result(call_back_id: str, block_key: str, url: str, token: str, sub_flag: bool, trace_id: str = None):
    """
    获取某个特定block节点的结果
    :param call_back_id: flow回调id
    :param block_key: flow对应的具体block节点的索引值
    :param url: 服务请求url
    :param token: 鉴权使用
    :param sub_flag: 当前flow是否为子流
    :param trace_id: 业务方生成的排查问题追踪的唯一ID 非必填
    :return: 返回结果
    """
    params = {
        "call_back_id": call_back_id,
        "block_key": block_key,
    }
    if trace_id and not sub_flag:
        params["trace_id"] = trace_id
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    # 获取子流(PlayBook)block节点结果的接口和非子流flow的接口不一样
    response = requests.get(
        url=f"{url}/chain_service" + ("/flow_plugin/block_detail" if sub_flag else "/block_detail"),
        headers=headers,
        params=params
    ).json()
    return response


def get_result(call_back_id: str, block_keys: list, url: str, token: str, sub_flag: bool, trace_id: str = None):
    """
    获取flow各个block节点的结果，如果遇到子流递归到子流中将子流的结果也返回
    :param call_back_id: flow回调id
    :param block_keys: flow对应的节点索引列表
    :param url: 服务请求url
    :param token: 鉴权使用
    :param sub_flag: 当前flow是否为子流
    :param trace_id: 业务方生成的排查问题追踪的唯一ID 非必填
    :return: 返回结果
 """
    max_retries = 10
    delay = 2
    block_key = block_keys[0]  # 从第二个节点开始, 第一个节点为开始节点
    idx = 1
    while block_key:
        # 获取下一个节点的索引
        if block_key in block_keys:
            idx += 1
        for count in range(max_retries):
            resp = get_block_result(call_back_id, block_key, url, token, sub_flag, trace_id)
            block_info, status = resp["data"], resp["data"]["status"]
            if status == 1:  # 未执行
                time.sleep(delay)
                continue
            if status == 2:  # 执行中
                if block_info.get('type', None) == 'flow_plugin':
                    # 当子流节点较长时，在执行中可以直接获取子流中所有block节点进行步步递进，而无需等待整个子流全部执行完成后再去依次获取子流block节点的结果
                    sub_call_back_id = block_info.get('c_log_id')
                    sub_block_keys = block_info.get('c_block_keys')
                    if not sub_call_back_id or not sub_block_keys:
                        time.sleep(delay)
                        continue
                    for sub_res in get_result(sub_call_back_id, sub_block_keys, url, token, True):
                        yield sub_res
                    for i in range(max_retries):
                        # 依次获取到子流中所有block节点的结果后，需要获取子流最终的结果后再去获取子流在主流程中后续block节点的key
                        sub_res = get_block_result(call_back_id, block_key, url, token, sub_flag, trace_id)
                        sub_status = sub_res["data"]["status"]
                        if sub_status <= 2:
                            time.sleep(delay)
                            continue
                        output = {"block_key": block_key, "result": sub_res['data'].get('result'), "status": True}
                        yield output
                        block_key = sub_res['data'].get('next_block_index', None)
                        break
                else:
                    time.sleep(delay)
                    continue
            elif status == 3:
                # 当子流节点较短时，可能轮询到子流时已经执行完成，则可以直接依次获取子流block节点的结果
                if block_info.get('type', None) == 'flow_plugin':
                    sub_call_back_id = block_info.get('c_log_id')
                    sub_block_keys = block_info.get('c_block_keys')
                    for sub_res in get_result(sub_call_back_id, sub_block_keys, url, token, True):
                        yield sub_res
                    output = {"block_key": block_key, "result": block_info.get('result'), "status": True}
                    yield output
                elif block_info.get('type', None) == 'llm':
                    for sse_res in get_llm_sse_result(call_back_id, block_key, url, token):
                        yield sse_res
                else:
                    # 由使用方自行决定是否根据节点结果输出的设置开关进行展示，如果和设置的保持一致就需添加下述注释的条件判断，如果所有节点结果都输出则无需关心
                    # if block_info["show_block_result"]:
                    output = {"block_key": block_key, "result": block_info.get('result'), "status": True}
                    yield output
                block_key = block_info.get('next_block_index', None)
            elif status == 4:  # 执行失败
                output = {"block_key": block_key, "result": block_info, "status": False}
                yield output
                if idx < len(block_keys):
                    block_key = block_keys[idx]
                else:
                    block_key = None
            break
        if count >= max_retries:
            output = {"block_key": block_key, "result": f'节点结果请求超过最大次数{max_retries}', "status": False}
            yield output
            if idx < len(block_keys):
                block_key = block_keys[idx]
            else:
                block_key = None`;

export const pyCodeFlowPollPro = `# 【第一步】 鉴权
    auth_param = {
        "app_id": request_inputs.get("app_id"),
        "app_secret": request_inputs.get("app_secret")
    }
    access_token = authentication(auth_param, base_url)

    # 【第二步】 执行flow获取对应的回调id和节点索引列表
    chain_inputs = {"chain_inputs": request_inputs.get("chain_inputs")}
    call_back_id, block_keys = get_flow_info(chain_inputs, base_url, template_id, access_token)

    # 【第三步】服务轮询接口（获取每个流程节点结果)
    for res in get_result(call_back_id, block_keys, base_url, access_token, False):
        print(res)`;

export const curlCodeFlowPoll = `
MAX_RETRIES=20
DELAY=2

# 重试请求函数
repeat_request() {
    local call_back_id="$1"
    local block_key="$2"
    local sub_flag="$3"
    local trace_id="$4"
    local num=0
    local max_retries=$MAX_RETRIES
    local delay=$DELAY
    local url="$base_url"
    local response status result

    while [ $num -le $max_retries ]; do
        # 获取节点结果
        response=$(get_block_result "$call_back_id" "$block_key" "$url" "$sub_flag" "$trace_id")
        status=$(echo "$response" | jq -r ".data.status")
        result=$(echo "$response" | jq -r ".data")

        if [ "$status" -le 2 ]; then
            # 如果执行中，延迟重试
            sleep $delay
            ((num++))
        elif [ "$status" -eq 3 ]; then
            # 执行成功
            echo "$result"
            return 0
        elif [ "$status" -eq 4 ]; then
            # 执行失败
            echo "接口执行失败，原因：$result"
            return 1
        else
            # 执行异常
            echo "状态异常，status=$status，原因：$result"
            return 1
        fi
    done

    echo "接口请求超过最大次数$max_retries"
    return 1
}

# 获取单个 block 节点结果
get_block_result() {
    local call_back_id="$1"
    local block_key="$2"
    local url="$3"
    local sub_flag="$4"
    local trace_id="$5"
    
    local params="call_back_id=$call_back_id&block_key=$block_key"
    
    local endpoint="/chain_service/block_detail"
    if [ "$sub_flag" -eq 1 ]; then
        endpoint="/chain_service/flow_plugin/block_detail"
    fi
    response=$(curl -s -L -X GET "$url$endpoint?$params" -H "Authorization: Bearer $access_token" -H "Content-Type: application/json")
    echo "$response"
}

# 获取多个 block 节点结果
get_result() {
    local call_back_id="$1"
    local sub_flag="$2"
    local trace_id="$3"
    local block_keys=("\${@:4}")
    local idx=1
    local block_key="\${block_keys[$idx]}"
    while [ -n "$block_key" ]; do
	local response
        response=$(repeat_request "$call_back_id" "$block_key" "$sub_flag" "$trace_id")
        if [ $? -eq 0 ]; then
            # 如果返回的是字典，继续处理
            if echo "$response" | jq -e 'has("type")' > /dev/null; then
                type=$(echo "$response" | jq -r '.type')
                if [ "$type" == "flow_plugin" ]; then
                    sub_call_back_id=$(echo "$response" | jq -r '.c_log_id')
                    sub_block_keys=$(echo "$response" | jq -r '.c_block_keys | @sh')
                    eval "sub_block_keys=($sub_block_keys)"
                    get_result "$sub_call_back_id"  1 "$trace_id"  "\${sub_block_keys[@]}"
                else
                    # 输出结果
                    result=$(echo "$response" | jq -r '.result')
                    echo "{\"block_key\": \"$block_key\", \"result\": \"$result\", \"status\": true}"
                fi
            else
                echo "{\"block_key\": \"$block_key\", \"result\": \"$response\", \"status\": false}"
            fi
        else
            echo "{\"block_key\": \"$block_key\", \"result\": \"$response\", \"status\": false}"
        fi

        if [[ " \${block_keys[@]} " =~ " \${block_key} " ]]; then
            ((idx++))  
        fi
        
        # 检查 block_info 是否是字典
        if [[ $(echo "$response" | jq -e 'type == "object"') == "true" ]]; then
            block_key=$(echo "$response" | jq -r '.next_block_index')
        elif [[ idx -lt \${#block_keys[@]} ]]; then
            block_key="\${block_keys[$idx]}"
        else
            block_key=""
        fi
    done
}


# 【第一步】鉴权，获取 access_token
export access_token=$(curl -s -L -X GET "\${base_url}/app_service/get_token?app_id=\${app_id}&app_secret=\${app_secret}" | jq -r ".data.access_token")
echo "Access token: $access_token"

# 【第二步】 获取flow对应的回调id和节点索引列表
response=$(curl -s -L -X POST -H "Authorization: Bearer \${access_token}" -H "Content-Type: application/json" "\${base_url}/chain_service/flow/\${template_id}" -d "\${chain_inputs}")
echo "Service response: $response"

# 获取回调 ID
export call_back_id=$(echo "$response" | jq -r '.data.call_back_id')
echo "Callback ID: $call_back_id"

# 获取 block_keys
block_keys=()
while IFS= read -r key; do
    block_keys+=("$key")
done <<< "$(echo "$response" | jq -r '.data.block_keys[]')"

# 输出 block_keys 以查看
echo "Block keys: \${block_keys[@]}"  

sleep 2

# 【第三步】服务轮询接口（获取每个流程节点结果）
get_result "$call_back_id" 0 "" "\${block_keys[@]}"`;

export const flowPoll = [
    {
        title: '【第一步】鉴权',
        func: 'GET',
        url: '/app_service/get_token',
        subTitles: '通过接口获取访问token',
        req: [
            {
                name: 'app_id',
                type: 'string',
                required: true,
                desc: 'app_id',
                location: 'query'
            },
            {
                name: 'app_secret',
                type: 'string',
                required: true,
                desc: '密钥',
                location: 'query'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>access_token</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token</div>
                            </div>
                        ),
                        type: 'string',
                        desc: 'token',
                        key: '0-0-0',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>expire</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>token失效时间</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: 'token失效时间',
                        key: '0-0-1',
                        required: false,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>当前请求时间戳</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '当前请求时间戳',
                        key: '0-0-2',
                        required: false,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div style={{ marginRight: '16px' }}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "275px",
        resCode: `{   
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiJwcm9waGV0IiwiZXhwIjoxNzAwOTg2NzAwLCJkYXRhIjp7ImFwcF9pZCI6IkZsb3czXzU2MDUiLCJhcHBfc2VjcmV0IjoiMXVVSW5jVmtWanN2VXYxVE80UTdnSnJ2c2dHbjU4MW8iLCJjaGFpbl9ubyI6ImFjUEozRnc1MjMxMTE1VkhKclFwIn19.NwadYraaAEqhb3v3LxqY5V2r47uGBTPuIdPGrOMK7Kc",
        "expire": 1700986700,
        "timestamp": 1700186700
        },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129899
    }
}`,
    },
    {
        title: '【第二步】Flow执行',
        func: 'POST',
        url: '/chain_service/flow/{template_id}',
        subTitles: 'flow执行并获取flow对应的回调id和节点索引列表',
        req: [
            {
                name: 'template_id',
                type: 'string',
                required: true,
                desc: '服务标识',
                location: 'path'
            },
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '服务标识 如 "Bearer token"',
                location: 'header'
            },
            {
                name: 'body',
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>body</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必须</span>
                    </div>
                ),
                key: '0-0',
                location: 'body',
                type: 'object',
                required: false,
                desc: '',
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>chain_inputs</span>
                                <span style={{ marginRight: '12px' }}>json</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>对应文生文中配置的槽位，json格式输入</div>
                            </div>
                        ),
                        key: '0-0-0',
                        location: 'body',
                        type: 'json',
                        required: true,
                        desc: '对应文生文中配置的槽位，json格式输入',
                        children: [
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>inputs</span>
                                        <span style={{ marginRight: '12px' }}>json</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>对应开始节点中的变量，以json格式输入</div>
                                    </div>
                                ),
                                key: '0-0-0-0',
                                location: 'body',
                                type: 'json',
                                required: true,
                                desc: '对应开始节点中的变量，以json格式输入',
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{ marginRight: '12px' }}>sys_vars</span>
                                        <span style={{ marginRight: '12px' }}>json</span>
                                        <span style={{ marginRight: '16px' }}>必需</span>
                                        <div>系统变量</div>
                                    </div>
                                ),
                                key: '0-0-0-1',
                                location: 'body',
                                type: 'json',
                                required: true,
                                desc: '系统变量',
                            }
                        ]
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>trace_id</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>方便联调排查问题，建议随机生成</div>
                            </div>
                        ),
                        key: '0-0-1',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '方便联调排查问题，建议随机生成'
                    }
                ]
            }
        ],
        reqHeight: "260px",
        reqCode: `{
    "chain_inputs": {
        "inputs": {
            "key1":"v1",
            "key2":"v2"
        },
        "sys_vars": {
            "sys_uid": "1",
            "sys_source": "agent"
        }
    },
    "trace_id":"xxxxxxxxxx"
}`,
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>call_back_id</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常',
                        key: '0-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>block_keys</span>
                                <span style={{marginRight: '12px'}}>array</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块IDS 第一个和最后一个分别为 flow 输入和输出</div>
                            </div>
                        ),
                        type: 'array',
                        desc: '模块IDS 第一个和最后一个分别为 flow 输入和输出',
                        key: '0-0-1',
                        required: true,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "200px",
        resCode: `{
    "data": {
        "call_back_id": "xxxx-xxxx-xxx",
        "block_keys":["2776720", "2776721", "2597571", "2597570"]
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1690012160
    }       
}`
    },
    {
        title: '【第三步】服务轮询接口',
        func: 'GET',
        url: '/chain_service/block_detail',
        subTitles: 'flow主流程各个节点的结果获取接口',
        req: [
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '服务标识 如 "Bearer token"',
                location: 'header'
            },
            {
                name: 'call_back_id',
                type: 'string',
                required: true,
                desc: '轮询标识',
                location: 'query'
            },
            {
                name: 'block_key',
                type:'string',
                required: true,
                desc: '模块ID',
                location: 'query'
            },
            {
                name: 'trace_id',
                type: 'string',
                required: false,
                desc: '业务方生成的唯一ID，方便联调排查问题，建议随机生成',
                location: 'query'
            }
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>result</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块结果</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块结果',
                        key: '0-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>status</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块执行状态 2:执行中 3:执行成功 4:执行失败</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块执行状态 2:执行中 3:执行成功 4:执行失败',
                        key: '0-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>block_key</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块结果</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模型编号',
                        key: '0-0-2',
                        required: true,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "300px",
        resCode: `{
    "data": {
        "result": "xxxxx",
        "status": 3,
        "block_key": "543841"
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1690012160
    }
}`,
    },
    {
        title: '【其他】调用子流',
        func: 'GET',
        url: '/chain_service/flow_plugin/block_detail',
        subTitles: '如果flow里调用子流，子流结果获取',
        req: [
            {
                name: 'Authorization',
                type: 'string',
                required: true,
                desc: '服务标识 如 "Bearer token"',
                location: 'header'
            },
            {
                name: 'call_back_id',
                type: 'string',
                required: true,
                desc: '轮询标识，使用主流程pb节点的c_log_id',
                location: 'query'
            },
            {
                name: 'block_key',
                type: 'string',
                required: true,
                desc: '模块ID',
                location: 'query'
            },
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>result</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块结果</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块结果',
                        key: '0-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>type</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块类型 ，当类型为flow_plugin时，返回值将包含子流程查询必要c_log_id和c_block_keys</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块类型 ，当类型为flow_plugin时，返回值将包含子流程查询必要c_log_id和c_block_keys',
                        key: '0-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>display_message</span>
                                <span style={{marginRight: '12px'}}>list</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>提示信息</div>
                            </div>
                        ),
                        type: 'list',
                        desc: '提示信息',
                        key: '0-0-2',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>status</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块执行状态 1:未执行 2:执行中 3:执行成功 4:执行失败</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块执行状态 1:未执行 2:执行中 3:执行成功 4:执行失败',
                        key: '0-0-3',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>block_key</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模型编号</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模型编号',
                        key: '0-0-4',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>show_block_result</span>
                                <span style={{marginRight: '12px'}}>boolean</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>是否前端显示，由使用方自行配置</div>
                            </div>
                        ),
                        type: 'boolean',
                        desc: '是否前端显示，由使用方自行配置',
                        key: '0-0-5',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>next_block_index</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>当前block的下一个节点index，运行至条件节点将显示分支内节点，当前节点运行完成后返回</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '当前block的下一个节点index，运行至条件节点将显示分支内节点，当前节点运行完成后返回',
                        key: '0-0-6',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>finish_reason</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>大模型节点专用，用来表示大模型异常原因</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '大模型节点专用，用来表示大模型异常原因',
                        key: '0-0-7',
                        required: true,
                    },
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "1000px",
        resCode: `{
    "data": {
        "result": {
            "output": {
                "test": "1234512345"
            }
        },
        "type": "code",
        "status": 3,
        "block_key": "719505",
        "display_message": [
            {
                "node_type": "code",
                "node_name": "未命名模块",
                "node_id": "719505",
                "node_status": 1,
                "node_msg": {
                    "title": "",
                    "ui_type": "",
                    "overridable": true,
                    "content": [
                        ""
                    ]
                }
            },
            {
                "node_type": "code",
                "node_name": "未命名模块",
                "node_id": "719505",
                "node_status": 2,
                "node_msg": {
                    "title": "",
                    "ui_type": "",
                    "overridable": "",
                    "content": [
                        ""
                    ]
                }
            }
        ],
        "show_block_result": false,
        "next_block_index": "977289",
        "finish_reason": ""
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1730189625
    }
}`
    },
    {
        title: '【其他】大模型流式输出',
        func: 'GET',
        url: '/chain_service/streams',
        subTitles: '如果flow里的节点为大模型节点，大模型流式输出',
        req: [
            {
                name: 'Authorization',
                type:'string',
                required: true,
                desc: '服务标识 如 "Bearer token"',
                location: 'header'
            },
            {
                name: 'call_back_id',
                type: 'string',
                required: true,
                desc: '轮询标识，使用主流程pb节点的c_log_id',
                location: 'query'
            },
            {
                name: 'block_key',
                type: 'string',
                required: true,
                desc: '模块ID',
                location: 'query'
            },
        ],
        res: [
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>data</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>message</span>
                                <span style={{marginRight: '12px'}}>dict</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div></div>
                            </div>
                        ),
                        type: 'dict',
                        desc: '',
                        key: '0-0-0',
                        required: true,
                        children: [
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>role</span>
                                        <span style={{marginRight: '12px'}}>string</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>角色</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '角色',
                                key: '0-0-0-0',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>type</span>
                                        <span style={{marginRight: '12px'}}>string</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '类型',
                                key: '0-0-0-1',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>block_type</span>
                                        <span style={{marginRight: '12px'}}>string</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>节点类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '节点类型',
                                key: '0-0-0-2',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>content</span>
                                        <span style={{marginRight: '12px'}}>string</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>具体内容</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '具体内容',
                                key: '0-0-0-3',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>errmsg</span>
                                        <span style={{marginRight: '12px'}}>string</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>错误信息</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '错误信息',
                                key: '0-0-0-4',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>content_type</span>
                                        <span style={{marginRight: '12px'}}>string</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>文本类型</div>
                                    </div>
                                ),
                                type: 'string',
                                desc: '文本类型',
                                key: '0-0-0-5',
                                required: true,
                            },
                            {
                                title: (
                                    <div>
                                        <span style={{marginRight: '12px'}}>status</span>
                                        <span style={{marginRight: '12px'}}>int</span>
                                        <span style={{marginRight: '16px'}}>必需</span>
                                        <div>状态</div>
                                    </div>
                                ),
                                type: 'int',
                                desc: '状态',
                                key: '0-0-0-6',
                                required: true,
                            }
                        ]
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>is_finish</span>
                                <span style={{marginRight: '12px'}}>boolean</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>是否结束</div>
                            </div>
                        ),
                        type: 'boolean',
                        desc: '是否结束',
                        key: '0-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>call_back_id</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>轮询标识</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '轮询标识',
                        key: '0-0-2',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>block_key</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>模块ID</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '模块ID',
                        key: '0-0-3',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>seq_id</span>
                                <span style={{marginRight: '12px'}}>int</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>序列号</div>
                            </div>
                        ),
                        type: 'int',
                        desc: '序列号',
                        key: '0-0-4',
                        required: true,
                    }
                ]
            },
            {
                title: (
                    <div>
                        <span style={{ marginRight: '12px' }}>context</span>
                        <span style={{ marginRight: '12px' }}>object</span>
                        <span style={{ marginRight: '16px' }}>必需</span>
                        <div></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '1-0',
                required: true,
                children: [
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>message</span>
                                <span style={{ marginRight: '12px' }}>string</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回消息说明</div>
                            </div>
                        ),
                        type: 'string',
                        desc: '返回消息说明',
                        key: '1-0-0',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>code</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>必需</span>
                                <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                        key: '1-0-1',
                        required: true,
                    },
                    {
                        title: (
                            <div>
                                <span style={{ marginRight: '12px' }}>timestamp</span>
                                <span style={{ marginRight: '12px' }}>integer</span>
                                <span style={{ marginRight: '16px' }}>可选</span>
                                <div>无需关注</div>
                            </div>
                        ),
                        type: 'integer',
                        desc: '无需关注',
                        key: '1-0-2',
                        required: false,
                    }
                ]
            }
        ],
        resHeight: "450px",
        resCode: `{
    "data": {
        "message": {
            "role": "assistant",
            "type": "answer",
            "block_type": "llm",
            "content": "地方",
            "errmsg": "",
            "content_type": "text",
            "status": "1"
        },
        "is_finish": true,
        "call_back_id": "7035-e0684750-95d8-11ef-aa77-dc4546d8ad4b",
        "block_key": "299205",
        "seq_id": 5
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1730189625
    }
}`
    }
]