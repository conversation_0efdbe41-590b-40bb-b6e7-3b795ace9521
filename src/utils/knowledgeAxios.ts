import axios from 'axios'
import { getCurrentUrlParamValue } from './url'
import { message } from 'antd'
import Router from 'next/router'
const knowledgeAxios = axios.create({
    baseURL: '/knowledge_api'
})

// 添加请求拦截器
knowledgeAxios.interceptors.request.use(
    config => {
        config.headers.Authorization = localStorage.getItem('prompt_authorization') || ''
        const teamId = getCurrentUrlParamValue('teamId')
        if(!teamId){
            message.error('缺少teamId')
        }
        // config.headers['Data-Auth-Id'] = '1'
        config.headers['teamId'] = teamId
        return config
    },
    error => {
        // 对请求错误做些什么
        // console.error('Request Error Interceptor:', error)
        return Promise.reject(error)
    }
)

knowledgeAxios.interceptors.response.use(
    response => {
        // 对响应数据做些什么
        // console.log('Response Interceptor:', response);
        if(response.data.code === 4001){
            localStorage.setItem('prompt_userName', '')
            localStorage.setItem('prompt_authorization', '')
            localStorage.setItem('prompt_userId', "");
            // 登录失效则跳到首页
            Router.push('/')
        }
        return response.data;
    },
    error => {
        // 对响应错误做些什么
        // console.error('Response Error Interceptor:', error)
        return Promise.reject(error)
    }
)

export default knowledgeAxios
