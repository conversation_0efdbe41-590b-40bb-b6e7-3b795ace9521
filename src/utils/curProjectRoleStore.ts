/*
 * @Author: yh
 * @Date: 2025-05-09 14:04:25
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-05-09 14:15:41
 * @FilePath: \prompt-web\src\utils\curProjectRoleStore.ts
 */
// 当前项目空间用户角色
let currentRole: number = 2;

// 这是一个监听器数组，保存所有对 currentRole 感兴趣的组件的回调
const listeners: ((val: number) => void)[] = [];

export const getRole = () => currentRole;

// 用于更新 currentRole 的函数, 每次更新值时，会通知所有监听者，让 UI 组件主动触发更新。
export const setRole = (val: number) => {
  currentRole = val;
  listeners.forEach((fn) => fn(val));
};


// 注册一个监听函数（组件中的 setState）
// 返回一个取消订阅函数（用于组件卸载时清理）
// 某个组件在挂载时监听 role 的变化，并在卸载时移除监听
export const subscribeRole = (fn: (val: number) => void) => {
  listeners.push(fn);
  return () => {
    const idx = listeners.indexOf(fn);
    if (idx !== -1) listeners.splice(idx, 1);
  };
};
