type AudioFormat = 'audio/mpeg' | 'audio/wav';

let audioUrl: string | null = null;

const AudioPlayerManager: any = {
  audio: null,
  playAudio(base64Audio: string, audioType: AudioFormat = 'audio/wav', isMuted: boolean = false, endCb: any): Promise<any> {
    // 将 Base64 编码的音频数据解码为二进制数据 (Uint8Array)
    const binaryString = atob(base64Audio);
    const binaryLen = binaryString.length;
    const bytes = new Uint8Array(binaryLen);

    for (let i = 0; i < binaryLen; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // 将 Uint8Array 转换为 Blob 对象
    const audioBlob = new Blob([bytes], { type: audioType });

    // 如果已有音频 URL，则释放
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    audioUrl = URL.createObjectURL(audioBlob);

    // 停止上一个正在播放的音频
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }

    // 创建新的 Audio 对象
    this.audio = new Audio(audioUrl);
    this.audio.muted = isMuted;
    // 自动播放音频
    this.audio.play()
      .then(() => {
        console.log('Audio is playing.');
      })
      .catch((error: any) => {
        console.error('Error playing audio:', error);
      });

    return new Promise((resolve: any, reject: any) => {
      // 监听音频播放结束事件
      this.audio?.addEventListener('ended', () => {
        console.log('Audio playback ended.');
        endCb && endCb()
        // 在音频播放结束后释放资源
        this.releaseAudio();
        resolve()
      });
    })
  },
  releaseAudio(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.audio = null;
    }

    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      audioUrl = null;
    }
  },

  // 停止当前播放中的音频
  stopAudio(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; // 重置音频播放时间
      console.log('Audio playback stopped.');
    }
  },

  // 设置音频静音状态
  setMute(isMuted: boolean): void {
    if (this.audio) {
      this.audio.muted = isMuted;
    }
  },
}

export default AudioPlayerManager;

