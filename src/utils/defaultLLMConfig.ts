export const parameterRules = [
    {
        "name": "temperature",
        "use_template": "temperature",
        "label": {
            "zh_Hans": "温度",
            "en_US": "Temperature"
        },
        "type": "float",
        "help": {
            "zh_<PERSON>": "调高温度会使得模型的输出更多样性和创新性，反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与 “Top p” 同时调整。",
            "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."
        },
        "required": false,
        "default": 0.0,
        "min": 0.0,
        "max": 1.0,
        "precision": 2,
        "options": [],
        "scopeDesc": {
            "min": "确定", 
            "max": "随机", 
        }
    },
    {
        "name": "top_p",
        "use_template": "top_p",
        "label": {
            "zh_Hans": "Top P",
            "en_US": "Top P"
        },
        "type": "float",
        "help": {
            "zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。",
            "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."
        },
        "required": false,
        "default": 0.7,
        "min": 0.0,
        "max": 1.0,
        "precision": 2,
        "options": [],
        "scopeDesc": {
            "min": "严谨", 
            "max": "发散", 
        }
    },
    {
        "name": "max_tokens",
        "use_template": "max_tokens",
        "label": {
            "zh_Hans": "最大回复Token",
            "en_US": "Max Tokens"
        },
        "type": "int",
        "help": {
            "zh_Hans": "控制模型输出的 Tokens 长度上限。",
            "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."
        },
        "required": false,
        "default": 4096,
        "min": 1.0,
        "max": 8192.0,
        "precision": 0,
        "options": [],
        "scopeDesc": {
            "min": 0, 
            "max": 8192, 
        }
    },
]

const defalutModelParams = () => {
    const modelParam: any = new Object();
    parameterRules.forEach((p: any) => {
        modelParam[p.name] = p.default
        let component = 'slider'
        if (p.type === 'float' || p.type === 'int') {
            if ((p.min || p.min === 0) && (p.max || p.max === 0)) {
                component = 'slider'
            } else {
                component = 'inputNumber'
            }
        } else if (p.type === 'string') {
            if (p.options.length) {
                component = 'selector'
            } else {
                component = 'input'
            }
        } else if (p.type === 'boolean') {
            component = 'radio'
        }
    })
    return modelParam
}

const apiENV = process.env.NEXT_PUBLIC_API_ENV ?? 'test'
export const default_model_params = {
    "role_id": (apiENV == 'production' || apiENV == 'pre')? '917ec92d6f5146cb9dc7789114b468ee' : "a53701f73ebf930a13ca778995df63cb",
    "model_type": "",
    "top_k": 0,
    "num_beams": 1,
    "provider_name": "",
    "support_mcp": true,
    ...defalutModelParams()
} 