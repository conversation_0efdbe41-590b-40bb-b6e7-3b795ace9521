import {
  reqTuitui<PERSON>ogin,
  reqYun<PERSON><PERSON><PERSON><PERSON><PERSON>,
  reqZhi<PERSON>ao<PERSON>ogin,
  reqClientLoginCommon,
} from "@/service/login";

function deal_with_locationSearch() {
  const search = location.search.substr(1).split("&"); // 初步处理数据-->去掉?分开多个键值对
  //  console.log("search===>",search);
  const obj = {};
  function deal_with_key_value(obj, key_and_value) {
    for (let i = 0; i < key_and_value.length; i++) {
      const pos = key_and_value[i].split("=");
      obj[pos[0]] = unescape(pos[1]); // 解码
    }
  }
  deal_with_key_value(obj, search); // 调用

  return obj; // 最终返回值为对象
}

/**
 * 请求 登录接口 1:推推登录 2:织语登录
 * @param {*} login_type
 * @returns
 */
function goToLoginWithClientUserInfo(login_type = 1) {
  const object = deal_with_locationSearch();
  // console.log("object===>",object);
  let params = {
    app_id: object.appid,
    msgSignature: object.msgSignature,
    timeStamp: object.timeStamp,
    nonce: object.nonce,
    encrypt: object.encrypt,
  };
  login_type === 2 && (params.corp_id = object.corpid); // 织语登录的添加 corpid 参数
  login_type === 2 && (params.domain = object.zhiyu_host); // 织语登录的添加 domain 参数
  // // 本地参数容错处理
  // if(params.msgSignature && params.msgSignature.length !== 0){
  //   localStorage.setItem("prompt_tuituiLoginParams",JSON.stringify(params));
  // }else {
  //   const loginParams = localStorage.getItem("prompt_tuituiLoginParams");
  //   loginParams && (params = JSON.parse(loginParams));
  // }
  // console.log("取到单点登录参数",params);

  return new Promise((resolve, reject) => {
    if (params.msgSignature && params.msgSignature.length !== 0) {
      reqTuituiLogin({ ...params, login_type })
        .then((res) => {
          // console.log("单点登录接口返回",res);
          resolve(res);
        })
        .catch((err) => {
          console.log("单点登录接口返回err", err);
          resolve(null);
        });
    } else {
      console.log("未获取到单点登录参数");
      // message.error("未获取到单点登录参数");
      resolve(null);
    }
  });
}

/**
 * 请求 云盘登录接口
 * @param
 * @returns
 */
function goToLoginWithYunPanToken() {
  const object = deal_with_locationSearch();
  // console.log("object===>",object);
  let params = object.client_id
    ? { token: object.token, client_id: object.client_id }
    : { token: object.token };
  // 本地参数容错处理
  if (params.token && params.token.length !== 0) {
    localStorage.setItem("prompt_yunPanLoginParams", JSON.stringify(params));
  } else {
    const loginParams = localStorage.getItem("prompt_yunPanLoginParams");
    loginParams && (params = JSON.parse(loginParams));
  }
  console.log("取到云盘单点登录参数", params);

  return new Promise((resolve, reject) => {
    if (params.token && params.token.length !== 0) {
      reqYunPanLogin({ ...params })
        .then((res) => {
          console.log("单点登录接口返回", res);
          resolve(res);
        })
        .catch((err) => {
          console.log("单点登录接口返回err", err);
          resolve(null);
        });
    } else {
      console.log("未获取到单点登录参数");
      // message.error("未获取到单点登录参数");
      resolve(null);
    }
  });
}

async function goToLoginWithCompanyBrainToken() {
  const microApp = parent.window.__MicroAPP__;
  const token = await microApp.app.getAppTokenSync(window);

  return new Promise((resolve, reject) => {
    if (token && token.length !== 0) {
      reqZhiNaoLogin({ token })
        .then((res) => {
          // console.log("单点登录接口返回",res);
          resolve(res);
        })
        .catch((err) => {
          console.log("单点登录接口返回err", err);
          resolve(null);
        });
    } else {
      console.log("未获取到单点登录参数");
      // message.error("未获取到单点登录参数");
      resolve(null);
    }
  });
}

// 平台单点登录公共方法， 1: Qpaas
/**
 * 请求 登录接口 1: Qpaas
 * @param {*} source_type: int
 * @param {*} params: json
 * @returns
 */
function goClientUserInfoCommon(source_type = 1) {
  const object = deal_with_locationSearch();
  // console.log("object===>",object);
  let params = object;
  // 不同的source_type 后面有新的平台接入，可以进行判断处理参数
  const isValidParams = () => {
    if (!source_type) {
      return false;
    }
    // Qpaas
    if (source_type === "qpaas") {
      if (params.appid && params.code) {
        return true;
      } else {
        return false;
      }
    }
    // llmops
    if (source_type === "llmopsBoard") {
      if (params.Authorization && params.project_id && params.source_key) {
        return true;
      } else {
        return false;
      }
    }
    // 前端放开参数校验
    return true;
    // return false;
  };

  return new Promise((resolve, reject) => {
    if (isValidParams()) {
      reqClientLoginCommon({ params, source_type })
        .then((res) => {
          // console.log("单点登录接口返回",res);
          resolve(res);
        })
        .catch((err) => {
          console.log("单点登录接口返回err", err);
          resolve(null);
        });
    } else {
      console.log("未获取到单点登录参数");
      // message.error("未获取到单点登录参数");
      resolve(null);
    }
  });
}

export {
  deal_with_locationSearch,
  goToLoginWithClientUserInfo,
  goToLoginWithYunPanToken,
  goToLoginWithCompanyBrainToken,
  goClientUserInfoCommon,
};
