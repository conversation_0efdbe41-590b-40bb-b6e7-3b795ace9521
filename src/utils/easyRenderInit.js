import React from 'react';
import ReactDOM from 'react-dom';

/**
 * 初始化 @q/easy-render 所需的全局对象
 * 这个函数应该在应用启动时调用一次
 */
export function initializeEasyRender() {
  if (typeof window !== 'undefined') {
    // 设置全局 React 对象
    window.React = React;
    
    // 设置全局 ReactDOM 对象，包含 createPortal polyfill
    window.ReactDOM = {
      ...ReactDOM,
      createPortal: ReactDOM.createPortal || ((children, container) => {
        console.warn('Using polyfill for createPortal');
        return children; // 简单降级策略
      })
    };
    
    // 设置加载标记
    window.easyRenderLoaded = true;
    
    return true;
  }
  return false;
}

/**
 * 检查 EasyRender 是否已经初始化
 */
export function isEasyRenderInitialized() {
  return typeof window !== 'undefined' && window.easyRenderLoaded === true;
}

// 导出默认的初始化函数，方便直接使用
export default initializeEasyRender;
