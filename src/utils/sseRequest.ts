import { fetchEventSource } from '@microsoft/fetch-event-source';
import { transferQueryStr } from '@/utils/common';
// sse 请求
export const sseRequest = async (params: any, url: any, callback: any = {}, method='POST', isMsgExt = false) => {
    const {
        onOpenCb = (data: any) => { }, 
        onSuccessCb = (data: any) => { },
        onFinishCb = (data: any) => { }, 
        onFailCb = (data: any) => { },
        onCancelCb = (data: any) => {}
    } = callback;
    const access_token = localStorage.getItem('GuiFlowToken');
    
    // 创建 AbortController 实例
    const controller = new AbortController();
    const query = transferQueryStr(params)
    
    try {
        fetchEventSource(method == 'GET' ? url+query : url, {
            method,
            openWhenHidden: true,
            headers: {
                'Content-Type': 'application/json',
                "teamId": localStorage.getItem('prompt_teamId') || params?.teamId || '',
                "Authorization": localStorage.getItem('prompt_authorization') || '',
            },
            credentials: 'include',
            ...(method == 'GET' ? {} : {body: JSON.stringify(params)}),
            // 添加 signal 配置
            signal: controller.signal,
            onopen: async (res: any) => {
                const { ok } = res || {};
                if (ok) {
                    onOpenCb && onOpenCb({
                        ...params,
                        data: ''
                    });
                    if (res.headers.get('content-type')?.includes('application/json')) {
                        try {
                            const myRes = res.clone();
                            const json = await myRes.json();
                            const { errno } = json;

                            if (errno != 0) {
                                onFailCb && onFailCb({
                                    ...params,
                                    data: ''
                                });
                            }
                        } catch (error) {
                            onFailCb && onFailCb({
                                ...params,
                                data: ''
                            });
                        }
                    }
                } else {
                    onFailCb && onFailCb({
                        ...params,
                        data: ''
                    });
                }
            },
            onmessage: async (ev: any) => {
               
                let jsonData:any = {};
                try {
                    jsonData = JSON.parse(ev.data)
                } catch (e) {
    
                }
                const { message, is_finish, message_id = '' } = jsonData;
                if(is_finish) {
                    // console.log("🚀 ~ 结束了:")
                    onFinishCb && onFinishCb({
                        ...(isMsgExt ? message : message?.content),
                        block_key: jsonData?.block_key || '',
                        type: jsonData?.message?.block_type,
                        call_back_id: jsonData?.call_back_id,
                        session_id: jsonData?.session_id || ''
                    });
                } else {
                    if(JSON.stringify(jsonData) != '{}') {
                        if(message.content_type == 'text') {
                            onSuccessCb && onSuccessCb({
                                ...(isMsgExt ? {message} : {content: message?.content}),
                                block_key: jsonData?.block_key || '',
                                type: jsonData?.message?.block_type,
                                call_back_id: jsonData?.call_back_id,
                                session_id: jsonData?.session_id || '',
                                message_id
                            });
                        }else {
                            onSuccessCb && onSuccessCb({
                                ...(isMsgExt ? {message} : {content: message?.content}),
                                block_key: jsonData?.block_key || '',
                                type: jsonData?.message?.block_type,
                                call_back_id: jsonData?.call_back_id,
                                session_id: jsonData?.session_id || ''
                            });
                        }
                    }
                }
            },
            onerror: (err: any) => {
                onFailCb && onFailCb({
                    ...params,
                    data: ''
                });
            },
            onclose: () => {
                onFinishCb && onFinishCb({
                    done: true
                });
            },
        });
        // 中断取消请求
        onCancelCb && onCancelCb({
            cancel: () => {
                controller.abort();
            }
        })
    } catch (error: any) {
        // console.log("🚀 ~ error:", error)
        // 判断是否是用户主动取消的请求
        if (error.name === 'AbortError') {
            console.log('SSE request was cancelled');
            return;
        }
        onFailCb && onFailCb({
            ...params,
            data: ''
        });
    }

    // 返回取消函数
    return () => {
        // console.log("🚀 --dd~ 取消请求sse")
        controller.abort();
        // console.log("🚀 ~取消请求sse 已执行")
    };
}
