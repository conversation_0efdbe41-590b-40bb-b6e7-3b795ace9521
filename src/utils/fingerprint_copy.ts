/*
 * @Author: yh
 * @Date: 2025-01-13 16:54:11
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-03-18 16:37:38
 * @FilePath: \prompt-web\src\utils\fingerprint_copy.ts
 */
import FingerprintJS from '@fingerprintjs/fingerprintjs'; //不建议使用这个插件，件以上pro版本，这个受缓存影响，刷新和清缓存获取到的有可能不是同一个
import CryptoJS from 'crypto-js'; // 直接使用crypto在有的浏览器里面{}

let cachedFingerprint: string | null = null;

export const getFingerprint = async (): Promise<string> => {
  // 定义特性数据接口
  interface FingerprintData {
    userAgent: string;
    language: string;
    platform: string;
    hardwareConcurrency: number;
    deviceMemory: string | 'unknown';
    screenResolution: string;
    colorDepth: number;
    timezoneOffset: number;
    sessionStorage: boolean;
    localStorage: boolean;
    indexedDB: boolean;
    doNotTrack: string | null;
  }

  if (cachedFingerprint) return cachedFingerprint;

  // 收集浏览器的特性信息
  const fingerprintData: FingerprintData = {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    hardwareConcurrency: navigator.hardwareConcurrency,
    deviceMemory: (navigator as any).deviceMemory || 'unknown', // 某些浏览器不支持 deviceMemory
    screenResolution: `${screen.width}x${screen.height}`,
    colorDepth: screen.colorDepth,
    timezoneOffset: new Date().getTimezoneOffset(),
    sessionStorage: !!window.sessionStorage,
    localStorage: !!window.localStorage,
    indexedDB: !!window.indexedDB,
    doNotTrack: navigator.doNotTrack,
  };

  // 将特性数据转换为字符串
  const fingerprintString = Object.entries(fingerprintData)
    .map(([key, value]) => `${key}:${value}`)
    .join(';');

  // 使用 SHA-256 散列处理字符串
  cachedFingerprint = await hashSHA256(fingerprintString);
  return cachedFingerprint;
}
// 使用 SubtleCrypto API 实现 SHA-256 散列
async function hashSHA256(data: string): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);

  // 将二进制数据转换为 CryptoJS 的 WordArray
  const wordArray = CryptoJS.lib.WordArray.create(dataBuffer);

  // 计算 SHA-256 哈希值
  const hashHex  = CryptoJS.SHA256(wordArray).toString(CryptoJS.enc.Hex);


  // 计算哈希值
  // const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);

  // 转换为十六进制字符串
  return hashHex;
}



