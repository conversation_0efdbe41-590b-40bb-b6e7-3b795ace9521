import { message as AntdMessage } from 'antd';
import { getCurrentUrlParamValue } from "@/utils/url";

import Router from 'next/router'
import { transferQueryStr } from './fetch';

export interface IResponseStructure {
    data: any;
    context: any;
    code: any;
}



// 对返回数据的状态码进行处理
function checkStatus(response: IResponseStructure, url: string) {
    const {
        context,
        data,
    } = response;
    const { message, code } = context;

    if (code == 0) {
        return response;
    }
    switch (code) {
        case '53021':
            throw new Error('访问权限验证失败');
        case 53021:
            throw new Error('访问权限验证失败');
        default:
            throw new Error(`Unexpected error: ${message}`);
    }


}

export const fetchAgentSDKData = async (
    url: string,
    data = {},
    method: string = 'GET',
    options?: Object
) => {
    let customHeaders: RequestInit = {
        mode: 'cors',
        redirect: 'follow',
        referrer: 'no-referrer',
        ...options,
    };

    const urlParams: any = new URLSearchParams(window.location.search);
    const referrer = urlParams.get('refer') ? urlParams.get('refer') : document.referrer;
    const cleanReferrer = referrer.endsWith('/') ? referrer.slice(0, -1) : referrer;

    customHeaders.headers = {
        "x-referer": cleanReferrer,
        "x-origin": cleanReferrer,
        "Authorization": localStorage.getItem('prompt_authorization') || `Bearer ${urlParams.get('token')}`,
    }


    method = method.toUpperCase();
    if (method === 'GET') {
        const queryStr = transferQueryStr(data);
        url = url + queryStr;
        customHeaders.method = 'GET';
    }
    if (data instanceof FormData && method === 'POST') {
        customHeaders.method = 'POST'
        customHeaders.body = data
    } else if (method === 'POST') {
        const reqData = JSON.stringify(data);
        customHeaders.method = 'POST';
        customHeaders.body = reqData;
        customHeaders.headers = {
            'content-Type': 'application/json',
            ...customHeaders.headers
        };
    }

    //  console.log(url + "头部-=" + JSON.stringify(customHeaders.headers))

    return fetch((urlParams.get('env') ? urlParams.get('env') : '') + url, customHeaders)
        .then((response: any) => {
            if (response.ok) {
                const isStream = response.headers.get('content-type') === 'application/octet-stream';
                if (isStream) {
                    // 如果是流的话直接返回
                    return response.blob();
                }

                return response.json().then((data: any) => {
                    return checkStatus(data, response.url);
                });
            } else {
                response.json().then((data: any) => {
                    const { messgae, errors } = data;
                    AntdMessage.error(errors && errors.length ? errors[0]?.msg : '服务端响应异常');
                    // throw new CustomError(response.status, errors[0].msg);
                });
            }
        })
        .catch((error) => {
            // throw new CustomError(error.errCode, error.errMsg);
        });
};
