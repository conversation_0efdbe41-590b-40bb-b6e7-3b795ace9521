/**
 * 图片预加载工具
 * 用于在页面加载时预先加载指定的图片，提升用户体验
 */

// 教程图片URL常量
export const TUTORIAL_IMAGES = [
  'https://p4.ssl.qhimg.com/t110b9a93019353054514b81931.png',
  'https://p0.ssl.qhimg.com/t110b9a9301118c48076c1e2e1a.png',
  'https://p3.ssl.qhimg.com/t110b9a93013ba83ae7d5bf3458.png',
  'https://p1.ssl.qhimg.com/t110b9a9301820735b165e67623.png'
];

/**
 * 预加载单张图片
 * @param src 图片URL
 * @returns Promise<HTMLImageElement>
 */
const preloadSingleImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      console.log(`图片预加载成功: ${src}`);
      resolve(img);
    };
    
    img.onerror = (error) => {
      console.warn(`图片预加载失败: ${src}`, error);
      reject(error);
    };
    
    // 设置crossOrigin以避免跨域问题
    img.crossOrigin = 'anonymous';
    img.src = src;
  });
};

/**
 * 预加载多张图片
 * @param imageUrls 图片URL数组
 * @returns Promise<HTMLImageElement[]>
 */
export const preloadImages = async (imageUrls: string[]): Promise<HTMLImageElement[]> => {
  try {
    console.log('开始预加载图片...', imageUrls);
    const promises = imageUrls.map(url => preloadSingleImage(url));
    const results = await Promise.allSettled(promises);
    
    const successfulImages: HTMLImageElement[] = [];
    const failedImages: string[] = [];
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successfulImages.push(result.value);
      } else {
        failedImages.push(imageUrls[index]);
      }
    });
    
    if (failedImages.length > 0) {
      console.warn('部分图片预加载失败:', failedImages);
    }
    
    console.log(`图片预加载完成: 成功 ${successfulImages.length}/${imageUrls.length}`);
    return successfulImages;
  } catch (error) {
    console.error('图片预加载过程中发生错误:', error);
    return [];
  }
};

/**
 * 预加载教程图片
 * 专门用于预加载教程引导的四张图片
 */
export const preloadTutorialImages = (): Promise<HTMLImageElement[]> => {
  return preloadImages(TUTORIAL_IMAGES);
};

/**
 * 检查图片是否已经在浏览器缓存中
 * @param src 图片URL
 * @returns boolean
 */
export const isImageCached = (src: string): boolean => {
  const img = new Image();
  img.src = src;
  return img.complete && img.naturalWidth > 0;
};

/**
 * 获取所有教程图片的缓存状态
 * @returns 缓存状态对象
 */
export const getTutorialImagesCacheStatus = (): Record<string, boolean> => {
  const status: Record<string, boolean> = {};
  TUTORIAL_IMAGES.forEach(url => {
    status[url] = isImageCached(url);
  });
  return status;
};
