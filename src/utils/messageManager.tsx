/*
 * @Author: yh
 * @Date: 2025-01-22 10:28:45
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-01-22 11:18:45
 * @FilePath: \prompt-web\src\utils\messageManager.tsx
 */
import { message } from 'antd';

// 消息管理器
class MessageManager {
  private activeMessages: Map<string, string> = new Map(); // key: 消息内容, value: 消息key

  /**
   * 通用消息处理方法
   * @param type 消息类型：'success' | 'error' | 'info' | 'warning'
   * @param content 消息内容
   * @param duration 消息显示时长
   */
  showMessage(type: 'success' | 'error' | 'info' | 'warning', content: string, duration: number = 3) {
    const existingKey = this.getKeyByContent(content);

    if (existingKey) {
      // 如果存在相同内容的消息，则更新消息
      message[type]({
        key: existingKey,
        content,
        duration,
        onClose: () => {
          this.handleOnClose(content);
        },
      });
    } else {
      // 如果不存在相同内容的消息，则创建新的消息
      const key = `${Date.now()}-${Math.random()}`;
      this.activeMessages.set(content, key);

      message[type]({
        key,
        content,
        duration,
        onClose: () => {
          this.handleOnClose(content);
        },
      });
    }
  }

  /**
   * 根据内容获取消息key
   * @param content 消息内容
   * @returns 消息key
   */
  private getKeyByContent(content: string): string | undefined {
    return this.activeMessages.get(content);
  }

  /**
   * 消息关闭时的处理
   * @param content 消息内容
   */
  private handleOnClose(content: string) {
    // 消息关闭后移除记录
    this.activeMessages.delete(content);
  }
}

// 创建消息管理实例
const messageManager = new MessageManager();

// 动态生成所有消息方法
type MessageType = 'success' | 'error' | 'info' | 'warning';

const exportedMethods = {} as Record<MessageType, (content: string, duration?: number) => void>;

(['success', 'error', 'info', 'warning'] as MessageType[]).forEach((type) => {
  exportedMethods[type] = (content: string, duration?: number) => {
    messageManager.showMessage(type, content, duration);
  };
});

// 导出封装的消息方法
// export const { success, error, info, warning } = exportedMethods;
export const notify = exportedMethods;
