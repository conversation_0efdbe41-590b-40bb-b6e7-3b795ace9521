
export const pyCodeForFlow = `
import time
import requests
from typing import Any


def repeat_request(max_retries: int = 100, delay: int = 2):
    """
    异步重复获取结果
    :param max_retries: 最大重试次数
    :param delay: 延迟时间
    :return: 返回结果
    """

    def decorator(func):
        def wrapper(*args, **kwargs) -> (Any, bool):
            num = 0
            while num <= max_retries:
                _resp = func(*args, **kwargs)
                status = _resp["data"]["status"]
                result = _resp["data"]["result"]
                if status <= 2:  # 执行中
                    time.sleep(delay)
                    num += 1
                    print(f"第{num}次获取结果，当前状态为执行中，{delay}秒后再次尝试")
                elif status == 3:  # 执行成功
                    return result, True
                elif status == 4:  # 执行失败
                    return f"接口执行失败，原因：{result}", False
                else:  # 执行失败
                    return f"状态异常，status={status}，原因：{result}", False
            return f"接口请求超过最大次数{max_retries}", False

        return wrapper

    return decorator


class AgentBuilder(object):
    def __init__(self, base_url, app_id: str, app_secret: str, template_id: str, type: str, trace_id: str = None):
        assert type in ["flow", "text2text"], "type参数只能为flow或者text2text"
        self._base_url = base_url  # 服务地址
        self._app_id = app_id  # AK
        self._app_secret = app_secret  # SK
        self._template_id = template_id  # 实例编号
        self._type = type  # 部署类型，flow或者model
        self._headers = None  # 请求头
        self._call_back_id = None  # 回调ID
        self._block_keys = None  # 获取流程节点顺序，Flow专用
        self._trace_id = trace_id  # 调用链ID，默认为服务端生成

    def _authentication(self):
        # 第一步：鉴权
        response = requests.get(
            url=f"{self._base_url}/app_service/get_token",
            params={
                "app_id": self._app_id,
                "app_secret": self._app_secret
            }
        ).json()
        self._headers = {
            'Authorization': f'Bearer {response["data"]["access_token"]}',
            'Content-Type': 'application/json'
        }

    def _async_request(self, chain_inputs: dict):
        # 准备请求参数
        if self._type == "flow":
            _body = {"chain_inputs": {"inputs": chain_inputs}}
        else:
            _body = {"chain_inputs": chain_inputs}
        if self._trace_id:
            _body["trace_id"] = self._trace_id
        # 第二步：请求服务
        response = requests.post(
            url=f"{self._base_url}/chain_service/{self._type}/{self._template_id}",
            headers=self._headers,
            json=_body
        ).json()
        self._call_back_id = response["data"]["call_back_id"]
        self._block_keys = response["data"].get("block_keys")

    def request(self, chain_inputs: dict):
        self._authentication()
        self._async_request(chain_inputs)

    @repeat_request(max_retries=10, delay=2)
    def _get_block_result(self, block_key: str) -> dict:
        """获取流程节点结果"""
        _params = {
            "call_back_id": self._call_back_id,
            "block_key": block_key
        }
        if self._trace_id:
            _params["trace_id"] = self._trace_id
        response = requests.get(
            url=f"{self._base_url}/chain_service/block_detail",
            headers=self._headers,
            params=_params
        ).json()
        return response

    @repeat_request(max_retries=10, delay=2)
    def _get_final_result(self) -> dict:
        """获取Flow或Txt2txt最终结果"""
        _params = {
            "call_back_id": self._call_back_id
        }
        if self._trace_id:
            _params["trace_id"] = self._trace_id
        response = requests.get(
            url=f"{self._base_url}/chain_service/detail",
            headers=self._headers,
            params=_params
        ).json()
        return response

    @property
    def results(self) -> (Any, bool):
        res, status = self._get_final_result()
        return res, status

    def __iter__(self):
        assert self._type == "flow", "该部署服务不是Flow类型，请检查服务类型是否正确"
        assert self._block_keys, "获取流程节点，请先调用request方法"
        for k in self._block_keys[1:]:
            res, status = self._get_block_result(k)
            yield {"block_key": k, "result": res, "status": status}

    def __call__(self, chain_inputs: dict):
        self.request(chain_inputs)
        return self.results


def flow_run(chain_inputs: dict, **kwargs):
    """
    帮助：
    \`\`\`python
    from deploy_flow import flow_run
    results, is_ok = flow_run(
        chain_inputs={"a": ...},
        app_id="app_id",
        app_secret="app_secret",
        template_id="template_id"
    )
    print(results)
    \`\`\`
    """
    _builder = AgentBuilder(
        base_url=kwargs["base_url"],
        type="flow",
        app_id=kwargs["app_id"],
        app_secret=kwargs["app_secret"],
        template_id=kwargs["template_id"]
    )
    return _builder(chain_inputs)


def flow_block_run(chain_inputs: dict, **kwargs):
    """
    帮助：
    \`\`\`python
    from deploy_flow import flow_block_run
    results = flow_block_run(
        chain_inputs={"a": ...},
        app_id="app_id",
        app_secret="app_secret",
        template_id="template_id"
    )
    print(results)
    \`\`\`
    """
    _builder = AgentBuilder(
        base_url=kwargs["base_url"],
        type="flow",
        app_id=kwargs["app_id"],
        app_secret=kwargs["app_secret"],
        template_id=kwargs["template_id"]
    )
    _builder.request(chain_inputs)
    return list(_builder)
`;

export const curlCodeForFlow = `
# 第一步：鉴权
export access_token=$(curl -X GET "\${base_url}/app_service/get_token?app_id=\${app_id}&app_secret=\${app_secret}" | jq -r ".data.access_token")
echo $access_token

# 第二步：请求服务
response=$(curl -X POST -H "Authorization: Bearer \${access_token}" -H "Content-Type: application/json" "\${base_url}/chain_service/flow/\${template_id}"  -d "\${chain_inputs}")
echo $response

export call_back_id=$(echo "$response" | jq -r '.data.call_back_id')
export block_key=$(echo "$response" | jq -r '.data.block_keys[1]')

sleep 10

# 第三步：获取结果（输出节点）
export response=$(curl -X GET \
  -H "Authorization: Bearer \${access_token}" "\${base_url}/chain_service/detail?call_back_id=\${call_back_id}")
echo $response


# 第三步：获取单个节点结果
export response=$(curl -X GET \
  -H "Authorization: Bearer \${access_token}" "\${base_url}/chain_service/block_detail?call_back_id=\${call_back_id}&block_key=\${block_key}")
echo $response`;

export const jsCodeForFlow = `
// 第一步：鉴权  
axios.get(\`\${base_url}/app_service/get_token?app_id=\${app_id}&app_secret=\${app_secret}\`)  
    .then((response) => {  
        console.log(response);
// 第二步：请求服务  
const access_token = response.data.data.access_token; 
const headers = {  
'Authorization': \`Bearer \${access_token}\`,  
'Content-Type': 'application/json'  
};
axios.post(\`\${base_url}/chain_service/flow/\${template_id}\`, {  
	chain_inputs: chain_inputs  
}, {  
	headers: headers  
}).then((response) => {   
console.log(response.data);  
const call_back_id = response.data.data.call_back_id;
const block_key = response.data.data.block_keys[1];
// 第三步：获取结果 (输出节点)
axios.get(\`\${base_url}/chain_service/detail?call_back_id=\${call_back_id}\`, {  
			headers: headers  
		}).then((response) => {  
			console.log(response.data.data);  
		}); 
// 第三步：获取单个节点结果
axios.get(\`\${base_url}/chain_service/block_detail?call_back_id=\${call_back_id}&block_key=\${block_key}\`, {  
			headers: headers  
		}).then((response) => {  
			console.log(response.data.data);  
		});  
	});  
});`;

export const flowData = [
    {
        title: '【第一步】鉴权',
        func: 'GET',
        url: '/app_service/get_token',
        subTitles: '通过接口获取访问token',
        req: [
                {
                    name: 'app_id',
                    type: 'string',
                    required: true,
                    desc: 'app_id',
                    location: 'query'
                },
                {
                    name: 'app_secret',
                    type: 'string',
                    required: true,
                    desc: '密钥',
                    location: 'query'
                }
            ],
        res: [
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>data</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '0-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>access_token</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>token</div>
                        </div>
                    ),
                    type: 'string',
                    desc: 'token',
                    key: '0-0-0',
                    required: false,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>expire</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>token失效时间</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: 'token失效时间',
                    key: '0-0-1',
                    required: false,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>timestamp</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>当前请求时间戳</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '当前请求时间戳',
                    key: '0-0-2',
                    required: false,
                }
            ]
           },
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>context</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '1-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回消息说明</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '返回消息说明',
                    key: '1-0-0',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>code</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div style={{marginRight: '16px'}}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                    key: '1-0-1',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>timestamp</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>无需关注</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '无需关注',
                    key: '1-0-2',
                    required: false,
                }
            ]
           }
        ],
        resHeight: "275px",
        resCode: `{   
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiJwcm9waGV0IiwiZXhwIjoxNzAwOTg2NzAwLCJkYXRhIjp7ImFwcF9pZCI6IkZsb3czXzU2MDUiLCJhcHBfc2VjcmV0IjoiMXVVSW5jVmtWanN2VXYxVE80UTdnSnJ2c2dHbjU4MW8iLCJjaGFpbl9ubyI6ImFjUEozRnc1MjMxMTE1VkhKclFwIn19.NwadYraaAEqhb3v3LxqY5V2r47uGBTPuIdPGrOMK7Kc",
        "expire": 1700986700,
        "timestamp": 1700186700
        },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129899
    }
}`,
    },
    {
        title: '【第二步】请求服务',
        func: 'POST',
        url: '/chain_service/flow/{template_id}',
        subTitles: '执行playbook服务接口，异步执行，返回回调标识ID与playbook中不同节点标识',
        req: [
                {
                    name: 'template_id',
                    type: 'string',
                    required: true,
                    desc: '部署的文件名',
                    location: 'path'
                },
                {
                    name: 'Authorization',
                    type: 'string',
                    required: true,
                    location: 'header',
                    desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                },
                {
                    name: 'body',
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>body</span>
                            <span style={{marginRight: '12px'}}>object</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                        </div>
                    ),
                    key:'0-0',
                    location: 'body',
                    type: 'object',
                    required: false,
                    desc: '',
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>chain_inputs</span>
                                    <span style={{marginRight: '12px'}}>json</span>
                                    <span style={{marginRight: '16px'}}>必需</span>
                                    <div>对应文生文中配置的槽位，json格式输入</div>
                                </div>
                            ),
                            key:'0-0-0',
                            location: 'body',
                            type: 'json',
                            required: true,
                            desc: '对应文生文中配置的槽位，json格式输入',
                            children: [
                                {
                                    title: (
                                        <div>
                                            <span style={{marginRight: '12px'}}>inputs</span>
                                            <span style={{marginRight: '12px'}}>json</span>
                                            <span style={{marginRight: '16px'}}>必需</span>
                                            <div>对应开始节点中的变量，以json格式输入</div>
                                        </div>
                                    ),
                                    key:'0-0-0-0',
                                    location: 'body',
                                    type: 'json',
                                    required: true,
                                    desc: '对应开始节点中的变量，以json格式输入',
                                }
                            ]
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>trace_id</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>方便联调排查问题，建议随机生成</div>
                                </div>
                            ),
                            key:'0-0-1',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '方便联调排查问题，建议随机生成'
                        },
                    ]
                }
            ],
            reqHeight: "190px",
        reqCode: `{
    "chain_inputs": {
        "inputs": {
        "description": "元旦与朋友去北京玩三天"
        }
    },
    "trace_id": "a43e7bd4-52d7-4373-aae9-e1409f6ba48a"
}`,
        res: [
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>data</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '0-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>call_back_id</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div style={{marginRight: '16px'}}>回调接口请求标识,获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '回调接口请求标识,获取到结果返回轮询结果ID；超过30mins未获取到结果，状态置为异常',
                    key: '0-0-0',
                    required: false,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>block_keys</span>
                            <span style={{marginRight: '12px'}}>[string]</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div style={{marginRight: '16px'}}>节点数组</div>
                        </div>
                    ),
                    type: '[string]',
                    desc: '节点数组',
                    key: '0-0-1',
                    required: false,
                }
            ]
           },
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>context</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '1-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回消息说明</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '返回消息说明',
                    key: '1-0-0',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>code</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                    key: '1-0-1',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>timestamp</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>无需关注</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '无需关注',
                    key: '1-0-2',
                    required: false,
                }
            ]
           }
        ],
        resHeight: "350px",
        resCode: `{
    "data": {
        "call_back_id": "a62f0b90-84f1-11ee-ae35-32cc9d19f2e1",
        "block_keys": [
        "612513",
        "553568",
        "461475",
        "996405"
        ]
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129805
    }
}`,
    },
    {
        title: '【第三步】服务轮询接口（获取输出节点结果）',
        func: 'GET',
        url: '/chain_service/detail',
        subTitles: '获取playbook中输出节点的结果',
        req: [
                {
                    name: 'call_back_id',
                    type: 'string',
                    required: true,
                    desc: '服务接口中返回的轮询标识',
                    location: 'query'
                },
                {
                    name: 'trace_id',
                    type: 'string',
                    required: false,
                    desc: '业务方生成的唯一ID，方便联调排查问题，建议随机生成',
                    location: 'query'
                },
                {
                    name: 'Authorization',
                    type: 'string',
                    required: true,
                    desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                    location: 'header'
                },
            ],
        res: [
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>data</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '0-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>执行结果</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '执行状态',
                    key: '0-0-0',
                    required: false,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>result</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>执行结果</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '执行结果',
                    key: '0-0-1',
                    required: false,
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>output</span>
                                    <span style={{marginRight: '12px'}}>object</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>输出节点结果</div>
                                </div>
                            ),
                            type: 'object',
                            desc: '输出节点结果',
                            key: '0-0-1-0',
                            required: false,
                        }
                    ]
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>status</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div style={{marginRight: '16px'}}>执行状态码: 2:执行中 3:执行成功 4:执行失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '执行状态码: 2:执行中 3:执行成功 4:执行失败',
                    key: '0-0-2',
                    required: false,
                }
            ]
           },
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>context</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '1-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回消息说明</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '返回消息说明',
                    key: '1-0-0',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>code</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div style={{marginRight: '16px'}}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                    key: '1-0-1',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>timestamp</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>无需关注</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '无需关注',
                    key: '1-0-2',
                    required: false,
                }
            ]
           }
        ],
        resHeight: "360px",
        resCode: `{
    "data": {
        "message": "执行成功",
        "result": {
            "output": {
               "title": "北京三天自由行"
            }
        },
        "status": 3
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1700129805
    }
}`,
    },
    {
        title: '【其他】服务单个模块轮询接口',
        func: 'GET',
        url: '/chain_service/block_detail',
        subTitles: '获取playbook中节点的输出结果',
        req: [
                {
                    name: 'call_back_id',
                    type: 'string',
                    required: true,
                    desc: '服务接口中返回的轮询标识',
                    location: 'query'
                },
                {
                    name: 'block_key',
                    type: 'string',
                    required: true,
                    desc: '模块标识ID',
                    location: 'query'
                },
                {
                    name: 'trace_id',
                    type: 'string',
                    required: false,
                    desc: '业务方生成的唯一ID，方便联调排查问题，建议随机生成',
                    location: 'query'
                },
                {
                    name: 'Authorization',
                    type: 'string',
                    required: true,
                    desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                    location: 'header'
                },
            ],
        res: [
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>data</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '0-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>执行结果</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '执行状态',
                    key: '0-0-0',
                    required: false,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>result</span>
                            <span style={{marginRight: '12px'}}>object</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div></div>
                        </div>
                    ),
                    type: 'object',
                    desc: '',
                    key: '0-0-1',
                    required: false,
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>output</span>
                                    <span style={{marginRight: '12px'}}>object</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>输出节点结果</div>
                                </div>
                            ),
                            type: 'object',
                            desc: '输出节点结果',
                            key: '0-0-1-0',
                            required: false,
                        }
                    ]
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>status</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div style={{marginRight: '16px'}}>执行状态码: 2:执行中 3:执行成功 4:执行失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '执行状态码: 2:执行中 3:执行成功 4:执行失败',
                    key: '0-0-2',
                    required: false,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>block_key</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div style={{marginRight: '16px'}}>模块标识ID</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '模块标识ID',
                    key: '0-0-3',
                    required: false,
                }
            ]
           },
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>context</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '1-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回消息说明</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '返回消息说明',
                    key: '1-0-0',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>code</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div style={{marginRight: '16px'}}>返回状态码: 为0表示成功，非0表示获取token失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                    key: '1-0-1',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>timestamp</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>无需关注</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '无需关注',
                    key: '1-0-2',
                    required: false,
                }
            ]
           }
        ],
        resHeight: "400px",
        resCode: `{
  "data": {
    "result": {
      "output": {
        "destination": "北京",
        "travel_type": "自由行",
        "days": 3
      }
    },
    "status": 3,
    "block_key": "553568"
  },
  "context": {
    "message": "OK",
    "code": 0,
    "timestamp": 1700129899
  }
}`,
    },
    {
        title: '【其他】服务停止接口',
        func: 'GET',
        url: '/chain_service/stop_flow',
        subTitles: '停止运行中的flow',
        req: [
                {
                    name: 'call_back_id',
                    type: 'string',
                    required: true,
                    desc: '服务接口中返回的轮询标识',
                    location: 'query'
                },
                {
                    name: 'block_index',
                    type: 'string',
                    required: true,
                    desc: '当前运行的模块标识ID',
                    location: 'query'
                },
                {
                    name: 'Authorization',
                    type: 'string',
                    required: true,
                    desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
                    location: 'header'
                },
            ],
        res: [
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>data</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '0-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>执行结果</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '执行状态',
                    key: '0-0-0',
                    required: false,
                }
            ]
           },
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>context</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '1-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回消息说明</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '返回消息说明',
                    key: '1-0-0',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>code</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div style={{marginRight: '16px'}}>返回状态码: 为0表示成功，非0表示失败</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '返回状态码: 为0表示成功，非0表示失败',
                    key: '1-0-1',
                    required: true,
                },
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>timestamp</span>
                            <span style={{marginRight: '12px'}}>integer</span>
                            <span style={{marginRight: '16px'}}>可选</span>
                            <div>无需关注</div>
                        </div>
                    ),
                    type: 'integer',
                    desc: '无需关注',
                    key: '1-0-2',
                    required: false,
                }
            ]
           }
        ],
        resHeight: "230px",
        resCode: `{
    "data": {
        "message": "success"
    },
    "context": {
        "message": "OK",
        "code": 0,
        "timestamp": 1687327616
    }
}`,
    }
]
