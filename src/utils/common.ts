declare const window: any;

import {message } from 'antd';
import { jsPDF } from "jspdf";
import * as constants from "@/constants/appConstants";

/**
 * 将内容复制到剪切板
 * @param str 复制内容
 * @returns 
 */
export const copyParamValue = (str:string, isToast=true) => {
    const isLlmopsBoard = localStorage.getItem(constants.prompt_isLlmopsBoard);
    if(navigator.clipboard && !isLlmopsBoard){
        navigator.clipboard.writeText(str);
        isToast && message.success('复制成功');
    }else {
        // 创建text area
        let textArea = document.createElement("textarea");
        textArea.value = str;
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = "absolute";
        textArea.style.opacity = '0';
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        return new Promise((resolve, reject) => {
            // 执行复制命令并移除文本框
            document.execCommand('copy')? resolve(isToast && message.success('复制成功')) : reject(isToast && message.success('复制失败'));
            textArea.remove();
        });
    }
}
/**
 * 将内容保存为PDF文件
 * @param data 内容
 * @param filename 文件名
 * @param isSave 是否保存到本地 default false
 * @returns Blob
 */
export const saveStringToPdfFile = (data: string, filename: string,isSave: boolean = false, cb = async(pdfBlob:any) =>{}) => {

    if (typeof window !== "undefined") {
        const jquery: any = window.$;
        jquery.getScript("https://pub-bjyt.s3.360.cn/360tq-package/fontBase64.js").then(function () {
            const doc = new jsPDF();
            // 添加并设置字体
            doc.addFileToVFS("SourceHanSans-Normal.ttf", window.b);
            doc.addFont('SourceHanSans-Normal.ttf', 'SourceHanSans-Normal', 'normal');
            doc.setFont('SourceHanSans-Normal');   
            // 添加文本到 PDF
            let text = data;
            let lines = doc.splitTextToSize(text, 190); // 190 是每行的最大宽度
            let y = 10; // 初始化 y 坐标
            for (let i = 0; i < lines.length; i++) {
                if (y > 280) { // 如果 y 坐标超过了页面的底部
                doc.addPage(); // 添加新的一页
                y = 10; // 重置 y 坐标
                }
                doc.text(lines[i], 10, y); // 添加文本
                y += 10; // 更新 y 坐标
            }
            if(isSave){
                doc.save(`${filename}.pdf`); // 保存到本地
            }
            // 将 PDF 保存为 Blob
            let pdfBlob = new Blob([doc.output('blob')], { type: 'application/pdf' });
            // console.log('pdfBlob==',pdfBlob);
            cb(pdfBlob);
            // return pdfBlob;  
       })
    }
}

export const formatDate = (dateS: Date | string , fmt: string = 'yyyy-MM-dd hh:mm:ss') => {
    const date = new Date(dateS);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getUTCHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    return formattedDate;
}

export const formatGMTDate = (dateS: Date | string , fmt: string = 'yyyy-MM-dd hh:mm:ss') => {
    const date = new Date(dateS);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    return formattedDate;
}


/**
 * 判断变量是否为空
 * @param variable 
 * @returns boolean
 */
export function isNullOrEmpty(variable: any): boolean {
    return variable === null || variable === undefined ||  variable === 'undefined'  ||  variable === 'null' ||
           (typeof variable === 'string' && variable.trim() === '') ||
           (Array.isArray(variable) && variable.length === 0) ||
           (typeof variable === 'object' && Object.keys(variable).length === 0);
}

export function transferQueryStr(data = {}) {
    let str = '?';
    for (let [key, value] of Object.entries(data)) {
        str += `${key}=${value}&`;
    }
    str = str.substring(0, str.length - 1);
    return str;
}
  


export const getType = (str: any) => {
    if (typeof str === 'object' && str) {
        if (str instanceof Array) {
            // 数组
            return {
                type: 'array',
                obj: str
            };
        } else {
            // 对象
            return {
                type: 'object',
                obj: str
            };
        }
    } else {
        try {
            const obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                if (obj instanceof Array) {
                    // 数组
                    return {
                        type: 'array',
                        obj: obj
                    };
                } else {
                    // 对象
                    return {
                        type: 'object',
                        obj: obj
                    };
                }
            } else {
                // 字符串 数字 布尔
                return {
                    type: typeof (str),
                    obj: str
                };
            }
        } catch (e) {
            return {
                type: 'string',
                obj: str
            };
        }
    }
}
export const getOriginalType = (str: any) => {
    if (typeof str === 'object' && str) {
        if (str instanceof Array) {
            // 数组
            return {
                type: 'array',
                obj: str
            };
        } else {
            // 对象
            return {
                type: 'object',
                obj: str
            };
        }
    } else {
        // 字符串 数字 布尔
        return {
            type: typeof (str),
            obj: str
        };
    }
}