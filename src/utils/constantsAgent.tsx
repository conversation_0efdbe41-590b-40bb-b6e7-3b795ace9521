
export const pyCodeForTest = `
import requests
import json

url = "127.0.0.1:8000/api/v2/openapi/chat"

payload = json.dumps({
  "agent_id": 406,
  "session_id": "424xxxxx",
  "user": "o3oxx",
  "query": "北京的天气怎么样",
  "stream": True,
  "verbose": True
})
headers = {
  'Authorization': 'Bearer your_token',
  'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
`;

export const curlCodeForTest = `
curl --location '127.0.0.1:8000/api/v2/openapi/chat' \
--header 'Authorization: Bearer your_token' \
--header 'Content-Type: application/json' \
--data '{
    "agent_id": 406,
    "session_id": "424xxxxx",
    "user": "o3oxx",
    "query":"北京的天气怎么样",
    "stream":true,
    "verbose":true
}'`;

export const jsCodeForTest = `
const myHeaders = new Headers();
myHeaders.append("Authorization", "Bearer your_token");
myHeaders.append("Content-Type", "application/json");

const raw = JSON.stringify({
  "agent_id": 406,
  "session_id": "424xxxxx",
  "user": "o3oxx",
  "query": "北京的天气怎么样",
  "stream": true,
  "verbose": true
});

const requestOptions = {
  method: "POST",
  headers: myHeaders,
  body: raw,
  redirect: "follow"
};

fetch("127.0.0.1:8000/api/v2/openapi/chat", requestOptions)
  .then((response) => response.text())
  .then((result) => console.log(result))
  .catch((error) => console.error(error));`;

export const agentData = [
    {
        title: '【第一步】鉴权',
        func: '',
        url: '',
        subTitles: '由api提供者提供，放入到header中Authorization: Bearer your_token',
        req: [
            ],
        res: [
        ]
    },
    {
        title: '【第二步】请求服务',
        func: 'POST',
        url: '/api/v2/openapi/chat',
        subTitles: '非流式返回',
        req: [
                {
                    name: 'body',
                    title: 'body',
                    key:'0-0',
                    location: 'body',
                    type: 'object',
                    required: false,
                    desc: '',
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>agent_id</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>必需</span>
                                    <div>agent_id，int 必填</div>
                                </div>
                            ),
                            key:'0-0-0',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: 'agent_id，int 必填'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>session_id</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>会话ID，str 调用方自行维护,用于上下文查询</div>
                                </div>
                            ),
                            key:'0-0-1',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '会话ID，str 调用方自行维护,用于上下文查询'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>user</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>必需</span>
                                    <div>用户id ，str 确认该渠道唯一用户信息</div>
                                </div>
                            ),
                            key:'0-0-2',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: '用户id ，str 确认该渠道唯一用户信息'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>query</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>必需</span>
                                    <div>用户问题，str必填</div>
                                </div>
                            ),
                            key:'0-0-3',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: '用户问题，str必填'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>session_id</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>会话ID，str 调用方自行维护,用于上下文查询</div>
                                </div>
                            ),
                            key:'0-0-4',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '会话ID，str 调用方自行维护,用于上下文查询'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>stream</span>
                                    <span style={{marginRight: '12px'}}>bool</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>是否流式返回 .默认为false，bool值，选填</div>
                                </div>
                            ),
                            key:'0-0-5',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '是否流式返回 .默认为false，bool值，选填'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{marginRight: '12px'}}>verbose</span>
                                    <span style={{marginRight: '12px'}}>string</span>
                                    <span style={{marginRight: '16px'}}>可选</span>
                                    <div>是否显示agent的中间过程 默认false, bool值，选填</div>
                                </div>
                            ),
                            key:'0-0-6',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '是否显示agent的中间过程 默认false, bool值，选填'
                        }
                    ]
                }
            ],
            reqHeight: "120px",
        reqCode: `{
    "agent_id": 406,
    "session_id":"404xxxxx",
    "user": "o3oxx",
    "query": "北京的天气怎么样",
    "stream":false, 
    "verbose":true ,
}`,
        res: [
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>data</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'array',
            desc:'',
            key: '0-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>object</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div style={{marginRight: '16px'}}></div>
                        </div>
                    ),
                    type: 'object',
                    desc: '',
                    key: '0-0-0',
                    required: true,
                }
            ]
           },
           {
            title: (
                <div>
                    <span style={{marginRight: '12px'}}>context</span>
                    <span style={{marginRight: '12px'}}>object</span>
                    <span style={{marginRight: '16px'}}>必需</span>
                    <div></div>
                </div>
            ),
            type: 'object',
            desc:'',
            key: '1-0',
            required: true,
            children: [
                {
                    title: (
                        <div>
                            <span style={{marginRight: '12px'}}>message</span>
                            <span style={{marginRight: '12px'}}>string</span>
                            <span style={{marginRight: '16px'}}>必需</span>
                            <div>返回消息说明</div>
                        </div>
                    ),
                    type: 'string',
                    desc: '返回消息说明',
                    key: '1-0-0',
                    required: true,
                },
                // {
                //     title: (
                //         <div>
                //             <span style={{marginRight: '12px'}}>code</span>
                //             <span style={{marginRight: '12px'}}>integer</span>
                //             <span style={{marginRight: '16px'}}>必需</span>
                //             <div>返回状态码: 为0表示成功，非0表示获取token失败</div>
                //         </div>
                //     ),
                //     type: 'integer',
                //     desc: '返回状态码: 为0表示成功，非0表示获取token失败',
                //     key: '1-0-1',
                //     required: true,
                // },
                // {
                //     title: (
                //         <div>
                //             <span style={{marginRight: '12px'}}>timestamp</span>
                //             <span style={{marginRight: '12px'}}>integer</span>
                //             <span style={{marginRight: '16px'}}>可选</span>
                //             <div>无需关注</div>
                //         </div>
                //     ),
                //     type: 'integer',
                //     desc: '无需关注',
                //     key: '1-0-2',
                //     required: false,
                // }
            ]
           }
        ],
        resHeight: "220px",
        resCode: `{
    "context": {
        "message": "OK",
        "code": 0,
        "request_id": "",
        "timestamp": 1717468850
    },
    "data": [
        {
            "message": {
                "role": "assistant",
                "type": "function_call",
                       "content": "{\"arguments\": \"{\\\"query__query\\\":\\\"北京\\\"}\"}",
                "content_type": "text",
                "message_id": "",
                "reply_id": "665e7ea7276d9a3f0e873606",
                "section_id": "",
                "extra_info": {
                    "local_message_id": "",
                    "input_tokens": "",
                    "output_tokens": "",
                    "total_tokens": "",
                    "plugin_status": "",
                    "time_cost": "3.01",
                    "workflow_tokens": "",
                    "bot_state": "",
                    "plugin_request": "",
                    "tool_name": "查询地区天气",
                    "plugin": "",
                    "mock_hit_info": "",
                    "log_id": ""
                },
                "status": "1"
            },
            "is_finish": true,
            "session_id": "404xxxxx",
            "seq_id": 1
        },
        {
            "message": {
                "role": "assistant",
                "type": "tool_response",
                "content": "{\"query\":\"北京\",\"results\":\"2024-06-04至2024-06-04，北京市的天气情况如下：\\n06月04日(星期二)天气：多云转阴，日间气温32℃，夜间气温21℃，西南风转南风，0级；\",\"location\":\"北京市\",\"duration\":[\"2024-06-04\",\"2024-06-04\"],\"code\":\"200\"}",
                "content_type": "text",
                "message_id": "",
                "reply_id": "665e7ea7276d9a3f0e873606",
                "section_id": "",
                "extra_info": {
                    "local_message_id": "",
                    "input_tokens": "",
                    "output_tokens": "",
                    "total_tokens": "",
                    "plugin_status": "",
                    "time_cost": "0.82",
                    "workflow_tokens": "",
                    "bot_state": "",
                    "plugin_request": "",
                    "tool_name": "",
                    "plugin": "",
                    "mock_hit_info": "",
                    "log_id": ""
                },
                "status": "1"
            },
            "is_finish": true,
            "session_id": "404xxxxx",
            "seq_id": 1
        },
    ]
}`,

    },
    {
        title: '【其他】请求服务',
        func: 'POST',
        url: '/api/v2/openapi/chat',
        subTitles: '流式返回',
        req: [
            {
                name: 'body',
                title: 'body',
                key:'0-0',
                location: 'body',
                type: 'object',
                required: false,
                desc: '',
                children: [
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>agent_id</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>agent_id，int 必填</div>
                            </div>
                        ),
                        key:'0-0-0',
                        location: 'body',
                        type: 'string',
                        required: true,
                        desc: 'agent_id，int 必填'
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>session_id</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>可选</span>
                                <div>会话ID，str 调用方自行维护,用于上下文查询</div>
                            </div>
                        ),
                        key:'0-0-1',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '会话ID，str 调用方自行维护,用于上下文查询'
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>user</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>用户id ，str 确认该渠道唯一用户信息</div>
                            </div>
                        ),
                        key:'0-0-2',
                        location: 'body',
                        type: 'string',
                        required: true,
                        desc: '用户id ，str 确认该渠道唯一用户信息'
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>query</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>必需</span>
                                <div>用户问题，str必填</div>
                            </div>
                        ),
                        key:'0-0-3',
                        location: 'body',
                        type: 'string',
                        required: true,
                        desc: '用户问题，str必填'
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>session_id</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>可选</span>
                                <div>会话ID，str 调用方自行维护,用于上下文查询</div>
                            </div>
                        ),
                        key:'0-0-4',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '会话ID，str 调用方自行维护,用于上下文查询'
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>stream</span>
                                <span style={{marginRight: '12px'}}>bool</span>
                                <span style={{marginRight: '16px'}}>可选</span>
                                <div>是否流式返回 .默认为false，bool值，选填</div>
                            </div>
                        ),
                        key:'0-0-5',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '是否流式返回 .默认为false，bool值，选填'
                    },
                    {
                        title: (
                            <div>
                                <span style={{marginRight: '12px'}}>verbose</span>
                                <span style={{marginRight: '12px'}}>string</span>
                                <span style={{marginRight: '16px'}}>可选</span>
                                <div>是否显示agent的中间过程 默认false, bool值，选填</div>
                            </div>
                        ),
                        key:'0-0-6',
                        location: 'body',
                        type: 'string',
                        required: false,
                        desc: '是否显示agent的中间过程 默认false, bool值，选填'
                    }
                ]
            }
        ],
        reqHeight: "120px",
    reqCode: `{
"agent_id": 406,
"session_id":"404xxxxx",
"user": "o3oxx",
"query": "北京的天气怎么样",
"stream":true, 
"verbose":true ,
}`,
    res: [
       {
        title: (
            <div>
                <span style={{marginRight: '12px'}}>data</span>
                <span style={{marginRight: '12px'}}>object</span>
                <span style={{marginRight: '16px'}}>必需</span>
                <div></div>
            </div>
        ),
        type: 'object',
        desc:'',
        key: '0-0',
        required: true,
        children: [
            {
                title: (
                    <div>
                        <span style={{marginRight: '12px'}}>message</span>
                        <span style={{marginRight: '12px'}}>object</span>
                        <span style={{marginRight: '16px'}}>必需</span>
                        <div style={{marginRight: '16px'}}></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0-0',
                required: true,
            }
        ]
       },
    ],
    resHeight: "220px",
    resCode: `{
        data: {"message": {"role": "assistant", "type": "function_call", "content": "{\"arguments\": \"{\\\"query__query\\\":\\\"北京\\\"}\"}", "content_type": "text", "message_id": "", "reply_id": "665e7ff8276d9a3f0e87360d", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "6.10", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "查询地区天气", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 1}
        data: {"message": {"role": "assistant", "type": "tool_response", "content": "{\"query\":\"北京\",\"results\":\"2024-06-04至2024-06-04，北京市的天气情况如下：\\n06月04日(星期二)天气：多云转阴，日间气温32℃，夜间气温21℃，西南风转南风，0级；\",\"location\":\"北京市\",\"duration\":[\"2024-06-04\",\"2024-06-04\"],\"code\":\"200\"}", "content_type": "text", "message_id": "", "reply_id": "665e7ff8276d9a3f0e87360d", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "0.94", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 1}
        data: {"message": {"role": "assistant", "type": "answer", "content": "{\"error\":{\"code\":\"10137\",\"message\":\"Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. (request id: 20240604104623671163765aXTBVTaR)\"}}", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "0", "output_tokens": "0", "total_tokens": "0", "plugin_status": "", "time_cost": "8.92", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 0}
        data: {"message": {"role": "assistant", "type": "follow_up", "content": "天津的未来三天天气怎么样", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 1}
        data: {"message": {"role": "assistant", "type": "follow_up", "content": "北京今日空气质量怎么样", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 2}
        data: {"message": {"role": "assistant", "type": "follow_up", "content": "北京和天津天气对比", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 3}
        data: done

}`,
    },
]
