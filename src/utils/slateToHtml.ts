/**
 * 将 Slate.js 的 JSON 文档转换为 HTML 字符串
 * @param {Array} nodes - Slate 文档的顶层节点数组 (editor.children)
 * @returns {string} 生成的 HTML 字符串
 */
export function slateToHtml(nodes) {
	let hasText = false
  // 递归处理节点
  const serializeNode = (node) => {
    // 处理文本节点
    if (node.text) {
			hasText = true;
      let text = node.text;
      
      // 处理文本格式（粗体、斜体、下划线等）
      if (node.bold) text = `<strong>${text}</strong>`;
      if (node.italic) text = `<em>${text}</em>`;
      if (node.underline) text = `<u>${text}</u>`;
      if (node.code) text = `<code>${text}</code>`;
      
      return text;
    }

    // 递归处理子节点
    const children = node.children?.map(serializeNode).join('');

    // 根据节点类型生成对应的 HTML 标签
    switch (node.type) {
      case 'paragraph':
        return `<p>${children}</p>`;
      
      case 'heading-one':
        return `<h1>${children}</h1>`;
      
      case 'heading-two':
        return `<h2>${children}</h2>`;
      
      case 'heading-three':
        return `<h3>${children}</h3>`;
      
      case 'list-item':
        return `<li>${children}</li>`;
      
      case 'numbered-list':
        return `<ol>${children}</ol>`;
      
      case 'bulleted-list':
        return `<ul>${children}</ul>`;
      
      case 'block-quote':
        return `<blockquote>${children}</blockquote>`;
      
      case 'link':
        return `<a href="${node.url}" target="${node.target || '_blank'}">${children}</a>`;
      
      case 'image':
        return `<img src="${node.url}" alt="${node.alt || ''}" />`;
      
      // 处理自定义节点类型
      case 'mention':
				hasText = true;
				return `<span \
			  style="padding: 1px 4px; margin: 0 2px; vertical-align: middle; display: inline-block; border-radius: 4px; font-size: 0.9em; color:#006BFF; background-color:#F0F2FF;" \
				data-mention="true" \
				data-mention-type="${node.payload.type}" \
				data-mention-id="${node.payload.id || ''}" \
				data-mention-servername="${node.payload.server_name || ''}" \
				 >${node.payload.name}</span>`.replace(/\t/g, '');
      
      // 默认情况：直接返回子节点
      default:
        return children;	
    }
  };

  // 处理顶层节点数组
  let html = nodes.map(serializeNode).join('')
	return hasText ? html : ''
}


