const brokenLine = ({ title, legend, labelArr, dataArr }: any) => {
    const option = {
        grid: {
            top: '8%',
            left: '0',
            right: '0',
            bottom: '0',
            containLabel: true
        },
        title: {
            text: title
        },
        legend: {
            data: legend
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type:'shadow'
            }
        },
        xAxis: {
            type: 'category',
            data: labelArr
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: legend[0],
            data: dataArr,
            type: 'line'
        }]
    }

    return option;
}

const columnChart = ({ title, legend, labelArr, dataArr }: any) => {
    const option = {
        grid: {
            top: '8%',
            left: '0',
            right: '0',
            bottom: '0',
            containLabel: true
        },
        title: {
            text: title
        },
        legend: {
            data: legend
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type:'shadow'
            }
        },
        xAxis: {
            type: 'category',
            data: labelArr
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: legend[0],
            data: dataArr,
            type: 'bar',
            barWidth: '60%'
        }]
    }

    return option;
}

const pieChart = ({ title, labelArr, dataArr }: any) => {
    const seriesData = labelArr.map((item: any, index: number) => {
        return {
            value: dataArr[index],
            name: item
        }
    })

    const option = {
        grid: {
            top: '8%',
            left: '0',
            right: '0',
            bottom: '0',
            containLabel: true
        },
        legend: {
            orient: 'vertical',
            left:'left'
        },
        title: {
            text: title,
            left: 'center'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                type: 'pie',
                radius: '85%',
                data: seriesData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    }

    return option;
}

export const drawEcharts = (params: any) => {
    const { 
        title, chat_type,
        xAxis, yAxis, legend_data,
        categories, values
    } = params;

    let data;
    if (chat_type === 'PieChart') {
        data = {
            title,
            labelArr: categories,
            dataArr: values
        }
    } else {
        data = {
            title,
            legend: legend_data,
            labelArr: xAxis,
            dataArr: yAxis
        }
    }

    switch (chat_type) {
        case 'BarChart':
            return columnChart(data);
        case 'PieChart':
            return pieChart(data);
        default:
            return brokenLine(data);
    }
}

