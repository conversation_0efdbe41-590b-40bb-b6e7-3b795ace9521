const getDefaultPromptConfig = () => {
	return {
		system: "",
		user: "<p data-slate-node=\"element\"><span data-slate-node=\"text\"><span data-slate-leaf=\"true\"><span data-slate-zero-width=\"z\" data-slate-length=\"0\">﻿</span></span></span><span class=\"\" data-slate-node=\"element\" data-slate-inline=\"true\" data-slate-void=\"true\" contenteditable=\"false\" data-mention=\"true\" data-mention-type=\"userInput\" style=\"padding: 1px 4px; margin: 0px 2px; vertical-align: bottom; display: inline-block; border-radius: 4px; font-size: 0.9em; box-shadow: none; position: relative; color: rgb(0, 107, 255); background-color: rgb(240, 242, 255);\"><span contenteditable=\"false\" style=\"color: rgb(0, 107, 255); vertical-align: middle;\">用户输入</span></span><span data-slate-node=\"text\"><span data-slate-leaf=\"true\"><span data-slate-zero-width=\"n\" data-slate-length=\"0\">﻿<br></span></span></span></p>",
		user_params: [
			{
				"type": "paragraph",
				"children": [
					{
						"text": ""
					},
					{
						"type": "mention",
						"payload": {
							"name": "用户输入",
							"type": "userInput",
							"icon": "",
							"color": "#64B5F6"
						},
						"children": [
							{
								"text": ""
							}
						]
					},
					{
						"text": ""
					}
				]
			}
		]
	}
}

export default getDefaultPromptConfig