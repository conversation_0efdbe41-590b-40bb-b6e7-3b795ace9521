/*
 * @Author: yh
 * @Date: 2025-01-13 16:54:11
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-03-18 17:27:15
 * @FilePath: \prompt-web\src\utils\fingerprint.ts
 */
import FingerprintJS from '@fingerprintjs/fingerprintjs'; //不建议使用这个插件，件以上pro版本，这个受缓存影响，刷新和清缓存获取到的有可能不是同一个
import CryptoJS from 'crypto-js'; // 直接使用crypto在有的浏览器里面{}
import { resolve } from 'path';
import { useEffect, useState } from 'react';
let cachedFingerprint: string | null = null;

const useFingerprint = async () => {
  // const [fingerprint, setFingerprint] = useState<string | null>(null);
  let fingerprint: any = '';
  const fp = await FingerprintJS.load();
  const result = await fp.get();
  fingerprint = result.visitorId
  
  return fingerprint;
};

export default useFingerprint;



