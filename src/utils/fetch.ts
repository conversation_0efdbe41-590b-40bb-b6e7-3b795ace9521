/**
 * <AUTHOR>
 * @time 2022-01-07
 * @description fetch封装
 */

import { message } from 'antd';
import { getCurrentUrlParamValue } from "@/utils/url";

import Router from 'next/router'
import { isNullOrEmpty } from "@/utils/common";

let lastErrorMessage: string | null = null;
let lastErrorKey: string | null = null;

// 错误处理函数，优化 message.error
const handleError = (errorMessage: string) => {
    // 判断错误消息是否相同
    if (errorMessage === lastErrorMessage) {
        // 如果错误相同，并且有 key 需要覆盖之前的错误提示
        if (lastErrorKey) {
            message.error({
                content: errorMessage,
                key: lastErrorKey, // 使用相同的 key 来覆盖之前的错误
            });
        }
        return;
    }

    // 生成一个新的唯一 key，确保每次是一个新的消息
    const newKey = `error_${Date.now()}`;

    // 如果有之前的错误，销毁它
    if (lastErrorKey) {
        message.destroy(lastErrorKey);
    }

    // 显示新的错误消息
    message.error({
        content: errorMessage,
        key: newKey, // 使用新的 key 显示新的错误消息
    });

    // 更新记录的错误信息和 key
    lastErrorMessage = errorMessage;
    lastErrorKey = newKey;
};

export interface IResponseStructure {
    data: any;
    context: any;
    code: any;
}

//  转换为get方法需要的查询字符串
export function transferQueryStr(data = {}) {
    let str = '?';
    for (let [key, value] of Object.entries(data)) {
        str += `${key}=${value}&`;
    }
    str = str.substring(0, str.length - 1);
    return str;
}

// 对返回数据的状态码进行处理
function checkStatus(response: IResponseStructure, url: string) {
    const {
        context,
        data,
    } = response;

    // 特殊处理update_block
    if (url.includes('/update_block')) {
        return response;
    }
    // 知识库的返回结构不一样
    if (response?.code === 200) {
        if (url.includes('/api/knowledge_data') || url.includes('/api/knowledge_qa') || url.includes('/knowledge_api')) {
            return response;
        }
    }
    const { message, code } = context
    if (code === 0) {
        if (url.includes('api_info')
            || url.includes('/api/chat')
            || url.includes('/api/agents/delete')
            || url.includes('/api/agents/copy_market')
            || url.includes('/api/agents/public_detail')
            || url.includes('/api/agents/detail')
            || url.includes('/api/feedback-list')
            || url.includes('api/team')
            || url.includes('/api/channel')
            || url.includes('/api/admin_agents')
            || url.includes('/api/member/list')
            || url.includes('/api/member/system/list')
            || url.includes('/api/team-audits')
            || url.includes('/api/market-audits')
            || url.includes('/api/admin/')
            || url.includes('/api/knowledge/list')
            || url.includes('/api/knowledge/segment_list')
            || url.includes('/api/knowledge/qa_list')
            || url.includes('/api/knowledge/knowledge_list')
            || url.includes('/api/knowledge/delete')
            || url.includes('/api/knowledge/add')
            || url.includes('/api/knowledge/rename')
            || url.includes('/api/agents/api_auth_list')
            || url.includes('/api/knowledge/upload')
            || url.includes('/api/external_user/list')
            || url.includes('/api/external_user/search')
            || url.includes('/api/knowledge_qa/list')
            || url.includes('/api/knowledge_qa/add')
            || url.includes('/api/knowledge/knowledge_fragment')
            || url.includes('/api/knowledge/qa_info')
            || url.includes('/api/knowledge/generate_similar_question')
            || url.includes('/api/knowledge/similar_question')
            || url.includes('/api/knowledge/update_fragment')
            || url.includes('/api/knowledge_qa/delete')
            || url.includes('/api/knowledge_qa/info')
            || url.includes('/api/knowledge_qa/update')
            || url.includes('/api/knowledge_qa/train')
            || url.includes('/api/knowledge_qa/train_task_status')
            || url.includes('/api/knowledge_qa/knowledge_data_list')
            || url.includes('/api/agent/report/list')
            || url.includes('/api/agent/report/agent_list')
            || url.includes('/api/agent/report/question_list')
            || url.includes('/api/deployment/gui_white/add')
            || url.includes('/api/deployment/gui_white/update')
            || url.includes('/api/knowledge/knowledge_data_list')
            || url.includes('/api/knowledge/knowledge_data_list')
            || url.includes('/api/agents/create_iframe')
            || url.includes('/api/agents/agent_version')
            || url.includes('/api/mcp_servers/detail')
            || url.includes('/api/mcp_servers/publish')
            || url.includes('/api/mcp_servers/unpublish')
            || url.includes('/api/version/mcp_servers/detail')
            || url.includes('/api/version/mcp_servers/publish')
            || url.includes('/api/version/mcp_servers/unpublish')
            || url.includes('/api/deployment/gui_white/add')
            || url.includes('/api/deployment/gui_white/update')

        ) {
            return response;
        } else {
            return data;
        }
    } else if (code == 4001 || code == 51004 || code == 4002) {
        // const localTeamId = localStorage.getItem('prompt_teamId');
        // if(Router.asPath !== '/' && !isNullOrEmpty(localTeamId)) {
        //     localStorage.setItem('prompt_targetUrl',`${document.location.href}`)// 保留当前地址
        // }
        // 遇到外部平台跳转到指定页面，单点登录没问题，但登录成功后，会自动清空跳转到home，请处理个别接口报错token问题，判断token有值后再发请求
        localStorage.setItem('prompt_userName', '')
        localStorage.setItem('prompt_authorization', '')
        localStorage.setItem('prompt_userId', '');
        localStorage.setItem('prompt_teamId', '');
        // 当前登录的平台
        let prompt_platform = localStorage.getItem('prompt_platform') || '';

        if (document.location.href.indexOf('tuituiBoard') > -1 ||
            document.location.href.indexOf('zhiyuBoard') > -1 ||
            document.location.href.indexOf('yunpanBoard') > -1 ||
            document.location.href.indexOf('companyBrain') > -1 || (prompt_platform && prompt_platform !== 'agent')) {
            return;
        }

        if (window.location.href.indexOf('/mobile') > -1) {
            localStorage.setItem("prompt_targetUrl", window.location.href);
            Router.push('/')
            return;
        }
        Router.push('/')
    } else if (code == 54003) {
        // 权限不足提示删除
        console.error(message)
    } else if (code === 90002) {
        // 知识库列表获取异常 不添加错误message提升
        console.error(message)
    } else {
        // 修改描述不返回错误  知识库列表获取异常不返回错误
        if ((!url.includes('api/file/upload') && !url.includes('/api/flow/check_flow')) || !url.includes('api/v2/nami_agent_flow/knowledge_list')) {
            handleError(message);
            throw new CustomError(code, message);
        }
    }
}

// 扩展原生的Error对象，使其可以返回错误码
class CustomError extends Error {
    constructor(code: number, message?: string) {
        super();
        this.message = code + (message || '请求失败');
    }
}
export const fetchData = async (
    url: string,
    data = {},
    method: string = 'GET',
    options?: Object
) => {
    let customHeaders: RequestInit = {
        mode: 'cors',
        redirect: 'follow',
        referrer: 'no-referrer',
        ...options,
    };

    const p: any = data;

    customHeaders.headers = {
        "teamId": p.team_id || getCurrentUrlParamValue('teamId') || '',
        "Authorization": localStorage.getItem('prompt_authorization') || '',
    }

    method = method.toUpperCase();
    if (method === 'GET') {
        const queryStr = transferQueryStr(data);
        url = url + queryStr;
        //  console.log('queryStr url = ',url)
        customHeaders.method = 'GET';
    }
    if (data instanceof FormData && method === 'POST') {
        customHeaders.method = 'POST'
        customHeaders.body = data
    } else if (method === 'POST') {
        const reqData = JSON.stringify(data);
        customHeaders.method = 'POST';
        customHeaders.body = reqData;
        customHeaders.headers = {
            'content-Type': 'application/json',
            ...customHeaders.headers
        };
    }
    if (method === 'DELETE') {
        const queryStr = transferQueryStr(data);
        url = url + queryStr;
        const reqData = JSON.stringify(data);
        customHeaders.method = 'DELETE';
        customHeaders.body = reqData;
        customHeaders.headers = {
            'content-Type': 'application/json',
            ...customHeaders.headers
        };
    }
    if (method === 'PUT') {
        const reqData = JSON.stringify(data);
        customHeaders.method = 'PUT';
        customHeaders.body = reqData;
        customHeaders.headers = {
            'content-Type': 'application/json',
            ...customHeaders.headers
        };
    }

    //  console.log(url + "头部-=" + JSON.stringify(customHeaders.headers))

    return fetch(url, customHeaders)
        .then((response) => {
            // 知识库下载文件接口，单独处理

            if (url.includes('/api/knowledge/file_info/') || url.includes('/api/flow/output') || url.includes('/api/flow/version/output') || url.includes('/api/flow/output') || url.includes('/api/flow/version/output')) {

                return response;
            }
            if (response.ok) {
                // 知识库下载文件接口
                if (url.includes('/api/knowledge/file_info/') || url.includes('/api/flow/output') || url.includes('/api/flow/version/output') || url.includes('/api/flow/output') || url.includes('/api/flow/version/output')) {
                    return response;
                }
                const isStream =
                    response.headers.get('content-type') === 'application/octet-stream';
                if (isStream) {
                    // 如果是流的话直接返回
                    return response.blob();
                } else if (url.includes('/api/provider/model-providers/')) {// 加载模型配置处理
                    const type = response.headers.get('content-type')
                    if (type && type.indexOf('image') > -1) { // 图片类型 直接返回
                        return response;
                    }
                    return response.json().then((data) => {
                        return checkStatus(data, response.url);
                    })
                } else {
                    return response.json().then((data) => {
                        return checkStatus(data, response.url);
                    });
                }
            } else {
                if (response.url.includes('/prompt/text2img') && response.status === 500) {
                    handleError('服务端超时');
                } else {
                    response.json().then((data) => {
                        const { messgae, errors } = data;
                        handleError(errors?.[0]?.msg || '服务端响应异常');
                        // throw new CustomError(response.status, errors[0].msg);
                    });
                }
            }
        })
        .catch((error) => {
            // console.log('error>>',error)
            // throw new CustomError(error.errCode, error.errMsg);
        });
};