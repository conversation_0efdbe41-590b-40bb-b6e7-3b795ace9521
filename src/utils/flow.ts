import html2canvas from 'html2canvas';
import { reqImageUpload } from '@/service/api'
import { upateFlowSnapshot } from '@/service/flow'

export const getWaibuModelList = (modelList: any) => {
    let resList: any = [];
    let defaultList = [{ value: 'zhinao 360gpt-pro', label: '360gpt-pro', provider: 'zhinao'}];
    let listVisible = ['volcengine/DeepSeek-R1-250120', 'deepseek-chat', 'deepseek-r1', 'qwen2.5-14b-instruct', 'qwen2.5-32b-instruct', 'qwen2.5-7b-instruct'];
    let zhinaoList = modelList.find((model:any) => model.provider === 'zhinao');
    if(zhinaoList) {
        zhinaoList.models.forEach((item:any) => {
            if (listVisible.includes(item.model)) {
                resList.push({ value: 'zhinao' + ' ' + item.model, label: item.label.zh_<PERSON>, provider: 'zhinao' });
            }
        });
        return [...defaultList, ...resList];
    }
    return defaultList as any;
}


const dataURLtoBlob =   function (dataurl){
		let arr = dataurl.split(','),
				mime = arr[0].match(/:(.*?);/)[1],
				bstr = atob(arr[1]),
				n = bstr.length,
				u8arr = new Uint8Array(n);
		while (n--) {
				u8arr[n] = bstr.charCodeAt(n);
		}
		return new Blob([u8arr], { type: mime })
}

export const uploadSnapShot = async (dom: HTMLElement, template_id: string) => {
	const canvas = await html2canvas(dom)
	// 转成图片
	const img = canvas.toDataURL('image/png');
	// 转成blob
	const blob = dataURLtoBlob(img);
	//上传
	const formData = new FormData();
	formData.append('file', blob);
	const res = await reqImageUpload(formData)
	const imgUrl= res?.image_url
	await upateFlowSnapshot({ thumb: imgUrl, template_id })
}