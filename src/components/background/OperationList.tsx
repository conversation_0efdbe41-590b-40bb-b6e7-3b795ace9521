import { Button, Input, Select, Table, Popconfirm } from 'antd';

import commonListStyles from '@/styles/CommonList.module.scss'
import teamMemberStyles from '@/styles/TeamMembers.module.css'
import operationListStyles from '@/styles/OperationList.module.scss'


import search from '@/images/commonList/search.png'
import deleteIcon from '@/images/delete.svg'

import { useState, useEffect } from 'react';
import Router, { useRouter } from 'next/router'

export default function OperationList(prop:any) {
    const router = useRouter();
    const teamId = router.query.teamId;
    const [openAddProject, setOpenAddProject] = useState(false);   
    const [tableData, setTableData] = useState([{
        name: "wang",
        projectName: "wang",
        type: 'API',
        summitTime: "2024.01.08",
        status:"正常",
        result:"通过"
     

    }])
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total:any) => `共 ${total} 条记录`
    })
    const [curPage, setCurPage] =  useState(1)

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
           
        },
        {
            title: '项目名称',
            dataIndex: 'projectName',
            key: 'projectName',

        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',

        },
        {
            title: '提交时间',
            dataIndex: 'summitTime',
            key: 'summitTime',

        },
      
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (text:any) => {
                return <>
                    
                </>
            }
        },
        {
            title: '结果',
            dataIndex: 'result',
            key: 'result',
           
        },
        {
            title: '操作',
            dataIndex: 'name',
            key: 'name',
            render: (_:any, record:any) => {
                return  <Popconfirm
                    title={"是否要删除 "+record.name+' ?'}
                    description=""
                    onConfirm={() => {

                    }}
                    onCancel={() => {

                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <img src={deleteIcon.src} className={teamMemberStyles.deleteBtn} />
                </Popconfirm>
            }
        }
    ]

    return (
    <>
      <div className={commonListStyles.commonList}>
        <div className={commonListStyles.titleWrapper}>
                    <span className={commonListStyles.title}>账号审核</span>  
            <div>
                <Input 
                    addonBefore={<Select
                    value='全部'
                    style={{width: 80,borderRadius: '8px'}}
                    options={[
                        { value: '全部', label: '全部' }
                    ]} 
                    />} 
                    placeholder="请输入关键词搜索" 
                    addonAfter={<img src={search.src} width='16' />}
                    className={'noBorderInput commonSearchInput'}
                />
                <Button type='primary' className={teamMemberStyles.addMember + ' primaryButton'} onClick={() => {setOpenAddProject(true)}}>添加项目</Button>
            </div>
            
        </div>
        <div className={commonListStyles.tableList + ' normalFont'}>
            <Table 
                columns={columns} 
                dataSource={tableData} 
                pagination={pagination}
                onChange={(pagination) => {
                    setCurPage(pagination.current || 1)
                }}
                className='commonTable normalFont'
            />
        </div>
      </div>
    </>
    )
}
