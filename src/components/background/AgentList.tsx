import { useState, useEffect, useRef } from 'react';
import { Button, Input, Table, message, Spin, Modal, Tooltip, Popconfirm, Empty, Switch } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined, SmileOutlined, LoadingOutlined } from '@ant-design/icons';
import commonListStyles from '@/styles/CommonList.module.scss';
import operationListStyles from '@/styles/OperationList.module.scss';
import channelClassify from '@/styles/channelClassify.module.scss';
import commonSelectIcon from '@/images/commonList/common-select.svg'
import search from '@/images/commonList/search.png';
import infoIcon from "@/images/info.png";
import { useRouter } from "next/router";
import { reqAgentList, reqAgentPublish, reqUnpublishedChannels,reqAgentSwitch,reqGetAgentPublishStatus} from '@/service/background/agent';
import { useRecoilState } from 'recoil';

export default function AgentList(prop: any) {
    const router = useRouter();
    const [tableData, setTableData] = useState(new Array())
    const [searchKey, setSearchKey] = useState('')
    const [agentId, setAgentId] = useState()
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)
    const [curPage, setCurPage] = useState(1)

    // const [propelList, setPropelList] = useState<any[]>([])
    // const [selectPropel, setSelectPropel] = useState(0)

    // 发布信息
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalData, setModalData] = useState(new Array())
    const [recordName, setRecordName] = useState('');
    const [recordAgentId, setRecordAgentId] = useState();
    const [teamId,setTeamId] = useState(0)

    //api管理
    const [isOpenApi,setIsOpenApi] = useState(1)

    //网站嵌入管理

    const [isOpenSdk,setIsOpenSdk] = useState(1)

    const columns = [
        {
            title: 'Agent ID',
            dataIndex: 'agent_id',
            key: 'agent_id',
            width: 200,
        },
        {
            title: 'Agent名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '所属项目',
            dataIndex: 'team_name',
            key: 'team_name',
        },
        {
            title: '操作',
            dataIndex: 'name',
            key: 'name',
            width: 180,
            render: (_: any, record: any) => {
                return (<div>
                    <a className={operationListStyles.detailBtn} onClick={() => openPublicDetail(record)}>发布信息</a>
                </div>)
            }
        }
    ]

    const getAgentPublishStatus  = async(agent_id:any) =>{
        let res = await reqGetAgentPublishStatus({
            agent_id: agent_id,
        });
        if(res?.data){
            setIsOpenApi(res.data.openapi_admin_status)
            setIsOpenSdk(res.data.iframe_admin_status)
        }

    }


    useEffect(() => {
        getMarketAuditList()
    }, [agentId, searchKey, curPage, pageSize])

    useEffect(() => {
        const queryTeamId = router.query.teamId;
        const localTeamId = localStorage.getItem('prompt_teamId');

        if (queryTeamId && typeof queryTeamId === 'string') {
        setTeamId(parseInt(queryTeamId));
        } else if (localTeamId) {
        setTeamId(parseInt(localTeamId));
        }
    }, [router.query.teamId]);

    // useEffect(() => {
    //     // select值待定（固定值 or 接口返回）
    //     const propelList = [
    //         { key: 0, label: '全部' },
    //         { key: 1, label: '项目1' },
    //         { key: 2, label: '项目2' },
    //         { key: 3, label: '项目3' },
    //         { key: 4, label: '项目4' },
    //     ]
    //     setPropelList(propelList)
    // }, [])

    const searchKeyEnter = (e: any) => {
        if (e.key === 'Enter') {
            setSearchKey(e.target.value)
        }
    }

    const searchAgentIdEnter = (e: any) => {
        if (e.key === 'Enter') {
            setAgentId(e.target.value)
        }
    }

    const onInputChangeHandle = (e: any) => {
        if (e.target.value === '') {
            setSearchKey('')
        }
    }

    const onAgentIdChangeHandle = (e: any) => {
        if (e.target.value === '') {
            setAgentId(undefined)
        }
    }

    const getMarketAuditList = async () => {
        const params = {
            agent_id: Number(agentId),
            keyword: searchKey,
            page: curPage,
            page_size: pageSize,
            order_by: 1,
        }
        let res = await reqAgentList(params);
        if (res?.data) {
            setTableData(res.data)
        }
        if (res?.pagination) {
            setPageSize(res.pagination.page_size)
            setTotal(res.pagination.total)
        }
    }

    // const changePropelHandle = (key: number) => {
    //     setSelectPropel(key)
    // }

    const openPublicDetail = async (record: any) => {
        setIsModalOpen(true)
        setRecordName(record.name);
        setRecordAgentId(record.agent_id);
        getAgentPublishStatus(record.agent_id)
        let res = await reqAgentPublish({ agent_id: record.agent_id })
        if (res?.data) {
            setModalData(res.data)
        }
    }

    // 更新modal
    const resModalData = async () => {
        let res = await reqAgentPublish({ agent_id: recordAgentId })
        if (res?.data) {
            setModalData(res.data)
        }
    }

    // const getItems = (key: number) => {
    //     if (key === 1) {
    //         const items: MenuProps['items'] = propelList.map((item, index) => {
    //             return {
    //                 key: item.key,
    //                 label: (
    //                     <div className={commonOuterStyle.selectProItem} onClick={() => { changePropelHandle(item.key) }}>
    //                         <span className={commonOuterStyle.selectProItemTitle}>{item.label}</span>
    //                         {selectPropel === item.key ? <img className={commonOuterStyle.selectProItemTrue} src={commonSelectIcon.src} alt="" /> : null}
    //                     </div>
    //                 ),
    //             }
    //         })
    //         return { items }
    //     }
    // }

    // 取消所有发布
    const handleAllConfirm = async () => {
        let ids = modalData.map(item => item.channel_id);
        let res = await reqUnpublishedChannels({
            agent_id: recordAgentId,
            channels: ids,
        });
        if (res?.context?.code == 0) {
            resModalData()
            message.success("取消成功")
        } else {
            message.error("取消失败")
        }
    };

    // 单个取消发布
    const handleChildConfirm = async (channel_id: number) => {
        let res = await reqUnpublishedChannels({
            agent_id: recordAgentId,
            channels: [channel_id],
        });
        if (res?.context?.code == 0) {
            resModalData()
            message.success("取消发布成功")
        } else {
            message.error("取消发布失败")
        }
    }

    const handleApiSwitchChange = async(checked:boolean) =>{
        console.log('checked',checked)
        let switch_satus;
        if(checked){
            switch_satus = 1;
        }else{
            switch_satus = 2;
        }
        try{
            const res = await reqAgentSwitch ({
                agent_id:recordAgentId,
                switch_type:1,
                switch_satus:switch_satus,
            })
            setIsOpenApi(switch_satus)
        }catch(err){
            // message.error("切换失败")
        }
       
    }

    const handleSdkSwitchChange = async(checked:boolean) =>{
        console.log('checked',checked)
        let switch_satus;
        if(checked){
            switch_satus = 1;
        }else{
            switch_satus = 2;
        }
        try{
            const res = await reqAgentSwitch ({
                agent_id:recordAgentId,
                switch_type:2,
                switch_satus:switch_satus,
            })
            setIsOpenSdk(switch_satus)
           
        }catch(err){
            // message.error("切换失败")
        }

    }

    useEffect(()=>{
        console.log('isOpenApi========',isOpenApi)
        console.log('isOpen--------',isOpenSdk)
    },[isOpenApi,isOpenSdk])

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <span className={commonListStyles.title}>Agent列表</span>
                    <div className={channelClassify.commonSearch}>
                        {/* '所属项目' 筛选暂时不做 */}
                        {/* <Dropdown menu={getItems(1)}>
                            <Space>
                                <span className={channelClassify.selectTitle}>所属项目:</span>
                                <span className={channelClassify.selectSubtitle}>{propelList.find(item => item.key === selectPropel)?.label}</span>
                                <DownOutlined />
                            </Space>
                        </Dropdown>
                        <div className={channelClassify.cutUp}></div> */}
                        <div className={channelClassify.commonSearchInput}>
                            <span className={channelClassify.selectTitle}> Agent ID </span>
                            <Input
                                placeholder="请输入ID"
                                style={{ width: '200px', borderRadius: '4px' }}
                                onPressEnter={e => searchAgentIdEnter(e)}
                                onChange={(e) => { onAgentIdChangeHandle(e) }}
                            />
                        </div>
                        <div className={'commonSearchInput'}>
                            <Input
                                placeholder="请输入Agent名称搜索"
                                prefix={<img src={search.src} width='16' />}
                                allowClear
                                style={{ width: '256px', borderRadius: '4px' }}
                                onPressEnter={e => searchKeyEnter(e)}
                                onChange={(e) => { onInputChangeHandle(e) }}
                            />
                        </div>
                    </div>
                </div>
                <div className={commonListStyles.tableList + ' normalFont'}>
                    <Table
                        columns={columns}
                        scroll={{ y: 720 }}
                        rowKey={(record) => record.id}
                        dataSource={tableData}
                        pagination={{
                            current: curPage,
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: any) => `共 ${total} 条记录`
                        }}
                        onChange={(pagination) => {
                            setCurPage(pagination.current || 1)
                            setPageSize(pagination.pageSize || 20)
                        }}
                        className='commonTable normalFont'
                    />
                </div>
            </div>

            <Modal
                width={840}
                className={["commonModal", channelClassify.commonList].join(' ')}
                title={recordName + '-发布信息'}
                centered
                destroyOnClose
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                maskClosable={false}
            >
                <div className={channelClassify.modalContent}>
                    <div className={channelClassify.modalSwitch}>
                        <div className={channelClassify.switchItem}>
                            <div className={channelClassify.itemTop}>
                                <span style={{marginRight:'12px'}}>API管理</span>
                                <Switch  checked={isOpenApi == 1}   onChange={handleApiSwitchChange}></Switch>
                            </div>
                            <div className={channelClassify.itemBottom}>
                                {/* 文案根据开关状态来显示 */}
                                {isOpenApi == 1 ?
                                    <span>关闭后，【{recordName}】Agent将无法再使用API能力</span>
                                    :
                                    <span>已关闭，【{recordName}】Agent已无法使用API能力</span>
                                }
                            </div>
                        </div>
                        <div className={channelClassify.switchItem}>
                            <div className={channelClassify.itemTop}>
                                <span style={{marginRight:'12px'}}>网站嵌入管理</span>
                                <Switch checked={isOpenSdk == 1}  onChange={handleSdkSwitchChange}></Switch>
                            </div>
                            <div className={channelClassify.itemBottom}>
                                {/* 文案根据开关状态来显示 */}
                                {isOpenSdk == 1?
                                    <span>关闭后，【{recordName}】Agent将无法再使用网站嵌入能力</span>
                                    :
                                    <span>已关闭，【{recordName}】Agent已无法使用网站嵌入能力</span>
                                }
                            </div>
                        </div>
                    </div>
                    <div className={channelClassify.modalSearch}>
                        <div>
                            <span className={channelClassify.modalNumTitle}>发布渠道 </span>
                            <span className={channelClassify.modalNum}>({modalData.length})</span>
                        </div>
                        <Popconfirm
                            title="确定取消所有发布吗？"
                            onConfirm={handleAllConfirm}
                            okText="确定"
                            cancelText="取消"
                        >
                            <Button>取消所有发布</Button>
                        </Popconfirm>
                    </div>
                    <div className={modalData.length > 0 ? channelClassify.modalList : channelClassify.modalEmpty}>
                        {
                            modalData.length > 0 ? modalData.map((item, index) => (
                                <div key={index} className={channelClassify.modalItem}>
                                    <div className={channelClassify.agentItem}>
                                        <div className={channelClassify.itemInfo}>
                                            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                                                {item.image_url && <img src={item.image_url} alt="" style={{width:'26px', height:'26px'}}/>}
                                                <div className={channelClassify.itemName}>{item.channel_name}</div>
                                                <Tooltip placement="top" title={item.description}>
                                                    <img src={infoIcon.src} width={16} />
                                                </Tooltip>
                                            </div>
                                            <div className={channelClassify.itemDetail}>
                                                <div className={item.template_check_status == '2' ? channelClassify.itemStatus : channelClassify.itemUnpublished}>{item.template_check_status == '2' ? '已发布' : '未发布'}</div>
                                                <div className={channelClassify.breakUp}></div>
                                                <div>
                                                    <span style={{ color: '#626F84' }}>发布版本 </span>
                                                    <span style={{ color: '#1D2531' }}>{item.version ? item.version : '-'}</span>
                                                </div>
                                                <div className={channelClassify.breakUp}></div>
                                                <div>
                                                    <span style={{ color: '#626F84' }}>发布时间 </span>
                                                    <span style={{ color: '#1D2531' }}>{item.created_at ? item.created_at.replace('T', ' ') : '-'}</span>
                                                </div>
                                                <div className={channelClassify.breakUp}></div>
                                                <div>
                                                    <span style={{ color: '#626F84' }}>发布人 </span>
                                                    <span style={{ color: '#1D2531' }}>{item.user_name ? item.user_name : '-'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className={channelClassify.modalButton}>
                                            <Button onClick={() => window.open(`/agentDetail?agentId=${recordAgentId}&teamId=${teamId}&from=1&identifier=${item.identifier}`)} disabled={item.template_check_status != '2'}>立即对话</Button>
                                            <Popconfirm
                                                title="确定取消发布吗？"
                                                onConfirm={() => handleChildConfirm(item.channel_id)}
                                                okText="确定"
                                                cancelText="取消"
                                            >
                                                <Button disabled={item.template_check_status != '2'}>取消发布</Button>
                                            </Popconfirm>
                                        </div>
                                    </div>
                                </div>
                            ))
                            :
                            <Empty description='暂无数据' className='common_ant_empty' />
                        }
                    </div>
                </div>
            </Modal>
        </>
    )
}
