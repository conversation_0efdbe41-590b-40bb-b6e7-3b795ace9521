import { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Button, Table, Popconfirm, message } from 'antd';
import { reqAgentTagList } from '@/service/agent';
import { reqPluginVersionMarketList, reqCancelPublic } from "@/service/api";
import { isNullOrEmpty } from "@/utils/common";
import { useRouter } from 'next/router'
// import ApiDetailModal from "@/components/template/api/apiDetail";
import styles from '@/components/background/plugin/index.module.scss';

export default function PluginMarket() {
    const router = useRouter();

    const [tagList, setTagList] = useState<any[]>([]);
    const [curPlugin, setCurPlugin] = useState<any>({});
    const [tableData, setTableData] = useState(new Array<any>());
    const [originData, setOriginData] = useState(new Array<any>());
    const [openApiDetail, setOpenApiDetail] = useState<boolean>(false);
    const [formData, setFormData] = useState<any>({
        pluginId: '',
        pluginName: '',
    })
    const [pageInfo, setPageInfo] = useState<any>({
        pageSize: 20,
        curPage: 1,
        total: 0
    })

    const columns = [
        {
            title: '插件ID',
            dataIndex: 'api_id',
            key: 'api_id',
        },
        {
            title: '插件名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '操作',
            dataIndex: 'operation',
            render: (_: any, record: any, index: number) => {
                return (
                    <span>
                        <Popconfirm 
                            title={`确认取消公开 ${record.name} 插件`}
                            onConfirm={async() => {
                                const res = await reqCancelPublic({
                                    template_id: record.api_id,
                                    template_type: 5
                                })

                                if (res) {
                                    message.success('取消插件公开成功');
                                    getAllList(false);
                                }
                            }}
                        >
                            <a style={{ marginRight: 20 }}>取消公开</a>
                        </Popconfirm>
                        <a onClick={() => handleDetail(record)}>插件详情</a>
                    </span>
                )
            }
        }
    ]

    useEffect(() => {
        if (router.query.teamId) {
            getAllList(false);
            getTagList();
        }
    }, [router.query.teamId])

    useEffect(() => {
        getList();
    }, [pageInfo])

    const getAllList = async(isSearch?: boolean) => {
        const res = await reqPluginVersionMarketList(
            {
                page: 1,
                page_size: 100000,
                keyword: formData.pluginName,
                id: formData.pluginId || null,
                tag_id: '0',
                sort: 2,
                is_action: 0,
            }
        )
        
        if (isNullOrEmpty(res)) {
            setOriginData([]);
            setTableData([]);
            setPageInfo({
                ...pageInfo,
                curPage: 1,
                total: 0
            })
            return;
        }

        setPageInfo({
           ...pageInfo,
            total: res.length
        })
        setOriginData(res);

        const curPageS = isSearch ? 1 : pageInfo.curPage;

        const curTable = res.slice(curPageS * pageInfo.pageSize - pageInfo.pageSize, curPageS * pageInfo.pageSize)
        setTableData(curTable);
    }

    const getList = async() => {
        if (originData.length === 0) return;

        const res = originData.slice(pageInfo.curPage * pageInfo.pageSize - pageInfo.pageSize, pageInfo.curPage * pageInfo.pageSize)

        setTableData(res);
    }

    const getTagList = async() => {
        try {
            const res = await reqAgentTagList({
                tag_type: 5
            })

            if (isNullOrEmpty(res)) return;

            const allTag = {
                id: '0',
                tag_name: '全部',
                description: '全部'
            }

            setTagList([allTag, ...res]);
            return [allTag,...res];
        } catch (error: any) {
            message.error(error.message);
            return [];
        }
    }

    const handleDetail = (plugin: any) => {
        setCurPlugin(plugin)
        setOpenApiDetail(true);
    }

    const onSearch = () => {
        if (formData.pluginId && !/^[0-9]*$/.test(formData.pluginId)) {
            message.error('插件ID只能是数字');
            return;
        }
        
        getAllList(true);
    }

    return (
        <div className={styles.market}>
            <div className={styles.marketTitle}>
                <Form
                    layout='inline'
                >
                    <Form.Item
                        label='插件ID'
                    >
                        <Input
                            status={ /^[0-9]*$/.test(formData.pluginId) ? '' : 'error' }
                            placeholder='请输入插件ID'
                            value={formData.pluginId}
                            onChange={e => {
                                setFormData({
                                   ...formData,
                                    pluginId: e.target.value,
                                })
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label='插件名称'
                    >
                        <Input
                            placeholder='请输入插件名称'
                            value={formData.pluginName}
                            onChange={e => {
                                setFormData({
                                    ...formData,
                                    pluginName: e.target.value,
                                })
                            }}
                        />
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" onClick={() => onSearch()}>查询</Button>
                    </Form.Item>
                </Form>
            </div>

            <div className={styles.marketTable + ' normalFont'}>
                <Table 
                    columns={columns}
                    dataSource={tableData}
                    scroll={{ y: 720 }}
                    className='commonTable normalFont'
                    pagination={{
                        current: pageInfo.curPage,
                        pageSize: pageInfo.pageSize,
                        total: pageInfo.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: any) => `共 ${total} 条记录`
                    }}
                    onChange={(pagination) => {
                        setPageInfo({
                           ...pageInfo,
                            curPage: pagination.current || 1,
                            pageSize: pagination.pageSize || 20,
                        })
                    }}
                />
            </div>
            {/* <ApiDetailModal
                open={openApiDetail}
                templateId={curPlugin.api_id}
                versionId={curPlugin.id}
                setOpen={(open: boolean) => setOpenApiDetail(open)}
            /> */}
        </div>
    )
}