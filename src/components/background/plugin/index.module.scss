.market, .tag {
    border: 1px solid #FFF;
    background: linear-gradient(90deg, #ffffff42 2.85%, rgba(255, 255, 255, 0.17) 98.96%);
    box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
    padding: 0px 0px 0;
    border-radius: 8px;
}

.market {
    .marketTitle {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        padding: 16px;
    }

    .marketTable {
        height: calc(100vh - 182px);
        overflow-y: hidden;
        border-radius: 8px;
        border: 1px solid #FFF;
        background: linear-gradient(90deg, #ffffff42 2.85%, rgba(255, 255, 255, 0.17) 98.96%);
        box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.04);
        padding: 0px 0px 0;
    }
}

.tag {
    .tagTitle {
        padding: 16px;
    }

    .tagTable {
        height: calc(100vh - 182px);
        overflow-y: hidden;
    }
}

.tagModal {
    padding: 20px 30px 0 30px;
}
