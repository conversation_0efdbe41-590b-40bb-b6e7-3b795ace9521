import { useState, useEffect } from "react";
import { Popconfirm, Button, Table, Modal, message } from "antd";
import { reqAgentTagList } from '@/service/agent';
import { reqAddDelTag } from '@/service/api';
import TagModal from "./TagModal";
import { isNullOrEmpty } from "@/utils/common";
import styles from './index.module.scss';

export default function PluginTag() {
    const [tableData, setTableData] = useState(new Array<any>());
    const [pageInfo, setPageInfo] = useState<any>({
        pageSize: 10,
        curPage: 1,
        total: 0
    })
    const [isAddOpen, setIsAddOpen] = useState<boolean>(false)
    const [formData, setFormData] = useState<any>({
        id: 0,
        tag_name: '',
        tagSort: 0,
    })
    const [isEdit, setIsEdit] = useState<boolean>(false)

    const columns = [
        {
            title: '标签ID',
            dataIndex: 'id',
            key: 'id',
        },
        {
            title: '标签名称',
            dataIndex: 'tag_name',
            key: 'tag_name',
        },
        {
            title: '显示排序',
            dataIndex:'sort',
            key:'sort',
        },
        {
            title: '操作',
            dataIndex: 'operation',
            render: (_: any, record: any, index: number) => {
                return (
                    <span>
                        <a style={{ marginRight: 20 }} onClick={() => handleEdit(record)}>编辑</a>
                        <Popconfirm
                            title={`确认删除 ${record.tag_name} 标签`}
                            onConfirm={async() => {
                                const res = await reqAddDelTag({
                                    id: record.id,
                                    name: record.tag_name,
                                    sort: record.sort,
                                    is_delete: 1
                                })

                                if (res) {
                                    message.success('删除成功')
                                    getTagList();
                                } else {
                                    message.error('删除失败')
                                }
                            }}
                        >
                            <a>删除</a>
                        </Popconfirm>
                    </span>
                )
            }

        }
    ]

    const getTagList = async() => {
        try {
            const tags = await reqAgentTagList({
                tag_type: 5
            })

            if (isNullOrEmpty(tags)) return;

            setTableData(tags);

            setPageInfo({
                ...pageInfo,
                total: tags.length
             })
            return tags;
        } catch (error: any) {
            message.error(error.message);
            return [];
        }
    }

    const handleAdd = () => {
        setIsEdit(false)
        setIsAddOpen(true)
    }

    const handleSave = async() => {
        let error = false;
        
        if (formData.tagName === '') {
            error = true;
            message.warning('请输入标签名称')
        }
        if (formData.tagName.length > 4) {
            error = true;
            message.warning('标签名称不能超过 4 个字')
        }
        if (formData.tagSort === '') {
            error = true;
            message.warning('请输入标签排序')
        }

        if (error) return;

        const res = await reqAddDelTag({
            id: isEdit ? formData.id : null,
            name: formData.tagName,
            sort: formData.tagSort,
            is_delete: 0
        })

        if (res) {
            message.success(`${isEdit ? '编辑' : '新建'}插件成功`)
            getTagList();
            setIsAddOpen(false);
        }
    }

    const handleEdit = (tag: any) => {
        setIsEdit(true)
        setFormData({
            ...formData,
            tagName: tag.tag_name,
            tagSort: tag.sort,
            id: tag.id
        })
        setIsAddOpen(true)
    }

    useEffect(() => {
        getTagList();
    }, [])

    useEffect(() => {
        if (!isAddOpen) {
            setFormData({
                tagName: '',
                tagSort: 0,
            })

        }
    }, [isAddOpen])


    return (
        <div className={styles.tag}>
            <Modal
                title="新建标签"
                destroyOnClose={true}
                open={isAddOpen}
                onCancel={() => setIsAddOpen(false)}
                footer={[
                    <Button type="primary" onClick={handleSave}>
                        保存
                    </Button>
                ]}
            >
                <TagModal
                    isEdit={isEdit}
                    formData={formData}
                    setFormData={setFormData}
                />
            </Modal>

            <div className={styles.tagTitle}>
                <Button type="primary" onClick={handleAdd}>新建</Button>
            </div>

            <div className={styles.tagTable + ' normalFont'}>
                <Table 
                    columns={columns}
                    dataSource={tableData}
                    scroll={{ y: 720 }}
                    className='commonTable normalFont'
                    pagination={{
                        current: pageInfo.curPage,
                        pageSize: pageInfo.pageSize,
                        total: pageInfo.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: any) => `共 ${total} 条记录`
                    }}
                    onChange={(pagination) => {
                        setPageInfo({
                           ...pageInfo,
                            curPage: pagination.current || 1,
                            pageSize: pagination.pageSize || 20,
                        })
                    }}
                />
            </div>
        </div>
    )
}