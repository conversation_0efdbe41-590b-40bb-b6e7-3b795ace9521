import { useEffect, useState } from "react";
import { Form, Input } from "antd";
import styles from './index.module.scss';

export default function TagModal(props: any) {
    const {
        isEdit,
        formData,
        setFormData
    } = props;

    const layout = {
        labelCol: { span: 5 },
        wrapperCol: { span: 19 }
    }

    return (
        <div className={styles.tagModal}>
            <Form
                {...layout}
                labelAlign="left"
            >
                <Form.Item
                    label="标签名称"
                    required
                    rules={[
                        {
                            required: true,
                            message: '请输入标签名称'
                        }
                    ]}
                >
                    <Input
                        placeholder="请输入标签名称"
                        value={formData.tagName}
                        maxLength={4}
                        showCount
                        status={(isEdit && formData.tagName === '') || formData.tagName.length > 4 ? 'error' : ''}
                        onChange={(e) => {
                            setFormData({
                                ...formData,
                                tagName: e.target.value
                            })
                        }}
                    />
                </Form.Item>
                <Form.Item
                    label="标签排序"
                    required
                    rules={[
                        {
                            required: true,
                            message: '请输入标签排序'
                        }
                    ]}
                >
                    <Input
                        placeholder="请输入标签排序"
                        value={'' + formData.tagSort}
                        status={formData.tagSort === '' ? 'error' : ''}
                        onChange={(e) => {
                            setFormData({
                                ...formData,
                                tagSort: e.target.value
                            })
                        }}
                    />
                </Form.Item>
            </Form>
        </div>
    )
}