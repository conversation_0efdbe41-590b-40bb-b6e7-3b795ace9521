import { useState, useEffect } from 'react';
import commonListStyles from '@/styles/CommonList.module.scss';
import PluginMarket from './Market';
import PluginTag from './Tag';

export default function PluginList(props: any) {
    const pluginMarket = '插件市场';
    const pluginTag = '插件标签';

    const [activeTab, setActiveTab] = useState(pluginMarket);

    const isManage = activeTab === pluginMarket;

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                        <span className={commonListStyles.title}>插件市场</span>
                        <div className={'titleTabs'}>
                            <div className={'tabItem ' + (isManage ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(pluginMarket) }}>{pluginMarket}</div>
                            <div className={'tabItem ' + (activeTab === pluginTag ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(pluginTag) }}>{pluginTag}</div>
                        </div>
                    </div>
                </div>

                <div>
                    {isManage? <PluginMarket /> : <PluginTag />}
                </div>
            </div>
        </>
    )
}