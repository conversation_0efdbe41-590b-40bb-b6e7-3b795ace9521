import { Button, Input, Select, Table, Popconfirm, message,Modal } from 'antd';

import commonListStyles from '@/styles/CommonList.module.scss'
import teamMemberStyles from '@/styles/TeamMembers.module.css'
import {reqDomainList } from '@/service/member'
import { reqSystemMemberList, reqAddMember, reqDeleteMember, updateSysRole } from '@/service/background/person'
import { getCurrentUrlParamValue } from  '@/utils/url'
import addMember from '@/images/addMember.svg'

import search from '@/images/commonList/search.png'
import deleteIcon from '@/images/delete.svg'
import operationListStyles from '@/styles/OperationList.module.scss'
import right_arrow from '@/images/common/right_arrow.png'
import { SearchOutlined } from '@ant-design/icons';


import { useState, useEffect, useRef } from 'react';
import Router, { useRouter } from 'next/router'

export default function PersonList(prop: any) {
    const router = useRouter();
    const teamId = router.query.teamId;
    // const teamId = getCurrentUrlParamValue('teamId');
    const [openAddProject, setOpenAddProject] = useState(false);
    const [openAddMember, setOpenAddMember ] = useState(false);
    const [searchList, setSearchList] = useState(new Array())

    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)

    const [tableData, setTableData] = useState(new Array())
    const [searchKey, setSeachKey] = useState('')
    const [editingKey, setEditingKey] = useState('');

    let originData = useRef(new Array())
    function isEditing(record: any) {
        if (record == undefined) {
            return false
        }
        return record.member_id === editingKey;

    }
    const edit = (record: any) => {
        setEditingKey(record?.member_id);
    };

    useEffect(() => {
        getMemberList()
    }, [searchKey, curPage])
    function searchkeyEnter(e: any) {
        if (e.key === 'Enter') {
            // 在这里处理用户按下Enter键的逻辑
            setSeachKey(e.target.value)
        }

    }
    const getAddMember = async (index: any) => {
        const activeItem = searchList[index]
        let res;

        res = await reqAddMember({
            team_id: router.query.teamId,
            id: activeItem.user_id,
            role: 2
        })


        if (res?.message === 'success') {
            message.success("添加成功");
            setOpenAddMember(false);
             getMemberList()
            searchList[index] = {
                ...searchList[index],
                isAdd: true
            }
            setSearchList([...searchList]);
        }
    }
    const deleteMember = async (params: {}) => {
        let res = await reqDeleteMember(params);
      
        if (res != undefined) {
            if (res?.message === 'success') {
                message.success("删除成功")
                getMemberList()

            } else {
                message.success(res.errmsg == undefined ? res.errmsg : '删除失败')

            }
        }



    }
    // useEffect(() => {
    //     // alert("元数据"+JSON.stringify(originData))

    //     if (searchKey != undefined && searchKey != '') {
    //         let newData = originData.current.filter(item => {
    //             //alert(JSON.stringify(item))

    //             return item.name.includes(searchKey) || item.message.includes(searchKey) || item.team_name.includes(searchKey)

    //         })
    //         // alert(JSON.stringify(newData))
    //         setTableData(newData)
    //     } else {
    //         setTableData(originData.current)
    //     }



    // }, [searchKey])


    const getMemberList = async () => {

        let res = await reqSystemMemberList({
            team_id:  getCurrentUrlParamValue("teamId"),
            name:searchKey,
            page_size:pageSize,
            page:curPage

        });
  
        if (res != undefined && res.context != undefined && res.context.code == 0) {
            setTableData(res.data)
            originData.current = res.data

            setPageSize(res.pagination.page_size)
            setTotal(res.pagination.total)
        } else {
            if (res != undefined && res.context != undefined) {
                message.error(res.context.message)
            } else {
                message.error("获取列表失败")

            }
        }

    }
    const updateRole = async (record: any) => {

        // const newData = [...tableData];
        // const index = newData.findIndex((item) => record.id === item.id);


        let res = await updateSysRole({ 'member_id': record.member_id, 'role': record.role, 'team_id': teamId })
        if (res != undefined) {

            if (res.message === "success") {
                setEditingKey('');
                message.success({
                    type: 'success',
                    content: '更新成功',
                });
                getMemberList()

            } else {
                message.error({
                    type: 'error',
                    content: res.context.message,
                });
            }

        } else {
            message.error({
                type: 'error',
                content: "更新失败"
            });

        }

    };
    const cancel = () => {
        setEditingKey('');
    };
    const [messageKey, setMessageKey] = useState(0);
    const timer = useRef<any>(null);
    const getDomainList = async (name: any) => {
        let res;

            res = await reqDomainList({
                name
            })
        // res = {
        //     "data": [
        //         {
        //             "user_id": 74,
        //             "name": "yyyy的账号",
        //             "email": "",
        //             "phone_number": "188****6873"
        //         }
        //     ],
        //     "context": {
        //         "message": "OK",
        //         "code": 0,
        //         "timestamp": 1704793645
        //     },
        //     "pagination": {
        //         "total": 1,
        //         "page": 1,
        //         "page_size": 20
        //     }
        // }
            

        if (res != undefined) {
            setSearchList(res.map((item: any) => {
                return {
                    ...item,
                    isAdd: false
                }
            }))

        }

        res && res.length == 0 && message.info({content: '未搜索到该账号'})
        // res && res.length == 0 && message.info({content: '未搜索到该账号', key: messageKey})
        // timer.current = setInterval(() => {
        //     setMessageKey(messageKey+1)
        // }, 3000)
    }

    useEffect(() => {
        return () => {timer.current = null}
    }, [])
 

    const columns = [
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',

        },
        {
            title: '角色',
            dataIndex: 'role',
            key: 'role',
            // render: (text: any, record: any, index: any) => {
            //     const show = text === 1
            //     return show ? <span>管理员</span> : <span>成员</span>
            // }
            render: (text: any, record: any, index: any) => {

                const editable = isEditing(record);
                const show = text === 1
                let roleName = "管理员"
                if (text === 1) {
                    roleName = "管理员"

                } else {
                    roleName = "成员"

                }
                return editable ? <Select
                    className={teamMemberStyles.selectRole}
                    options={[
                        {
                            label: '管理员',
                            value: 1

                        },
                        {
                            label: '成员',
                            value: 2
                        },

                    ]}
                    onChange={(value) => {
                        record.role = value
                    }}
                    defaultValue={roleName}
                    style={{ width: '90px' }}
                ></Select> : <span>{roleName}</span>
            }


        },
        {
            title: '邮箱',
            dataIndex: 'email',
            key: 'email'
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (_: any, record: any) => {
                const editable = isEditing(record);
                return editable ? (
                    <div>
                        <Popconfirm
                            title={"当前人员角色已经被修改，是否保存？"}
                            description=""
                            onConfirm={() => {
                                updateRole(record)

                            }}
                            onCancel={() => {

                            }}
                            okText="确认"
                            cancelText="取消"
                        >

                            <span
                                className={operationListStyles.detailBtn}
                            >保存</span>
                        </Popconfirm>

                        <span
                             onClick={() => cancel()}
                            className={operationListStyles.detailBtn}
                        >取消</span>

                    </div>

                ) : (
                    <>
                        <a
                            onClick={() => edit(record)}
                            className={operationListStyles.detailBtn}
                        >编辑</a>
                        <Popconfirm
                            title={"是否要删除 " + record.name + ' ?'}
                            description=""
                            onConfirm={() => {
                                deleteMember({
                                    member_id: record.member_id,
                                    team_id: router.query.teamId,
                                })
                            }}
                            onCancel={() => {

                            }}
                            okText="确认"
                            cancelText="取消"
                        >
                            <span className={operationListStyles.cancelDeployBtn}>删除</span>
                        </Popconfirm>
                    </>

                )
                       
            // render: (_: any, record: any) => {
            //     return <Popconfirm
            //         title={"是否要删除 " + record.name + ' ?'}
            //         description=""
            //         onConfirm={() => {
                        // deleteMember({
                        //     member_id: record.member_id,
                        //     team_id: router.query.teamId,
                        // })
            //         }}
            //         onCancel={() => {

            //         }}
            //         okText="确认"
            //         cancelText="取消"
            //     >
            //         <span className={operationListStyles.cancelDeployBtn}>删除</span>
            //     </Popconfirm>
             }
        }
    ];

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <a href='/background/projectList'>
                        <img src={right_arrow.src} width='16' height='12' style={{ marginRight: 24 }} />
                        <span className={commonListStyles.title}>成员</span>

                    </a>
                    <div>
                        <span className={'commonSearchInput'}>
                        <Input
                            placeholder="请输入关键词搜索"
                            prefix={<img src={search.src} width='16' />}
                            onPressEnter={e => searchkeyEnter(e)}
                            allowClear
                            style={{ width: '256px', borderRadius: '8px' }}
                          
                        />
                        </span>
                        <Button type='primary' className={teamMemberStyles.addMember + ' primaryButton'} onClick={() => { setOpenAddMember(true) }}>添加成员</Button>

                    </div>


                </div>
                <div className={commonListStyles.tableList + ' normalFont'}>
                    <Table
                        columns={columns}
                        rowKey={(record) => record.id}
                        scroll={{   y: 720 }}
                        dataSource={tableData}
                        pagination={{
                            current: curPage,
                            pageSize: pageSize,
                            total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: () => `共 ${total} 条记录`
                        }}
                        onChange={(pagination) => {
                            setCurPage(pagination.current || 1)
                            setPageSize(pagination.pageSize || 20)
                        }}
                        className='commonTable normalFont'
                    />
                </div>
            </div>
            <Modal
                open={openAddMember}
                style={{ width: 480 }}

                className={'commonModal noFooterModal'}
                onCancel={() => setOpenAddMember(false)}
                title={'添加成员'}
                footer={(<></>)}
            >
                <Input
                    prefix={<SearchOutlined />}
                    style={{ border: '1px solid #BCCAD6' }}
                    className={teamMemberStyles.searchInput + ' ' + teamMemberStyles.addMemberInput}
                    placeholder='请输入成员姓名' 
                    onKeyUp={(e) => {
                        const event: any = e.target;
                       
                            getDomainList(event.value)
                       
                    }}
                
                />
                <div className={teamMemberStyles.searchList}>
                    {searchList&&searchList.map((item, index) => {
                        return <div className={teamMemberStyles.searchItem}>
                            <div className={teamMemberStyles.nameWrapper}>
                                <span className={teamMemberStyles.firstName}>{item.name.substr(0, 1)}</span>
                                <span>{item.name}</span>
                            </div>
                            <span  className={teamMemberStyles.email}>{item.email}</span>
                            {item.isAdd ? <span>已添加</span> : <Button className={teamMemberStyles.addBtn} onClick={() => { getAddMember(index) }}><img src={addMember.src} /><span>添加</span></Button>}
                        </div>
                    })}
                </div>
            </Modal>
        </>
    )
}
