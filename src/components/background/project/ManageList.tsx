import { Button, Input, Select, Table, Popconfirm, Form, InputNumber, message } from 'antd';

import styles from './index.module.scss';
import commonListStyles from '@/styles/CommonList.module.scss'
import teamMemberStyles from '@/styles/TeamMembers.module.css'
import operationListStyles from '@/styles/OperationList.module.scss'

import { reqProjectList, reqAddProject, reqDeleteProject, reqUpdateProject } from '@/service/background/project'
import { useState, useEffect } from 'react';
import Router, { useRouter } from 'next/router'


export default function MangerList(props:any) {
    const router = useRouter();
    const teamId = router.query.teamId;

    const { addCount, channelOptions } = props;

    const [tableData, setTableData] = useState(Array())
    const [editingKey, setEditingKey] = useState('');
    const [form] = Form.useForm();
    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)
    const [tableChange, setTableChange] = useState(new Date())
    const [formData, setFormData] = useState({
        channel: '',
        projectName: '',
        manager: '',
    })

    //获取项目列表
    useEffect(() => {
        getProjectList(false);
    }, [addCount, curPage, tableChange,pageSize])
    //删除项目列表
    const deleteTeamReq = async (id: any) => {
        let res = await reqDeleteProject({'id':id})

        if (res != undefined) {
            if (res.context != undefined ) {
                if (res.context.code == 0){
                    message.success({
                        type: 'success',
                        content: '删除成功',
                    });
                    setTableChange(new Date())
                }else{
                   message.error({
                        type: 'error',
                        content: res.context.message,
                    });
                }
            }
        }else{
            message.error({
                type: 'error',
                content: "删除失败"
            });
        }
    }
   
    const look = async (record: any)=>{
        router.push("/templateList?teamId=" + record.id)
    }

    //获取项目列表
    const getProjectList = async (isSearch: boolean) => {
        let res = await reqProjectList({
            name: formData.projectName,
            page: isSearch ? 1 : curPage,
            page_size: pageSize,
            en_src: formData.channel,
            username: formData.manager
        })

        if (res != undefined) {
            if (res.data != undefined) {
                setTableData(res.data)
            }
            if (res.pagination != undefined) {
                setPageSize(res.pagination.page_size)
                setTotal(res.pagination.total)
            }
        }
    }

    const getTeamTypeValue = (type: any) => {
        if (type == undefined) {
            return '-'
        }
        if (type === 1) {
            return '普通'
        }
        if (type === 2) {
            return '官方'
        }
        if (type === 3) {
            return '示例'
        }
        if (type === 4) {
            return '个人'
        }
        return '-'
    }
  
    function isEditing(record:any){
        if (record ==undefined){
            return false
        }
        return record.id === editingKey;
    }

    function updateProjectType(record:any,value:any){
        record.team_type =value
    }
   
    const columns = [
        {
            title: '渠道src',
            dataIndex: 'src',
            key: 'src',
            editable: true,
            render: (text:any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '项目名称',
            dataIndex: 'name',
            key: 'name',
            editable: true,
            render: (text:any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '项目类型',
            dataIndex: 'team_type',
            key: 'team_type',
                render: (text: any, record: any, index: any) => {
                    const editable = isEditing(record);
                    return editable ? <Select
                        className={teamMemberStyles.selectRole}
                        options={[
                            {
                                label: '普通',
                                value: 1
                            },
                            {
                                label: '官方',
                                value: 2
                            },
                            {
                                label: '实例',
                                value: 3
                            },
                            {
                                label: '个人',
                                value: 4
                            }
                        ]}
                        onChange={(value) => {
                            record.team_type=value
                        }}
                        defaultValue={getTeamTypeValue(text)}
                        style={{ width: '90px' }}
                    ></Select> : <span>{getTeamTypeValue(text)}</span>
                }
        },
        {
            title: '管理员',
            dataIndex: 'manager',
            key: 'manger',
            width: 350,
            render: (text: any) => {
                return <div>{text.join(',')}</div>
            }
        },
        {
            title: '成员数',
            dataIndex: 'member_num',
            key: 'member_num',
            width: 100,
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            render: (text: any) => {
                return <div>{text ? text.replace('T', ' ') : ''}</div>
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            render: (_:any, record: any, index: number) => {
                const editable = isEditing(record);
                return  editable?(
                    <div>
                        <Popconfirm
                            title={"当前项目设定已经被修改，是否保存？"}
                            description=""
                            onConfirm={() => {
                                save(record)
                            }}
                            onCancel={() => {}}
                            okText="确认"
                            cancelText="取消"
                        >
                    <span
                        className={operationListStyles.detailBtn}
                    >保存</span>
                    </Popconfirm>
                        <span
                            onClick={() => cancel()}
                            className={operationListStyles.detailBtn}
                        >取消</span>
                    </div>
                ):(
                    <span>
                        <a
                            onClick={() => edit(record)}
                            className={operationListStyles.detailBtn}
                        >编辑</a>
                        <a
                            href={"/background/personList" + "?teamId=" + (record.id == undefined ? '' : record.id)}
                            className={operationListStyles.detailBtn}
                        >成员</a>
                        <Popconfirm
                            title={"是否要删除 " + record?.name + ' ?'}
                            description=""
                            onConfirm={() => {
                                deleteTeamReq(record?.id)

                            }}
                            onCancel={() => {}}
                            okText="确认"
                            cancelText="取消"
                        >
                            <span className={operationListStyles.cancelDeployBtn}>删除</span>
                        </Popconfirm>
                        <a
                            className={operationListStyles.detailBtn}
                            onClick={() => look(record)}
                        >查看</a>
                    </span>
                )
            }
        }
    ]

    const edit = (record:any) => {
        form.setFieldsValue({
            name: "wan2323",
            size: 0,
            create_time: '',
            type: '',
            manger: "",
            ...record,
        });
        setEditingKey(record?.id);
    };
    
    const save = async(record: any) => {
        let res = await reqUpdateProject({ 'id': record.id, 'name': record.name, 'team_type': record.team_type })
        if (res != undefined) {
            if (res.context != undefined) {
                if (res.context.code == 0) {
                    setEditingKey('');
                    message.success({
                        type: 'success',
                        content: '更新成功',
                    });
                    setTableChange(new Date())
                } else {
                    message.error({
                        type: 'error',
                        content: res.context.message,
                    });
                }
            }
        } else {
            message.error({
                type: 'error',
                content: "更新失败"
            });
        }
    };

    const cancel = () => {
        setEditingKey('');
    };

    const mergedColumns = columns.map((col:any) => {
        if (!col.editable) {
            return col;
        }

        return {
            ...col,
            onCell: (record:any) => ({
                record,
                inputType: col.dataIndex === 'name' ? 'number' : 'text',
                dataIndex: col.dataIndex,
                title: col.title,
                editing: isEditing(record),
            }),
        };
    });

    const EditableCell = (dataBean:any) => {
        const {
            editing,
            dataIndex,
            title,
            inputType,
            record,
            inde,
            children,
            ...restProps
        } = dataBean
        const inputNode = <Input   width={60} onChange={(e)=>{
            record.name= e.target.value
        }}/>;

        return (
            <td {...restProps}>
                {editing ? (
                    <Form.Item
                        name={dataIndex}
                        style={{ margin: 0 }}
                        rules={[
                            {
                                required: true,
                                message: `请输入 ${title}!`,
                            },
                        ]}
                    >
                        {inputNode}
                    </Form.Item>
                ) : (
                    children
                )}
            </td>
        );
    };

    const handleSearch = async () => {
        setCurPage(1);
        getProjectList(true);
    }

    return (
        <div className={styles.list}>
            <div className={styles.listTitle}>
                <Form
                    layout='inline'
                >
                    <Form.Item
                        label='渠道SRC'
                    >
                        <Select
                            value={formData.channel}
                            options={channelOptions}
                            style={{ width: 150 }}
                            onChange={(value) => {
                                setFormData({
                                    ...formData,
                                    channel: value
                                }) 
                            }}
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        label='项目名称'
                    >
                        <Input
                            value={formData.projectName}
                            placeholder='请输入项目名称'
                            onChange={(e) => {
                                setFormData({
                                   ...formData,
                                    projectName: e.target.value
                                }) 
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label='管理员'
                    >
                        <Input
                            value={formData.manager}
                            placeholder='请输入管理员'
                            onChange={(e) => {
                                setFormData({
                                   ...formData,
                                    manager: e.target.value
                                }) 
                            }}
                        />
                    </Form.Item>
                    <Button type='primary' onClick={handleSearch}>查询</Button>
                </Form>
            </div>
            <div className={commonListStyles.checkTableList + ' normalFont'}>
                <Form form={form} component={false}>
                    <Table 
                        components={{
                            body: {
                                cell: EditableCell,
                            },
                        }}
                        scroll={{y: 720 }}
                        rowKey={(record) => record.id}
                        columns={mergedColumns}
                        dataSource={tableData} 
                        pagination={{
                            current: curPage,
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: any) => `共 ${total} 条记录`
                        }}
                        onChange={(pagination) => {
                            setCurPage(pagination.current || 1)
                            setPageSize(pagination.pageSize||20)
                        }}
                        className='commonTable normalFont'
                    />
                </Form>
            </div>
        </div>
    )
}
