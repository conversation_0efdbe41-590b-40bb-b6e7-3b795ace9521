import { Button, Input, Select, Modal, message, Form } from 'antd';

import commonListStyles from '@/styles/CommonList.module.scss'
import teamMemberStyles from '@/styles/TeamMembers.module.css'
import search from '@/images/commonList/search.png'
import { reqAddProject, reqGetSrcList } from '@/service/background/project'

// 项目管理
import MangerList from '@/components/background/project/ManageList'
// 服务运维
import AuditList from '@/components/background/project/AuditList'

import { useState, useEffect } from 'react';

export default function CommonList(prop: any) {
  const projectManage = '项目管理';
  const projectAudit = '项目审核';

  const [activeTab, setActiveTab] = useState(projectManage)
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [teamType, setTeamType] = useState(1)
  const [addCount, setAddCount] = useState(0);
  const [channelOptions, setChannelOptions] = useState(new Array());
  const isManage = activeTab === projectManage;
  const [form] = Form.useForm();

  const options = [
    {
      label: '普通',
      value: 1

    },
    {
      label: '官方',
      value: 2
    },
    {
      label: '实例',
      value: 3
    },
    {
      label: '个人',
      value: 4
    }
  ]

  const inputStyle = {
    width: '246px', // 自适应父元素宽度
    height: '32px', // 设置高度
  };

  let addTeamInfo = {
    name: '',
    team_type: 1
  }

  function showAddTeamModle() {
    setIsModalOpen(true)
  }

  function handleOk() {
    form.validateFields().then((values) => {
      setIsModalOpen(false)
      addTeam()
    });
  }

  function handleCancel() {
    setIsModalOpen(false)
  }

  const addTeam = async () => {
    addTeamInfo = {
      name: inputValue,
      team_type: teamType
    }

    let res = await reqAddProject(addTeamInfo)
    form.resetFields()

    if (res != undefined && res.context != undefined) {
      if (res.context.code == 0) {
        message.success("添加成功")
        setInputValue('')
        setAddCount(Date.now())
        setTeamType(1)
      } else {
        message.error(res.context.message)
      }
    } else {
      message.error("添加失败")
    }
  }

  useEffect(() => {
    reqGetSrcList().then((res) => {
      res && res.data && setChannelOptions([{
        value: '',
        label: '全部'
      }, ...res.data.map((item: any) => ({
        value: item.en_src,
        label: item.src_name
      }))])
    })
  }, [])

  return (
    <>
      <div className={commonListStyles.commonList}>
        <div className={commonListStyles.titleWrapper}>
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <span className={commonListStyles.title}>项目管理</span>
            <div className={'titleTabs'}>
              <div className={'tabItem ' + (activeTab === projectAudit ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(projectAudit) }}>{projectAudit}</div>
              <div className={'tabItem ' + (isManage ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(projectManage) }}>{projectManage}</div>
            </div>
          </div>
          <div>
            <Button type='primary' className={teamMemberStyles.addMember + ' primaryButton'} onClick={() => { showAddTeamModle() }}>添加项目</Button>
          </div>
        </div>
        <div>
          {isManage ? <MangerList 
            addCount={addCount}
            channelOptions={channelOptions}
          /> : <AuditList 
            addCount={addCount} 
            channelOptions={channelOptions}
          />}
        </div>
      </div>
      <Modal
        style={{ width: 480 }}
        className={"commonModal"}
        title="添加项目" open={isModalOpen}
        onOk={handleOk} onCancel={handleCancel}
        footer={
          <>
            <Button onClick={() => { handleCancel(); form.resetFields(); }}>取消</Button>
            <Button
              type="primary"
              onClick={
                () => {
                  handleOk();
                }
              }
            >确认</Button>
          </>
        }>
        <Form
          form={form}
          layout="horizontal"
          autoComplete="off"
          style={{ width: "100%", padding: "0 8px" }}
        >
          <Form.Item required label="项目名称" name="projectName" rules={[{ required: true, message: '请输入项目名称' }]} >
            <Input placeholder="请输入项目名称" style={inputStyle} onChange={(e) => {
              setInputValue(e.target.value)
            }} />
          </Form.Item>
          <Form.Item required label="项目类型" name="projectType" rules={[{ required: false, message: '请选择项目类型' }]} >
            <Select
              className={teamMemberStyles.selectRole}
              value={teamType}
              onChange={(value) => {
                setTeamType(value)
              }}
              defaultValue={1}
              style={{ width: '90px' }}
            >
              {options.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}
