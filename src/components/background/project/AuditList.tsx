import { Button, Input, Select, Table, Popconfirm, message, Form } from 'antd';

import styles from './index.module.scss';
import commonListStyles from '@/styles/CommonList.module.scss'
import operationListStyles from '@/styles/OperationList.module.scss'
import backgroundStyles from '@/styles/Background.module.scss'
import { reqAuditeList, reqAudite } from '@/service/background/project'
import Router, { useRouter } from 'next/router'


import { useState, useEffect } from 'react';

export default function AuditList(props: any) {
    const router = useRouter();
    const teamId = router.query.teamId;

    const { addCount, channelOptions } = props;
    
    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)
    const [tableData, setTableData] = useState(new Array())
    const [formData, setFormData] = useState({
        channel: '',
        status: 0,
        projectName: '',
        manager: '',
    })

    //获取项目列表
    useEffect(() => {
        getAuditeList(false);
    }, [addCount, curPage, pageSize])

    const aggreeTeam = async (record: any) => {
        let res = await reqAudite({
            id: record.id,
            status: 2
        })

        if (res != undefined) {
            if (res.context != undefined) {
                if (res.context.code == 0) {
                    message.success({
                        content: '已同意',
                    });
                    getAuditeList(false);
                } else {
                    message.error({
                        type: 'error',
                        content: res.context.message,
                    });
                }
            }
        } else {
            message.error({
                content: "审批出错"
            });
        }
    }

    const refuseTeam = async (record: any) => {
        let res = await reqAudite({
            id: record.id,
            status: 3
        })

        if (res != undefined) {
            if (res.context != undefined) {
                if (res.context.code == 0) {
                    message.success({
                        content: '已拒绝',
                    });
                    getAuditeList(false);
                } else {
                    message.error({
                        type: 'error',
                        content: res.context.message,
                    });
                }
            }
        } else {
            message.error({
                content: "审批出错"
            });
        }
    }

    //获取项目列表
    const getAuditeList = async (isSearch: boolean) => {
        let res = await reqAuditeList({
            page: isSearch ? 1 : curPage,
            page_size: pageSize,
            keyword: formData.projectName,
            status: formData.status,
            en_src: formData.channel,
            username: formData.manager,
        })

        if (res != undefined) {
            if (res.data != undefined && Array.isArray(res.data)) {
                setTableData(res.data)
            }

            if (res.pagination != undefined) {
                setPageSize(res.pagination.page_size)
                setTotal(res.pagination.total)
            }
        }
    }
    
    const columns = [
        {
            title: '渠道src',
            dataIndex: 'src',
            key: 'src',
            width: 100,
        },
        {
            title: '项目名称',
            dataIndex: 'team_name',
            key: 'team_name',
            width: 100,
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (text: any) => {
                return <>
                    <div><span className={text == 1 ? backgroundStyles.blueStatus :
                        text === 2 ? backgroundStyles.greenStatus : backgroundStyles.redStatus}>
                    </span>{getStatusValue(text)}</div>
                </>
            }
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            width: 100,
            key: 'created_at',
            render: (text: any) => {
                return <div>{text ? text.replace('T', ' ') : ''}</div>
            }
        },
        {
            title: '项目类型',
            dataIndex: 'team_type',
            width: 100,
            key: 'team_type',
            render: (text: any) => {
                return <>
                    <span>{getTeamTypeValue(text)}</span>
                </>
            }
        },
        {
            title: '申请人',
            dataIndex: 'user_contact',
            width: 150,
            key: 'user_contact',
        },
        {
            title: '申请说明',
            width: 200,
            dataIndex: 'application_information',
            key: 'application_information',
        },
        {
            title: '操作',
            width: 200,
            dataIndex: 'operation',
            render: (_: any, record: any, index: number) => {
                let show = record.status == 1
                return show ? (<div>
                    < Popconfirm
                        title={"是否通过 " + record.team_name + ' 的申请?'}
                        description=""
                        onConfirm={() => {
                            aggreeTeam(record)
                        }}
                        onCancel={() => {}}
                        okText="确认"
                        cancelText="取消"
                    >
                        <a className={operationListStyles.detailBtn}>通过</a>
                    </Popconfirm>
                    < Popconfirm
                        title={"是否拒绝 " + record.team_name + ' 的申请?'}
                        description=""
                        onConfirm={() => {
                            refuseTeam(record)
                        }}
                        onCancel={() => {}}
                        okText="确认"
                        cancelText="取消"
                    >
                        <a className={operationListStyles.detailBtn}>拒绝</a>
                    </Popconfirm>
                </div>) : <a className={operationListStyles.detailBtn}>-</a>
            }
        }
    ]

    const statusOptions = [
        { value: 0, label: '全部' },
        { value: 1, label: '待审核' },
        { value: 2, label: '已通过' },
        { value: 3, label: '已拒绝' },
        { value: 4, label: '已撤销审核' }
    ]

    const getStatusValue = (type: any) => {
        if (type == undefined) {
            return '-'
        }
        if (type === 1) {
            return '审批中'
        }
        if (type === 2) {
            return '已通过'
        }
        if (type === 3) {
            return '已拒绝'
        }
        if (type === 4) {
            return '已撤销审核'
        }
        return '-'
    }

    const getTeamTypeValue = (type: any) => {
        if (type == undefined) {
            return '-'
        }
        if (type === 1) {
            return '普通'
        }
        if (type === 2) {
            return '官方'
        }
        if (type === 3) {
            return '示例'
        }
        if (type === 4) {
            return '个人'
        }
        return '-'
    }

    const handleSearch = () => {
        setCurPage(1);
        getAuditeList(true);
    }

    return (
        <div className={styles.list}>
            <div className={styles.listTitle}>
                <Form
                    layout='inline'
                >
                    <Form.Item
                        label='渠道SRC'
                    >
                        <Select
                            value={formData.channel}
                            options={channelOptions}
                            style={{ width: 150 }}
                            onChange={(value) => {
                                setFormData({...formData, channel: value}) 
                            }}
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        label='状态'
                    >
                        <Select
                            value={formData.status}
                            options={statusOptions}
                            style={{ width: 150 }}
                            onChange={(value) => {
                                setFormData({...formData, status: value}) 
                            }}
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        label='项目名称'
                    >
                        <Input
                            value={formData.projectName}
                            placeholder='请输入项目名称'
                            onChange={(e) => {
                                setFormData({...formData, projectName: e.target.value}) 
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label='管理员'
                    >
                        <Input
                            value={formData.manager}
                            placeholder='请输入管理员'
                            onChange={(e) => {
                                setFormData({...formData, manager: e.target.value}) 
                            }}
                        />
                    </Form.Item>
                    <Button type='primary' onClick={handleSearch}>查询</Button>
                </Form>
            </div>
            <div className={commonListStyles.checkTableList + ' normalFont'}>
                <Table
                    columns={columns}
                    scroll={{ y: 720 }}
                    rowKey={(record) => record.id}
                    dataSource={tableData}
                    pagination={{
                        current: curPage,
                        pageSize: pageSize,
                        total: total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: any) => `共 ${total} 条记录`
                    }}
                    onChange={(value) => {
                        setCurPage(value.current || 1)
                        setPageSize(value.pageSize || 20)
                    }}
                    className='commonTable normalFont'
                />
            </div>
        </div>
    )
}
