import { Button, Input, Select, Modal, message, Form } from 'antd';
import commonListStyles from '@/styles/CommonList.module.scss';
import teamMemberStyles from '@/styles/TeamMembers.module.css';
import search from '@/images/commonList/search.png';
import { reqAddProject } from '@/service/background/project';
import { useState, useEffect } from 'react';
import { getCurrentUrlParamValue } from '@/utils/url';
import MangerList from '@/components/background/channel/ManageList'; // 渠道管理
import DataList from '@/components/background/channel/DataList'; // 渠道数据
import { useRouter } from 'next/router';

export default function CommonList(prop: any) {
    const router = useRouter();
    const projectManage = '渠道管理';
    const projectAudit = '渠道数据';
    const [searchKey, setSearchKey] = useState('');
    const [activeTab, setActiveTab] = useState(projectManage);
    const [createModel, setCreateModel] = useState(false);
    const isManage = activeTab === projectManage;
    const tab = router.query.tab;
    const teamId = router.query.teamId;

    useEffect(() => {
        if (tab == undefined) {
            setActiveTab(projectManage)
        } else if (tab == '0' && projectManage) {
            setActiveTab(projectManage)
        } else if (tab == '1') {
            setActiveTab(projectAudit)
        }
    }, [tab, teamId])

    function searchedEnter(e: any) {
        if (e.key === 'Enter') {
            setSearchKey(e.target.value)
        }
    }

    const onInputChangeHandle = (e: any) => {
        if (e.target.value === '') {
            setSearchKey('')
        }
    }


    function showAddTeamModel() {
        setCreateModel(true)
    }

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                        <span className={commonListStyles.title}>发布渠道</span>
                        <div className={'titleTabs'}>
                            <div className={'tabItem ' + (isManage ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(projectManage) }}>{projectManage}</div>
                            <div className={'tabItem ' + (activeTab === projectAudit ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(projectAudit) }}>{projectAudit}</div>
                        </div>
                    </div>

                    <div>
                        <span className={'commonSearchInput'}>
                            <Input
                                prefix={<img src={search.src} width='16' />}
                                placeholder="请输入渠道名称搜索"
                                allowClear
                                style={{ width: '256px', borderRadius: '4px' }}
                                onPressEnter={e => searchedEnter(e)}
                                onChange={(e) => { onInputChangeHandle(e) }}
                            />
                        </span>
                        <Button type='primary' className={teamMemberStyles.addMember} onClick={() => { setActiveTab(projectManage); showAddTeamModel() }}>新建渠道</Button>
                    </div>
                </div>

                <div>
                    {isManage ?
                        <MangerList searchKey={searchKey} createModel={createModel} setCreateModel={setCreateModel} />
                        :
                        <DataList searchKey={searchKey} />
                    }
                </div>
            </div>
        </>
    )
}
