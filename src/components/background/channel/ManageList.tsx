import { Button, Input, Select, Table, Popconfirm, Form, InputNumber, message, Switch, Modal, Upload, UploadProps, Spin, Tooltip } from 'antd';
import commonListStyles from '@/styles/CommonList.module.scss';
import teamMemberStyles from '@/styles/TeamMembers.module.css';
import operationListStyles from '@/styles/OperationList.module.scss';
import channelClassify from '@/styles/channelClassify.module.scss';
import { useState, useEffect } from 'react';
import Router, { useRouter } from 'next/router';
import { reqChannelList, reqAddChannel, reqEditChannel } from '@/service/background/channel';
import { reqImageUpload } from '@/service/api';
import imgUpload from "@/images/imgUpload.svg";
import imgUploadActive from "@/images/imgUploadActive.svg";

export default function MangerList(prop: any) {
    const { searchKey, createModel, setCreateModel } = prop;
    const { TextArea } = Input;
    const { Dragger } = Upload;
    const router = useRouter();

    const [tableData, setTableData] = useState(Array());
    const [form] = Form.useForm();
    const [curPage, setCurPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [total, setTotal] = useState(0);
    const [imgUploadUrl, setImgUploadUrl] = useState('');
    const [imgLoading, setImgLoading] = useState(false);

    // 渠道新增 && 编辑
    const [modelType, setModelType] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [addCount, setAddCount] = useState(0);
    const [tableRecordId, setTableRecordId] = useState();
    const [isHovered, setIsHovered] = useState(false);

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            width: 100,
        },
        {
            title: '渠道logo',
            dataIndex: 'image_url',
            key: 'image_url',
            render: (text: any) => {
                return <>
                    {text && <img src={text} alt="" style={{ width: '26px', height: '26px' }} />}
                </>
            }
        },
        {
            title: '渠道名称',
            dataIndex: 'channel_name',
            key: 'channel_name',
            render: (text: any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '介绍',
            dataIndex: 'description',
            key: 'description',
            // width: 380,
            ellipsis: true,
            render: (text: any) => {
                return <>
                    <Tooltip placement="topLeft" title={text}>
                        <span>{text}</span>
                    </Tooltip>
                </>
            }
        },
        {
            title: '渠道标识',
            dataIndex: 'identifier',
            key: 'identifier',
            render: (text: any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '显示顺序',
            dataIndex: 'sort',
            key: 'sort',
            render: (text: any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '可见src',
            dataIndex: 'src',
            key: 'src',
            render: (text: any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '全渠道展示入口',
            dataIndex: 'show_all',
            key: 'show_all',
            width: 150,
            render: (_: any, record: any,) => {
                return <>
                    <Popconfirm
                        title={_ == 1 ? "确定关闭吗？" : "确定开启吗？"}
                        onConfirm={() => handleConfirm(1, record)}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Switch checked={_ == 1} />
                    </Popconfirm>
                </>
            }
        },
        {
            title: '启用状态',
            dataIndex: 'used_status',
            key: 'used_status',
            render: (_: any, record: any,) => {
                return <>
                    <Popconfirm
                        title={_ == 1 ? "确定关闭吗？" : "确定开启吗？"}
                        onConfirm={() => handleConfirm(2, record)}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Switch checked={_ == 1} />
                    </Popconfirm>
                </>
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 180,
            render: (_: any, record: any, index: number) => {
                return (
                    <span>
                        <a onClick={() => edit(record)} className={operationListStyles.detailBtn}>编辑</a>
                        <a
                            href={"/background/channelClassify?channelName=" + record.channel_name + "&channelId=" + record.id + "&identifier=" + record.identifier}
                            className={operationListStyles.detailBtn}
                        >分类管理</a>
                        <a href={"/background/agentSort?channelName=" + record.channel_name + "&channelId=" + record.id}
                            className={operationListStyles.detailBtn}
                        >排序</a>
                    </span>
                )
            }
        }
    ]

    useEffect(() => {
        if (createModel) {
            form.setFieldsValue({
                channel_name: '',
                image_url: '',
                sort: null,
                description: '',
                identifier: '',
                show_all: true,
                src: '',
            });
            setModelType('create');
            setIsModalOpen(true);
        } else {
            setModelType('edit');
        }
    }, [createModel])

    useEffect(() => {
        getChannelList()
    }, [curPage, searchKey, pageSize, addCount])

    //获取渠道列表
    const getChannelList = async () => {
        let res = await reqChannelList({
            keyword: searchKey,
            page: curPage,
            page_size: pageSize,
        })

        if (res?.data) {
            setTableData(res.data)
        }
        if (res?.pagination) {
            setPageSize(res.pagination.page_size)
            setTotal(res.pagination.total)
        }
    }

    const handleConfirm = (type: number, record: any) => {
        onSwitchChange(type, record);
    };

    // 列表switch
    const onSwitchChange = async (type: number, record: any) => {
        let params;
        // 1 全渠道展示入口，2 启用状态
        if (type == 1) {
            params = { id: record.id, show_all: record.show_all === 1 ? 2 : 1 };
        } else {
            params = { id: record.id, used_status: record.used_status === 1 ? 2 : 1 };
        }
        let res = await reqEditChannel(params);
        if (res?.context?.code == 0) {
            setAddCount(Date.now());
            message.success("更新成功")
        } else {
            // message.error(res.context.message)
            message.error('更新失败')
        }
    };

    // 编辑
    const edit = (record: any) => {
        setModelType('edit');
        setIsModalOpen(true);
        setTableRecordId(record.id);
        form.setFieldsValue({
            channel_name: record.channel_name,
            image_url: record.image_url,
            sort: record.sort,
            description: record.description,
            identifier: record.identifier,
            show_all: record.show_all == 1,
            src: record.src,
        });
        setImgUploadUrl(record.image_url);
    };

    // 新建&编辑Modal 渠道
    const handleOk = () => {
        form.validateFields().then((values) => {
            addTeam(values);
        });
    }

    // 取消
    const onCancel = () => {
        setIsModalOpen(false);
        form.resetFields();
        setImgUploadUrl('');
        setCreateModel(false);
        setImgLoading(false);
    }

    const addTeam = async (values: any) => {
        values.channel_name = values.channel_name.trim();
        values.show_all = values.show_all ? 1 : 2;
        if (modelType === 'create') {
            let res = await reqAddChannel(values)
            if (res?.context?.code == 0) {
                onCancel();
                setAddCount(Date.now());
                message.success("添加成功")
            } else {
                message.error("添加失败")
            }
        } else {
            const params = { id: tableRecordId, ...values };
            let res = await reqEditChannel(params);
            if (res?.context?.code == 0) {
                onCancel();
                setAddCount(Date.now());
                message.success("编辑成功")
            } else {
                // message.error("编辑失败")
            }
        }
    }

    const normFile = (e: any) => {
        if (Array.isArray(e)) {
            return e;
        }
        return e?.file;
    };

    // 上传logo
    const props: UploadProps = {
        name: 'file',
        multiple: false,
        showUploadList: false,
        async beforeUpload(file) {
            setImgLoading(true)
            const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/webp';
            const isLt2M = file.size / 1024 / 1024 < 2;
            const isSize = await new Promise<boolean>((resolve) => {
                const _URL = window.URL || window.webkitURL;
                const img = new Image();
                img.onload = () => {
                    const valid = img.width / img.height === 1;
                    resolve(valid);
                };
                img.src = _URL.createObjectURL(file);
            });

            if (!isJpgOrPng) {
                message.error('请上传png、jpeg、jpg、webp格式图片');
            } else if (!isLt2M) {
                message.error('上传图片大小不超过2M');
            } else if (!isSize) {
                message.error('上传图片比例建议1:1');
            }

            if (!isJpgOrPng || !isLt2M || !isSize) {
                setImgLoading(false)
            }

            if (isJpgOrPng && isLt2M && isSize) {
                const files = new FormData();
                files.append('file', file);
                reqImageUpload(files).then(res => {
                    setImgUploadUrl(res.image_url)
                    form.setFieldValue('image_url', res.image_url)
                    setImgLoading(false)
                }).catch(error => {
                    setImgLoading(false)
                    message.error("图片上传失败，请重新上传")
                })
            }

            return false;
        },
    };

    return (
        <>
            <div className={commonListStyles.tableList + ' normalFont'}>
                <Table
                    scroll={{ y: 720 }}
                    rowKey={(record) => record.id}
                    columns={columns}
                    dataSource={tableData}
                    pagination={{
                        current: curPage,
                        pageSize: pageSize,
                        total: total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: any) => `共 ${total} 条记录`
                    }}
                    onChange={(pagination) => {
                        setCurPage(pagination.current || 1)
                        setPageSize(pagination.pageSize || 20)
                    }}
                    className='commonTable normalFont'
                />
            </div>

            <Modal
                style={{ width: 480, maxHeight: 650, overflowY: 'scroll' }}
                className={"commonModal"}
                title={modelType === 'create' ? '新建渠道' : '编辑渠道'}
                centered
                destroyOnClose
                open={isModalOpen}
                maskClosable={false}
                onOk={handleOk}
                onCancel={onCancel}
                footer={
                    <>
                        <Button onClick={onCancel}>取消</Button>
                        <Button type="primary" onClick={handleOk}>确认</Button>
                    </>
                }>

                <Form
                    form={form}
                    layout="vertical"
                    autoComplete="off"
                    style={{ width: "100%", padding: "0 8px" }}
                >
                    <Form.Item
                        required
                        label="渠道名称"
                        name="channel_name"
                        rules={[
                            { required: true, message: '请输入渠道名称' },
                            {
                                validator: (_, value) => {
                                    if (value && value.trim() === '') {
                                        return Promise.reject(new Error('请输入渠道名称'));
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ]}
                    >
                        <Input placeholder="请输入渠道名称，最多10个文字" maxLength={10} />
                    </Form.Item>
                    <Form.Item required label="渠道logo" name="image_url" getValueFromEvent={normFile} rules={[{ required: true, message: '请上传渠道logo' }]} >
                        <Dragger {...props}>
                            {imgUploadUrl ?
                                <>
                                    <img src={imgUploadUrl} alt="" style={{ height: '93px', width: '93px' }} />
                                    {imgLoading && <div className={channelClassify.urlLoading}><Spin /></div>}
                                    <div className={`${channelClassify.urlLoading} ${channelClassify.urlMark}`}>重新上传</div>
                                </>
                                :
                                <div onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
                                    <p style={{ marginBottom: '8px' }}>
                                        <img src={isHovered ? imgUploadActive.src : imgUpload.src} />
                                    </p>
                                    <p className="ant-upload-text" style={{ fontSize: '12px', color: "#1B2532" }}>点击或拖拽文件到此区域上传</p>
                                    <p className="ant-upload-hint" style={{ fontSize: '12px' }}>支持png、jpeg、jpg、webp格式 / 建议比例1:1 / 大小不超过2M</p>
                                </div>
                            }
                        </Dragger>
                    </Form.Item>
                    <Form.Item required label="显示顺序" name="sort" rules={[{ required: true, message: '请输入显示顺序' }]} >
                        <InputNumber min={1} max={99} precision={0} placeholder="请输入渠道显示顺序，数字越大显示越靠前，最大为99" style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item required label="介绍" name="description" rules={[{ required: true, message: '请输入介绍信息' }]}>
                        <TextArea showCount placeholder="请输入介绍信息，最多50文字" maxLength={50} rows={4} />
                    </Form.Item>
                    <Form.Item
                        required
                        label="渠道标识"
                        name="identifier"
                        rules={(modelType === 'create' ? [
                            { required: true, message: '请输入渠道标识' },
                            {
                                validator: (_, value) => {
                                    if (value && value.trim() === '') {
                                        return Promise.reject(new Error('请输入渠道标识'));
                                    } else if (value && !/^[a-z_]{1,20}$/.test(value.trim())) {
                                        return Promise.reject(new Error('请输入最多20个小写字母组合'));
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ] : [])}
                    >
                        <Input placeholder="请输入渠道标识，最多20个小写字母组合" maxLength={20} disabled={modelType !== 'create'} />
                    </Form.Item>
                    <Form.Item
                        // required
                        label="可见src(不填默认为all)"
                        name="src"
                        rules={(modelType === 'create' ? [
                            // { required: true, message: '请输入渠道来源src' },
                            {
                                validator: (_, value) => {
                                    if (value && value.trim() === '') {
                                        return Promise.reject(new Error('请输入渠道来源src'));
                                    } else if (value && !/^[a-z_]{1,20}$/.test(value.trim())) {
                                        return Promise.reject(new Error('请输入最多20个小写字母组合'));
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ] : [])}
                    >
                        <Input placeholder="请输入渠道来源src，最多20个小写字母组合" maxLength={20} />
                    </Form.Item>
                    <div style={{ display: 'flex', gap: '5px' }}>
                        <span style={{ color: '#1B2532', lineHeight: '32px' }}>是否全渠道展示入口</span>
                        <Form.Item name="show_all" valuePropName="checked" style={{ marginBottom: '0' }}>
                            <Switch />
                        </Form.Item>
                    </div>
                </Form>

            </Modal>
        </>
    )
}
