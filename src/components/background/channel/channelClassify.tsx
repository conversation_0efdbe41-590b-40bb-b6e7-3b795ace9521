import { useState, useEffect, useRef } from 'react';
import { Button, Input, Select, Table, Tooltip, Popconfirm, message, Modal, Switch, Form, ConfigProvider, InputNumber } from 'antd';
import commonListStyles from '@/styles/CommonList.module.scss';
import teamMemberStyles from '@/styles/TeamMembers.module.css';
import channelClassify from '@/styles/channelClassify.module.scss';
import operationListStyles from '@/styles/OperationList.module.scss';
import right_arrow from '@/images/common/right_arrow.png';
import search from '@/images/commonList/search.png';
import arrowDownCare from '@/images/arrowDownCare.svg';
import arrowRightCare from '@/images/arrowRightCare.svg';
import { getCurrentUrlParamValue } from '@/utils/url';
import { reqClassifyList, reqAddClassify, reqEditClassify } from '@/service/background/channel';
import { useRouter } from "next/router";
import ListEmpty from '@/components/commonComponents/listEmpty/ListEmpty';

export default function ChannelClassify(prop: any) {

    const [tableData, setTableData] = useState(Array());
    const [curPage, setCurPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [total, setTotal] = useState(0);
    const [addCount, setAddCount] = useState(0);
    const router = useRouter();
    const channelName = router.query.channelName;

    // 一级分类
    const [form] = Form.useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modelType, setModelType] = useState('');
    const [firstClassifyId, setFirstClassifyId] = useState();

    // 子分类
    const [formChild] = Form.useForm();
    const [isModalChildOpen, setIsModalChildOpen] = useState(false);
    const [modelChildType, setModelChildType] = useState('');
    const [childClassifyId, setChildClassifyId] = useState();
    const [isYunPanBoard, setIsYunPanBoard] = useState(false);

    useEffect(() => {

        if (router.query.identifier === 'fangcloud') {
            setIsYunPanBoard(true);
        } else {
            setIsYunPanBoard(false);
        }
    }, [router.query.identifier])
    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            render: (_: any, record: any) => {
                return (
                    <>
                        {record.tag_parent_id === 0 ? _ : '-'}
                    </>
                )
            }
        },
        {
            title: '分类名称',
            dataIndex: 'tag_name',
            key: 'tag_name',
            render: (_: any, record: any) => {
                return <>
                    {record.tag_parent_id === 0 ? _ : '-'}
                </>
            }
        },
        {
            title: '子分类名称',
            dataIndex: 'tag_name',
            key: 'tag_name',
            render: (_: any, record: any) => {
                return <>
                    {record.tag_parent_id === 0 ? <span>-</span> : _}
                </>
            }
        },
        {
            title: '显示排序',
            dataIndex: 'sort',
            key: 'sort'
        },
        {
            title: '启用状态',
            dataIndex: 'used_status',
            key: 'used_status',
            render: (_: any, record: any) => {
                return <>
                    <Popconfirm
                        title="确定切换开关吗？"
                        onConfirm={() => handleConfirm(record)}
                        okText="确定"
                        cancelText="取消"
                    >
                        {isYunPanBoard ? <Tooltip title="无法修改，数据以API形式与云盘系统进行同步；">
                            <Switch checked={_ == 1} disabled={isYunPanBoard} />
                        </Tooltip> :   // 云盘系统
                            <Switch checked={_ == 1} />
                        }
                    </Popconfirm>
                </>
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (_: any, record: any) => {
                // 判断记录是否应该被禁用
                return (
                    <>
                        {record.level === 1 ?
                            <>
                                <a
                                    onClick={() => !isYunPanBoard && editFirstClassify(record)}
                                    className={`${operationListStyles.detailBtn} ${isYunPanBoard ? operationListStyles.disabledBtn : ''}`}
                                    style={{ cursor: isYunPanBoard ? 'not-allowed' : 'pointer', color: isYunPanBoard ? '#999' : '' }}
                                >
                                    <Tooltip title={isYunPanBoard ? '无法修改，数据以API形式与云盘系统进行同步；' : ''}>编辑</Tooltip>
                                </a>
                                <a
                                    onClick={() => !isYunPanBoard && setOpenAddChild(record)}
                                    className={`${operationListStyles.detailBtn} ${isYunPanBoard ? operationListStyles.disabledBtn : ''}`}
                                    style={{ cursor: isYunPanBoard ? 'not-allowed' : 'pointer', color: isYunPanBoard ? '#999' : '' }}
                                >
                                    <Tooltip title={isYunPanBoard ? '无法修改，数据以API形式与云盘系统进行同步；' : ''}>新建子分类</Tooltip>
                                </a>
                            </>
                            :
                            <>
                                <a
                                    onClick={() => !isYunPanBoard && editChildClassify(record)}
                                    className={`${operationListStyles.detailBtn} ${isYunPanBoard ? operationListStyles.disabledBtn : ''}`}
                                    style={{ cursor: isYunPanBoard ? 'not-allowed' : 'pointer', color: isYunPanBoard ? '#999' : '' }}
                                >编辑</a>
                            </>
                        }
                    </>
                )
            }
        }
    ];

    useEffect(() => {
        getClassifyList();
    }, [curPage, pageSize, addCount])

    // 获取分类管理列表
    const getClassifyList = async () => {
        let res = await reqClassifyList({
            channel_id: Number(getCurrentUrlParamValue('channelId')),
            page: curPage,
            page_size: pageSize,
        })

        if (res?.data) {
            setTableData(res.data)
        }

        if (res?.pagination) {
            setPageSize(res.pagination.page_size)
            setTotal(res.pagination.total)
        }
    };

    // 一级分类 - 列表switch
    const handleConfirm = async (record: any) => {
        let res = await reqEditClassify({
            id: record.id,
            used_status: record.used_status === 1 ? 2 : 1
        });
        if (res?.context?.code == 0) {
            setAddCount(Date.now());
            message.success("更新成功")
        } else {
            // message.error(res.context.message)
            message.error("更新失败")
        }
    };

    // 一级分类
    // 新建
    const setOpenAddClassify = () => {
        setIsModalOpen(true);
        setModelType('create');
    }

    // 编辑
    const editFirstClassify = (record: any) => {
        setModelType('edit');
        setFirstClassifyId(record.id);
        setIsModalOpen(true);
        form.setFieldsValue({
            business_id: record.business_id,
            tag_name: record.tag_name,
            sort: record.sort,
        });
    }

    // 新建&编辑Modal
    const handleOk = () => {
        form.validateFields().then((values) => {
            setIsModalOpen(false);
            addTeam(values);
        });
    }

    const addTeam = async (values: any) => {
        if (modelType === 'create') {
            let res = await reqAddClassify({
                channel_id: Number(getCurrentUrlParamValue('channelId')),
                level: 1,
                ...values,
            })
            form.resetFields()
            if (res?.context?.code == 0) {
                setAddCount(Date.now())
                message.success("新建成功")
            } else {
                // message.error(res.context.message)
                message.error("新建失败")
            }
        } else {
            let res = await reqEditClassify({
                id: firstClassifyId,
                ...values
            })
            form.resetFields()
            if (res?.context?.code == 0) {
                setAddCount(Date.now())
                message.success("更新成功")
            } else {
                // message.error(res.context.message)
                message.error("更新失败")
            }
        }
    }

    // 子分类
    // 新建
    const setOpenAddChild = (record: any) => {
        setModelChildType('create');
        setFirstClassifyId(record.id);
        setIsModalChildOpen(true);
        formChild.setFieldsValue({
            tag_name: record.tag_name
        })
    }

    // 编辑
    const editChildClassify = (record: any) => {
        let tag_parent = tableData.find((item) => {
            return item.id === record.tag_parent_id;
        });
        setModelChildType('edit');
        setChildClassifyId(record.id);
        setIsModalChildOpen(true);
        formChild.setFieldsValue({
            tag_name: tag_parent.tag_name,
            business_id: record.business_id,
            tag_child_name: record.tag_name,
            sort: record.sort,
        });
    }

    // 新建&编辑Modal
    const handleChildOk = () => {
        formChild.validateFields().then((values) => {
            setIsModalChildOpen(false);
            addChildTeam(values);
        });
    }

    const addChildTeam = async (values: any) => {
        if (modelChildType === 'create') {
            const params = {
                channel_id: Number(getCurrentUrlParamValue('channelId')),
                level: 2,
                sort: values.sort,
                tag_name: values.tag_child_name,
                business_id: values.business_id,
                tag_parent_id: firstClassifyId,
            }
            let res = await reqAddClassify(params)
            formChild.resetFields()
            if (res?.context?.code == 0) {
                setAddCount(Date.now())
                message.success("新建成功")
            } else {
                // message.error(res.context.message)
                message.error('新建失败')
            }
        } else {
            let res = await reqEditClassify({
                id: childClassifyId,
                tag_name: values.tag_child_name,
                business_id: values.business_id,
            })
            formChild.resetFields()
            if (res?.context?.code == 0) {
                setAddCount(Date.now())
                message.success("更新成功")
            } else {
                // message.error(res.context.message)
                message.error('更新失败')
            }
        }
    }

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <a href='/background/channelList?tab=0'>
                        <img src={right_arrow.src} width='16' height='12' style={{ marginRight: 24 }} />
                        <span className={commonListStyles.title}>{channelName}</span>
                    </a>
                    {
                        isYunPanBoard ? <Tooltip title="无法修改，数据以API形式与云盘系统进行同步；">
                            <Button type='primary' disabled={isYunPanBoard} className={teamMemberStyles.addMember} onClick={setOpenAddClassify}>新建一级分类</Button>
                        </Tooltip> : <Button type='primary' className={teamMemberStyles.addMember} onClick={setOpenAddClassify}>新建一级分类</Button>
                    }

                </div>
                <div className={commonListStyles.tableList + ' normalFont' + channelClassify.tableExpand}>
                    {tableData.length > 0 ?
                        <ConfigProvider>
                            <Table
                                columns={columns}
                                rowKey={(record) => record.id}
                                scroll={{ y: 720 }}
                                dataSource={tableData}
                                pagination={{
                                    current: curPage,
                                    pageSize: pageSize,
                                    total: total,
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total: any) => `共 ${total} 条记录`
                                }}
                                onChange={(pagination) => {
                                    setCurPage(pagination.current || 1)
                                    setPageSize(pagination.pageSize || 20)
                                }}
                                className={["commonTable", "normalFont", channelClassify.tableExpand].join(' ')}
                                expandable={{
                                    expandedRowRender: record => (
                                        null
                                    ),
                                    expandIcon: ({ expanded, onExpand, record }) =>
                                        record.children ?
                                            (expanded ? (
                                                <img src={arrowDownCare.src} onClick={e => onExpand(record, e)} />
                                            ) : (
                                                <img src={arrowRightCare.src} onClick={e => onExpand(record, e)} />
                                            ))
                                            :
                                            null
                                }}
                            />
                        </ConfigProvider>
                        :
                        <div className={channelClassify.listEmpty}>
                            <ListEmpty createTitle="创建一级分类" createNewHandel={setOpenAddClassify} desc="当前渠道暂无分类，点击下方按钮立即创建～" />
                        </div>
                    }
                </div>

                <Modal
                    style={{ width: 480 }}
                    className={"commonModal"}
                    title={modelType === 'create' ? '新建一级分类' : '编辑一级分类'}
                    maskClosable={false}
                    open={isModalOpen}
                    onOk={handleOk}
                    onCancel={() => setIsModalOpen(false)}
                    footer={
                        <>
                            <Button onClick={() => { setIsModalOpen(false); form.resetFields(); }}>取消</Button>
                            <Button
                                type="primary"
                                onClick={() => { handleOk() }}
                            >
                                确认
                            </Button>
                        </>
                    }>

                    <Form
                        form={form}
                        layout="vertical"
                        autoComplete="off"
                        style={{ width: "100%", padding: "0 8px" }}
                    >
                        <Form.Item label="分类标签ID" name="business_id" >
                            <Input placeholder="请输入分类标签ID" />
                            {/* <InputNumber min={0} precision={0} placeholder="请输入分类标签ID" style={{ width: '100%' }} /> */}
                        </Form.Item>
                        <Form.Item required label="分类名称" name="tag_name" rules={[{ required: true, message: '请输入分类名称' }]} >
                            <Input placeholder="请输入分类名称" />
                        </Form.Item>
                        <Form.Item required label="分类排序" name="sort" rules={[{ required: true, message: '请输入分类排序' }]} >
                            <InputNumber style={{ width: '100%' }} placeholder="请输入分类排序" />
                        </Form.Item>
                    </Form>

                </Modal>

                <Modal
                    style={{ width: 480 }}
                    className={"commonModal"}
                    title={modelChildType === 'create' ? '新建子分类' : '编辑子分类'}
                    open={isModalChildOpen}
                    onOk={handleChildOk}
                    onCancel={() => setIsModalChildOpen(false)}
                    footer={
                        <>
                            <Button onClick={() => { setIsModalChildOpen(false); formChild.resetFields(); }}>取消</Button>
                            <Button
                                type="primary"
                                onClick={() => { handleChildOk() }}
                            >
                                确认
                            </Button>
                        </>
                    }>

                    <Form
                        form={formChild}
                        layout="vertical"
                        autoComplete="off"
                        style={{ width: "100%", padding: "0 8px" }}
                    >
                        <Form.Item label="父级分类名称" name="tag_name">
                            <Input placeholder="请输入分类名称" disabled />
                        </Form.Item>
                        <Form.Item label="子分类标签ID" name="business_id" >
                            <Input placeholder="请输入子分类标签ID" />
                            {/* <InputNumber min={0} precision={0} placeholder="请输入子分类标签ID" style={{ width: '100%' }} /> */}
                        </Form.Item>
                        <Form.Item required label="子分类名称" name="tag_child_name" rules={[{ required: true, message: '请输入子分类名称' }]} >
                            <Input placeholder="请输入子分类名称" />
                        </Form.Item>
                        <Form.Item required label="分类排序" name="sort" rules={[{ required: true, message: '请输入分类排序' }]} >
                            <InputNumber style={{ width: '100%' }} placeholder="请输入分类排序" />
                        </Form.Item>
                    </Form>

                </Modal>
            </div>
        </>
    )
}
