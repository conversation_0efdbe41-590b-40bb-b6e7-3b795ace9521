import { useState, useEffect } from 'react';
import { Table, Popconfirm, message } from 'antd';
import commonListStyles from '@/styles/CommonList.module.scss'
import operationListStyles from '@/styles/OperationList.module.scss'
import { reqDataList } from '@/service/background/channel'
import { useRouter } from "next/router";

export default function DataList(prop: any) {
    const { searchKey } = prop;
    const [tableData, setTableData] = useState(Array());
    const [curPage, setCurPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [total, setTotal] = useState(0);
    const router = useRouter();

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
        },
        {
            title: '渠道名称',
            dataIndex: 'channel_name',
            key: 'channel_name',
            render: (text: any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: 'Agent发布数量',
            dataIndex: 'agents_num',
            key: 'agents_num',
            ellipsis: true,
            render: (text: any) => {
                return <>
                    <span>{text}</span>
                </>
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            render: (_: any, record: any, index: number) => {
                return (
                    <span>
                        <a
                            href={`/background/agentBgPublishChannel?teamId=${router.query?.teamId}&templateTab=agents&publishedChannel=` + record.id}
                            className={operationListStyles.detailBtn}
                        >查看</a>
                    </span>
                )
            }
        }
    ]

    useEffect(() => {
        getDataList()
    }, [curPage, searchKey, pageSize])

    const getDataList = async () => {
        let res = await reqDataList({
            keyword: searchKey,
            page: curPage,
            page_size: pageSize,
        })

        if (res?.data) {
            setTableData(res.data)
        }
        if (res?.pagination) {
            setPageSize(res.pagination.page_size)
            setTotal(res.pagination.total)
        }
    }

    return (
        <div className={commonListStyles.tableList + ' normalFont'}>
            <Table
                scroll={{ y: 720 }}
                rowKey={(record) => record.id}
                columns={columns}
                dataSource={tableData}
                pagination={{
                    current: curPage,
                    pageSize: pageSize,
                    total: total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: any) => `共 ${total} 条记录`
                }}
                onChange={(pagination) => {
                    setCurPage(pagination.current || 1)
                    setPageSize(pagination.pageSize || 20)
                }}
                className='commonTable normalFont'
            />
        </div>
    )
}
