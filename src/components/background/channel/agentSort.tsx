/*
 * @Author: zx
 * @Date: 2025-03-06 17:22:12
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-04-10 16:43:20
 * @FilePath: \prompt-web\src\components\background\channel\agentSort.tsx
 */
import { useState, useEffect, useRef } from 'react';
import { Button, Input, Select, Table, Popconfirm, message, Modal, Switch, Form, ConfigProvider, InputNumber, Tooltip } from 'antd';
import commonListStyles from '@/styles/CommonList.module.scss';
import right_arrow from '@/images/common/right_arrow.png';
import search from '@/images/commonList/search.png';
import { reqChannelAgentList, reqModifyForkNums, reqModifyWeight } from '@/service/background/agentsort';
import { useRouter } from "next/router";
import { sortBy } from 'ramda';
import editBtn from '@/images/editBtn.png'


export default function AgentSort(prop: any) {


    const [tableData, setTableData] = useState(Array());
    const [curPage, setCurPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [total, setTotal] = useState(0);
    const [addCount, setAddCount] = useState(0);
    const [orderBy, setOrderBy] = useState(3);


    const router = useRouter();
    const channelName = router.query.channelName;
    const channelId = router.query.channelId;
    const [isEditableRow, setIsEditableRow] = useState<string | null>(null);   //热度编辑
    const [isEditableRowWeight, setIsEditableRowWeight] = useState<string | null>(null);   //自定义排序编辑




    const columns = [
        {
            title: '排序',
            key: 'index',
            width:60,
            render: (text: any, record: any, index: number) => index + 1,
        },
        {
            title: 'LOGO',
            dataIndex: 'icon',
            width:100,
            key:'icon',
            render: (text: any, record: any, index: number) => {
                return <img src={text} width='40' height='40' /> 
            }
        },
        {
            title: 'agent名称',
            dataIndex: 'name',
            key: 'name',
            width:100,
          
        },
        {
            title: '产品描述',
            dataIndex: 'description',
            key: 'description',
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            width:200,
            render: (text: any, record: any, index: number) => {
                return <div>{text ? text.replace('T', ' ') : ''}</div>;
            }
        },
        {
            title: '热度',
            dataIndex: 'fork_nums',
            key: 'fork_nums',
            width:100,
            sorter: true,
            render: (text: any, record: any, index: number) => {
                return (
                    !isEditableRow || isEditableRow !== record.id ? (
                        <div>
                            <Tooltip title={text} placement='top'> 
                                <span>
                                    {text}
                                </span>
                            </Tooltip>
                            <img
                                src={editBtn.src}
                                height={14}
                                width={14}
                                onClick={() => handleEditClick(record.id)}
                                style={{ cursor: 'pointer', marginLeft: '5px' }}
                            />
                        </div>
                    ) : (
                        <Input
                            value={text}
                            maxLength={200}
                            onBlur={(e: any) => { handleBlur(e, index, record); }}
                            onPressEnter={(e: any) => { handleSave(e, index, record); }}
                            onChange={(e) => handleForkNumsInputChange(e, index, record)}
                        />
                    )
                );
            }
        },
        {
            title: '自定义排序',
            dataIndex: 'weight',
            key: 'weight',
            width:150,
            sorter: true,
            render: (text: any, record: any, index: number) => {
                return (
                    !isEditableRowWeight || isEditableRowWeight !== record.id ? (
                        <div>
                            <Tooltip title={text} placement='top'> 
                                <span>
                                    {text}
                                </span>
                            </Tooltip>
                            <img
                                src={editBtn.src}
                                height={14}
                                width={14}
                                onClick={() => handleEditClickWeight(record.id)}
                                style={{ cursor: 'pointer', marginLeft: '5px' }}
                            />
                        </div>
                    ) : (
                        <Input
                            value={text}
                            onBlur={(e: any) => { handleBlurWeight(e, index, record); }}
                            onPressEnter={(e: any) => { handleSaveWeight(e, index, record); }}
                            onChange={(e) => handleInputChange(e, index, record)}
                        />
                    )
                );
            }
        }
    ];

    

    // 编辑热度
    const handleEditClick = (id: string) => {
        setIsEditableRow(isEditableRow === id ? null : id);
    };
    // 编辑自定义排序
    const handleEditClickWeight = (id: string) => {
        setIsEditableRowWeight(isEditableRowWeight === id? null : id);
    };
    //热度
    const handleBlur = async (e: Event, index: number, item: any) => {
        console.log("🚀 ~ handleBlur ~ item:", item)
        e.preventDefault();
        e.stopPropagation()
        setIsEditableRow(null)
        try {
            const res = await reqModifyForkNums({
                agent_channel_id: item.agent_channel_id,
                fork_nums: tableData[index].fork_nums,
            });
            res && message.success('保存成功');
            res && getAgentChannelList();
        } catch (error) {
            message.error('保存失败');
        }
    }
    //自定义排序
    const handleBlurWeight = async (e: Event, index: number, item: any) => {
        e.preventDefault();
        e.stopPropagation()
        setIsEditableRowWeight(null)  
        try {
            const res = await reqModifyWeight({
                agent_channel_id: item.agent_channel_id,
                weight: tableData[index].weight,
            });
            res && message.success('保存成功');
            res && getAgentChannelList();
        } catch (error) {
            message.error('保存失败');
        }

    }
    // 保存热度
    const handleSave = (e: any, index: number, record: any) => {
        e.preventDefault(); // 阻止默认提交行为
        setIsEditableRow(null); // 退出编辑模式
        console.log('保存的数据:', record.version_description);
    };

    //保存自定义
    const handleSaveWeight = (e: any, index: number, record: any) => {
        e.preventDefault(); // 阻止默认提交行为
        setIsEditableRowWeight(null); // 退出编辑模式
        console.log('保存的数据:', record.version_description); 
    }
    
    
    const handleForkNumsInputChange = (e: any, index: number, record: any) => {
        console.log(e.target.value,'999999999999');
        
        const updatedList = [...tableData];
        console.log("🚀 ~ handleForkNumsInputChange ~ updatedList:", updatedList)
        
        updatedList[index] = {
            ...record,
            fork_nums: e.target.value, // 更新输入的内容
        };

        setTableData(updatedList); // 更新表格数据
    };

    const handleInputChange = (e: any, index: number, record: any) => {

        const updatedList = [...tableData];
        updatedList[index] = {
           ...record,
            weight: e.target.value, // 更新输入的内容
        }; 
        setTableData(updatedList); // 更新表格数据

    }
      
    



    const getAgentChannelList = async () => {
        const res = await reqChannelAgentList({
            page: curPage,
            page_size: pageSize,
            channel_id: channelId,
        });
        console.log(res);
        if(res.data){
            const uniqueData = Array.from(new Set(res.data.data.map((item:any) => item.id)))
            .map(id => res.data.data.find((item:any) => item.id === id));
            setTableData(uniqueData); 
            setTotal(res.data.pagination.total);
            // setTableData(res.data.data); 
        }
    }


    const handleTableAction = (pag: any, filters: any, sorter: any) => {
        console.log('pag',pag);
        console.log('filters',filters);
        console.log('sorter',sorter);
        

        let params = {};
        if(sorter.order === 'ascend'){
            params = {
                page: curPage,
                page_size: pageSize,
                channel_id: channelId,
            } 
        }else{
            params = {
                page: curPage,
                page_size: pageSize,
                channel_id: channelId,
                order_by: sorter.columnKey == 'created_at' ? 1 : sorter.columnKey == 'fork_nums' ? 3 : 4,
            }
        }
        if (channelId) {
            reqChannelAgentList(params).then((res) => {
                const uniqueData = Array.from(new Set(res.data.data.map((item:any) => item.id)))
            .map(id => res.data.data.find((item:any) => item.id === id));
                // res && setTableData(res.data.data)
                res && setTableData(uniqueData)
                res && setTotal(res.data.pagination.total)
                setCurPage(pag.current || 1)
                setPageSize(pag.pageSize || 20)
            })
        }
    }

    useEffect(() => {
        if(channelId){
            getAgentChannelList();
        }
    }, [curPage, pageSize,channelId])







    return(
        <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <a href='/background/channelList?tab=0'>
                        <img src={right_arrow.src} width='16' height='12' style={{ marginRight: 24 }} />
                        <span className={commonListStyles.title}>{channelName}</span>
                    </a>
                </div>
                <div className={commonListStyles.tableList + ' normalFont'}>
                        <ConfigProvider>
                            <Table
                                columns={columns}
                                rowKey={(record) => record.id}
                                scroll={{ y: 720 }}
                                dataSource={tableData}
                                pagination={{
                                    current: curPage,
                                    pageSize: pageSize,
                                    total: total,
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total: any) => `共 ${total} 条记录`
                                }}
                                // onChange={(pagination) => {
                                //     setCurPage(pagination.current || 1)
                                //     setPageSize(pagination.pageSize || 20)
                                // }}
                                onChange={handleTableAction}
                                // className={["commonTable", "normalFont", channelClassify.tableExpand].join(' ')}
                            />
                        </ConfigProvider>
                       
                </div>
        </div>
    )

    
}
