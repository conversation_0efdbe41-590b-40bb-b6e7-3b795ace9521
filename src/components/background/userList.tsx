
import commonListStyles from '@/styles/CommonList.module.scss'
import { Button, Input, Select, Table, Popconfirm, message,Modal, Tabs,Upload,UploadProps, Progress} from 'antd';
import search from '@/images/commonList/search.png'
import { useState, useEffect, useRef } from 'react';
import teamMemberStyles from '@/styles/TeamMembers.module.css'
import userListStyles from "./userList.module.scss"
import {reqGetOutnetUserList ,reqUpdateUserStatus,reqGetUserInfo,reqAddUser,reqAddUserFile} from '@/service/background/userlist'
import operationListStyles from '@/styles/OperationList.module.scss'



export default function userList() {

    const [searchKey, setSeachKey] = useState('')  //搜索用户
    const [userSearchKey,setUserSearchKey] = useState('')  //搜索用户的手机号、邮箱、姓名
    const [isSearchFailed,setIsSearchFailed] = useState(false)
    const [openAddUser, setOpenAddUser ] = useState(false);
    const [tableData, setTableData] = useState(new Array())
    const [isBatchAdd,setIsBatchAdd] = useState(false)
    const [inputPlaceholder, setInputPlaceholder] = useState('请输入手机号查询')
    const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
    const [uploadedFileSize, setUploadedFileSize] = useState(0);
    const [uploadedFile,setUploadedFile] =useState<File | null>(null);
    const [binaryFile, setBinaryFile] = useState<string | null>(null);
    const [uploading, setUploading] = useState(false); // 是否正在上传
    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)
    const [editingKey, setEditingKey] = useState('');
    const [queryMode,setQueryMode] = useState(2)
    const [isAddSucess,setIsAddSuccess] = useState(false) //单个用户添加请求是否成功
    const [isBatchAddSucess,setIsBatchAddSuccess] = useState(false) //批量添加是否成功
    const [addStatusMessage,setAddStatusMessage] = useState('')
    const [status,setStatus] = useState(0)
    const [activeKey, setActiveKey] = useState('1');
    const [singleStatus,setSingleStatus] = useState(0)  // 单个用户添加状态
    const [userInfo,setUserInfo] = useState<{ qid: ''; username: ''; nickname: '' } | null>(null); 
    const [failedList,setFailedList] = useState<{ qid: string; reason:string }[]>([]); 
    const [singleFailedReason,setSingleFailedReason] = useState<{qid:'',reason:''} | null>(null)





    const { Option } = Select;
    const { Dragger } = Upload;


    const props: UploadProps = {
        name: 'file',
        multiple: false,
        showUploadList: false,
        accept: '.xls,.xlsx',
        beforeUpload(file:File) {
            const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            if (!isExcel) {
              message.error('请上传Excel格式文件');
              return Upload.LIST_IGNORE; // 阻止文件上传
            }
            return true; // 允许文件上传
          },
          customRequest({ file, onSuccess }) {
            const reader = new FileReader();
            reader.onload = () => {
                const f = file as File;
                const result = reader.result as string;
                const binary = result.split(',')[1]; // 获取二进制数据部分
                setUploadedFileName(f.name); // 设置文件名
                setUploadedFileSize(f.size)
                setUploadedFile(f); // 设置文件对象
                // setBinaryFile(binary);
                message.success('上传成功');
            };
            if (file instanceof Blob) {
                reader.readAsDataURL(file);
              } else {
                message.error('文件类型错误');
              }
          },
          onChange(info) {
            const { status } = info.file;
            if (status === 'uploading') {
              setUploading(true);
            } else if (status === 'done') {
                setUploading(false);
                message.success('上传成功');
            }
            else if (status === 'error') {
              setUploading(false);
              message.error(`${info.file.name} file upload failed.`);
            }
          },
          onDrop(e) {
            console.log('Dropped files', e.dataTransfer.files);
          },
    };

    const formatSize = (size: number) => {
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
        return `${(size / 1024 / 1024).toFixed(2)} MB`;
    };

    useEffect(()=>{
        getUserList()
    },[searchKey,curPage,pageSize])


    //根据手机号、邮箱、姓名查询用户
    const getUserInfo = async () => {
        let res = await reqGetUserInfo({
            keyword:userSearchKey,
            search_type:queryMode
        });
        if(res == undefined){
            setIsSearchFailed(true)
        }
        if(res){
            setUserInfo(res.data)
        }
    }

    useEffect(()=>{
        if(userSearchKey !== ''){
            getUserInfo()
        }
    },[userSearchKey])


    const  searchkeyEnter = (e:any) =>{
        if (e.key === 'Enter') {
           setSeachKey(e.target.value)
        }
    }

    const queryModeEnter = (e:any) =>{
        if (e.key === 'Enter') {
            setUserSearchKey(e.target.value)
         }
    }

       
    const getUserList = async () => {
        let res = await reqGetOutnetUserList({
            page: curPage,
            page_size: pageSize,
            keyword:searchKey

        });
        if (res != undefined && res.context != undefined && res.context.code == 0) {
            setTableData(res.data)
            // originData.current = res.data
            if (res.pagination!=undefined){
                setPageSize(res.pagination.page_size)
                setTotal(res.pagination.total)
            }
        } else {
            if (res != undefined && res.context != undefined) {
                message.error(res.context.message)
            } else {
                message.error("获取列表失败")
            }
        }
    }
    
    const  getUserStatus =  (user_status:any) =>{
        if(user_status == undefined){
             return '-'
        }
        if (user_status == 0) {
            return '否'
        }
        if (user_status == 1) {
            return '是'
        }
        return '-'
    }

    const cancel = () => {
        setEditingKey('');
    };
    const  isEditing = (record: any) =>{
        if (record == undefined) {
            return false
        }
        return record.id === editingKey;
    }
    const save = async(record: any) => {
        let res = await reqUpdateUserStatus({
            user_id:record.id,
            enable_white:record.user_status
        });
        console.log('res',res)
        if(res){
            message.success("更改权限成功")
        }
        setEditingKey('');
    };
    const edit = (record: any) => {
        setEditingKey(record?.id);
    };

    const columns = [
        {
            title: 'qid',
            dataIndex: 'qid',
            key: 'qid',

        },
        {
            title: '用户名',
            dataIndex: 'username',
            key: 'username',
        },
        {
            title: '是否加白',
            dataIndex: 'user_status',
            key: 'user_status',
            render: (_: any, record: any) => {
                const editable = isEditing(record);
                return editable ? <Select
                    className={teamMemberStyles.selectRole}
                    options={[
                        {
                            label: '否',
                            value: 0

                        },
                        {
                            label: '是',
                            value: 1

                        },
                    ]}
                    onChange={(value) => {
                        record.user_status = value
                    }}
                    defaultValue={getUserStatus(_)}
                    style={{ width: '160px' }}
                ></Select> : <span>{getUserStatus(_)}</span>
            },

        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (_: any, record: any, index: number) => {
                const editable = isEditing(record);
               
                return editable ? (
                    <div>
                        <Popconfirm
                            title={'是否确定更改状态?'}
                            onConfirm={() => {
                                save(record)
                            }}
                            onCancel={() => {

                            }}
                            okText="确认"
                            cancelText="取消"
                        >
                            <span className={operationListStyles.cancelDeployBtn}>保存</span>
                        </Popconfirm>

                        <span
                            onClick={() => cancel()}
                            className={operationListStyles.detailBtn}
                        >取消</span>

                    </div>

                ) : (
                    <span>
                            <a
                                onClick={() => edit(record)}
                                className={operationListStyles.detailBtn}
                            >编辑</a>
                        
                    </span>

                )








            }
        }
    ];

    const handleTabChange = (e: any) => {
        if (e == 2) {
            console.log(2)
            setActiveKey('2')
            setIsBatchAdd(true)
        } else {
            console.log(1)
            setActiveKey('1')
            setIsBatchAdd(false)
        }
    }
    const handleSelectChange = (value:any) => {
        if (value === 'phone') {
          setQueryMode(2)
          setInputPlaceholder('请输入手机号查询');
        } else if (value === 'name') {
          setQueryMode(4)
          setInputPlaceholder('请输入用户名查询');
        }else if( value === 'email') {
          setQueryMode(1)
          setInputPlaceholder('请输入邮箱查询');
        }
    };

    const addUser = async() =>{
        let res = await reqAddUser (userInfo)
        if(res){
            setIsAddSuccess(true)
        }
        console.log('res',res)

        if(res && res.status == 1){
            message.success(res.message)
            setSingleStatus(1)
        }else if(res && res.status == 2){
            message.error(res.message)
            setSingleStatus(2)
            setSingleFailedReason(res.failed_list[0])
        }
    }

    
    const closeModal = () =>{
        setActiveKey('1')
        setIsBatchAdd(false)
        setOpenAddUser(false)
        setUserInfo(null)
        setUploadedFile(null)
        setUploadedFileName(null);
        setBinaryFile(null)
        setIsAddSuccess(false)
        setIsBatchAddSuccess(false)
        setQueryMode(2)
        setUserSearchKey('')
        getUserList()
        setSingleStatus(0)
        setStatus(0)
    }
    useEffect(()=>{
        if(userSearchKey == ''){
            setIsSearchFailed(false)
        }
    },[userSearchKey])

    const deleteFile = () =>{
        console.log('deleteFile')
        setUploadedFile(null)
        setUploadedFileName(null);
        setBinaryFile(null)
    }

    const sumbitFile = async() =>{
        console.log('sumbitFile')
        if(uploadedFile){
            const formData = new FormData();
            formData.append('file',uploadedFile);
            const res = await reqAddUserFile(formData)
            console.log('res',res)
            if(res){
                setIsBatchAddSuccess(true)
                setAddStatusMessage(res.message)
            }
            if(res && res.status == 1){
                // message.success(res.message)
                setStatus(1)
            }else{
                // message.error(res.message)
                setStatus(2)
                setFailedList(res.failed_list)
            }
        }
    }

    const handleEXCELTemplateDownload = async() =>{
        const fileName = '批量导入外网用户模版.xlsx'

        const originUrl = '/common/批量导入外网用户模版.xlsx'

        try {
          const res = await fetch(originUrl)
        // 检查响应状态
        if (!res.ok) {
            throw new Error(`请求失败，状态码: ${res.status}`);
        }
          const blob = await res.blob()
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        } catch (e) {
          message.error('模板下载失败')
        }

    }

    const setFooterBtn = () => {
        if (isBatchAdd && uploadedFile && status == 0) {
            return <Button type='primary' onClick={sumbitFile}>提交</Button>
        } else if (isBatchAdd && isBatchAddSucess && status !== 0) {
            return (
                <>
                    <Button onClick={closeModal}>取消</Button>
                    <Button type='primary' onClick={closeModal}>确定</Button>
                </>
            )
        }
        return null
    }
    return (
        <>
            <div className={commonListStyles.commonList}>
                <div  className={commonListStyles.titleWrapper}>
                    <span className={commonListStyles.title}>用户管理</span>
                    <div>
                        <span  className={'commomSearchInput'}>
                        <Input
                            placeholder="请输入关键词搜索"
                            prefix={<img src={search.src} width='16' />}
                            onPressEnter={e => searchkeyEnter(e)}
                            allowClear
                            style={{ width: '256px', borderRadius: '8px' }}
                          
                        />
                        </span>
                        <Button type='primary' className={teamMemberStyles.addMember + ' primaryButton'} onClick={() => { setOpenAddUser(true) }}>添加用户</Button>
                    </div>
                </div>
                <div className={commonListStyles.tableList + ' normalFont'}>
                    <Table
                        columns={columns}
                        rowKey={(record) => record.id}
                        scroll={{   y: 720 }}
                        dataSource={tableData}
                        pagination={{
                            current: curPage,
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: any) => `共 ${total} 条记录`
                        }}
                        onChange={(pagination) => {
                            setCurPage(pagination.current || 1)
                            setPageSize(pagination.pageSize || 20)
                        }}
                        className='commonTable normalFont'
                    />
                </div>
                <Modal
                    title={"添加用户"}
                    open={openAddUser}
                    onOk={() => { setOpenAddUser(false) }}
                    onCancel={closeModal}
                    footer={setFooterBtn()}
                    style={{ width: 560,height:480}}
                    className={'commonModal'}
                >
                    <div   style={{display:'flex',alignItems:'center',}}>
                        <Tabs
                            className='common_card_tabs'
                            defaultActiveKey="1"
                            items={[
                                {
                                    label: '查询账号',
                                    key: '1',
                                },
                                {
                                    label: '批量添加',
                                    key: '2',
                                },
                            ]}
                            onTabClick={handleTabChange}
                            style={{ width: 260}}
                            activeKey={activeKey}
                        >
                        </Tabs>
                        {isBatchAdd && <div  style={{textAlign:'right',marginLeft: '130px',marginTop: '-11px',color:'#006BFF'}}>
                            <img src='https://p5.ssl.qhimg.com/t11098f6bcd9443248dcdc596cf.png'  width={16} height={16}></img>
                            <span className={userListStyles.downloadBtn}  onClick={handleEXCELTemplateDownload}>下载模板</span>
                        </div>}
                    </div>

                    {!isBatchAdd && (userInfo ?
                        (
                            <div style={{height:'328px'}}>
                                <div className={userListStyles.userInfo}>
                                    <div style={{ display: 'flex' ,alignItems:'center'}}>
                                        <span className={userListStyles.imgInfo}>名</span>
                                        <span style={{ marginLeft: '8px', color: '#1D2531' }}>{userInfo.username}</span>
                                        {!isAddSucess ?
                                        (
                                            <div className={userListStyles.addBtn}>
                                                <img src='https://p3.ssl.qhimg.com/t11098f6bcdeaf4f9fe4de08551.png' width={16} height={16} />
                                                <span onClick={addUser}>添加</span>
                                            </div>
                                        ) :
                                        (
                                            <div style={{marginLeft:'auto'}}>
                                                {singleStatus == 1 && (<div className={userListStyles.addBtn}>
                                                    <img src='https://p1.ssl.qhimg.com/t11098f6bcde4f1ac906c2cd710.png' width={16} height={16} />
                                                    <span style={{color:'#00B42A'}}>添加成功</span>
                                                </div>)}
                                                {singleStatus == 2 && (<div className={userListStyles.addBtn}>
                                                    <img src='https://p4.ssl.qhimg.com/t11098f6bcd24bab7be0f884307.png' width={16} height={16} />
                                                    <span style={{color:'#F53F3F'}}>添加失败</span>
                                                </div>)}
                                            </div>
                                        )}
                                    </div>
                                    <div className={userListStyles.userTitle}>
                                        <span style={{ color: '#9EA7B8' }}>qid</span>
                                        <span style={{ marginLeft: '8px' }}>{userInfo.qid}</span>
                                        <span style={{ color: '#9EA7B8', marginLeft: '60px' }}>昵称</span>
                                        <span style={{ marginLeft: '8px' }}>{userInfo.nickname ?userInfo.nickname :'-' }</span>
                                    </div>
                                </div>
                                {isAddSucess && singleStatus == 2 && <div  className={userListStyles.failedContent}>
                                <span style={{color:'#1B2532',fontWeight:'600'}}>失败内容</span>
                                <div className={userListStyles.failedItem}>
                                    <span>{singleFailedReason?.qid}</span>
                                    <span  style={{ marginLeft: '32px' }}>{singleFailedReason?.reason}</span>
                                </div>
                                </div> }


                            </div>

                        ) :
                        (
                            <div className={userListStyles.queryContainer}>
                                <Input addonBefore={
                                    <Select defaultValue="phone" onChange={handleSelectChange} style={{ width: 90 }}>
                                        <Option value="phone">手机号</Option>
                                        <Option value="email">邮箱</Option>
                                        <Option value="name">用户名</Option>
                                    </Select>
                                }
                                    placeholder={inputPlaceholder}
                                    style={{ width: '472px', borderRadius: '4px' }}
                                    onPressEnter={e => queryModeEnter(e)}
                                    allowClear
                                />
                                <img src='https://p5.ssl.qhimg.com/t11098f6bcd11212359a5fa5141.png' width={121} height={92} style={{ marginTop: '60px' }} />
                                <div className={userListStyles.tipWrapper}>
                                    {!isSearchFailed ? (
                                        <>
                                            <p>可输入手机号、邮箱或用户名添加成员，</p>
                                            <p>      亦可上传excel文件批量添加</p>
                                        </>) : (
                                        <p>不存在360账号</p>
                                    )
                                    }
                                </div> 
                            </div>
                        )
                    )}
                    {
                        isBatchAdd && !isBatchAddSucess &&  <div className={userListStyles.importContainer} style={{ height: 328 }}>
                            <Dragger  {...props}>
                                <div className={userListStyles.importContent}>
                                    <svg className={userListStyles.linkIcon} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path fillRule="evenodd" clipRule="evenodd" d="M12.0002 0.949219L8.10017 5.68606H10.8002V17.6861H13.2002V5.68606H15.9002L12.0002 0.949219ZM2.4 15.4727H0V23.0516H24V15.4727H21.6V20.5253H2.4V15.4727Z" />
                                    </svg>
                                    <div className={userListStyles.importText} style={{ marginTop: 10 }}>
                                        <p>点击或拖拽文件到此区域上传</p>
                                        <p style={{ color: '#9EA7B8' }}>当前仅支持上传单个excel格式文件</p>
                                    </div>
                                </div>
                            </Dragger>
                            {uploadedFileName && <div className={userListStyles.importInfo}>
                                <div className={userListStyles.importImg}>
                                    <img src='https://p0.ssl.qhimg.com/t11098f6bcd047e538a4558d56f.png' width={26}  height={32}/>
                                </div>
                                <div className={userListStyles.importTitle}>
                                    <span>{uploadedFileName}</span>
                                    <span style={{ marginLeft: '32px' }}>{formatSize(uploadedFileSize)}</span>
                                </div>
                                <div className={userListStyles.deleteContainer} >
                                    <img src='https://p2.ssl.qhimg.com/t11098f6bcd93bb343392ed746a.png' width={16} onClick={deleteFile}  style={{marginRight:'4px'}}/>
                                    <span onClick={deleteFile}>删除</span>
                                </div>
                            </div>
                            }
                        </div>
                    }
                    {
                        isBatchAdd &&  isBatchAddSucess && <div className={userListStyles.resultContent}>
                            <div className={userListStyles.importInfo}  style={{marginTop:'0px'}}>
                                <div className={userListStyles.importImg}>
                                    <img src='https://p0.ssl.qhimg.com/t11098f6bcd047e538a4558d56f.png' width={26}  height={32}/>
                                </div>
                                <div className={userListStyles.importTitle}>
                                    <span>{uploadedFileName}</span>
                                    <span style={{ marginLeft: '32px' }}>{formatSize(uploadedFileSize)}</span>
                                </div>
                            </div>
                            {status == 1 && <div className={userListStyles.resultInfo}>
                                <img src='https://p1.ssl.qhimg.com/t11098f6bcde4f1ac906c2cd710.png'  width={20} height={20}/>
                                <span style={{marginLeft:'8px'}}>{addStatusMessage}</span>
                            </div>}
                            {status == 2 && <div className={userListStyles.failedResultInfo}>
                                <img src='https://p3.ssl.qhimg.com/t11098f6bcd1341ca4e0064c54e.png'  width={20} height={20}/>
                                <span style={{marginLeft:'8px'}}>{addStatusMessage}</span>
                            </div>}
                            <div className={userListStyles.failedContent}>
                                <span style={{color:'#1B2532',fontWeight:'600'}}>失败内容</span>
                                <div  className={userListStyles.failedItem}>
                                    <ul>
                                        {failedList?.map((item, index) => (
                                            <li key={index}>
                                                <span>{item.qid}</span>
                                                <span style={{ marginLeft: '32px' }}>{item.reason}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    }
                </Modal>
            </div>
        </>
    )
}
