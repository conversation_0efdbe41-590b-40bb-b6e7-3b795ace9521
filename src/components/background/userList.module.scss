
.importContainer{
    .importContent{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .importImg{
        display: flex;
        width: 28px;
        height: 28px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: #FFF;
    }
    .importImg{
        display: flex;
        width: 28px;
        height: 28px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: #FFF;
      }
    
      .linkIcon{
        cursor: pointer;
        fill: #9EA7B8;
      }
      .importContent:hover{
        .linkIcon {
            fill: #006BFF;
        }
        .importText{
            color: #006BFF;
        }
      }
      :global {
              .ant-upload-drag{
                  margin-top: 12px;
                  display: flex;
                  height: 100px;
                  padding: 0px 16px;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  border-radius: 4px;
                  border: 1px dashed  #D8E0E8;
                  background:  #F7F9FA;
              }
              .ant-upload-drag:hover{
                  border: 1px dashed  #006BFF;
              }
      }
      :global {
        :where(.css-kghr11).ant-upload-wrapper .ant-upload-drag .ant-upload-btn {
            height: auto;
        }
      }

}
.importInfo{
    display:flex;
    flex-direction: row;
    padding: 8px;
    border-radius: 4px;
    background: #F7F9FA;
    height:52px;
    margin-top: 36px;
    align-items: center;

    .importTitle{
        margin-left: 6px;
    }
}
.deleteContainer:hover{
    color:#006BFF
}
.deleteContainer{
    display: flex;
    align-items: center;
    margin-left: auto;
    cursor: pointer;
    color: #006BFF;
}
.resultContent{

}
.resultInfo{
    display:flex;
    flex-direction: row;
    padding: 8px;
    border-radius: 4px;
    height: 38px;
    margin-top: 36px;
    align-items: center;
    border-radius: 4px;
    background:  #E8FFEA;

}
.failedResultInfo{
    display:flex;
    flex-direction: row;
    padding: 8px;
    border-radius: 4px;
    height: 38px;
    margin-top: 16px;
    align-items: center;
    border-radius: 4px;
    background: #FFF7E8;
}
.failedContent{
    border-radius: 4px;
    background:  #F7F9FA;
    display: flex;
    padding: 8px 12px;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 16px;
}
.userInfo{
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    padding: 12px;
    height: 68px;
    border-radius: 4px;
    background:  #F7F9FA;
    width: 100%;
}
.imgInfo{
    border-radius: 12px;
    background: #2E8CFF;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
}
.addBtn{
    margin-left: auto;
    display:flex;
    align-items: center;
    color: #006BFF;
    cursor: pointer;
}
.downloadBtn{
    margin-left: 4px;
    cursor: pointer;
}
.failedItem{
    color: #657083;
    margin-top: 8px;
    max-height: 110px;
    overflow-y: auto;
    width: 100%;

}
.queryContainer{
    height: 328px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}
.tipWrapper{
    color:#9EA7B8;
    margin-Top: 24px;
    text-align: center;
}