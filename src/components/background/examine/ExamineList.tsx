import { useState } from 'react';
import { Input } from 'antd';
import AgentList from './AgentList';
import FlowList from './FlowList';
import PluginList from './PluginList';
import OtherList from './OtherList';
import commonListStyles from '@/styles/CommonList.module.scss';
import search from '@/images/commonList/search.png';

export default function ExamineListNew() {
    const agentTab = 'Agent';
    const flowTab = 'Flow';
    const pluginTab = '插件';
    const otherTab = '提示词';
    const tabs = ['agent', 'flow', 'plugin', 'other'];

    const [searchKey, setSeachKey] = useState('')
    const [activeTab, setActiveTab] = useState(tabs[0]);

    const searchkeyEnter = (event: any) => {
        if (event.key === 'Enter') {
            setSeachKey(event.target.value)
        }
    }

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                        <span className={commonListStyles.title}>市场审核</span>
                        <div className={'titleTabs'}>
                            <div className={'tabItem ' + (activeTab === 'agent' ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(tabs[0]) }}>{agentTab}</div>
                            <div className={'tabItem ' + (activeTab === 'flow' ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(tabs[1]) }}>{flowTab}</div>
                            <div className={'tabItem ' + (activeTab === 'plugin' ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(tabs[2]) }}>{pluginTab}</div>
                            <div className={'tabItem ' + (activeTab === 'other' ? 'activeTitleTab' : '')} onClick={() => { setActiveTab(tabs[3]) }}>{otherTab}</div>
                        </div>
                    </div>
                    <div className={'commonSearchInput'}>
                        <Input
                            placeholder="请输入关键词搜索"
                            prefix={<img src={search.src} width='16' />}
                            allowClear
                            style={{ width: '256px', borderRadius: '4px' }}
                            onPressEnter={event => searchkeyEnter(event)}
                        />
                    </div>
                </div>

                <div>
                    {activeTab === 'agent' && <AgentList 
                        activeTab={activeTab}
                        searchKey={searchKey}  
                    />}
                    {activeTab === 'flow' && <FlowList 
                        activeTab={activeTab}
                        searchKey={searchKey}  
                    />}
                    {activeTab === 'plugin' && <PluginList 
                        activeTab={activeTab}
                        searchKey={searchKey}  
                    />}
                    {activeTab === 'other' && <OtherList
                        activeTab={activeTab}
                        searchKey={searchKey}  
                    />}
                </div>
            </div>
        </>
    )
}