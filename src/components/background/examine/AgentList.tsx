import { useState, useEffect, useRef } from 'react';
import { Table, Tooltip, Popconfirm, message } from 'antd';
import { useRouter } from 'next/router';
import { reqAudit,reqMarkAuditsList } from '@/service/background/examine';
import commonListStyles from '@/styles/CommonList.module.scss';
import backgroundStyles from '@/styles/Background.module.scss';
import operationListStyles from '@/styles/OperationList.module.scss';
import * as constants from "@/constants/appConstants";

export default function AgentList(props: any) {
    const { activeTab, searchKey } = props;

    const router = useRouter();

    let originData = useRef(new Array())
    const [total, setTotal] = useState(0)
    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [tableData, setTableData] = useState(new Array())
    // 是否是智脑平台, 解决智脑平台， 弹框确认点击不生效的问题
    const [isFromCompanyBrainBoard, setIsFromCompanyBrainBoard] = useState(false); // 智脑

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: 100, 
        },
        {
            title: '状态',
            dataIndex: 'status',
            width: 100, 
            key: 'status',
            render: (text: any) => {
                return <>
                    <div><span className={text == 1 ? backgroundStyles.blueStatus :
                        text === 2 ? backgroundStyles.greenStatus : backgroundStyles.redStatus}>
                    </span>{getStatusValue(text)}</div>
                </>
            }
        },
        {
            title: '所属项目',
            dataIndex: 'team_name',
            width: 100, 
            key: 'team_name',
        },
        {
            title: '类型',
            dataIndex: 'market_type',
            key: 'market_type',
            width: 100, 
            render: (text: any) => {
                return <>
                    <span>{getModeValue(text)}</span>
                </>
            }
        },
        {
            title: '发布渠道',
            dataIndex: 'template_channel',
            key: 'template_channel',
            width: 100, 
            render: (text: any) => {
                return <>
                        <Tooltip title={text}>
                            <span>{text ? text : '-'}</span>
                        </Tooltip>
                </>
            }
        },
        {
            title: '提交时间',
            dataIndex: 'created_at',
            width: 100, 
            key: 'created_at',
            render: (text: any) => {
                return <div>{text ? text.replace('T', ' ') : ''}</div>
            }
        },
        {
            title: '结果',
            dataIndex: 'status',
            width: 100,
            key: 'status',
            render: (text: any) => {
                return <>
                    <div className={
                        text == 1 ? '' :
                            text === 2 
                                ? backgroundStyles.greenStatusResult 
                                : backgroundStyles.redStatusResult}
                    >{getStatusResult(text)}</div>
                </>
            }
        },
        {
            title: '操作',
            dataIndex: 'name',
            width: 100, 
            key: 'name',
            render: (_: any, record: any) => {
                const show = record.status == 1
                return show ? (<div>
                    <Popconfirm
                        trigger={isFromCompanyBrainBoard ? 'hover': 'click'}
                        title={"是否要同意 " + record.name + ' 的申请 ?'}
                        description=""
                        onConfirm={() => {
                            audit({
                                id:record.id,
                                status:2
                            })

                        }}
                        okText="确认"
                        cancelText="取消"
                    >
                        <a className={operationListStyles.detailBtn}>通过</a>
                    </Popconfirm>
                    <Popconfirm
                        trigger={isFromCompanyBrainBoard ? 'hover': 'click'}
                        title={"是否要拒绝 " + record.name + ' 的申请 ?'}
                        description=""
                        onConfirm={() => {
                            audit({
                                id: record.id,
                                status: 3
                            })
                        }}
                        okText="确认"
                        cancelText="取消"
                    >
                        <a
                            className={operationListStyles.detailBtn}
                        >拒绝</a>
                    </Popconfirm>

                    <span 
                        onClick={() => look(record)}
                        className={operationListStyles.detailBtn}
                    >查看</span>
                </div>) : <span
                        onClick={() => look(record)}
                    className={operationListStyles.detailBtn}
                >查看</span>
            }
        }
    ]

    const jumpUrl = (url: string) => {
        router.push(url);
    };

    const look = async (record: any) => {
        jumpUrl("/agentDetail?agentId=" + record.template_id + "&teamId=" + record.team_id + "&from=3")
    }

    const getMarketAuditList = async () => {
        let res = await reqMarkAuditsList({
            keyword: searchKey,
            page: curPage,
            page_size: pageSize,
            template_type: 6
        });
        
        if (res != undefined && res.context != undefined && res.context.code == 0) {
            setTableData(res.data)
            originData.current = res.data
            if (res.pagination != undefined) {
                setPageSize(res.pagination.page_size)
                setTotal(res.pagination.total)
            }
        } else {
            if (res != undefined && res.context != undefined) {
                message.error(res.context.message)
            } else {
                message.error("获取列表失败")
            }
        }
    }

    const audit = async (params: {}) => {
        let res = await reqAudit(params);

        if (res != undefined) {
            if (res.context != undefined && res.context.code == 0) {
                message.success("审批成功")
                getMarketAuditList()
            } else {
                message.success(res.errmsg == undefined ? res.errmsg : '审批失败')
            }
        }
    }

    function getStatusValue(type:any){
        if(type == undefined){
            return '-'
        }
        if(type ==1){
            return "审核中"
        }
        if (type == 2) {
            return "通过"
        }
        if (type == 3) {
            return "拒绝"
        }
        if (type == 4) {
            return "撤销审核"
        }
        return '-'
    }

    function getStatusResult(type: any) {
        if (type == undefined) {
            return '-'
        }
        if (type == 1) {
            return "-"
        }
        if (type == 2) {
            return "已通过"
        }
        if (type == 3) {
            return "已拒绝"
        }
        if (type == 4) {
            return "已撤销审核"
        }
        return '-'
    }

    function getModeValue(type: any) {
        return "Agent"
    }

    useEffect(() => {
        if (localStorage.getItem(constants.prompt_isCompanyBrainBoard) === "true" || window.location.href.indexOf("companyBrain") !== -1) {
            setIsFromCompanyBrainBoard(true);
        }
    }, []);

    useEffect(() => {
        if (activeTab === 'agent') {
            getMarketAuditList()
        }
    }, [searchKey, curPage, pageSize])

    return (
        <div className={commonListStyles.tableList + ' normalFont'}>
            <Table
                columns={columns}
                scroll={{   y: 720 }}
                rowKey={(record) => record.id}
                dataSource={tableData}
                pagination={{
                    current: curPage,
                    pageSize: pageSize,
                    total: total,
                    showSizeChanger: true,
                        showQuickJumper: true,
                    showTotal: (total: any) => `共 ${total} 条记录`
                }}
                onChange={(pagination) => {
                    setCurPage(pagination.current || 1)
                    setPageSize(pagination.pageSize||20)
                }}
                className='commonTable normalFont'
            />
        </div>
    )
}

