import { Button, Input, Select, Table, Popconfirm, message } from 'antd';

import commonListStyles from '@/styles/CommonList.module.scss'

import { reqfeedbackList } from '@/service/background/feedback'

import search from '@/images/commonList/search.png'
import deleteIcon from '@/images/delete.svg'

import { useState, useEffect, useRef } from 'react';
import Router, { useRouter } from 'next/router'
import { set } from 'ramda';

export default function FeedbackList(prop: any) {
    const router = useRouter();
    const teamId = router.query.teamId;
    const [openAddProject, setOpenAddProject] = useState(false);
    const [tableData, setTableData] = useState(new Array())
    const [searchKey, setSeachKey] = useState('')
    let originData = useRef(new Array())
    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)
   
   
    useEffect(()=>{
        getFeedbackList()
    },[searchKey,curPage,pageSize])
    function searchkeyEnter(e: any) {
        if (e.key === 'Enter') {
            // 在这里处理用户按下Enter键的逻辑
            setSeachKey(e.target.value)
        }

    }
 

   
    const getFeedbackList = async () => {
        let res = await reqfeedbackList({
            page: curPage,
            page_size: pageSize,
            keyword:searchKey

        });
       
        if (res != undefined && res.context != undefined && res.context.code == 0) {
            setTableData(res.data)
            originData.current = res.data
            if (res.pagination!=undefined){
                
                setPageSize(res.pagination.page_size)
                setTotal(res.pagination.total)
               

            }
            
        } else {
            if (res != undefined && res.context != undefined) {
                message.error(res.context.message)
            } else {
                message.error("获取列表失败")

            }
        }

    }
  

    const columns = [
        {
            title: '帐户名称',
            dataIndex: 'user_name',
            key: 'user_name',
            width: 100, 
        },
        {
            title: '渠道src',
            dataIndex: 'src',
            key: 'src',
            width: 100, 
        },
        {
            title: '提交时间',
            dataIndex: 'created_at',
            key: 'created_at',
            width: 100, 
            render: (text: any) => {
                return <div>{text? text.replace('T', ' '):'-'}</div>
            }
        },
        {
            title: '联系方式',
            dataIndex: 'contact_information',
            key: 'contact_information',
            
            width: 100, // 设置列宽度

        },
        {
            title: '标题',
            dataIndex: 'title',
            key: 'title',
            width: 150, // 设置列宽度

        },
        {
            title: '反馈信息',
            dataIndex: 'message',
            key: 'message',
            width: 300, // 设置列宽度
            // 设置最大宽度
            style: {
                maxWidth: 800, // 设置最大宽度
                maxHeight:20,
            
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
            },

        },

    ]

    return (
        <>
            <div className={commonListStyles.commonList}>
                <div className={commonListStyles.titleWrapper}>
                    <span className={commonListStyles.title}>反馈信息</span>
                   
                    <div className={'commonSearchInput'}>
                        <Input
                          
                            placeholder="请输入关键词搜索"
                            prefix={<img src={search.src} width='16' />}
                            onPressEnter={e => searchkeyEnter(e)}
                            allowClear
                            style={{ width: '256px', borderRadius: '8px' }}
  
                        />
                    </div>

                </div>
                <div className={commonListStyles.tableList + ' normalFont'}>
                    <Table
                        columns={columns}
                        rowKey={(record) => record.id}
                        scroll={{   y: 720 }}
                        dataSource={tableData}
                        pagination={{
                            current: curPage,
                            pageSize:pageSize,
                            total: total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: any) => `共 ${total} 条记录`
                        }}
                        onChange={(value) => {
                            
                            
                             setCurPage(value.current || 1)
                             setPageSize(value.pageSize||20)
                        }}
                        className='commonTable normalFont'
                      

                    />
                </div>
            </div>
        </>
    )
}
