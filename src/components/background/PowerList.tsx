import { Button, Input, Select, Table, Popconfirm, message } from 'antd';

import commonListStyles from '@/styles/CommonList.module.scss'
import teamMemberStyles from '@/styles/TeamMembers.module.css'
import operationListStyles from '@/styles/OperationList.module.scss'

import search from '@/images/commonList/search.png'
import deleteIcon from '@/images/delete.svg'
import {reqAdminList,reqEditAdmin} from '@/service/background/power'

import { useState, useEffect, useRef } from 'react';
import Router, { useRouter } from 'next/router'

export default function PowerList(prop:any) {
    const router = useRouter();
    const teamId = router.query.teamId;
    const [openAddProject, setOpenAddProject] = useState(false);   
    const [tableData, setTableData] = useState(new Array())
    const [searchKey,setSeachKey] =useState('')
    let originData = useRef([])
    const [editingKey, setEditingKey] = useState('');


    const [curPage, setCurPage] = useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(0)
   
    useEffect(()=>{
        getAdminList()

    }, [searchKey, curPage,pageSize])
   function searchkeyEnter(e:any){
        if (e.key === 'Enter') {
            // 在这里处理用户按下Enter键的逻辑
           setSeachKey(e.target.value)
        }

    }
    // useEffect(() => {
    //    // alert("元数据"+JSON.stringify(originData))
        
    //    if(searchKey!=undefined&&searchKey!=''){
    //       let  newData = originData.current.filter(item=>{
    //         //alert(JSON.stringify(item))
             
    //           return item.username.includes(searchKey)
             
    //       })
    //       // alert(JSON.stringify(newData))
    //        setTableData(newData)
    //    }else{
    //      setTableData(originData.current)
    //    }
     
       

    // }, [searchKey])

    const getAdminList= async ()=>{
        let res = await reqAdminList({
            name: searchKey,
            page_size:pageSize,
            page:curPage

        });
        //  res = {
        //     "data": [
        //         {
        //             "id": 163,
        //             "username": "高颖",
        //             "email": "<EMAIL>",
        //             "created_at": "2023-11-13T17:51:20",
        //             "role_type": 0
        //         },
        //         {
        //             "id": 152,
        //             "username": "高飞",
        //             "email": "<EMAIL>",
        //             "created_at": "2023-11-10T15:35:09",
        //             "role_type": 0
        //         },
        //         {
        //             "id": 20,
        //             "username": "高献光",
        //             "email": "<EMAIL>",
        //             "created_at": "2023-07-21T10:23:21",
        //             "role_type": 0
        //         },
        //         {
        //             "id": 10,
        //             "username": "高姗姗",
        //             "email": "<EMAIL>",
        //             "created_at": "2023-07-05T19:51:32",
        //             "role_type": 0
        //         },
        //         {
        //             "id": 2,
        //             "username": "高颖",
        //             "email": "<EMAIL>",
        //             "created_at": "2023-06-30T17:17:49",
        //             "role_type": 1
        //         }
        //     ],
        //     "context": {
        //         "message": "OK",
        //         "code": 0,
        //         "timestamp": 1703670372
        //     },
        //     "pagination": {
        //         "total": 125,
        //         "page": 1,
        //         "page_size": 20
        //     }
        // }
        if(res!=undefined&&res.context!=undefined&&res.context.code==0){
            setTableData(res.data)
            originData.current =res.data
            setTotal(res.pagination.total)
            setPageSize(res.pagination.page_size)
            setTotal(res.pagination.total)
           
        }else{
            if (res != undefined && res.context != undefined){
                message.error(res.context.message)
            }else{
                message.error("获取列表失败")

            }
        }

    }
    const editAdmin =async (params:{}) => {
       let res = await reqEditAdmin(params);
        
        if(res!=undefined){
            if(res.context!=undefined&&res.context.code==0){
                message.success("更改权限成功")

            }else{
                message.success(res.errmsg == undefined ? res.errmsg:'更改权限失败')

            }
        }


        
    }
   
    const edit = (record: any) => {
       
        setEditingKey(record?.id);
    };
    const save = (record: any) => {
        editAdmin(record)
         setEditingKey('');
    };
    const cancel = () => {
        setEditingKey('');
    };
    function isEditing(record: any) {
        if (record == undefined) {
            return false
        }

        return record.id === editingKey;

    }
    function getRoleName(role_type:any){
        if(role_type == undefined){
             return '-'
        }
        if (role_type == 0) {
            return '无系统权限'
        }
        if (role_type == 1) {
            return '系统超级管理员'
        }
        if (role_type == 2) {
            return '系统管理员'
        }
        return '-'
    }


    const columns = [
        {
            title: '帐户名称',
            dataIndex: 'username',
            key: 'username',
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            render: (text: any) => {
                return <div>{text ? text.replace('T', ' ') : ''}</div>
            }
        },
        {
            title: '角色',
            dataIndex: 'role_type',
            key: 'role_type',

            render: (text: any, record: any, index: any) => {
                const editable = isEditing(record);
                return editable ? <Select
                    className={teamMemberStyles.selectRole}
                    options={[
                        {
                            label: '无系统权限',
                            value: 0

                        },
                        {
                            label: '系统超级管理员',
                            value: 1

                        },
                        {
                            label: '系统管理员',
                            value: 2
                        }
                    ]}
                    onChange={(value) => {
                        record.role_type = value
                    }}
                    defaultValue={getRoleName(text)}
                    style={{ width: '160px' }}
                ></Select> : <span>{getRoleName(text)}</span>
            }

        },
        {
            title: '操作',

            dataIndex: 'operation',
            render: (_: any, record: any, index: number) => {
                const editable = isEditing(record);
               
                return editable ? (
                    <div>
                        <Popconfirm
                            title={'是否确定更改权限?'}
                           
                            onConfirm={() => {
                                save(record)
                            }}
                            onCancel={() => {

                            }}
                            okText="确认"
                            cancelText="取消"
                        >
                            <span className={operationListStyles.cancelDeployBtn}>保存</span>
                        </Popconfirm>

                        <span
                            onClick={() => cancel()}
                            className={operationListStyles.detailBtn}
                        >取消</span>

                    </div>

                ) : (
                    <span>
                            <a
                                onClick={() => edit(record)}
                                className={operationListStyles.detailBtn}
                            >编辑</a>
                        
                    </span>

                )








            }
         }
       

    ]


    return (
    <>
      <div className={commonListStyles.commonList}>
        <div className={commonListStyles.titleWrapper}>
          <span className={commonListStyles.title}>权限管理</span>  
                    <div className={'commonSearchInput'}>
                <Input 
                    placeholder="请输入关键词搜索" 
                            onPressEnter={e=>searchkeyEnter(e)}
                    prefix={<img src={search.src} width='16' />}
                    allowClear
                    style={{ width: '256px', borderRadius: '8px' }}
                />
            </div>
            
        </div>
        <div className={commonListStyles.tableList + ' normalFont'}>
            <Table 
                    columns={columns} 
                        rowKey={(record) => record.id}
                    dataSource={tableData} 
                    scroll={{   y: 720 }}
                    pagination={{
                       
                        current: curPage,
                        pageSize: pageSize,
                        total: total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: any) => `共 ${total} 条记录`
                    }}
                onChange={(pagination) => {
                    setCurPage(pagination.current || 1)
                    setPageSize(pagination.pageSize ||20)
                   

                }}
                className='commonTable normalFont'
            />
        </div>
      </div>
    </>
    )
}
