import { defalutScript } from '@/config/flowConfig'
import { BarcodeOutlined } from '@ant-design/icons';

import { message } from 'antd';
import * as constants from "@/constants/appConstants";
import { default_model_params } from '@/utils/defaultLLMConfig';
import { iconList, colorList, colorRowList } from  '../flowEditor3.0/flowConfig'
import getDefaultPromptConfig from '@/utils/defaultPromptConfig';
import { deepClone } from '@/utils/utils';
import { checkFlow } from '@/service/flow3.0';
import { slateToHtml } from '@/utils/slateToHtml';
import TemplatePlaceholder, { DefaultTemplate, ConditionTemplate, IterationTemplate } from '../flow3.0/block/commons/TemplatePlaceholder'

export const blockTemplate = {
  nami_agent: () => DefaultTemplate(),
  nami_agent_condition: () => ConditionTemplate(),
  nami_agent_loop: () => IterationTemplate()
}

export const blockNameObj = {
    nami_agent: '专家智能体',
    nami_agent_condition: '条件智能体',
    nami_agent_loop: '迭代智能体'
}
export const blockDescObj = {
    nami_agent: '通过大模型自主使用MCP工具、知识库，完成推理任务',
    nami_agent_condition: '通过设定条件连接判断下游分支，完成流程分支',
    nami_agent_loop: '通过设定迭代条件和逻辑，重复执行一系列任务'
}
export const blockMetaObj = {
    nami_agent: {
        color: '#4DD0E1',
        colorIndex: 5,
        iconIndex: 0
    },
    nami_agent_condition: {
        color: '#64B5F6',
        colorIndex: 4,
        iconIndex: 1
    },
    nami_agent_loop: {
        color: '#9C89B8',
        colorIndex: 1,
        iconIndex: 2
    }
}

let colorRowIndex = 0 // 0-5 把颜色分为6行，按行依次取值，避免前后重复

export const getRandomMeta  = (type: any) => {
    const randomIconNumber = Math.floor(Math.random() * 28);
    const randomRow = Math.floor(Math.random() * 7);
    const randomColor = colorRowList[colorRowIndex % 6][randomRow]
    colorRowIndex++;
    return  {
        color: randomColor,
        colorIndex: colorList.indexOf(randomColor),
        iconIndex: randomIconNumber
    }
}

export const addNewBlock = (type: any, name = '') => {
    let newBlock;
    name = name || (blockNameObj[type] + '_0') || '未命名模块';
    // const desc = blockDescObj[type] || '';
    const desc = '';
    const meta = getRandomMeta(type);

    console.log("blockTemplate[type]-------------", blockTemplate[type]())
    if (type === 'nami_agent') {
        newBlock = {
            "id": Math.ceil(Math.random() * 1000000) + '',
            "name": name,
            "desc": desc,
            "meta": meta,
            "type": type,  // 节点类型
            "loop_id": "",
            "agent_id": "",
            "inputs": {},  
            "prompts": {
							"user": "",
							"system": "",
              "user_params": blockTemplate[type]()
						},
            "mcp_list": [],
            "knowledge_list": [],
            "result": {},
            "status": 1,
            "block_version": 0,
            "model_params": default_model_params
        }
    }else if (type === 'nami_agent_condition') { 
        newBlock = {
            "id": Math.ceil(Math.random() * 1000000) + '',
            "name": name,
            "desc": desc,
            "meta": meta,
            "type": type,  // 节点类型
            "loop_id": "",
            "agent_id": "",
            "inputs": {},  
            "branches": [{
                branch_id: Math.ceil(Math.random() * 1000000) + "",
                content: "",
            },{
                branch_id: Math.ceil(Math.random() * 1000000) + "",
                content: "",
            }],
            "prompts": {
							"user": "",
							"system": "",
              "user_params": blockTemplate[type]()
						},
            "mcp_list": [],
            "knowledge_list": [],
            "result": {},
            "status": 1,
            "block_version": 0,
            "model_params": default_model_params
        }
    }else if(type == 'nami_agent_loop'){
        newBlock = {
            "id": Math.ceil(Math.random() * 1000000) + '',
            "name": name,
            "desc": desc,
            "meta": meta,
            "type": type,  // 节点类型
            "loop_id": "",
            "agent_id": "",
            "inputs": {},
            "prompts": {
							"user": "",
							"system": "",
              "user_params": blockTemplate[type]()
						},
            "mcp_list": [],
            "knowledge_list": [],
            "result": {},
            "status": 1,
            "block_version": 0,
            "model_params": default_model_params
        }
    }
    return newBlock;
}

export const getType = (str: any) => {
    if (typeof str === 'object' && str) {
        if (str instanceof Array) {
            // 数组
            return {
                type: 'array',
                obj: str
            };
        } else {
            // 对象
            return {
                type: 'object',
                obj: str
            };
        }
    } else {
        try {
            const obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                if (obj instanceof Array) {
                    // 数组
                    return 'array';
                } else {
                    // json
                    return 'object';
                }
            } else {
                // 字符串
                return 'string'
            }
        } catch (e) {
            return 'string'
        }
    }
}

export const getCardType = (str: any) => {
    if (typeof str === 'object' && str) {
        if (str instanceof Array) {
            // 数组

            return {
                type: 'arr[' + typeof (str[0]) + ']',
                obj: str
            };
        } else {
            // 对象
            return {
                type: 'object',
                obj: str
            };
        }
    } else {
        try {
            const obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                if (obj instanceof Array) {
                    // 数组
                    return {
                        type: 'arr[' + typeof (str[0]) + ']',
                        obj: obj
                    };
                } else {
                    // 对象
                    return {
                        type: 'object',
                        obj: obj
                    };
                }
            } else {
                // 字符串 数字 布尔
                return {
                    type: typeof (str),
                    obj: str
                };
            }
        } catch (e) {
            return {
                type: 'string',
                obj: str
            };
        }
    }
}

// 对结果进行格式化 后期服务端做了格式化 这个方法实际没啥用
export const getResult = (result: any) => {
    if (result?.output) {
        try {
            const obj = JSON.parse(result.output);
            if (typeof obj === 'object' && obj) {
                if (obj instanceof Array) {
                    // 数组
                    return {
                        // JSON.stringify(obj)
                        output: obj
                    };
                } else {
                    // json
                    return {
                        // JSON.stringify(obj)
                        output: obj
                    };
                }
            } else {
                // 字符串
                return result
            }
        } catch (e) {
            // 字符串
            return result
        }
    } else {
        return result
    }
}
export const initJsonEditor = async (obj: object, id: any, isReadOnly = true, cb = (c: any) => { }, blurCb = () => { }) => {
    const { JSONEditor } = await import('@/utils/vanilla-jsoneditor')
    let content = {
        text: undefined,
        json: obj
    }
    const jsoneditor = document.getElementById('jsoneditor-' + id);
    if (jsoneditor) {
        jsoneditor.innerHTML = '';
        const editor = new (JSONEditor as any)({
            target: document.getElementById('jsoneditor-' + id),
            props: {
                content,
                mode: 'text',
                readOnly: isReadOnly,
                onChange: (updatedContent: any) => {
                    content = updatedContent
                    cb && cb(updatedContent.text)
                },
                onBlur: () => {
                    blurCb && blurCb()
                }
            }
        })
    }
}
export const copyParamValue = (str: any, isNeedTip = true) => {
    const isLlmopsBoard = localStorage.getItem(constants.prompt_isLlmopsBoard);
    if (navigator.clipboard && !isLlmopsBoard) {
        navigator.clipboard.writeText(str);
        isNeedTip && message.success('复制成功');
    } else {
        // 创建text area
        let textArea = document.createElement("textarea");
        textArea.value = str;
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = "absolute";
        textArea.style.opacity = '0';
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        return new Promise((resolve, reject) => {
            // 执行复制命令并移除文本框
            document.execCommand('copy') ? resolve(isNeedTip && message.success('复制成功')) : reject(isNeedTip && message.success('复制失败'));
            textArea.remove();
        });
    }
}

const getLoopBodyBlockIds = (loop_id: any, blocks: any, loopConfigs: any) => {
    // 获取循环体内节点渲染顺序
    const resBlockIds = new Array()
    const renderBlockId = (block_keys: any) => {
        block_keys?.forEach((id: any) => {
            if (blocks[id].type === 'condition') {
                resBlockIds.push(id)
                blocks[id].branch_keys.forEach((b: any) => {
                    renderBlockId(loopConfigs[loop_id].branches[b].block_keys)
                })
            } else {
                resBlockIds.push(id)
            }
        })
    }
    renderBlockId(loopConfigs[loop_id]?.block_keys)
    return resBlockIds;
}

export const isInLoop = (blockid: any, blocks: any): boolean => {
    if (blocks[blockid]?.type == 'nami_agent_loop' || blocks[blockid]?.parentId || blocks?.[blockid]?.loop_id) return true
    return false
}
// 循环体内所有节点 除了条件节点
export const getLoopGroupBlocks = (loopConfigs, loop_id, blocks) => {
    return loopConfigs[loop_id].all_block_keys.filter(b => {
        return blocks[b].type != 'nami_agent_condition'
    })
}

export const getAllPrevBlocks = (activeBlockId: any, blocks: any, edges: any, loopConfigs: any = '') => {
    // 循环体内部节点
    const loopId = blocks[activeBlockId]?.loop_id;
    
    // 到循环节点就break
    let isBreak = false;
    let index = 0
    // 第一层节点
    let firstPrevBlocks = [];
    const findPrevBlock = (id: any, edges, isLoop) => {
        index++;
        // 往前找一步 所有的节点
        const prevBlocks = [];
        edges.forEach((edge: any) => {
            if(isLoop) {
                if(isInLoop(edge.sourceNodeID, blocks)) {
                    if (edge.targetNodeID == id && !isBreak) {
                        if(!prevAllBlocks.includes(edge.sourceNodeID)){
                            // 去重
                            prevBlocks.push(edge.sourceNodeID)
                            // 到循环节点则结束 避免成环
                            if(blocks[edge.sourceNodeID].type == 'loop'){
                                isBreak = true
                            }
                        }
                    }
                }
            }else {
                if (edge.targetNodeID == id) {
                    if(!prevAllBlocks.includes(edge.sourceNodeID)){
                        // 去重
                        prevBlocks.push(edge.sourceNodeID)
                    }
                }
            }
            
        })
        // index == 1 || 
        if(prevBlocks.length > 1) {
            firstPrevBlocks = prevBlocks
        }
        prevAllBlocks = [...prevAllBlocks, ...prevBlocks]
        // 多维数组
        prevAllBlocksArr.push(prevBlocks)
        if (prevBlocks.length) {
            prevBlocks.forEach(id => {
                if(firstPrevBlocks.includes(id)) {
                    // 第一层重置isbreak
                    isBreak = false
                }
                findPrevBlock(id, edges, isLoop)
            })
        }
    }
    // 无序
    let prevAllBlocks = new Array();
    // 有序 todo 产品如果需要排序
    let prevAllBlocksArr = new Array();
    if (loopId) {
        findPrevBlock(activeBlockId, edges.concat(loopConfigs[loopId]?.edges), true);
        return prevAllBlocks.filter((b) => {
            if(blocks[b].type == 'loop' && b != loopId) {
                // 只返回本循环节点
                return false
            }
            return blocks[b].type != 'loop_break'
        }).reverse().map(id => {
            return blocks[id]
        })
    } else {
        if(blocks[activeBlockId]?.type == 'loop'){
            // 循环节点不能选择循环节内节点
            edges = edges.filter(edge => {
                return !blocks[edge.sourceNodeID].loop_id
            })
        }
        findPrevBlock(activeBlockId, edges, false);
        return prevAllBlocks.reverse().map(id => {
            return blocks[id]
        })
    }
}
export const getAllNextBlocks = (activeBlockId: any, blocks: any, edges: any, loopConfigs: any = '',) => {
    // 循环体内部节点
    const loopId = blocks[activeBlockId]?.loop_id;

    // 到循环节点就break
    let isBreak = false;
    let index = 0
    // 第一层节点
    let firstNextBlocks = [];
    const findNextBlock = (id: any, edges, isLoop) => {
        index++;
        // 往后找一步 所有的节点
        const nextBlocks = [];
        edges.forEach((edge: any) => {
            if(isLoop) {
                if(isInLoop(edge.sourceNodeID, blocks)) {
                    if (edge.sourceNodeID == id && !isBreak) {
                        if(!prevNextBlocks.includes(edge.targetNodeID)){
                            // 去重
                            nextBlocks.push(edge.targetNodeID)
                            // 到循环节点则结束 避免成环
                            if(blocks[edge.targetNodeID]?.type == 'loop'){
                                isBreak = true
                            }
                        }
                    }
                }
            }else {
                if (edge.sourceNodeID == id) {
                    if(!prevNextBlocks.includes(edge.targetNodeID)){
                        // 去重
                        nextBlocks.push(edge.targetNodeID)
                    }
                }
            }
        })
        // index == 1 ||
        if(nextBlocks.length > 1) {
            firstNextBlocks = nextBlocks
        }
        prevNextBlocks = [...prevNextBlocks, ...nextBlocks]
        if (nextBlocks.length) {
            nextBlocks.forEach(id => {
                if(firstNextBlocks.includes(id)) {
                    // 第一层重置isbreak
                    isBreak = false
                }
                findNextBlock(id, edges, isLoop)
            })
        }
    }
    let prevNextBlocks = new Array();
    if (loopId) {
        findNextBlock(activeBlockId, edges.concat(loopConfigs[loopId]?.edges || []), true);
        return prevNextBlocks
    } else {
        findNextBlock(activeBlockId, edges, false)
        return prevNextBlocks;
    }
}

// 前面所有的节点 循环体内的节点可以引用外部节点
export const getAllPrevBlocksWithLoopOut = (activeBlockId: any, blocks: any, edges: any, loopConfigs: any = '') => {
	const prevBlocks = getAllPrevBlocks(activeBlockId, blocks, edges, loopConfigs)
	// console.log(`getAllPrevBlocksWithLoopOut：`, prevBlocks)
	const inLoop = isInLoop(activeBlockId, blocks)
	if(inLoop && blocks[activeBlockId].type != 'nami_agent_loop') {
		const block = blocks[blocks[activeBlockId].loop_id]
		const blocksBeforeLoop = getAllPrevBlocks(block.id, blocks, edges, loopConfigs)
		if(blocksBeforeLoop?.length) {
			prevBlocks.unshift(...blocksBeforeLoop)
		}
	}
	return prevBlocks
}


/**
 * 根据toolId获取mcp信息
 * @param {*} mcpToolList
 * @param {*} toolId
 * @return {*} { ...tool, parent: { ...mcpInfo }} 包括工具和mcp的对象信息
 */
const findMcpByToolId = (mcpToolList, toolId) => {
	let result = null
	if(!mcpToolList || !mcpToolList.length || !toolId) return null
	for(let i=0; i<mcpToolList.length; i++) {
		for(let j=0; j<mcpToolList[i].tools?.length; j++) {
			if(mcpToolList[i].tools[j].name === toolId) {
				result = {
					...mcpToolList[i].tools[j],
					parent: mcpToolList[i],
				}
				break
			}
		}
	}
	return result
}

/**
 * 检查更新prompt提示词引参
 * @param {object} blocks 所有节点信息
 * @param {string} activeBlockId 当前节点id
 * @param {object} prevBlocks 前面节点信息
 * @param {Object} loopConfigs 循环节点信息
 * @param {array} mcpToolList mcp工具列表
 * @return {object} 新的prompt
 */
export const checkAndUpdateMention = (blocks, activeBlockId, prevBlocks, loopConfigs, mcpToolList) => {
	const prompt = blocks[activeBlockId].prompts.user_params
	if(!prompt || !prompt.length) {
		return prompt
	}
	const traverseNode = (prompt: any) => {
		return prompt.map((item: any) => {
						if(item.type == 'mention') {
							if(item.payload.type === 'agentOutput' || item.payload.type === 'iteratorItem') {
								if(blocks[item.payload.id]){
									item.payload.name = item.payload.type === 'iteratorItem' ?  `${blocks[item.payload.id].name}_迭代项` : blocks[item.payload.id].name
								}
								if(item.payload.type === 'agentOutput') {
									let allPrevBlocks = [...prevBlocks]
									prevBlocks.forEach(b => {
										if(b.type === 'nami_agent_loop') {
											let blocksInLoop = getLoopGroupBlocks(loopConfigs, b.id, blocks)?.map(blockKey => {
												return {
													name: blocks[blockKey].name,
													id: blocks[blockKey].id,
												}
											})
											allPrevBlocks.push(...blocksInLoop)
										}
									})
									console.log('allPrevBlocks', allPrevBlocks)
									item.payload.hasError = allPrevBlocks.findIndex((b: any) => b.id === item.payload.id) === -1
								} else {
									//迭代项
									const inLoop = isInLoop(activeBlockId, blocks)
									console.log(`inLoop: ${inLoop}`)
									item.payload.hasError = !(inLoop && item.payload.id === blocks[activeBlockId].loop_id)
								}
							} else if (item.payload.type === 'mcp') {
								//更新mcp提示词引参
								if(item.payload.id) {
									let toolInfo = findMcpByToolId(mcpToolList, item.payload.id)
									console.log(`toolInfo`, toolInfo)
									if(toolInfo) {
										item.payload.name = `${toolInfo.parent.display_name}-${toolInfo.name_ui}`
										item.payload.hasError = false
									} else {
										item.payload.hasError = true
									}
								} else {
									item.payload.hasError = blocks[activeBlockId].mcp_list.findIndex((m: any) => m.server_name === item.payload.server_name) === -1
								}
							}
						}

						if(item.children && item.children.length) {
							traverseNode(item.children)
						}
						return item
		})
	}
	return traverseNode(deepClone(prompt))
}

/**
 * 连线的时候判断结束节点是否存在开始节点的引用,不存在自动插入
 * @param {object} blocks 所有节点信息
 * @param {string} from 开始节点id
 * @param {string} to 结束节点id
 * @param {function} setBlocks 设置节点信息
 * @return
 */
export const checkAutoInsertMention = (blocks, from, to, setBlocks) => {
	console.log(`checkAutoInsertMention:`, blocks, from, to)
	if(!blocks || !from || !to) return false
	if(blocks[from].type === 'nami_agent_loop' && blocks[to].loop_id === from) {
		// 循环节点内部的节点不能插入循环节点引用
		return false;
	}
	// if(blocks[to]?.prompts?.user && !blocks[to]?.prompts?.user_params?.length) {
	// 	// 从智能体广场创建的节点
	// 	return false
	// }
	let result = false
	const prompt = blocks[to]?.prompts?.user_params

	const traverseNode = (prompt: any) => {
		for(let i=0; i<prompt.length; i++) {
			const item = prompt[i]
			// if(item.text || item.type === 'mention') {
			// 	result = true
			// 	return
			// }
			if(item.type === 'mention' && item.payload.type === 'agentOutput') {
				if(item.payload.id == from) {
					result = true
					break
				}
			}


			if(item.children && item.children.length) {
				traverseNode(item.children)
			}
		}
	}
	prompt && prompt.length && traverseNode(prompt)
	if(!result) {
		!blocks[to].prompts.user_params && (blocks[to].prompts.user_params = [])
		const mention = {
			type: 'mention',
			payload: {
				type: 'agentOutput',
				id: from,
				name: blocks[from].name,
				hasError: false
			},
			children: [{ text: '' }]
		}

   const userParams =  TemplatePlaceholder.updateInput(blocks[to].prompts.user_params, mention)
		// if(blocks[to].prompts.user_params[0]?.type === 'paragraph') {
		// 	blocks[to].prompts.user_params[0].children.unshift(...[
		// 		{ text: '' },
		// 		mention,
		// 		{ text: '' }
		// 	])
		// } else {
		// 	blocks[to].prompts.user_params.unshift({
		// 		type: 'paragraph',
		// 		children: [
		// 			{
		// 				text: ''
		// 			},
		// 			mention,
		// 			{
		// 				text: ''
		// 			}
		// 		]
		// 	})
		// }
		// blocks[to].prompts.user = slateToHtml(userParams)
		setBlocks(_ => ({ ...blocks, [to]:{
      ...blocks[to],
      prompts:{
        user: slateToHtml(userParams),
        user_params: userParams
      }
    }}))
	}
}

/**
 * 检查是否开始节点，自动插入用户输入引参
 * @param {object} blocks 所有节点信息
 * @param {string} flowId flow流的id
 * @param {function} setBlocks 设置节点信息
 * @return 
 */
export const checkAutoInsertUserInput = async (blocks, flowId, setBlocks) => {
	const res = await checkFlow({ template_id: flowId, is_validate: true })
	if(!res || !res.flow_start_block){
		return
	}
	let blockId = res.flow_start_block
	let prompt = blocks[blockId].prompts.user_params
	
	let result = false
	
	if(prompt && prompt.length){
		const traverseNode = (prompt: any) => {
			for(let i=0; i<prompt.length; i++) {
				const item = prompt[i]
				if(item.type === 'mention' && item.payload.type === 'userInput') {
					result = true
					return
				}
				if(item.children && item.children.length) {
					traverseNode(item.children)
				}
			}
		}
		traverseNode(prompt)
	}

	if(!result) {
		!blocks[blockId].prompts.user_params && (blocks[blockId].prompts.user_params = [])
		const mention = {
			type: 'mention',
			payload: {
				type: 'userInput',
				name: '用户输入',
				hasError: false
			},
			children: [{ text: '' }]
		}
		const userParams =  TemplatePlaceholder.updateInput(blocks[blockId].prompts.user_params, mention)
		setBlocks(_ => ({ ...blocks, 
			[blockId]:{
				...blocks[blockId],
				prompts:{
					user: slateToHtml(userParams),
					user_params: userParams
				}
    	}
	}))
	}
}