import React, { useState } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import styles from './CollapsibleCategory.module.scss';

interface CollapsibleCategoryProps {
  title: string;
  defaultExpanded?: boolean;
  children: React.ReactNode;
  titleClassName?: string;
}

const CollapsibleCategory: React.FC<CollapsibleCategoryProps> = ({
  title,
  defaultExpanded = true,
  children,
  titleClassName,
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={styles.collapsibleCategory}>
      <div className={classnames(styles.categoryHeader, titleClassName)} onClick={toggleExpanded}>
        <h3 className={styles.categoryTitle}>{title}</h3>
        <div className={styles.expandIcon}>{isExpanded ? <DownOutlined /> : <UpOutlined />}</div>
      </div>

      {isExpanded && <div className={styles.categoryContent}>{children}</div>}
    </div>
  );
};

export default CollapsibleCategory;
