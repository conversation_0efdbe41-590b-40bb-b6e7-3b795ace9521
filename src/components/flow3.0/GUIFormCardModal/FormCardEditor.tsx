import React, { useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import { Divider, Tooltip } from 'antd';

import styles from './FormCardEditor.module.scss';
import DivideLayout from './DivideLayout';
import HoverOperationPannel from './HoverOperationPannel';
import GUIComponent from './GUIComponent';
import { getComponentDescription, isComponentHasUserOverrides, type SchemaItem } from './index';
import EmptyCanvasIcon from './EmptyCanvasIcon.png';

interface FormCardEditorProps {
  schema: SchemaItem[];
  editingComponentId?: string;
  onSpecComponentPropsChange?: (id: string, props: Record<string, any>) => void;
  onDelete?: (id: string) => void;
  onOrderChange?: (id: string, direction: 'up' | 'down') => void;
  onEditingChange?: (id: string) => void;
}

function FormCardEditor({
  schema,
  editingComponentId = '',
  onSpecComponentPropsChange = () => {},
  onDelete = () => {},
  onOrderChange = () => {},
  onEditingChange = () => {},
}: FormCardEditorProps) {
  const formCardEditorRef = useRef<HTMLDivElement>(null);

  // 使用全局点击监听
  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      // 检查点击是否在组件区域内
      const target = e.target as HTMLElement;
      const componentElement = target.closest(`.${styles.component}`);

      if (!componentElement) {
        // 点击在组件外部，退出编辑状态
        onEditingChange('');
      }
    };

    if (editingComponentId) {
      document.addEventListener('click', handleDocumentClick);
    }

    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [editingComponentId, onEditingChange]);

  const getOperations = (item: SchemaItem) => [
    {
      id: 'edit',
      label: '编辑',
      type: 'edit' as const,
      onClick: () => {
        console.log('编辑组件:', item.id);
        onEditingChange(item.id);
      },
      disabled: false,
    },
    {
      id: 'delete',
      label: '删除',
      type: 'delete' as const,
      onClick: () => {
        console.log('删除组件:', item.id);
        onDelete(item.id);
      },
      disabled: false,
    },
    {
      id: 'moveUp',
      label: '上移',
      type: 'moveUp' as const,
      onClick: () => {
        console.log('上移组件:', item.id);
        onOrderChange(item.id, 'up');
      },
      disabled: item.id === schema[0].id,
    },
    {
      id: 'moveDown',
      label: '下移',
      type: 'moveDown' as const,
      onClick: () => {
        console.log('下移组件:', item.id);
        onOrderChange(item.id, 'down');
      },
      disabled: item.id === schema[schema.length - 1].id,
    },
  ];

  // Tooltip 内容
  const getTooltipContent = (item: SchemaItem) => {
    const description = getComponentDescription(item.type, item.props);
    return (
      <div className={styles.tooltipContent}>
        <div className={styles.tooltipTitle}>组件提示词</div>
        <div className={styles.tooltipDescription}>{description}</div>
      </div>
    );
  };

  // 提取公共的组件渲染逻辑
  const renderComponent = (item: SchemaItem) => {
    const isEditing = editingComponentId === item.id;
    const getPopupContainer = () => formCardEditorRef.current || document.body;

    return (
      <Tooltip
        // title={getTooltipContent(item)}
        placement="right"
        open={isEditing && !isComponentHasUserOverrides(item.type, item.props)}
        color="#fff"
        overlay={getTooltipContent(item)}
      >
        <div
          className={classnames(styles.component, {
            [styles.editing]: isEditing,
          })}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <GUIComponent
            item={item}
            isEditing={isEditing}
            onPropsChange={onSpecComponentPropsChange}
            className={styles.componentPreview}
            getPopupContainer={getPopupContainer}
          />
        </div>
      </Tooltip>
    );
  };

  return (
    <div className={styles.formCardEditor} ref={formCardEditorRef}>
      <div className={styles.canvas}>
        {schema.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>
              <img src={EmptyCanvasIcon.src} alt="empty" />
            </div>
            <div className={styles.emptyText}>请从左侧点击组件添加到画布</div>
          </div>
        ) : (
          <DivideLayout>
            {schema.map((item) => {
              const isEditing = editingComponentId === item.id;

              return (
                <div key={item.id}>
                  {/* 编辑状态下不显示操作面板 */}
                  {!isEditing ? (
                    <HoverOperationPannel
                      operations={getOperations(item)}
                      position="top-right"
                      displayMode="icon-and-text"
                    >
                      {renderComponent(item)}
                    </HoverOperationPannel>
                  ) : (
                    renderComponent(item)
                  )}
                </div>
              );
            })}
          </DivideLayout>
        )}
      </div>
    </div>
  );
}

export default FormCardEditor;
