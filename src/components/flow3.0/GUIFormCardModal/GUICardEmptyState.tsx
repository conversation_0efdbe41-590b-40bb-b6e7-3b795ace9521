import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import EmptyListPic from './EmptyListPic.svg';
import styles from './GUICardEmptyState.module.scss';

interface GUICardEmptyStateProps {
  onAddBtnClick: () => void;
}

const GUICardEmptyState: React.FC<GUICardEmptyStateProps> = ({ onAddBtnClick }) => {
  return (
    <div className={styles.emptyState}>
      <img src={EmptyListPic.src} alt="empty" className={styles.emptyListPic} />
      <div className={styles.content}>
        <h3 className={styles.title}>为智能体定制交互界面</h3>
        <p className={styles.description}>针对智能体返回结果的交互表单和展示页面进行自定义</p>
        <button className={styles.createButton} onClick={onAddBtnClick}>
          <PlusOutlined />
          立即创建
        </button>
      </div>
    </div>
  );
};

export default GUICardEmptyState;
