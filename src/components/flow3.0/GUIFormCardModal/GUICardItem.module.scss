.GUICardItem {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 16px;
  // background: #ffffff;
  // border-radius: 16px;
  // overflow: hidden;
  // margin-bottom: 16px;
  // border: 1px solid #e5e7eb;
  // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  height: 24px;
}

.title {
  flex: 1;
  margin: 0;
  color: #1d2531;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}

.operation {
  display: flex;
  align-items: center;
  flex-shrink: 0;

  :global(.ant-btn-link) {
    color: #657083;
    gap: 4px;
    padding: 4px;

    &:hover {
      color: #006bff !important;
    }
  }
  .editButton {
    color: #006bff;
    &:hover {
      color: #2e8cff !important;
    }
  }
}

.content {
  border-radius: 8px;
  border: 1px solid #e1e7ed;
  background: #edf1f5;
  padding: 12px;
}

.contentInner {
  background: #ffffff;
  overflow: hidden;
  border-radius: 8px;
  max-height: 250px;
}

.emptyComponents {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #9ca3af;
  font-size: 14px;
  font-style: italic;
}

/* 模版预览样式 */
.templatePreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 0 4px;
  max-height: 250px;
  justify-content: flex-start;
}

.templateImage {
  width: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.templateLabel {
  font-size: 12px;
  color: #657083;
  text-align: center;
  margin-top: 4px;
}
