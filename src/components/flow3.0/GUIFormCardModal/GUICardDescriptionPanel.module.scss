.colorful-border {
  background-image: linear-gradient(#fff, #fff),
    /* 内容背景 */ conic-gradient(from 352deg, #483dff, #35f2df, #7eec92, #ffb04e, #bb51ff, #483dff);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}
.GUICardDescriptionPannelContainer {
  padding: 12px 16px;
  border-radius: 12px 12px 20px 12px;
  background: #fff;
  width: 100%;
  display: flex;
  align-items: flex-start;
  gap: 2px;

  .GUICardDescriptionPannelTitle {
    position: relative;
    color: #1d2531;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 0;
    flex-shrink: 0;
    width: 60px;
    padding-top: 5px; // 与内容区域的padding保持对齐
    margin-left: 10px;
    white-space: nowrap;
    &::before {
      color: #f53f3f;
      content: '*';
      font-size: 14px;
      font-weight: 400;
      left: -10px;
      line-height: 32px;
      position: absolute;
      top: 0;
    }
  }

  .GUICardDescriptionPannelContent {
    border: 1px solid transparent;
    border-radius: 8px;
    background: #fff;
    font-size: 0;
    flex: 1;
    transition: all 0.3s ease;
    // @extend .colorful-border;
    &:hover,
    &:focus-within {
      border: 1px solid #006bff;
    }

    .textInputContainer {
      position: relative;
      min-height: var(--gui-desc-pannel-min-height, 48px); // 使用CSS变量，回退值为2行高度

      .highlightLayer {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 5px 8px;
        margin: 0;
        border: none;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
          'Noto Sans', sans-serif;
        font-size: 14px;
        line-height: 1.5715;
        color: transparent;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow: auto;
        pointer-events: none;
        z-index: 1;
        box-sizing: border-box;
        letter-spacing: normal;
        text-align: left;

        &.showPlaceholder {
          color: #9EA7B8;
          font-weight: 400;
        }

        .normalText {
          color: #1d2531;
        }

        .variable {
          color: #006bff;
        }
      }

      .textInput {
        position: relative;
        width: 100%;
        min-height: var(--gui-desc-pannel-min-height, 48px); // 使用CSS变量，回退值为2行高度
        padding: 5px 8px;
        margin: 0;
        border: none;
        outline: none;
        background: transparent;
        resize: vertical;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
          'Noto Sans', sans-serif;
        font-size: 14px;
        line-height: 1.5715;
        color: #1d2531;
        z-index: 2;
        box-sizing: border-box;
        letter-spacing: normal;
        text-align: left;
        word-wrap: break-word;
        white-space: pre-wrap;

        &::placeholder {
          color: transparent;
        }

        &:focus {
          box-shadow: none;
          border: none;
        }

        // 让输入的文字透明，这样可以看到下层的高亮效果
        &:not(:placeholder-shown) {
          color: transparent;
          caret-color: #1d2531;
        }

        &:focus:not(:placeholder-shown) {
          color: transparent;
          caret-color: #1d2531;
        }
      }
    }
  }
}

// 保持向后兼容性
.GUICardDescriptionPannel {
  @extend .GUICardDescriptionPannelContainer;
}
