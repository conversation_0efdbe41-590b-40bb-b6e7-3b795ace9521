.formCardEditor {
  .canvas {
    border-radius: 12px;
    border: 1px solid #f7f9fa;
    background: #fff;
    padding: 16px;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
    color: #8c9196;

    .emptyIcon {
      width: 120px;
      height: 120px;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .emptyText {
      font-size: 14px;
      line-height: 22px;
    }
  }

  .component {
    border-radius: 8px;
    transition: all 0.3s ease;
    background: transparent;
    border: 2px solid transparent;

    &:hover {
      background: rgba(0, 107, 255, 0.08);
    }

    &.editing {
      border: 2px solid #006BFF;
    }
  }

  .componentPreview {
    .componentWrapper {
      // padding: 12px;
      // border-radius: 6px;
      // background: #fafbfc;
      // border: 1px solid #eef1f4;

      // 确保组件在包装器内正确显示
      > * {
        width: 100%;
      }
    }

    .componentHeader {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .componentName {
        font-size: 14px;
        font-weight: 500;
        color: #1d2531;
      }
    }
  }
}

.tooltipContent {
  padding: 6px;
  .tooltipTitle {
    font-size: 14px;
    color: #1D2129;
    font-weight: 600;
    line-height: 22px;
  }

  .tooltipDescription {
    font-size: 12px;
    color: #657083;
    font-weight: 400;
    line-height: 22px;
    max-height: 400px;
    overflow-y: auto;
  }
}