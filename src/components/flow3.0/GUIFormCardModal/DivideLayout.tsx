import React, { ReactNode, CSSProperties } from 'react';

interface DivideLayoutProps {
  children: React.ReactNode;
  direction?: 'horizontal' | 'vertical';
  dividerStyle?: React.CSSProperties;
  spacing?: string;
  className?: string;
  style?: React.CSSProperties;
} 

const DivideLayout: React.FC<DivideLayoutProps> = ({
  children,
  direction = 'vertical',
  dividerStyle,
  spacing = '8px',
  className = '',
  style = {},
}) => {
  // 根据方向设置默认的分隔条样式
  const getDefaultDividerStyle = (): CSSProperties => {
    if (direction === 'horizontal') {
      return {
        width: '1px',
        height: '16px',
        background: 'transparent',
        borderLeft: '1px solid #EDF1F5',
        margin: `0 ${spacing}`,
        flexShrink: 0,
        ...dividerStyle,
      };
    } else {
      return {
        width: '100%',
        height: '1px',
        background: 'transparent',
        borderTop: '1px dashed #edf1f5',
        margin: `${spacing} 0`,
        flexShrink: 0,
        ...dividerStyle,
      };
    }
  };

  const dividerDefaultStyle = getDefaultDividerStyle();

  // 将 children 转化为数组并在每个元素间插入分隔条
  const items = React.Children.toArray(children).reduce<ReactNode[]>(
    (acc, child, index, array) => {
      acc.push(child);
      if (index < array.length - 1) {
        // 最后一个元素后不添加分隔条
        acc.push(<div key={`divider-${index}`} style={dividerDefaultStyle} />);
      }
      return acc;
    },
    []
  );

  // 根据方向设置容器样式
  const containerStyle: CSSProperties = {
    display: 'flex',
    flexDirection: direction === 'horizontal' ? 'row' : 'column',
    alignItems: direction === 'horizontal' ? 'center' : 'stretch',
    ...style,
  };

  return (
    <div className={`divide-layout ${className}`} style={containerStyle}>
      {items}
    </div>
  );
};

export default DivideLayout; 