.GUIFormCardList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.list {
  flex: 1;
  overflow-y: auto;
}

.optionPannel {
  display: flex;
  position: sticky;
  bottom: 0;
  padding: 16px 16px 0 16px;
  border-top: 1px solid #edf1f5;
  background: #ffffff;
}

.addButton {
  border: 1px solid #2a76ff;
  color: #2a76ff;
  background: #f8f9ff;
  padding: 0 16px;
  font-size: 14px;
  border-radius: 8px;
  height: 28px;

  &:hover {
    border-color: #2e8cff;
    color: #2e8cff;
    background: #f0f3ff;
  }

  &:focus {
    border-color: #2e8cff;
    color: #2e8cff;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
}
