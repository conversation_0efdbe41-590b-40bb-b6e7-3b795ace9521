.hoverOperationPannel {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.operationPannel {
  position: absolute;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  padding: 4px;
  display: flex;
  align-items: center;
  gap: 0;
  z-index: 1000;
  animation: slideIn 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  // 位置定位
  &.top-right {
    top: -12px;
    right: -12px;
  }

  &.top-left {
    top: -12px;
    left: -12px;
  }

  &.bottom-right {
    bottom: -12px;
    right: -12px;
  }

  &.bottom-left {
    bottom: -12px;
    left: -12px;
  }
}

.operationButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  // height: 32px;
  border: none;
  border-radius: 0;
  background: transparent;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
  padding: 4px 8px;

  // 第一个按钮的左圆角
  &:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  // 最后一个按钮的右圆角
  &:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  // 如果只有一个按钮，保持完整圆角
  &:only-child {
    border-radius: 8px;
  }

  &:active {
    transform: scale(0.95);
    transition: all 0.1s ease;
  }

  // 不同类型按钮的颜色主题
  &.edit {
    &:hover {
      color: #006BFF;
    }
  }

  &.delete {
    &:hover {
      color: #dc2626;
    }
  }

  &.copy {
    &:hover {
      color: #16a34a;
    }
  }

  &.view {
    &:hover {
      color: #ca8a04;
    }
  }

  &.setting {
    &:hover {
      color: #7c3aed;
    }
  }

  &.more {
    &:hover {
      color: #475569;
    }
  }

  &.moveUp {
    &:hover {
      color: #006BFF;
    }
  }

  &.moveDown {
    &:hover {
      color: #006BFF;
    }
  }

  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;

    &:hover {
      color: #64748b;
    }
  }
}

.buttonIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;

  svg {
    width: 100%;
    height: 100%;
  }
}

.buttonLabel {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
  line-height: 1.2;
}

// 当按钮同时有图标和文字时，调整布局
.operationButton:has(.buttonIcon):has(.buttonLabel) {
  width: auto;
  min-width: 52px;
  padding: 4px;
  gap: 4px;
  flex-direction: row;
}

// 只有图标时的样式
.operationButton:has(.buttonIcon):not(:has(.buttonLabel)) {
  width: 32px;
  height: 32px;
  padding: 4px;
}

// 只有文字时的样式  
.operationButton:not(:has(.buttonIcon)):has(.buttonLabel) {
  width: auto;
  min-width: 52px;
  padding: 4px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .operationButton {
    width: 28px;
    height: 28px;
    padding: 4px;
    
    // 移动端按钮组合样式调整
    &:has(.buttonIcon):has(.buttonLabel) {
      min-width: 44px;
      padding: 4px;
    }

    &:not(:has(.buttonIcon)):has(.buttonLabel) {
      min-width: 44px;
      padding: 4px;
    }
  }

  .buttonIcon {
    width: 14px;
    height: 14px;
  }

  .operationPannel {
    padding: 3px;
  }
}
