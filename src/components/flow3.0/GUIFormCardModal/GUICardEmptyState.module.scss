.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.emptyListPic {
  max-width: 280px;
}

.content {
  margin-top: 32px;
  text-align: center;
}
.title {
  margin-bottom: 10px;
  color: #1d2531;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}
.description {
  margin-bottom: 24px;
  color: #9ea7b8;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.createButton {
  display: flex;
  align-items: center;
  margin: 0 auto;
  padding: 0 16px;
  border: 1px solid #006bff;
  height: 28px;
  gap: 4px;
  color: #006bff;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  background: #f8f9ff;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
}
.createButton:hover {
  background: #f0f3ff;
}
