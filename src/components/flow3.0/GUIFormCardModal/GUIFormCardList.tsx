import React, { useEffect, useState } from 'react';
import {
  reqGuiMCPCardList,
  createGuiMCPCard,
  deleteGuiMCPCard,
  reqGuiMCPCardDetail,
  updateGuiMCPCard,
} from '@/service/guiMCP';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Spin, message } from 'antd';
import { DivideLayout, type GUICardData } from './index';
import GUICardItem from './GUICardItem';
import GUIFormCardEditorModal from './GUIFormCardEditorModal';
import GUICardEmptyState from './GUICardEmptyState';
import styles from './GUIFormCardList.module.scss';

interface GUIFormCardListProps {
  flowId: number;
  blockKey: number;
  onGUIFormCardListChange?: (cardList: GUICardData[]) => void;
}

function GUIFormCardList({ flowId, blockKey, onGUIFormCardListChange }: GUIFormCardListProps) {
  const [GUICardList, setGUICardList] = useState<GUICardData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [editingCard, setEditingCard] = useState<GUICardData | null>(null);
  const [isLoadingCardDetail, setIsLoadingCardDetail] = useState(false);

  const loadCardList = async () => {
    setIsLoading(true);
    try {
      const res = await reqGuiMCPCardList({ flow_id: flowId, block_key: blockKey });
      const list = Array.isArray(res) ? res : [];
      setGUICardList(list);
      onGUIFormCardListChange?.(list);
    } catch (error) {
      message.error('加载卡片列表失败');
      onGUIFormCardListChange?.([]);
    }
    setIsLoading(false);
    setHasLoaded(true);
  };

  const handleEditCard = async (id: string) => {
    setIsLoadingCardDetail(true);
    try {
      const cardDetail = await reqGuiMCPCardDetail({ id: parseInt(id) });
      setEditingCard({ ...cardDetail, id });
      setShowEditor(true);
    } catch (error) {
      message.error('获取卡片详情失败');
    }
    setIsLoadingCardDetail(false);
  };

  const handleDeleteCard = async (id: string) => {
    try {
      await deleteGuiMCPCard({ id: parseInt(id) });
      // message.success('删除成功');
      await loadCardList(); // 重新加载列表
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleAddCard = () => {
    setEditingCard(null);
    setShowEditor(true);
  };

  const handleSaveCard = async (
    cardSchema: any,
    cardDescription: string,
    cardTitle?: string,
    hasInputComponent: boolean = true
  ) => {
    setIsCreating(true);
    try {
      // 构建 form_data，根据是否有 template_meta 来决定结构
      const formData = cardSchema && cardSchema.template_meta 
        ? {
            template_meta: cardSchema.template_meta,
            template_id: cardSchema.template_id,
            description: cardDescription,
            hasInputComponent,
          }
        : {
            schema: cardSchema,
            description: cardDescription,
            hasInputComponent,
          };

      if (editingCard) {
        // 更新卡片
        await updateGuiMCPCard({
          id: parseInt(editingCard.id),
          flow_id: flowId,
          block_key: blockKey,
          title: cardTitle,
          form_data: formData,
        });
        // message.success('卡片更新成功');
      } else {
        // 创建新卡片
        await createGuiMCPCard({
          flow_id: flowId,
          block_key: blockKey,
          title: cardTitle,
          form_data: formData,
        });
        // message.success('卡片创建成功');
      }
      setShowEditor(false);
      setEditingCard(null);
      await loadCardList(); // 重新加载列表
    } catch (error) {
      message.error(editingCard ? '卡片更新失败' : '卡片创建失败');
    }
    setIsCreating(false);
  };

  const handleCloseEditor = () => {
    setShowEditor(false);
    setEditingCard(null);
  };

  useEffect(() => {
    setHasLoaded(false);
    loadCardList();
  }, [flowId, blockKey]);

  return (
    <div className={styles.GUIFormCardList}>
      {!hasLoaded ? (
        <div style={{ padding: '40px', textAlign: 'center' }}>
          <Spin tip="加载中..." />
        </div>
      ) : GUICardList.length === 0 ? (
        <GUICardEmptyState onAddBtnClick={handleAddCard} />
      ) : (
        <>
          <Spin spinning={isLoading} tip="加载中...">
            <DivideLayout
              className={styles.list}
              direction="vertical"
              spacing="0"
              dividerStyle={{ borderTop: '1px solid #EDF1F5' }}
            >
              {GUICardList.map((cardData) => (
                <GUICardItem
                  key={cardData.id}
                  data={cardData}
                  onEdit={handleEditCard}
                  onDelete={handleDeleteCard}
                />
              ))}
            </DivideLayout>
          </Spin>
          <div className={styles.optionPannel}>
            <Button
              type="default"
              icon={<PlusOutlined />}
              className={styles.addButton}
              onClick={handleAddCard}
            >
              用户界面
            </Button>
          </div>
        </>
      )}
      <GUIFormCardEditorModal
        open={showEditor}
        onCancel={handleCloseEditor}
        onConfirm={handleSaveCard}
        confirmLoading={isCreating}
        editingCard={editingCard}
        isLoadingCardDetail={isLoadingCardDetail}
        cardListLength={GUICardList.length}
      />
    </div>
  );
}

export default GUIFormCardList;
