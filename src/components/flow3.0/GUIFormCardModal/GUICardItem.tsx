import React, { useMemo } from 'react';
import classnames from 'classnames';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button } from 'antd';

import { convertSDKSchemaToSchemaItems, DivideLayout, type GUICardData } from './index';
import GUIComponent from './GUIComponent';
import { TEMPLATES } from './constant';
import styles from './GUICardItem.module.scss';

interface GUICardItemProps {
  data: GUICardData;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

function GUICardItem({ data, onEdit, onDelete }: GUICardItemProps) {
  const { schemaItems, templateData } = useMemo(() => {
    try {
      // 检查是否有模版数据
      if (data.form_data && (data.form_data as any).template_meta && (data.form_data as any).template_id) {
        // 使用 template_id 查找模版
        const template = TEMPLATES.find(t => t.id === (data.form_data as any).template_id);
        
        return {
          schemaItems: [],
          templateData: template
        };
      }
      
      // 处理普通组件数据
      if (data.form_data && data.form_data.schema && Array.isArray(data.form_data.schema)) {
        return {
          schemaItems: convertSDKSchemaToSchemaItems(data.form_data),
          templateData: null
        };
      }
      
      return {
        schemaItems: [],
        templateData: null
      };
    } catch (error) {
      console.warn('转换 schema 失败:', error);
      return {
        schemaItems: [],
        templateData: null
      };
    }
  }, [data.form_data]);

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(data.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(data.id);
  };

  const title = data.title || '自定义界面';

  return (
    <div className={styles.GUICardItem}>
      <div className={styles.header}>
        <div className={classnames(styles.title, 'truncate')}>{title}</div>
        <div className={styles.operation}>
          <DivideLayout direction="horizontal" spacing="8px" dividerStyle={{ height: '12px' }}>
            <Button
              className={styles.editButton}
              type="link"
              icon={<EditOutlined />}
              onClick={handleEdit}
              size="small"
            >
              编辑
            </Button>
            <Button type="link" icon={<DeleteOutlined />} onClick={handleDelete} size="small" />
          </DivideLayout>
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.contentInner}>
          {templateData ? (
            // 渲染模版预览图
            <div className={styles.templatePreview}>
              <img 
                src={templateData.templatePreviewImg} 
                alt={templateData.title}
                className={styles.templateImage}
              />
            </div>
          ) : schemaItems.length > 0 ? (
            // 渲染普通组件
            schemaItems.map((item) => (
              <div key={item.id} className={styles.componentPreviewItem}>
                <GUIComponent item={item} isEditing={false} onPropsChange={() => {}} />
              </div>
            ))
          ) : (
            <div className={styles.emptyComponents}>暂无组件内容</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default GUICardItem;
