import React from 'react';
import { Tag } from 'antd';
import GUIComponents from '@q/gui';
import { type SchemaItem } from './index';

interface GUIComponentProps {
  item: SchemaItem;
  isEditing: boolean;
  onPropsChange: (id: string, props: Record<string, any>) => void;
  className?: string;
  getPopupContainer?: () => HTMLElement;
}

function GUIComponent({
  item,
  isEditing,
  onPropsChange,
  className,
  getPopupContainer = () => document.body,
}: GUIComponentProps) {
  const { props, type, id } = item;

  // 从 GUIComponents 中获取对应的组件
  const componentDefinition = GUIComponents[type];
  const Component = componentDefinition?.editor;

  if (!Component) {
    return (
      <div className={className}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 12 }}>
          <Tag color="red">未知组件</Tag>
          <span style={{ fontSize: 14, fontWeight: 500, color: '#1d2531' }}>
            {componentDefinition?.name || type}
          </span>
        </div>
      </div>
    );
  }

  // 渲染实际的 GUI 组件
  return (
    <div className={className}>
      <div style={{ width: '100%' }}>
        <Component
          {...props}
          editable={isEditing}
          getPopupContainer={getPopupContainer}
          onPropsChange={({ type: field, value }) => {
            console.debug('field', field, 'value', value);
            onPropsChange(id, { ...props, [field]: value });
          }}
        />
      </div>
    </div>
  );
}

export default GUIComponent;
