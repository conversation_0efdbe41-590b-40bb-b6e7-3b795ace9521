import React, { useState, ReactNode } from 'react';
import { 
  EditOutlined, 
  DeleteOutlined, 
  CopyOutlined, 
  EyeOutlined, 
  SettingOutlined, 
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import styles from './HoverOperationPannel.module.scss';
import DivideLayout from './DivideLayout';

interface OperationButton {
  id: string;
  label: string;
  icon?: ReactNode;
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  type?: 'edit' | 'delete' | 'copy' | 'view' | 'setting' | 'more' | 'moveUp' | 'moveDown' | 'custom';
}

interface HoverOperationPannelProps {
  children: ReactNode;
  operations: OperationButton[];
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  className?: string;
  disabled?: boolean;
  showOnHover?: boolean;
  displayMode?: 'icon-only' | 'text-only' | 'icon-and-text';
}

// 预定义的图标映射
const getDefaultIcon = (type?: string) => {
  switch (type) {
    case 'edit':
      return <EditOutlined />;
    case 'delete':
      return <DeleteOutlined />;
    case 'copy':
      return <CopyOutlined />;
    case 'view':
      return <EyeOutlined />;
    case 'setting':
      return <SettingOutlined />;
    case 'more':
      return <MoreOutlined />;
    case 'moveUp':
      return <ArrowUpOutlined />;
    case 'moveDown':
      return <ArrowDownOutlined />;
    default:
      return null;
  }
};

function HoverOperationPannel({ 
  children, 
  operations, 
  position = 'top-right',
  className = '',
  disabled = false,
  showOnHover = true,
  displayMode = 'icon-only'
}: HoverOperationPannelProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    if (!disabled && showOnHover) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const handleButtonClick = (operation: OperationButton, event: React.MouseEvent) => {
    event.stopPropagation();
    event.preventDefault();
    if (!operation.disabled) {
      operation.onClick();
    }
  };

  const shouldShowPanel = showOnHover ? isHovered : true;

  return (
    <div 
      className={`${styles.hoverOperationPannel} ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      
      {shouldShowPanel && operations.length > 0 && (
        <div className={`${styles.operationPannel} ${styles[position]}`}>
          <DivideLayout direction="horizontal" spacing="0">
            {operations.map((operation) => {
              const iconToShow = operation.icon || getDefaultIcon(operation.type);
              
              return (
                <button
                  key={operation.id}
                  className={`${styles.operationButton} ${operation.className || ''} ${
                    operation.disabled ? styles.disabled : ''
                  } ${operation.type ? styles[operation.type] : ''}`}
                  onClick={(e) => handleButtonClick(operation, e)}
                  disabled={operation.disabled}
                  // title={operation.label}
                >
                  {(displayMode === 'icon-only' || displayMode === 'icon-and-text') && iconToShow && (
                    <span className={styles.buttonIcon}>{iconToShow}</span>
                  )}
                  {(displayMode === 'text-only' || displayMode === 'icon-and-text') && operation.label && (
                    <span className={styles.buttonLabel}>{operation.label}</span>
                  )}
                </button>
              );
            })}
          </DivideLayout>
        </div>
      )}
    </div>
  );
}

export default HoverOperationPannel;

// 使用示例：
/*
const ExampleComponent = () => {
  const operations = [
    {
      id: 'edit',
      label: '编辑',
      type: 'edit' as const,
      onClick: () => console.log('编辑操作')
    },
    {
      id: 'copy',
      label: '复制',
      type: 'copy' as const,
      onClick: () => console.log('复制操作')
    },
    {
      id: 'delete',
      label: '删除',
      type: 'delete' as const,
      onClick: () => console.log('删除操作')
    }
  ];

  return (
    <HoverOperationPannel 
      operations={operations} 
      position="top-right"
      displayMode="icon-and-text" // 'icon-only' | 'text-only' | 'icon-and-text'
    >
      <div style={{ 
        width: 200, 
        height: 100, 
        background: '#f5f5f5', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        borderRadius: '8px'
      }}>
        悬停显示操作按钮
      </div>
    </HoverOperationPannel>
  );
};
*/ 