import React, { useState, useRef, useEffect } from 'react';
import styles from './GUICardDescriptionPanel.module.scss';

interface GUICardDescriptionPanelProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  rows?: number;
}

export default function GUICardDescriptionPanel({
  value = '',
  onChange,
  placeholder = '设置自定义界面的描述说明提示词，方便智能体精准选择所需的用户界面',
  rows = 1,
}: GUICardDescriptionPanelProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const highlightRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // 统一的行高计算逻辑
  const LINE_HEIGHT = 1.5715; // 与CSS保持一致
  const FONT_SIZE = 14; // 与CSS保持一致
  const VERTICAL_PADDING = 10; // 上下padding总和

  const getRowHeight = () => FONT_SIZE * LINE_HEIGHT;
  const getMinHeight = () => getRowHeight() * rows + VERTICAL_PADDING;

  // 解析文本并添加高亮
  const parseTextWithHighlight = (text: string) => {
    if (!text) return placeholder;

    // 匹配 {{...}} 模式，不允许跨行
    const variablePattern = /(\{\{[^}\r\n]*\}\})/g;
    const parts = text.split(variablePattern);

    return parts.map((part, index) => {
      if (variablePattern.test(part)) {
        return (
          <span key={index} className={styles.variable}>
            {part}
          </span>
        );
      }
      return (
        <span key={index} className={styles.normalText}>
          {part}
        </span>
      );
    });
  };

  // 自动调整高度
  const adjustHeight = () => {
    if (textareaRef.current && highlightRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      const minHeight = getMinHeight();
      const newHeight = Math.max(minHeight, textarea.scrollHeight);
      textarea.style.height = `${newHeight}px`;
      highlightRef.current.style.height = `${newHeight}px`;
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange?.(newValue);
    // 延迟调整高度，确保内容已更新
    setTimeout(adjustHeight, 0);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // 在失去焦点时进行 trim 处理
    if (value !== value.trim()) {
      onChange?.(value.trim());
    }
  };

  // 同步滚动
  const handleScroll = () => {
    if (textareaRef.current && highlightRef.current) {
      highlightRef.current.scrollTop = textareaRef.current.scrollTop;
      highlightRef.current.scrollLeft = textareaRef.current.scrollLeft;
    }
  };

  // 设置CSS变量来控制最小高度
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.style.setProperty('--gui-desc-pannel-min-height', `${getMinHeight()}px`);
    }
  }, [rows]);

  // 初始化和内容变化时调整高度
  useEffect(() => {
    adjustHeight();
  }, [value]);

  return (
    <div className={styles.GUICardDescriptionPannelContainer}>
      <h2 className={styles.GUICardDescriptionPannelTitle}>提示词：</h2>
      <div className={styles.GUICardDescriptionPannelContent}>
        <div ref={containerRef} className={styles.textInputContainer}>
          {/* 高亮显示层 */}
          <div
            ref={highlightRef}
            className={`${styles.highlightLayer} ${
              !value && !isFocused ? styles.showPlaceholder : ''
            }`}
          >
            {value || !isFocused ? parseTextWithHighlight(value) : ''}
          </div>

          {/* 实际输入框 */}
          <textarea
            ref={textareaRef}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onScroll={handleScroll}
            placeholder={isFocused ? placeholder : ''}
            rows={rows}
            className={styles.textInput}
          />
        </div>
      </div>
    </div>
  );
}
