.createCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: 1px solid #e1e7ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-width: 2px;
    border-color: #006bff;
  }
}

.iconWrapper {
  margin-bottom: 8px;
}

.icon {
  font-size: 44px;
  color: #d9d9d9;
  transition: color 0.3s ease;

  .createCard:hover & {
    color: #006bff;
  }
}

.text {
  color: #657083;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  .createCard:hover & {
    color: #006bff;
  }
}
