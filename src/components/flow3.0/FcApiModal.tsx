import { use, useEffect, useState } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { Modal, Checkbox, Button, message, Tabs, Empty,Spin ,Row,Col} from 'antd';
import { LeftOutlined, PlusOutlined } from '@ant-design/icons';

import apiStyles from '@/styles/Api.module.scss'
import { reqApiList, reqPublishApiList, reqGetApiDetail, reqGetPublishedApiDetail } from '@/service/api';

import { useRouter } from 'next/router'

import apiModal from '@/images/apiModal.svg'
import right from '@/images/api/right.svg'
import deleteApi from '@/images/api/deleteApi.svg'
import {fcSelectedFCApiListAtom,fcCurrentCheckFcApiAtom,fcUseApiStampAtom,fcChooseStampAtom, choosedStampAtom } from '@/atoms/flowAtoms';


export default function FcApiModal(prop:any) {
    const { openApi, setOpenApi, selectOrgList, activeClickIndex } = prop;
    const [activeTab, setActiveTab] = useState('2')
    const [apiList, setApiList] = useState(new Array())
    const [activeApiIndex, setActiveApiIndex] = useState(-1)
    const [selectedApiIndex, setSelectedApiIndex] = useState(-1)

    const [isLoading, setIsLoading] = useState(false)
    const [fcSelectedFCApiList, setFcSelectedFCApiList] = useRecoilState<any>(fcSelectedFCApiListAtom); // 选择的api列表
    const [chooseApi, setChooseApi] = useRecoilState(fcCurrentCheckFcApiAtom);
    const [fcChooseStamp, setFcChooseStamp] = useRecoilState(fcChooseStampAtom); // 点击查看api详情
    const [chooseStamp, setChooseStamp] = useRecoilState(choosedStampAtom); // 点击查看api详情
    const [useApiStamp, setUseApiStamp] = useRecoilState(fcUseApiStampAtom) // 点击确定

    const router = useRouter()
    const teamId = router.query.teamId;

    const [deleteIndex, setDeleteIndex] = useState(-1)

    const getApiList = async() => {
        setIsLoading(true)
        let res;
        if(activeTab === '2'){
            res = await reqPublishApiList({page_size: 10000, page: 1})
        }else {
            // {page_size: 10000, page: 1}
            res = await reqApiList(teamId ? {
                team_id: teamId,
                api_type: activeTab
            }: {
                api_type: activeTab
            })
        }
        res && setApiList(res)
        setIsLoading(false)
    }


    useEffect(()=>{
        setFcSelectedFCApiList(selectOrgList)
    },[activeClickIndex])

    useEffect(()=>{
        if(openApi) {
            getApiList()
            setSelectedApiIndex(-1)
        }
    },[openApi,activeTab])

    const handleOk = () => {
       setOpenApi(false)
       setUseApiStamp(new Date().getTime())
    }

    const onCheckBoxChange = async(api:any,index:number,isChecked:boolean) => {
        let res;
        const apiId = api.id || api.template_id;
        if(api.template_id) {
            res = await reqGetPublishedApiDetail({
                api_id: api.template_id
            })
        }else if (api.id){
            res = await reqGetApiDetail({
                api_id: api.id
            })
        }

        const newApi = {
            api_id: apiId,
            name: res.api_name,
            description: res.api_desc,
            method: res.method,
            url: res.url,
            headers:res.headers.map((item: any) => {
                return {
                    [item['headerName']]: item.value
                }
            }).reduce((key:any, value:any) => {
                return {...key, ...value};
              }, {}),
            query: res.querystring.map((item:any) => {
                return {
                    name: item.name,
                    type: item.type,
                    description: item.description,
                    default: item.defaultValue,
                    enum: item.enum || [],
                }
            }),
            body: res.body.map((item:any) => {
                return {
                    name: item.name,
                    type: item.type,
                    description: item.description,
                    default: item.defaultValue,
                    properties: item.properties || {},
                }
            }),
        }
        setFcSelectedFCApiList((oldList:any) => {
            // console.log(oldList)
            if(isChecked) {
                return [...oldList, newApi]
            } else {
                return oldList.filter((item:any) => item.api_id !== apiId)
            }
        })
    }
    // console.log(fcSelectedFCApiList, apiList)

    const checkFcApiDetail = (api:any,index:number) => {
        setChooseApi(api)
        setFcChooseStamp(new Date().getTime())
        setChooseStamp(0)
        setSelectedApiIndex(index)
    }
    return (
        <>
            <Modal 
                open={openApi}
                className={apiStyles.apiModal + ' commonModal fcApiModal'}
                width={910}
                onCancel={() => setOpenApi(false)}
                cancelText='取消'
                okText='确定'
                footer={selectedApiIndex !== -1 ? <></> : 
                   [
                        <Button key="back"  type="default"  className='defaultButton smallDefaultButton' onClick={() => setOpenApi(false)}>
                          取消
                        </Button>,
                        <Button key="submit" type="primary" className='primaryButton smallPrimaryButton' onClick={handleOk}>
                          确定
                        </Button>,
                      ]
                    }
                title={selectedApiIndex === -1 ? 'API工具' : <><LeftOutlined onClick={() => setSelectedApiIndex(-1)} /><img src={apiModal.src} className={apiStyles.apiTitleIconModal} /><span className={apiStyles.apiTitleNameModal}>{chooseApi.api_name}</span><span className={apiStyles.apiTitleDescModal}> {chooseApi.api_desc}</span></>}
            >
                {selectedApiIndex === -1 ? <><Tabs 
                    className={'common_noBorder_tabs'}
                    items={[{
                        key: '2',
                        label: '发现',
                        children: <></>,
                    },
                    {
                        key: '1',
                        label: '自定义',
                        children: <></>,
                    }
                    ]} 
                    activeKey={activeTab}
                    onChange={(e)=> {
                        setActiveTab(e)
                    }}
                />
                <Spin spinning	={isLoading} tip="加载中...">
                <div className={apiStyles.fcApiListModal}>
                    {apiList.map((api:any, index:any) => {
                        return <div 
                            onMouseEnter={()=>{
                                setActiveApiIndex(index)
                            }}
                            onMouseLeave={() => {
                                setActiveApiIndex(-1)
                            }}
                            className={apiStyles.fcApiItemModal + ' ' + ((index + 1) % 3 === 0 ? apiStyles.lastApiItemModal : '') + ' ' + (fcSelectedFCApiList?.map((item:any) => item.api_id).includes(api.id || api.template_id) ? apiStyles.fcApiItemModalSelected : '') }
                            >
                            <div className={apiStyles.apiTitleModal + ' ' + apiStyles.fcApiTitleModal}>
                                <img src={apiModal.src} alt=""/>
                                <span className='singleLineEllipsis'>{api.api_name}</span>
                            </div>
                            <div className={apiStyles.apiDescModal}>
                                {api.api_desc}
                            </div>

                            <div className={apiStyles.btnWrapperModal + ' ' + (activeApiIndex === index ? apiStyles.showBtnWrapperModal : apiStyles.hideBtnWrapperModal)}>
                                <Button className={'defaultButton smallDefaultButton'} type='default' onClick={()=>{checkFcApiDetail(api,index)}} >查看</Button>
                            </div>
                            <div className={apiStyles.fcCheckBox} onClick={e=>e.stopPropagation()}>
                                {fcSelectedFCApiList?.length && fcSelectedFCApiList.map((item:any) => item.api_id).includes(api.id || api.template_id) ?  
                                <>
                                    {deleteIndex === index ? <span 
                                        className={apiStyles.deleteApiBtn} 
                                        onClick={() => {onCheckBoxChange(api,index,false)}} 
                                        onMouseEnter={() => {
                                            setDeleteIndex(index)
                                        }}
                                        onMouseLeave={() => {
                                            setDeleteIndex(-1)
                                        }}
                                    >
                                        <img src={deleteApi.src} /> 取消添加
                                    </span>  : <span 
                                        className={apiStyles.addedApiBtn} 
                                        onClick={() => {onCheckBoxChange(api,index,false)}} 
                                        onMouseEnter={() => {
                                            setDeleteIndex(index)
                                        }}
                                        onMouseLeave={() => {
                                            setDeleteIndex(-1)
                                        }}>
                                            <img src={right.src} /> 已添加
                                    </span>}
                                </>
                               : <span 
                                    className={apiStyles.addApiBtn}  
                                    onClick={() => {onCheckBoxChange(api,index,true)}}> 
                                        <PlusOutlined /> 添加
                                    </span>}
                                
                            </div>
                        </div> 
                    })}
                    {!apiList.length && <Empty description='暂无数据' />}
                </div>
                </Spin></> 
                : ''}
            </Modal>
        </>
    )
}
