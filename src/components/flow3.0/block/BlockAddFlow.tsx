import { useEffect, useState, forwardRef } from "react";
import styles from "@/components/agent/detail/AgentDetailAddPluginModal.module.scss";
import { message, Button, Modal, Input, Divider } from "antd";
import { SearchOutlined, CloseOutlined } from "@ant-design/icons";
import MenuAllIcon from "@/icons/MenuAllIcon";
import MenuPersonIcon from "@/icons/MenuPersonIcon";
import SidebarButton from "@/components/agent/SidebarButton";
import DoneIcon from "../../../icons/DoneIcon";
import plusBtn from "@/images/plus.svg";
import minusBtn from "@/images/minus.png";
import arrowRightIcon from "@/images/arrowRight.png";
import InfiniteScroll from "react-infinite-scroll-component";
import {
  choosedFlowAtom,
  choosedFlowStampAtom
} from '@/atoms/flowAtoms';


import { useRecoilState } from "recoil";
import AgentListEmpty from "@/components/agent/detail/AgentListEmpty";

import { useRouter } from "next/router";

import { 
  reqAgentFlowTagList
} from "@/service/agentFlow";

import {
  reqFlowPluginList,
  reqFlowPluginTemplateList,
  reqFlowPluginDetail,
  reqCopyVersionFlow
} from "@/service/flow3.0";


const BlockAddFlow = forwardRef((props: {
  open: any;
  setOpen: any;
  chainId: string;
  addedArr: any;
  blockType: string;
  blockId: string;
  setChainId: (id:any) => void;
  setFlowStatus: (s:any) => void;
  setFlowType: (t:any) => void;
}, ref) => {
  const { open, setOpen, chainId, addedArr, blockType, blockId, setChainId, setFlowStatus, setFlowType } = props;

  // 标签列表
  const [tags, setTags] = useState<any[]>([]);

  // 当前激活tab，默认我的(值为-1)
  const [activeTab, setActiveTab] = useState<number>(-1);

  // 列表
  const [list, setList] = useState<any[]>([]);

  // 搜索关键字
  const [keyword, setKeyword] = useState<string>("");

  // 当前页
  const [page, setPage] = useState<number>(1);

  // 每页数量
  const [pageSize, setPageSize] = useState<number>(200);

  const [hasMore, setHasMore] = useState(true);

  // flow 单选
  const [choosedFlow, setChoosedFlow] = useRecoilState(choosedFlowAtom); // 当前选择的flow
  const [choosedFlowStamp, setChoosedFlowStamp] = useRecoilState(choosedFlowStampAtom);

  const [propelValue, setPropelValue] = useState(0);

  const router = useRouter();
  const teamId = router.query.teamId;

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setKeyword(e.target.value);
  };

  const handleOk = () => {
    setOpen({
      open: false,
      isAddBlock: false
    });
  };

  const handleCancel = () => {
    setOpen({
      open: false,
      isAddBlock: false
    });
  };

  // useEffect(() => {
  //   getFlowTag();
  //   getFlowList(1);
  // }, []);

  // useEffect(() => {
  //   setPage(1);
  //   if (activeTab < 0) {
  //     getFlowList(1);
  //   } else {
  //     getPublishFlowList(1);
  //   }
  // }, [keyword, activeTab]);

  const getFlowList = async (pageS: number = page) => {
    const resp = await reqFlowPluginList({
      page: pageS,
      page_size: pageSize,
      keyword: keyword,
      // user_id: propelValue,
      // publish_status: "2",
      // is_action: 1
    });
    const resList = resp?.list.filter((item:any) => {
      return item.id != chainId
    })
    if (resp && resList) {
      if (pageS === 1) {
        setList(resList);
      } else {
        setList([...list, ...resList]);
      }
      setPage(pageS + 1);
      setHasMore(list.length < resp.total);
    } else {
      setList([]);
    }
  };
  
  // console.log(hasMore,'--hasMore')

  const getPublishFlowList = async (pageS: number = page) => {
    const resp = await reqFlowPluginTemplateList({
      page: pageS,
      page_size: pageSize,
      keyword: keyword,
      ...activeTab == 0 ? {} : {tags: [activeTab]},
      // is_action: 1
    });

    if (resp && resp?.list) {
      if (pageS === 1) {
        setList(resp.list);
      } else {
        setList([...list, ...resp.list]);
      }
      setPage(pageS + 1);
      setHasMore(list.length < resp.total);
    }else{
      setList([]);
    }
  };


  const getFlowTag = async () => {
    const resp = await reqAgentFlowTagList({});
    setTags([
      {
        id: 0,
        name: "全部",
        desc: "全部",
      },
      ...resp,
    ]);
  };

  const handleCreate = () => {
    const pluginUrl = window.location.origin + '/flowList?teamId=' + teamId + '&flowCreate=' + 1;
    window.open(pluginUrl, '_blank');
  }

  // console.log(open,'-------open')

  return (
    <>
      <Modal
        width={'60%'}
        className={styles.agentDetailAddPluginModalContainer}
        open={open.open}
        closeIcon={null}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        title={''}
      >
        <div className={styles.agentDetailAddPluginModalContent} style={{height: "calc(100vh - 200px)"}}>
          <div className={styles.agentDetailAddPluginModalSidebar}>
            <div className={styles.agentSidebarTitle}>技能</div>
            <Button
              type="primary"
              style={{ width: "100%", height: "32px" }}
              onClick={() => handleCreate()}
            >
              创建技能
            </Button>
            <div style={{ height: "20px" }}></div>
            <SidebarButton
              icon={MenuPersonIcon({ color: "#1B2532" })}
              selectedIcon={MenuPersonIcon({ color: "#1D7CFF" })}
              selected={activeTab === -1}
              onClick={() => setActiveTab(-1)}
              showIcon={true}
              title="我的技能"
            />
            <div style={{ height: "20px" }}></div>
            <div style={{ height: "1px", background: "#EBECF0" }}></div>
            <div style={{ height: "20px" }}></div>
            <div className={styles.pluginDiscover}>发现</div>
            <div style={{ height: "12px" }}></div>
            <div className={styles.agentDetailAddPluginModalLabel}>
              {tags.map((tag, index) => (
                <SidebarButton
                  key={index}
                  icon={MenuAllIcon({ color: "#1B2532" })}
                  selectedIcon={MenuAllIcon({ color: "#1D7CFF" })}
                  selected={activeTab === tag.id}
                  onClick={() => setActiveTab(tag.id)}
                  title={tag.name}
                />
              ))}
            </div>
          </div>
          {
            <div className={styles.agentDetailAddPluginModalInner}>
              <div className={styles.agentDetailAddPluginModalSearch}>
                <Input
                  style={{
                    width: "240px",
                    marginRight: "24px",
                  }}
                  placeholder="搜索技能名称"
                  onChange={onChange}
                  prefix={<SearchOutlined style={{ color: "#9BA7BA" }} />}
                />
                <CloseOutlined onClick={handleCancel} />
              </div>
              <div
                className={styles.agentDetailAddPluginModalList}
                id="agentDetailAddPluginModalList"
              > {
                list && list.length === 0 ? (
                  <AgentListEmpty desc={"抱歉，没有找到相关技能～"} />
                ) : (
                  <InfiniteScroll
                    dataLength={list ? list.length : 0}
                    next={activeTab < 0 ? getFlowList : getPublishFlowList}
                    hasMore={hasMore}
                    loader={<></>}
                    endMessage={<Divider plain>没有更多数据了</Divider>}
                    scrollableTarget={"agentDetailAddPluginModalList"}
                  >
                  {list?.map((item, index) => (
                    <FlowItem
                      key={index}
                      item={item}
                      flowIndex={index}
                      addedArr={addedArr}
                      blockType={blockType}
                      isDiscover={activeTab >= 0}
                      setChainId={setChainId}
                      setFlowStatus={setFlowStatus}
                      setFlowType={setFlowType}
                      setOpen={setOpen}
                      open={open}
                      teamId={teamId}
                      changeItem={async(action, checked) => {
                        // 添加 或者 取消添加
                        if(checked) {
                          const res = await reqFlowPluginDetail({
                            template_id: action.flow_plugin_id
                          })

                          // console.log(res.inputs_config.data.var_params)
                          
                          const flow_params_key_mapping:any = [];
                          if(res?.inputs_config?.var_params) {
                            res.inputs_config.var_params.forEach((p:any) => {
                              flow_params_key_mapping.push({
                                "source": [],
                                "target": p.name,
                                "desc": p.desc,
                                "required": p.required,
                                "type": p.type
                              })
                            })

                            console.log(flow_params_key_mapping)
                            // 添加
                            setChoosedFlowStamp(new Date().getTime())
                            setChoosedFlow({
                              ...action,
                              flow_plugin_name: action.title,
                              flow_params_key_mapping,
                              isAddBlock:  open.isAddBlock
                            })
                            handleCancel()
                          }
                        }
                      }}
                    />
                  ))}
                  </InfiniteScroll>
                )
              }
              </div>
            </div>
          }
        </div>
      </Modal>
    </>
  );
})

const FlowItem = (props: {
  item: any;
  isDiscover: boolean;
  flowIndex: number;
  changeItem: (item: any, checked:boolean) => void;
  addedArr: any;
  blockType: string;
  teamId: any;
  setChainId: (id:any) => void;
  setFlowStatus: (s:any) => void;
  setFlowType: (t:any) => void;
  setOpen: (t:any) => void;
  open: any;
}) => {
  const { flowIndex, addedArr, blockType, item, isDiscover, setChainId, setFlowStatus, setFlowType, open, setOpen, teamId } = props;
  const { title, desc, publish_time, images, id } = props.item;
  const [isHovered, setIsHovered] = useState(-1);
  const openItem = (item:any) => {
    if(item.version){
      window.open('flowDetailV3?id='+item.id+'&teamId='+teamId+'&version='+item.version+'&from=flow')
    }else{
      window.open('flowDetailV3?id='+item.id+'&teamId='+teamId+'&from=flow')
    }
  }
  return (
    <div 
      className={styles.agentDetailAddPluginModalPlugin} 
      onMouseEnter={() => setIsHovered(flowIndex)}
      onMouseLeave={() => setIsHovered(-1)}
    >
      <div className={styles.agentDetailAddPluginModalItem} onClick={() => {openItem(item)}}>
        <img src={images} className={styles.pluginIcon} />
        <div className={styles.pluginContent}>
          <div className={styles.pluginTitle}>{title}</div>
          <span className={styles.pluginDesc}>{desc}</span>
          <div className={styles.pluginDate}>
            发布时间：{publish_time ? publish_time.replace(/T/g, " ") : ""}
          </div>
        </div>
        {addedArr.includes(item.id) && !open.isAddBlock && (isHovered !== flowIndex) && (
          <div className={styles.pluginAdded}>
            <DoneIcon color="#4EC01E" />
            <span className={styles.pluginAddedText}>已添加</span>
          </div>
        )}
        {/* {addedArr.includes(item.id) && !(isHovered !== flowIndex) && (
          <div className={styles.pluginCancel} onClick={() => {props.changeItem(item, false)}}>
            <img src={minusBtn.src} width={16} />
            <span className={styles.pluginCancelText}>取消添加</span>
          </div>
        )} */}
        {addedArr.includes(item.id) && !open.isAddBlock && !(isHovered !== flowIndex) && (
          <div className={styles.pluginAdded}>
            <DoneIcon color="#4EC01E" />
            <span className={styles.pluginAddedText}>已添加</span>
          </div>
        )}
        {(!addedArr.includes(item.id) || open.isAddBlock) && !(isHovered !== flowIndex) && (
          !isDiscover ? <div className={styles.pluginAdd} onClick={(e:any) => {
            props.changeItem({...item, flow_plugin_id: item.id}, true);
            e.stopPropagation();
          }}>
            <img src={plusBtn.src} width={16} />
            <span className={styles.pluginAddText}>添加</span>
          </div> : <div className={styles.pluginAdd} style={{width: 90}} onClick={async(e) => {
              e.stopPropagation();
              const res = await reqCopyVersionFlow({
                template_id: item.id
              })

              if (res) {
                setChainId(res.id);
              }
              setFlowStatus("editable");
              // setTeamId(router.query.teamId);
              setFlowType('1')
              setOpen({
                open: false,
                isAddBlock: false
              });

              // props.changeItem({...item, template_id: item.id}, true)
            }}>
          <span className={styles.pluginAddText}>创建副本</span>
          <img src={arrowRightIcon.src} width={16} />
        </div>
        )}
      </div>
    </div>
  );
};

export default BlockAddFlow;
