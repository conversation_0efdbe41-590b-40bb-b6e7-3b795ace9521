import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import modelConfig from '@/images/modelConfig.svg';
import modelConfigDefault from '@/images/modelConfigDefault.svg';
import { prompt_authorization } from '@/constants/appConstants';
import modelIcon from './commons/modelIcon'
import arrowDown from '@/images/arrowDownV3.svg'
interface BlockLlmSelectProps {
  isWaiWang: boolean;
  waiwangLlmList: any[];
  llmList: any[];
  activeBlockId: string;
  blocks: any;
  setBlocks: (blocks: any) => void;
  teamId: string;
  isShowLlmParam: boolean;
  setIsShowLlmParam: React.Dispatch<React.SetStateAction<boolean>>;
  setChangeStamp: (stamp: number) => void;
  isShowIcon?: boolean;
  isFunctionCall?: boolean;
  type?: string
}

interface ModelIcon {
  [key: string]: string;
}

export default function BlockLlmSelect({
  isWaiWang,
  waiwangLlmList,
  llmList,
  activeBlockId,
  blocks,
  setBlocks,
  teamId,
  isShowLlmParam,
  setIsShowLlmParam,
  setChangeStamp,
  isShowIcon = true,
  isFunctionCall = false,
  type,
}: BlockLlmSelectProps) {
  const [modelIcons, setModelIcons] = useState<ModelIcon>({});
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const llmOptionsList = isWaiWang ? waiwangLlmList : llmList;

  const getModelValue = () => {
    // if(type == 'code' && !blocks[activeBlockId].llm_model) {
    //   const defaultLLM = llmOptionsList[0]['options'][0];
    //   return defaultLLM.label;
    // }
    const providerName = blocks[activeBlockId].provider_name || 'zhinao';
    const modelField = (isFunctionCall ? blocks[activeBlockId].model : blocks[activeBlockId].model_type) || '360gpt-pro';
    return `${providerName} ${modelField}`;
  };

  useEffect(() => {
    if(!blocks[activeBlockId].llm_model && type == 'code') {
      const defaultLLM = llmOptionsList[0]['options'][0];
      blocks[activeBlockId].provider_name = 'zhinao';
      blocks[activeBlockId].llm_model = '360gpt-pro';
    }
  }, [type])

  // 获取所有模型提供商的图标
  useEffect(() => {
    // 从modelIcon中获取已缓存的图标
    const iconCache: ModelIcon = {};
    Array.from(modelIcon.modelIconMap.keys()).forEach((provider) => {
      const cachedIcon = modelIcon.getModelIconCache(provider);
      if (cachedIcon) {
        iconCache[provider] = cachedIcon;
      }
    });
    setModelIcons(iconCache);
  }, [llmOptionsList]);

  // 获取当前选中的模型提供商
  const getCurrentProvider = () => {
    return blocks[activeBlockId].provider_name || 'zhinao';
  };

  const customSuffixIcon = (
    <img 
      src={arrowDown.src} 
      alt="arrow" 
      style={{ 
        transition: 'transform 500ms ease',
        transform: dropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)'
      }}
    />
  );

  return (
    <div className={flowStyles.modelSelectWrapper}>
      <Select
        style={{ width: '100%',maxWidth:'363px' }}
        options={llmOptionsList}
        value={getModelValue()}
        className={flowStyles.modelType}
        optionLabelProp="label"
        suffixIcon={customSuffixIcon}
        onDropdownVisibleChange={(open) => setDropdownOpen(open)}
        labelRender={(option: any) => {
          const provider = getCurrentProvider();
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {modelIcons[provider] && (
                <img 
                  src={modelIcons[provider]} 
                  alt={provider}
                  className={flowStyles.llmSelectIcon}
                />
              )}
              <span className={flowStyles.llmSelectText}>{option.value.split(' ')[1] || option.label || option.value}</span>
            </div>
          );
        }}
        optionRender={(option) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {modelIcons[option.data.provider] && (
              <img 
                src={modelIcons[option.data.provider]} 
                alt={option.data.provider}
                className={flowStyles.llmSelectIcon}
              />
            )}
            <span className={flowStyles.llmSelectText}>{option.label}</span>
          </div>
        )}
        onChange={async (v: string, data: any) => {
          const res = v.split(' ')
          if (res && res.length === 2) {
            if (isFunctionCall) {
              blocks[activeBlockId].model = res[1];
            } else {
              blocks[activeBlockId].model_type = res[1];
            }
            blocks[activeBlockId].provider_name = data.provider;
            blocks[activeBlockId].llm_model = data.label;
            blocks[activeBlockId].team_id = teamId;
            setBlocks(prev => ({...blocks}));
            setChangeStamp(new Date().getTime())
          }
        }}
      />
      {/* 添加llm-select-trigger这类名，排除在点击外部关闭组件的场景，因为这个是组件的显隐开关 */}
      {isShowIcon && <div className={flowStyles.modelTypeIcon + ' llm-select-trigger'} onClick={() => {
        setIsShowLlmParam(prev => !prev)
      }}>
        <img src={!isShowLlmParam ? modelConfigDefault.src : modelConfig.src} />
      </div>}
    </div>
  );
}
