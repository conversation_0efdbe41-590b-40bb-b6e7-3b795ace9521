import { useEffect, useState, useImper<PERSON><PERSON><PERSON><PERSON>, forwardRef } from "react";
import styles from "@/components/agent/detail/AgentDetailAddPluginModal.module.scss";
import { message, Button, Modal, Input, Divider } from "antd";
import { SearchOutlined, CloseOutlined } from "@ant-design/icons";
import MenuAllIcon from "@/icons/MenuAllIcon";
import SidebarButton from "@/components/agent/SidebarButton";
import DoneIcon from "../../../icons/DoneIcon";
import plusBtn from "@/images/plus.svg";
import minusBtn from "@/images/minus.png";
import InfiniteScroll from "react-infinite-scroll-component";
import AuthConfig from "@/components/common/AuthConfig";
import { agentDetailErrorAtom } from "@/atoms/agentDetailAtoms";
import {
    choosedApiAtom,
    choosedStampAtom,
    useApiStampAtom,
    fcSelectedFCApiListAtom,
    fcCurrentCheckFcApiAtom,
    fcUseApiStampAtom,
    fcChoose<PERSON>tamp<PERSON>tom,
} from '@/atoms/flowAtoms';

import apiStyles from '@/styles/Api.module.scss'
import { LeftOutlined } from '@ant-design/icons';
import apiModal from '@/images/apiModal.svg'
import { useRecoilState } from "recoil";
// import ApiDetail from "@/components/api/executeActionDetail/actionDetail";
import AgentListEmpty from "@/components/agent/detail/AgentListEmpty";
import { isNullOrEmpty } from "@/utils/common";
import expandArrow from '@/images/expandArrow.svg';
import shrinkArrow from '@/images/shrinkArrow.svg';

import { useRouter } from "next/router";

import {
  reqCheckPluginAuth,
  reqPluginAddAgentFlow,
  reqDeletePluginAgentFlow,
} from "@/service/api";
import { blockConnectorType, blockConnectorTypeApi, blockConnectorTypeApiBatch, blockFunctionCallType, blockMCPType, blockAIAgentType } from '@/config/newflow/flowConfig';
import { reqGetMCPTags, reqGetNmAgentList } from "@/service/flow3.0";

const BlockAddMCP = forwardRef((props: {
  open: boolean;
  setOpen: any;
  chainId: string;
  addedArr: any;
  blockType: string;
  blockId: string;
  blocks: any;
}, ref) => {
  const { open, setOpen, chainId, addedArr, blockType, blockId, blocks } = props;

  // 标签列表
  const [tags, setTags] = useState<any[]>([]);

  // 当前激活tab，默认全部(值为0)
  const [activeTab, setActiveTab] = useState(0);

  // MCP列表
  const [list, setList] = useState<any[]>([]);

  // 搜索关键字
  const [keyword, setKeyword] = useState<string>("");

  // 当前页
  const [page, setPage] = useState<number>(1);

  // 每页数量
  const [pageSize, setPageSize] = useState<number>(20);

  const [hasMore, setHasMore] = useState(true);

  const [agentDetailError, setAgentDetailError] =
    useRecoilState<any>(agentDetailErrorAtom);

  // 详情弹窗开关状态
  const [openPluginDetail, setOpenPluginDetail] = useState<boolean>(false);

  const [checkDetail, setCheckDetail] = useState<boolean>(false);
  // api 单选
  const [choosedApi, setChoosedApi] = useRecoilState(choosedApiAtom); // 当前选择的api
  const [useApiStamp, setUseApiStamp] = useRecoilState(useApiStampAtom); // 使用 按钮
  const [chooseStamp, setChooseStamp] = useRecoilState(choosedStampAtom); // 点击查看api详情

  // fc 多选
  const [fcSelectedFCApiList, setFcSelectedFCApiList] = useRecoilState<any>(fcSelectedFCApiListAtom); // 选择的api列表
  const [fcCurrentCheckFcApi, setFcCurrentCheckFcApi] = useRecoilState(fcCurrentCheckFcApiAtom);// 当前选择的api
  const [fcChooseStamp, setFcChooseStamp] = useRecoilState(fcChooseStampAtom); // 点击查看api详情
  const [fcUseApiStamp, setFcUseApiStamp] = useRecoilState(fcUseApiStampAtom) // 点击添加

  const [expandIndex, setExpandIndex] = useState(-1);

  // 授权 Modal
  const [openAuth, setOpenAuth] = useState(false);

  const [curPlugin, setCurPlugin] = useState(null);
  const [curAction, setCurAction] = useState(null);
  const [authId, setAuthId] = useState(-1);

  const [dataObj, setDataObj] = useState({
    0: []
  });

  const router = useRouter();
  const teamId = router.query.teamId;

  useImperativeHandle(ref, () => ({
    changePropelHandle: (value: number) => {}
  }));

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setKeyword(e.target.value);
  };

  const handleOk = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
    setKeyword(""); // 关闭弹框时清空搜索关键字
  };

  useEffect(() => {
    // getMCPTags();
    getMCPList();
    setCheckDetail(false);
  }, []);

  useEffect(()=>{
    setFcSelectedFCApiList(addedArr)
  },[blockId])

  useEffect(() => {
    if (keyword === '') {
      setList(dataObj[activeTab] || [])
    } else {
      let actList = dataObj[activeTab] || [];
      let curList = actList.filter(item => item.title.includes(keyword));
     setList(curList);
    }
  }, [keyword]);

  useEffect(() => {
    setKeyword('');
    let res = dataObj[activeTab];
    setList(res || []);
  }, [activeTab])

  useEffect(() => {
    setList([]);
    if (open) {
      setTimeout(async() => {
        await setActiveTab(0);
        setKeyword(""); // 打开弹框时清空搜索关键字
        // getMCPTags();
        getMCPList();
      }, 120);
    }
  }, [open])

  const getMCPList = async (pageS: number = page) => {
    const resp = await reqGetNmAgentList({});
    let list = resp?.list || [];
    let tagList: any = [
      {
        id: 0,
        name: "全部",
        desc: "全部",
      },
    ];
    let dataObjRes: any = {
      0: []
    };
    list.forEach((item => {
      if (item) {
        tagList.push({
          id: item.category,
          name: item.title,
          desc: item.intro
        });
        let curList = item.list.map((item: any) => {
          return {
            ...item,
            detail: item.intro,
            images: item.icon,
            name: item.title
          }
        })
        dataObjRes[item.category] = curList;
        dataObjRes[0] = [...dataObjRes[0], ...curList];
      }
    }));
    setTags(tagList);
    setDataObj(dataObjRes);
    setList(dataObjRes[0]);
    setHasMore(false);
    setActiveTab(0);
  };

  const getMCPTags = async () => {
    const resp = await reqGetMCPTags({
      tag_type: 7
    });
    
    // 确保resp是数组
    if (Array.isArray(resp)) {
      setTags([
        {
          id: 0,
          name: "全部",
          desc: "全部",
        },
        ...resp.map(tag => ({
          id: tag.id,
          name: tag.tag_name || tag.name,
          desc: tag.description || tag.desc || '',
        }))
      ]);
    } else {
      setTags([
        {
          id: 0,
          name: "全部",
          desc: "全部",
        }
      ]);
      console.error('获取到的标签不是数组格式', resp);
    }
  };

  const clickAuth = async(authId: number) => {
    // 删除当前插件动作的授权关联
    if (blocks[blockId] && blocks[blockId].is_auth === 1) {
      reqDeletePluginAgentFlow({
        api_id: blocks[blockId].api_pid,
        template_id: blocks[blockId].api_id,
        auth_id: blocks[blockId].auth_id,
        relation_id: +chainId,
        relation_type: 3,
        relation_block_id: +blockId
      })
    }

    const res = await reqPluginAddAgentFlow({
      api_id: (curPlugin as any)?.id,
      api_version: (curAction as any)?.publish_version,
      template_id: (curAction as any)?.api_template_id,
      auth_id: authId,
      relation_id: +chainId,
      relation_type: 3,
      relation_block_id: +blockId
    })
    if (!res) return;

    const resCurAction:any = curAction;
    const resAction = {
      ...resCurAction,
      is_public: resCurAction.public_status === 2,
      id: (curAction as any)?.api_template_id,
      api_version_id: (curAction as any)?.api_version_id,
      auth_id: authId,
      is_auth: 1
    }
    if(blockConnectorType.includes(blockType)) {
      setUseApiStamp(new Date().getTime())
      curAction && setChoosedApi(resAction)
      handleCancel()
    }else if(blockFunctionCallType.includes(blockType)) {
      setFcUseApiStamp(new Date().getTime())
      onCheckMCPChange(resAction, true)
    }
    message.success('添加成功')
  }

  const onCheckMCPChange = async(mcp:any, isChecked:boolean, connectorObj={id: ''}) => {
    const mcpId = mcp.id;
    
    if(!isChecked){
      // 删除授权
      const mcpObj = addedArr.filter((item:any) => item.api_id === mcpId)
      if(mcpObj.length && mcpObj[0].auth_id) {
        const deleteRes = await reqDeletePluginAgentFlow({
          api_id: connectorObj?.id, 
          template_id: mcpId,
          auth_id: mcpObj.length ? mcpObj[0].auth_id : '',
          relation_id: +chainId,
          relation_type: 3,
          relation_block_id: +blockId
        })

        if (!deleteRes) return;
      }
      // 取消添加
      setFcSelectedFCApiList((oldList:any) => {
        const newList = oldList.filter((item:any) => item.api_id !== mcpId);
        return newList;
      })
      message.success('取消成功')
    }else {
      // 添加MCP
      // 从mcp中提取正确的字段
      let mcp_config = {};
      try {
        if (typeof mcp.mcp_config === 'string') {
          mcp_config = JSON.parse(mcp.mcp_config);
        } else if (typeof mcp.mcp_config === 'object') {
          mcp_config = mcp.mcp_config;
        }
      } catch (error) {
        console.error('解析MCP配置出错:', error);
        mcp_config = {};
      }
      
      const newMcp = {
        api_id: mcpId, 
        name: mcp.name,
        api_pname: mcp.name, 
        api_pid: mcp.id, 
        is_auth: mcp.is_auth || 0,
        auth_id: mcp.auth_id || 0,
        description: mcp.description || mcp.detail || '',
        mcp_config: mcp_config,
        mcp_type: mcp.mcp_type || 1,
        tool_type: "mcp",
        images: mcp.images || '',
        ...mcp
      }
      
      // setFcSelectedFCApiList((oldList:any) => {
      //   const newList = [...oldList, newMcp];
      //   return newList;
      // })
      setFcSelectedFCApiList([newMcp]);
      
      message.success('添加成功')
    }
  }

  return (
    <>
      <Modal
        width={'60%'}
        className={checkDetail ? apiStyles.apiModal + ' commonModal noFooterModal apiModal' : styles.agentDetailAddPluginModalContainer}
        open={open}
        closeIcon={null}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        title={checkDetail ? <><LeftOutlined onClick={() => setCheckDetail(false)} /><img src={apiModal.src} width={24} className={apiStyles.apiTitleIconModal} /><span className={apiStyles.apiTitleNameModal}>{choosedApi.api_name}</span><span className={apiStyles.apiTitleDescModal}> {choosedApi.api_desc}</span></> : ''}
      >
        {!checkDetail ? <div className={styles.agentDetailAddPluginModalContent} style={{height: "calc(100vh - 165px)"}}>
          <div className={styles.agentDetailAddPluginModalSidebar}>
            <div className={styles.agentSidebarTitle}>MCPAgent</div>
            <div className={styles.agentDetailAddPluginModalLabel}>
              {tags.map((tag, index) => (
                <SidebarButton
                  key={index}
                  icon={MenuAllIcon({ color: "#1B2532" })}
                  selectedIcon={MenuAllIcon({ color: "#1D7CFF" })}
                  selected={activeTab === tag.id}
                  onClick={() => setActiveTab(tag.id)}
                  title={tag.name}
                />
              ))}
            </div>
          </div>
          {
            <div className={styles.agentDetailAddPluginModalInner}>
              <div className={styles.agentDetailAddPluginModalSearch}>
                <Input
                  style={{
                    width: "240px",
                    marginRight: "24px",
                  }}
                  placeholder="搜索MCPAgent名称"
                  onChange={onChange}
                  value={keyword}
                  prefix={<SearchOutlined style={{ color: "#9BA7BA" }} />}
                />
                <CloseOutlined onClick={handleCancel} />
              </div>
              <div
                className={styles.agentDetailAddPluginModalList}
                id="agentDetailAddPluginModalList"
              > {
                list && Array.isArray(list) && list.length === 0 ? (
                  <AgentListEmpty desc={"抱歉，没有找到相关MCPAgent～"} />
                ) : (
                  <InfiniteScroll
                  dataLength={Array.isArray(list) ? list.length : 0}
                  scrollThreshold={0.6}
                  next={getMCPList}
                  hasMore={hasMore}
                  loader={<></>}
                  endMessage={<Divider plain>没有更多数据了</Divider>}
                  scrollableTarget={"agentDetailAddPluginModalList"}
                >
                  {Array.isArray(list) && list.map((item, index) => (
                    <MCPItem
                      key={index}
                      item={item}
                      flowIndex={index}
                      addedArr={addedArr}
                      blockType={blockType}
                      expandIndex={expandIndex}
                      isDiscover={true}
                      clickExpand={() => {
                        setExpandIndex(index)
                      }}
                      clickShrink={() => {
                        if (expandIndex === index) {
                          setExpandIndex(-1)
                        }
                      }}
                      changeItem={async(mcp, checked) => {
                        console.error('纳米----', mcp, checked)
                        if(checked) {
                          // if (mcp.is_auth === 1) {
                          //   setAuthId(mcp.auth_id || -1)
                          //   setCurAction(mcp)
                          //   setCurPlugin({
                          //     ...item,
                          //     id: item.id
                          //   });
                          //   setOpenAuth(true);                            
                          // } else {
                          //   if(blockConnectorType.includes(blockType)) {
                          //     setUseApiStamp(new Date().getTime())
                          //     setChoosedApi({
                          //       ...mcp,
                          //       is_auth: 0,
                          //       auth_id: 0
                          //     })
                          //     handleCancel()
                          //   } else if(blockAIAgentType?.includes(blockType) || blockMCPType?.includes(blockType)) {
                          //     setFcUseApiStamp(new Date().getTime())
                          //     onCheckMCPChange({
                          //       ...mcp,
                          //       is_auth: 0,
                          //       auth_id: 0,
                          //     }, checked)
                          //   } else if(blockFunctionCallType.includes(blockType)) {
                          //     setFcUseApiStamp(new Date().getTime())
                          //     onCheckMCPChange({
                          //       ...mcp,
                          //       is_auth: 0,
                          //       auth_id: 0,
                          //     }, checked)
                          //   } else {
                          //     setFcUseApiStamp(new Date().getTime())
                          //     onCheckMCPChange({
                          //       ...mcp,
                          //       is_auth: 0,
                          //       auth_id: 0,
                          //     }, checked)
                          //   }
                          // }
                          setFcUseApiStamp(new Date().getTime())
                          onCheckMCPChange({
                            ...mcp,
                            is_auth: 0,
                            auth_id: 0,
                          }, checked)
                          handleCancel()
                        }else {
                          if(blockFunctionCallType.includes(blockType) || blockAIAgentType?.includes(blockType) || blockMCPType?.includes(blockType)) {
                            setFcUseApiStamp(new Date().getTime())
                            onCheckMCPChange(mcp, checked, item)
                          } else {
                            setFcUseApiStamp(new Date().getTime())
                            onCheckMCPChange(mcp, checked, item)
                          }
                        }
                      }}
                      checkItem={(mcp) => {
                        // 查看
                        setChoosedApi(mcp)
                        if(blockConnectorType.includes(blockType)){
                          setChoosedApi(mcp)
                          setFcChooseStamp(0)
                          setChooseStamp(new Date().getTime())
                        }else if(blockFunctionCallType.includes(blockType)) {
                          setFcCurrentCheckFcApi(mcp)
                          setFcChooseStamp(new Date().getTime())
                          setChooseStamp(0)
                        }
                        setCheckDetail(true)
                      }}
                    />
                  ))}
                  </InfiniteScroll>
                )
              }
              </div>
            </div>
          }
          </div> : ''
          // <ApiDetail
          //     showInModal={checkDetail}
          //     setOpenApi={setOpen}
          //   />
          }
      </Modal>
      <AuthConfig
        open={openAuth}
        setOpen={setOpenAuth}
        curPlugin={curPlugin}
        handleAuth={clickAuth}
        authId={authId}
      />
    </>
  );
})

const MCPItem = (props: {
  item: any;
  isDiscover: boolean;
  flowIndex: number,
  expandIndex: number,
  changeItem: (item: any, checked:boolean) => void;
  checkItem: (item: any) => void;
  clickExpand: () => void;
  clickShrink: () => void;
  addedArr: any;
  blockType: string;
}) => {
  const { flowIndex, expandIndex, addedArr, blockType, isDiscover } = props;
  const { name, description, detail, update_time, created_at, updated_at, public_time, publish_time, images, id } = props.item;
  const [isHovered, setIsHovered] = useState(false);
  const isExpand = expandIndex === flowIndex;

  // 处理描述
  const itemDescription = description || detail || '';
  
  // 处理时间
  const itemUpdateTime = update_time || updated_at || public_time || publish_time || created_at || '';
  const formattedTime = typeof itemUpdateTime === 'string' ? itemUpdateTime.replace(/T/g, " ") : '';
  
  // 简化MCP项目显示，没有子项目，直接在主项目上显示添加按钮
  return (
    <div className={styles.agentDetailAddPluginModalPlugin}>
      <div 
        className={styles.agentDetailAddPluginModalItem} 
        onMouseEnter={() => {
          setIsHovered(true);
        }}
        onMouseLeave={() => {
          setIsHovered(false);
        }}
      >
        {images ? <img src={images} className={styles.pluginIcon} /> : 
        <div className={styles.pluginIcon} style={{background: '#E5EFFF', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#1D7CFF', fontSize: '18px'}}>MCP</div>}
        <div className={styles.pluginContent} onClick={props.clickExpand}>
          <div className={styles.pluginTitle}>{name}</div>
          <span className={styles.pluginDesc}>{itemDescription || '暂无描述'}</span>
          <div className={styles.pluginDate}>
            发布时间：{formattedTime || '暂无'}
          </div>
        </div>
        
        <div className={styles.actionButtons}>
          {blockConnectorType.includes(blockType) ? <>
            {!addedArr.includes(id) && isHovered && (
              <div className={styles.pluginAdd} onClick={(e) => {
                e.stopPropagation(); 
                props.changeItem(props.item, true);
              }}>
                <img src={plusBtn.src} width={16} />
                <span className={styles.pluginAddText}>使用</span>
              </div>
            )}
            {addedArr.includes(id) && (
              <div className={styles.pluginAdded}>
                <DoneIcon color="#4EC01E" />
                <span className={styles.pluginAddedText}>已使用</span>
              </div>
            )}
          </> : <>
            {addedArr.map((item:any) => item.api_id).includes(id) && !isHovered && (
              <div className={styles.pluginAdded}>
                <DoneIcon color="#4EC01E" />
                <span className={styles.pluginAddedText}>已添加</span>
              </div>
            )}
            {addedArr.map((item:any) => item.api_id).includes(id) && isHovered && (
              <div className={styles.pluginCancel} onClick={(e) => {
                e.stopPropagation(); 
                props.changeItem(props.item, false);
              }}>
                <img src={minusBtn.src} width={16} />
                <span className={styles.pluginCancelText}>取消添加</span>
              </div>
            )}
            {!addedArr.map((item:any) => item.api_id).includes(id) && isHovered && (
              <div className={styles.pluginAdd} onClick={(e) => {
                e.stopPropagation(); 
                props.changeItem(props.item, true);
              }}>
                <img src={plusBtn.src} width={16} />
                <span className={styles.pluginAddText}>添加</span>
              </div>
            )}
          </> }
        </div>
      </div>
    </div>
  );
};

export default BlockAddMCP;
