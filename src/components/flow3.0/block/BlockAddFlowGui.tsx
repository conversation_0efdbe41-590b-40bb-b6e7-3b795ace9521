import { useState, useEffect } from "react";
import {
  Input,
  But<PERSON>,
  Modal,
} from "antd";
import flowStyles from "@/styles/flow3.0/Flow.module.scss";
import styles from "@/components/agent/detail/AgentDetailAddPluginModal.module.scss";
import SidebarButton from "@/components/agent/SidebarButton";
import MenuAllIcon from "@/icons/MenuAllIcon";
import MenuPersonIcon from "@/icons/MenuPersonIcon";
import { SearchOutlined, CloseOutlined } from "@ant-design/icons";
import AgentListEmpty from "@/components/agent/detail/AgentListEmpty";

import { reqGuiCardList } from '@/service/api';

import { useRecoilState } from "recoil";

import {
  choosedCardAtom,
  choosedCardStampAtom
} from '@/atoms/flowAtoms';
import { useRouter } from "next/router";
export default function BlockAddFlowGui(props: any) {
  let { open, setOpen } = props;

  const [listData, setListData] = useState(new Array())
  const router = useRouter();
  // 标签列表
  const [tags, setTags] = useState<any[]>([
    {
      id: 0,
      name: "全部",
      desc: "全部",
    },
    {
      id: 1,
      name: "推荐",
      desc: "推荐",
    },
    {
      id: 2,
      name: "智能阅读",
      desc: "智能阅读",
    },
    {
      id: 3,
      name: "写作",
      desc: "写作",
    },
    {
      id: 4,
      name: "设计",
      desc: "设计",
    },
    {
      id: 5,
      name: "排版",
      desc: "排版",
    },
    {
      id: 6,
      name: "视频编导",
      desc: "视频编导",
    },
    {
      id: 7,
      name: "动漫",
      desc: "动漫",
    },
  ]);
  // 当前激活tab，默认我的(值为-1)
  const [activeTab, setActiveTab] = useState<number>(-1);
  // 搜索关键字
  const [keyword, setKeyword] = useState<string>("");
  // 列表
  const [list, setList] = useState(listData);

  // gui card 单选
  const [choosedCard, setChoosedCard] = useRecoilState(choosedCardAtom); // 当前选择的card
  const [choosedCardStamp, setChoosedCardStamp] = useRecoilState(choosedCardStampAtom);

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setKeyword(e.target.value);
  };
  const handleCancel = () => {
    setOpen({
      open: false,
      isAddBlock: false
    });
  };

  useEffect(() => {
    if (keyword) {
      let res = listData.filter((item: any) => {
        return item.name.includes(keyword);
      })
      setList(res);
    } else {
      setList(listData);
    }
  }, [keyword,listData]);

  useEffect(() => {
    if(open){
      getCardList();
    }
  }, [open])

  const getCardList = async() => {
    const res = await reqGuiCardList({
        page: 1,
        page_size: 100,
        keyword: '',
        user_id: 0
    });
    if(res?.list){
      setListData(res?.list.map((item:any) => {
          return {
              ...item,
              imgUrl: item.image,
              name: item.name,
              card_id: item.id,
              card_data: item.data_desc
          }
      }))
    }
}

  return (
    <Modal
      title={""}
      closeIcon={null}
      footer={null}
      style={{ top: 100 }}
      open={open.open}
      onOk={() => setOpen({
        open: false,
        isAddBlock: false
      })}
      onCancel={() => setOpen({
        open: false,
        isAddBlock: false
      })}
      width={"60%"}
      className={styles.agentDetailAddPluginModalContainer}
    >
      <div
        className={styles.agentDetailAddPluginModalContent}
        style={{ height: "calc(100vh - 200px)" }}
      >
        <div className={styles.agentDetailAddPluginModalSidebar}>
          <div className={styles.agentSidebarTitle}>GUI卡片</div>
          <Button
            type="primary"
            style={{ width: "100%", height: "32px" }}
            onClick={() => {
              let {teamId} = router?.query;
              // 跳转到gui添加页面
              if (teamId) {
                window.open(`/projectList?tab=4&isAddGuiModalOpen=true&teamId=${teamId}`);
              } else {
                window.open("/projectList?tab=4&isAddGuiModalOpen=true");
              }
            }}
          >
            创建GUI卡片
          </Button>
          <div style={{ height: "20px" }}></div>
          <SidebarButton
            icon={MenuPersonIcon({ color: "#1B2532" })}
            selectedIcon={MenuPersonIcon({ color: "#1D7CFF" })}
            selected={activeTab === -1}
            onClick={() => setActiveTab(-1)}
            showIcon={true}
            title="我创建的"
          />
          <div style={{ height: "20px" }}></div>
          <div style={{ height: "1px", background: "#EBECF0" }}></div>
          <div style={{ height: "20px" }}></div>
          <div className={styles.pluginDiscover}>发现</div>
          <div style={{ height: "12px" }}></div>
          <div
            className={styles.agentDetailAddPluginModalLabel}
            style={{ height: "calc(100% - 220px)" }}
          >
            {tags.map((tag, index) => (
              <SidebarButton
                key={index}
                icon={MenuAllIcon({ color: "#1B2532" })}
                selectedIcon={MenuAllIcon({ color: "#1D7CFF" })}
                selected={activeTab === tag.id}
                onClick={() => setActiveTab(tag.id)}
                title={tag.name}
              />
            ))}
          </div>
        </div>

        <div className={styles.agentDetailAddPluginModalInner}>
          <div className={styles.agentDetailAddPluginModalSearch}>
            <Input
              style={{
                width: "240px",
                marginRight: "24px",
              }}
              placeholder="搜索"
              onChange={onChange}
              prefix={<SearchOutlined style={{ color: "#9BA7BA" }} />}
            />
            <CloseOutlined onClick={handleCancel} />
          </div>
            {list && list.length === 0 ? (
              <AgentListEmpty desc={"抱歉，没有找到相关卡片～"} />
            ) : (
              <div
                className={
                  flowStyles.guiCardListBox +
                  " grid grid-cols-3 gap-4 mt-5"
                }
                id="agentDetailAddPluginModalList"
              >
                {list?.map((item: any, index: any) => (
                  <>
                    <div
                      className={
                        "bg-white rounded-lg shadow-md hover:shadow-lg " +
                        flowStyles.guiCardBox
                      }
                    >
                      <div
                        style={{
                          width: "100%",
                          height: "200px",
                        }}
                      >
                        <img
                          src={item.imgUrl}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "contain",
                          }}
                        />
                      </div>
                      <div
                        className="flex items-center justify-start pl-4 pr-4 mt-1"
                        style={{
                          height: "30px",
                          borderRadius: "8px",
                          fontWeight: 700,
                        }}
                      >
                        {item.name}
                      </div>

                      <Button
                        className={flowStyles.guiCardBoxButton}
                        type="primary"
                        onClick={() => {
                          // 添加
                          setChoosedCardStamp(new Date().getTime())
                          console.log(item)
                          setChoosedCard({
                            "card_url": item.url,
                            "isAddBlock": open.isAddBlock,
                            "card_name": item.name,
                            "card_image": item.imgUrl,
                            "card_data": JSON.parse(item.card_data),
                            "card_id": item.card_id
                          })

                          setOpen({
                            open: false,
                            isAddBlock: false
                          });
                        }}
                      >
                        添加
                      </Button>
                    </div>
                  </>
                ))}
              </div>
            )}
          </div>
      </div>
    </Modal>
  );
}
