import { useState, useEffect, useRef } from 'react';
import { Tooltip, Input, Collapse, Divider, Button, Popover } from 'antd';
import { Editor } from 'slate'
import { QuestionCircleOutlined } from '@ant-design/icons';
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import fullScreenIcon from '@/images/fullScreenIcon.svg';
import starIcon from '@/images/flow3.0/star.svg';
import cikuIcon from '@/images/flow3.0/ciku.svg';
import historyIcon from '@/images/flow3.0/history.svg';
import extendIcon from '@/images/flow3.0/extend.svg';
import shrinkIcon from '@/images/flow3.0/shrink.svg';
const { TextArea } = Input;
const { Panel } = Collapse;
import PromptBeautifyModal from './promptModal/PromptBeautifyModal'
import PromptLibraryModal from './promptModal/PromptLibraryModal';
import PromptHistoryModal from './promptModal/PromptHistoryModal';
import MentionEditor from './MentionEditor';
import { slateToHtml } from '@/utils/slateToHtml';
import styles from './styles/BlockPromt.module.scss'

interface BlockPromptProps {
	isFullScreen: boolean;
	setIsFullScreen: (value: boolean) => void;
	title: string;
	tooltipText: string;
	hideWhen?: boolean;
	value?: string;
	otherFullScreenActive?: boolean;
	showTooltip?: boolean;
	inputs?: string[];
	blocks: any;
	activeBlockId: string;
	flowInfo?: any;
	loopConfigs: any;
	resizeWidth: () => void;
	setBlocks: (value: any) => void;
	borderTop?: boolean;
	borderBottom?: boolean;
}

export default function BlockPrompt({
  isFullScreen,
  setIsFullScreen,
  title,
  tooltipText,
  hideWhen = false,
  value,
  otherFullScreenActive = false,
  showTooltip = true,
  inputs = [],
	blocks,
	activeBlockId,
	loopConfigs,
	flowInfo,
	resizeWidth,
	setBlocks,
	borderTop = true,
	borderBottom = false,
}: BlockPromptProps) {
	const [ libraryModalOpen, setLibraryModalOpen ] = useState(false)
	const [ historyModalOpen, setHistoryModalOpen ] = useState(false)
	const [ activeKey, setActiveKey ] = useState(['1']);
	const editorRef = useRef(null)
	const promptWrapRef = useRef(null)
	const [ promptWrapWidth, setPromptWrapWidth ] = useState(0)

  const extraContent = (
		<div className={flowStyles.collapseExtra}>
			{/* <Tooltip title='按‘{’ 或 ‘/’键插入变量'>
				<div className={flowStyles.collapseExtraTip}>
					<svg className={flowStyles.collapseExtraTipIcon} xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
						<g clip-path="url(#clip0_21965_31188)">
							<path d="M4.9082 4.42188H5.7959C6.36862 4.42202 6.87231 4.82241 7.0293 5.40234L7.26855 6.28516L7.30176 6.4082L7.38184 6.30957L8.32129 5.15625C8.50824 4.92601 8.74058 4.74087 9.00195 4.61426C9.26339 4.48765 9.54818 4.42189 9.83594 4.42188H9.89355C10.0438 4.42199 10.1887 4.48532 10.2959 4.59766C10.4031 4.71017 10.4639 4.86318 10.4639 5.02344C10.4639 5.18369 10.4031 5.33671 10.2959 5.44922C10.1887 5.56155 10.0438 5.62488 9.89355 5.625H9.83594C9.58653 5.625 9.35001 5.73969 9.18848 5.93848L7.69922 7.76855L7.67871 7.79395L7.6875 7.82617L8.15723 9.5625V9.56348C8.16493 9.59365 8.18259 9.6208 8.20703 9.64062C8.21919 9.65048 8.23253 9.65875 8.24707 9.66406L8.29395 9.67188H9.18164C9.33185 9.67201 9.47679 9.73534 9.58398 9.84766C9.69118 9.96016 9.75195 10.1132 9.75195 10.2734C9.75195 10.4337 9.69118 10.5867 9.58398 10.6992C9.47679 10.8115 9.33185 10.8749 9.18164 10.875H8.29395C8.01556 10.8747 7.74408 10.7791 7.52148 10.6025C7.29885 10.4259 7.13688 10.1772 7.06055 9.89453L6.82031 9.01172L6.78711 8.88867L6.70703 8.9873L5.76855 10.1406C5.58161 10.3709 5.34925 10.556 5.08789 10.6826C4.82645 10.8092 4.54166 10.875 4.25391 10.875H4.19629C4.04604 10.8749 3.90117 10.8116 3.79395 10.6992C3.68672 10.5867 3.62598 10.4337 3.62598 10.2734C3.62598 10.1132 3.68672 9.96017 3.79395 9.84766C3.90117 9.73531 4.04604 9.67198 4.19629 9.67188H4.25391C4.50273 9.67188 4.73926 9.55704 4.90137 9.3584L6.39062 7.52832L6.41113 7.50293L6.40234 7.4707L5.93164 5.7334H5.93262C5.92492 5.70324 5.90724 5.67607 5.88281 5.65625C5.87042 5.6462 5.85664 5.63814 5.8418 5.63281L5.7959 5.625H4.9082C4.75798 5.62488 4.61307 5.56155 4.50586 5.44922C4.39865 5.33671 4.33789 5.18369 4.33789 5.02344C4.33789 4.86318 4.39865 4.71017 4.50586 4.59766C4.61307 4.48533 4.75798 4.422 4.9082 4.42188ZM0.792969 8.28516L0.774414 8.2666L0.233398 7.69922C0.126214 7.58671 0.0654297 7.43367 0.0654297 7.27344C0.0654297 7.11321 0.126214 6.96016 0.233398 6.84766L0.774414 6.28027L0.792969 6.26172V3.46387C0.792915 3.18442 0.8453 2.90746 0.947266 2.64941C1.04925 2.39146 1.19857 2.1571 1.38672 1.95996C1.57493 1.76278 1.79844 1.60651 2.04395 1.5C2.28928 1.3936 2.55206 1.33885 2.81738 1.33887H2.81836C2.96857 1.339 3.11351 1.40135 3.2207 1.51367C3.3281 1.62621 3.38867 1.78003 3.38867 1.94043C3.38859 2.10064 3.32793 2.25376 3.2207 2.36621C3.11351 2.47853 2.96857 2.54088 2.81836 2.54102C2.32703 2.54102 1.93469 2.95744 1.93457 3.46387V6.51172C1.93446 6.67201 1.87298 6.82505 1.76562 6.9375L1.48828 7.22852L1.44531 7.27344L1.48828 7.31836L1.76562 7.60938C1.87298 7.72182 1.93446 7.87486 1.93457 8.03516V11.083C1.93469 11.5902 2.32773 12.0059 2.81836 12.0059C2.96857 12.006 3.11351 12.0683 3.2207 12.1807C3.32793 12.2931 3.38859 12.4462 3.38867 12.6064C3.38867 12.7668 3.3281 12.9207 3.2207 13.0332C3.11351 13.1455 2.96857 13.2079 2.81836 13.208H2.81738C2.55206 13.208 2.28928 13.1533 2.04395 13.0469C1.79844 12.9404 1.57493 12.7841 1.38672 12.5869C1.19857 12.3898 1.04925 12.1554 0.947266 11.8975C0.8453 11.6394 0.792915 11.3625 0.792969 11.083V8.28516Z" fill="currentColor" stroke="white" stroke-width="0.130889"/>
							<path d="M11.1836 1.33887C11.4489 1.33875 11.7117 1.39367 11.957 1.5C12.2026 1.60648 12.4269 1.76273 12.6152 1.95996C12.8034 2.15711 12.9527 2.39144 13.0547 2.64941C13.1567 2.9075 13.209 3.18436 13.209 3.46387V6.26172L13.2275 6.28027L13.7676 6.84766C13.875 6.96019 13.9365 7.11306 13.9365 7.27344C13.9365 7.43382 13.875 7.58668 13.7676 7.69922L13.2275 8.2666L13.209 8.28516V11.083C13.2089 11.6475 12.9953 12.1886 12.6152 12.5869C12.2352 12.9852 11.7197 13.208 11.1836 13.208C11.0333 13.208 10.8885 13.1455 10.7812 13.0332C10.6738 12.9207 10.6133 12.7669 10.6133 12.6064C10.6134 12.4462 10.6739 12.2931 10.7812 12.1807C10.8885 12.0683 11.0333 12.0059 11.1836 12.0059C11.6743 12.0059 12.0673 11.5895 12.0674 11.083V8.03516C12.0675 7.87498 12.1282 7.7218 12.2354 7.60938L12.5137 7.31836L12.5566 7.27344L12.5137 7.22852L12.2354 6.9375C12.1282 6.82508 12.0675 6.67189 12.0674 6.51172V3.46387C12.0673 2.95687 11.6753 2.54127 11.1836 2.54102C11.0332 2.54102 10.8876 2.47865 10.7803 2.36621C10.6731 2.25376 10.6124 2.10063 10.6123 1.94043C10.6123 1.78003 10.6729 1.62621 10.7803 1.51367C10.8876 1.40123 11.0332 1.33887 11.1836 1.33887Z" fill="currentColor" stroke="white" stroke-width="0.130889" />
						</g>
						<defs>
							<clipPath id="clip0_21965_31188">
								<rect width="14" height="14" fill="white"/>
							</clipPath>
						</defs>
					</svg>
					<span className={flowStyles.collapseExtraTipText}>变量</span>
				</div>
			</Tooltip> */}
			{/* <Divider size="middle" type="vertical" /> */}
			{/* <div className={flowStyles.collapseExtraBtn}>
				<img 
					src={fullScreenIcon.src}
					width={14}
					height={14}
					className={flowStyles.fullscreenBtn}
					onClick={(e) => {
						e.stopPropagation();
						setIsFullScreen(!isFullScreen);
					}}
				/>
			</div> */}
		</div>
  );

	// 处理面板切换事件
  const handleChange = (keys: string[]) => {
    setActiveKey(keys); // 直接更新 activeKey 为新的数组
  }

	//处理提示词内容变化
	const handlePromptChange = (value) => {
		//获取编辑器里面的html
		// const html = document.querySelector('[data-slate-editor]').innerHTML;
		let user = slateToHtml(value);
		setBlocks(prev => ({
			...blocks,
			[activeBlockId]: {
				...blocks[activeBlockId],
				prompts: {
					system: '',
					user: user,
					user_params: value
				}
			}
		}));
	};

	useEffect(() => {
		if(!activeKey.length) {
			setIsFullScreen(false)
		}
		setPromptWrapWidth(promptWrapRef.current?.clientWidth)
	}, [ activeKey ])

	// 全屏时，获取promptWrap的宽度
	useEffect(() => {
		setTimeout(() => {
			setPromptWrapWidth(promptWrapRef.current?.clientWidth)
		}, 0);
	}, [ isFullScreen ])
	
	
  return (
    <div ref={promptWrapRef}  className={[flowStyles.blockContainerWrapper, styles.container, borderTop && flowStyles.borderTop, borderBottom && flowStyles.borderBottom].filter(Boolean).join(' ')} style={{ paddingBottom: isFullScreen? 0 : '' }}  id="block-prompt">
      <Collapse activeKey={activeKey} onChange={handleChange} ghost className={flowStyles.collapseMaskWrapper}>
        <Panel 
          header={
            <div className={flowStyles.collapseMaskTitle}>
								<div className={flowStyles.collapseTitle}>{title}</div>
						</div>
          } 
          key="1" 
          extra={
						activeKey.includes('1') && (
							<div className={flowStyles.collapseExtra} onClick={e => e.stopPropagation()}>
								<PromptBeautifyModal flowInfo={flowInfo} curBlock={blocks[activeBlockId]} containerWidth={promptWrapWidth} handleChange={(value) => {
									editorRef.current.handleReset(value)
								}}>
									<Button onClick={(e) => { e.stopPropagation() }} color="primary" variant="outlined" className={flowStyles.collapseExtraBtn}>
										<img className={flowStyles.collapseExtraBtnIcon} src={starIcon.src} alt="" />
										AI优化
									</Button>
								</PromptBeautifyModal>
								<Button color="primary" variant="outlined" className={flowStyles.collapseExtraBtn} onClick={(e) => { e.stopPropagation(); setHistoryModalOpen(true);}}>
									<img className={flowStyles.collapseExtraBtnIcon} src={historyIcon.src} alt="" />
									历史记录
								</Button>
								<Popover
									overlayClassName={styles.popover}
									trigger="hover"
									content={<div style={{ padding: "2px 10px" }}>{isFullScreen? "收起" : "展开"}</div>}
								>
									<div 
										className={styles.fullScreenBtn}
										onClick={(e) => {
											e.stopPropagation();
											setIsFullScreen(!isFullScreen);
										}}
									>
									  {
											isFullScreen ?
											<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
												<g clip-path="url(#clip0_23224_121379)">
													<path fill-rule="evenodd" clip-rule="evenodd" d="M12.527 1.43977C12.6985 1.61001 12.6995 1.88701 12.5292 2.05849L8.30984 6.30848C8.1396 6.47996 7.86259 6.48096 7.69112 6.31072C7.51965 6.14049 7.51865 5.86348 7.68889 5.69201L11.9083 1.44201C12.0785 1.27054 12.3555 1.26954 12.527 1.43977Z" fill="currentColor"/>
													<path fill-rule="evenodd" clip-rule="evenodd" d="M7.96875 1.81254C8.21037 1.81254 8.40625 2.00842 8.40625 2.25004V5.56254H11.7188C11.9604 5.56254 12.1562 5.75842 12.1562 6.00004C12.1562 6.24167 11.9604 6.43754 11.7188 6.43754H7.96875C7.72713 6.43754 7.53125 6.24167 7.53125 6.00004V2.25004C7.53125 2.00842 7.72713 1.81254 7.96875 1.81254Z" fill="currentColor"/>
													<path fill-rule="evenodd" clip-rule="evenodd" d="M6.34013 7.63288C6.5116 7.80312 6.5126 8.08013 6.34236 8.2516L2.12298 12.5016C1.95274 12.6731 1.67573 12.6741 1.50426 12.5038C1.33279 12.3336 1.33179 12.0566 1.50202 11.8851L5.72141 7.63512C5.89165 7.46365 6.16865 7.46265 6.34013 7.63288Z" fill="currentColor"/>
													<path fill-rule="evenodd" clip-rule="evenodd" d="M1.8737 7.94336C1.8737 7.70173 2.06957 7.50586 2.3112 7.50586H6.0612C6.30282 7.50586 6.4987 7.70173 6.4987 7.94336V11.6934C6.4987 11.935 6.30282 12.1309 6.0612 12.1309C5.81957 12.1309 5.6237 11.935 5.6237 11.6934V8.38086H2.3112C2.06957 8.38086 1.8737 8.18498 1.8737 7.94336Z" fill="currentColor"/>
												</g>
												<defs>
												<clipPath id="clip0_23224_121379">
													<rect width="14" height="14" fill="currentColor"/>
												</clipPath>
												</defs>
											</svg>
											:
											<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path fill-rule="evenodd" clip-rule="evenodd" d="M6.31043 7.69167C6.48069 7.86311 6.47973 8.14012 6.30829 8.31039L2.05829 12.5311C1.88684 12.7014 1.60984 12.7004 1.43957 12.529C1.26931 12.3575 1.27027 12.0805 1.44171 11.9103L5.69171 7.68953C5.86316 7.51927 6.14016 7.52023 6.31043 7.69167Z" fill="currentColor"/>
												<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5289 1.43952C12.7004 1.60976 12.7014 1.88677 12.5312 2.05824L8.31178 6.30824C8.14154 6.47971 7.86453 6.48071 7.69306 6.31048C7.52159 6.14024 7.52059 5.86323 7.69083 5.69176L11.9102 1.44176C12.0804 1.27029 12.3575 1.26929 12.5289 1.43952Z" fill="currentColor"/>
												<path fill-rule="evenodd" clip-rule="evenodd" d="M8.0625 1.75C8.0625 1.50838 8.25838 1.3125 8.5 1.3125H12.25C12.4916 1.3125 12.6875 1.50838 12.6875 1.75V5.5C12.6875 5.74162 12.4916 5.9375 12.25 5.9375C12.0084 5.9375 11.8125 5.74162 11.8125 5.5V2.1875H8.5C8.25838 2.1875 8.0625 1.99162 8.0625 1.75Z" fill="currentColor"/>
												<path fill-rule="evenodd" clip-rule="evenodd" d="M1.75 8.0625C1.99162 8.0625 2.1875 8.25838 2.1875 8.5V11.8125H5.5C5.74162 11.8125 5.9375 12.0084 5.9375 12.25C5.9375 12.4916 5.74162 12.6875 5.5 12.6875H1.75C1.50838 12.6875 1.3125 12.4916 1.3125 12.25V8.5C1.3125 8.25838 1.50838 8.0625 1.75 8.0625Z" fill="currentColor"/>
											</svg>
										}

									</div>
								</Popover>
							</div>
						)
					}
        >
          <div
            style={isFullScreen ? { height: "calc(100vh - 356px)" } : { height: '280px' }}
					>
						<MentionEditor
							ref={editorRef}
							isFullScreen={isFullScreen}
							setIsFullScreen={setIsFullScreen}
							blocks={blocks}
							loopConfigs={loopConfigs}
							activeBlockId={activeBlockId}
							onChange={handlePromptChange}
							resizeWidth={resizeWidth}
						/>
          </div>
        </Panel>
      </Collapse>
			{/* <PromptLibraryModal 
				open={libraryModalOpen}
				setOpen={setLibraryModalOpen}
				handleInsert={(value) => {
					console.log(`handleInsert: ${value}`)
					onChange(value)
				}}
			/> */}
			<PromptHistoryModal
				open={historyModalOpen}
				setOpen={setHistoryModalOpen}
				blocks={blocks}
				activeBlockId={activeBlockId}
				handleInsert={(value) => {
					handlePromptChange(value)
					editorRef.current.handleReset(value)
				}}
			/>
    </div>
  );
}
