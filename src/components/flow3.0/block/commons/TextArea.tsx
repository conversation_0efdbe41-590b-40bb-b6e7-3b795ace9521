import React, { useRef, useState, useEffect } from 'react';
import { Input, Dropdown } from 'antd';
import getCaretCoordinates from 'textarea-caret';


const TextAreaWithDropdown: React.FC<{ 
  inputs: string[], 
  autoSize: any, 
  onChange: (value: string) => void, 
  onBlur: () => void, 
  value: string,
  disabled?: boolean
}> = ({ inputs = [], autoSize, onChange, onBlur, value: initialValue, disabled = false }) => {
  const [value, setValue] = useState(initialValue);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const textAreaRef = useRef<any>(null);

  // 计算弹窗位置
  const getPos = () => {
    
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    // 获取当前光标位置（从 0 开始的索引）
    const cursorPosition = textarea.selectionStart;
    // 获取光标坐标（x, y 位置）
    const coordinates = getCaretCoordinates(textarea, cursorPosition);

    // // 计算弹窗位置
    const rect = textarea.getBoundingClientRect();
    const textareaStyle = window.getComputedStyle(textarea);
    const lineHeightPx = parseInt(textareaStyle.lineHeight);

    return { top: coordinates.top + rect.top + lineHeightPx, left: rect.left + 5 }
  }

  // 检查是否输入了 {{
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
    onChange(e.target.value);
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 排除删除键的情况
    if (e.key === 'Delete' ) {
      setDropdownVisible(false);
      return;
    }

    const textarea = e.currentTarget;
    const cursorPos = textarea.selectionStart;
    const text = textarea.value;
    // 检查光标前两个字符是否为 { 或 {} 或 {{ 显示下拉框
    if (text.slice(cursorPos - 2, cursorPos) === '{{' || text.slice(cursorPos - 2, cursorPos) === '{}' || text.slice(cursorPos - 2, cursorPos) === '{') {
      // 计算光标在文本中的位置
      const pos = getPos();

      setDropdownPosition(pos);
      setDropdownVisible(true);
    } else {
      setDropdownVisible(false);
    }
  };

  // 插入变量
  const handleMenuClick = ({ key }: { key: string }) => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;
    
    const cursorPos = textarea.selectionStart;
    const text = textarea.value;
    const beforeCursor = text.slice(0, cursorPos);
    const afterCursor = text.slice(cursorPos);
    
    // 根据不同的触发情况处理
    let newValue = '';
    if (beforeCursor.endsWith('{{')) {
      // 如果已经输入了 {{，直接添加变量和 }}
      newValue = beforeCursor + key + '}}' + afterCursor;
    } else if (beforeCursor.endsWith('{')) {
      // 如果只输入了 {，添加{ 变量 }}
      newValue = beforeCursor + '{' + key + '}}' + afterCursor;
    } else if (beforeCursor.endsWith('{}')) {
      // 如果输入了 {}，在中间添加{变量}
      newValue = beforeCursor.slice(0, -1) + '{' + key + '}}' + afterCursor;
    }
    
    setValue(newValue);
    setDropdownVisible(false);
    
    // 恢复光标位置
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = cursorPos + key.length + (beforeCursor.endsWith('{{') ? 2 : 1);
      textarea.selectionStart = textarea.selectionEnd = newCursorPos;
    }, 0);
  };


  // 新的 menu 配置
  const dropdownMenu = {
    items: inputs.map(item => ({ 
      key: item, 
      label: <span className="block w-full overflow-hidden text-ellipsis whitespace-nowrap max-w-[350px]">{item}</span> 
    })),
    onClick: handleMenuClick,
  };


  
  useEffect(() => {
    if (dropdownVisible) {
      const handleResize = () => {
        const textarea = textAreaRef.current?.resizableTextArea?.textArea;
        if (!textarea) return {};

        const pos = getPos();
        setDropdownPosition(pos);
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [dropdownVisible]);

  return (
    <div style={{ position: 'relative', height: '100%' }}>
      <Input.TextArea
        disabled={disabled}
        autoSize={autoSize}
        ref={textAreaRef}
        value={value}
        onChange={handleChange}
        onKeyUp={handleKeyUp}
        onBlur={() => {
          onBlur()
        }}
        rows={4}
				style={{ height: '270px' }}
        placeholder="在这里写你的提示词，输入‘{{}}’ 或‘/’插入变量"
      />
      {dropdownVisible && inputs.length > 0 && (
        <div
          data-sss="sss"
          style={{
            position: 'fixed',
            top: dropdownPosition.top,
            left: dropdownPosition.left,
            zIndex: 1000,
          }}
        >
          <Dropdown 
            menu={dropdownMenu} 
            open={true} 
            trigger={[]} 
            overlayStyle={{ boxShadow: '0 2px 8px rgba(0,0,0,0.15)',  borderRadius: 4,  minWidth: 200, maxWidth: 400, overflowX: 'hidden' }}>
            <div></div>
          </Dropdown>
        </div>
      )}
    </div>
  );
};

export default TextAreaWithDropdown;
