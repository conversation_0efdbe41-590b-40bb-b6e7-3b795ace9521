import { Transforms } from 'slate'

const Paragraph = {
  headingNode: (text = "输入") => ({
    type: 'heading',
    children: [{ text: `${text}`, bold: true }]
  }),
  inputNode: () => ({
    type: 'paragraph',
    children: [
      {text: ''},
      {
        type: 'template-input',
        children: [{ text: `输入: `, bold: true }]
      },
      {
        type: 'template-select',
        placeholder: '+工作文件',
        children: [{ text: '' }]
      },
      {text: ''}
    ],
    inputNode: true
  }),
  placeHolderNode: (text) => ({
    type: 'paragraph',
    children: [
      {text: ''},
      {
        type: 'template-placeholder',
        placeholder: text,
        children: [{ text: '' }]
      },
      {text: ''}
    ]
  }),
  textNode: (text) => ({
    type: 'paragraph',
    children: [
      {text},
    ],
  })
}

export const DefaultTemplate = () => [
  // Paragraph.headingNode("输入"),
  Paragraph.inputNode(),
  Paragraph.headingNode("工作步骤"),
  Paragraph.placeHolderNode("描述专家的如何工作"),
  Paragraph.placeHolderNode('可以输入"/"引用“已配置的MCP工具、用户界面”'),
  Paragraph.placeHolderNode("例如，调用纳米AI语音工具，将输入中视频台词转化为音频"),
  Paragraph.headingNode("输出"),
  Paragraph.placeHolderNode("描述输出需要遵循的条件"),
  Paragraph.placeHolderNode("例如，仅输出得到的配音音频链接，不要添加其他任何内容")
]

export const IterationTemplate = () =>[
  // Paragraph.headingNode("输入"),
  Paragraph.inputNode(),
  Paragraph.headingNode("补充要求"),
  Paragraph.placeHolderNode("当提取数组未达预期时, 在此补充要求或提供样式样例"),
  Paragraph.placeHolderNode('例如请按以下样例输出: [["故事台词1", "生图提示词1"], ["故事台词2", "生图提示词2"]]')
]

export const ConditionTemplate = () => [
  // Paragraph.headingNode("输入"),
  Paragraph.inputNode(),
  Paragraph.headingNode("补充要求"),
  Paragraph.placeHolderNode("当自动判断未达预期时, 在此补充判断要求")
]

export const MarketTemplate = (text) => [
  // Paragraph.headingNode("输入"),
  Paragraph.inputNode(),
  Paragraph.textNode(text)
]

const TemplatePlaceholder = ({ attributes, children, element }: any) => {
  // 检查是否有实际内容
  console.log(element.children)
  const textContent = element.children?.[0]?.text || ''
  const hasContent = textContent.trim() !== '' || element.children?.[1]?.type === "mention"

  // 基础样式
  const baseStyle: React.CSSProperties = {
    display: 'inline-block',
    padding: '4px 8px',
    margin: '2px',
    backgroundColor: '#F5F9FE',
    color: '#A6C0FA',
    borderRadius: '4px',
    fontSize: '12px',
    transition: 'all 0.2s ease',
    cursor: 'text',
    position: 'relative',
  }

  return (
    <span {...attributes} style={baseStyle}>
    {!hasContent && (
      // 无内容时显示占位符文本
      <span contentEditable={false} style={{
        color: '#A6C0FA',
      }}>
        {element.placeholder}
      </span>
    )}

    {/* 可编辑内容区域 */}
    <span style={{
      color: '#1890FF',
      // 当没有内容时覆盖在占位符上
      ...(hasContent ? {} : {
        position: 'absolute',
        left: '4px', top: '4px', right: '4px', bottom: '4px'
      })
    }}>
      {children}
    </span>
  </span>
  )
}


TemplatePlaceholder.updateInput = (userParams, mention) => {
  const copy = JSON.parse(JSON.stringify(userParams))
  if(!mention) return
  copy.forEach((node) => {
    if(node.inputNode && mention) {
      const index = node?.children?.findIndex(item => item.type === 'template-input');
      node?.children?.splice(index+1, 0, mention)
    }
  })
  return copy
}

export default TemplatePlaceholder;