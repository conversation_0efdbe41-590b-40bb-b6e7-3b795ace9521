
import { getKnowledgeList } from '@/service/nami_agent';
import { useState, useEffect } from 'react';

const PAGE_SIZE = 100;
const TYPE_MAP = {
    CREATED: {
        text: '我创建的知识库',
        value: 0,
    },
    JOINED: {
        text: '别人分享给我的知识库',
        value: 1,
    },
    MINE: {
        text: '我的知识库',
        value: 0,
    }
}
const LOCAL_KNOWLEDGE_LIST = 'knowledgeList';

const useKnowledgeList = () => {
    const [joinedLoading, setJoinedLoading] = useState(false);
    const [createdLoading, setCreatedLoading] = useState(false);
    const [joinedKnowledgeList, setJoinedKnowledgeList] = useState<any[]>(() => {
        return JSON.parse(localStorage.getItem(LOCAL_KNOWLEDGE_LIST) || '{}').joinedKnowledgeList || [];    
    });
    const [createdKnowledgeList, setCreatedKnowledgeList] = useState<any[]>(() => {
        return JSON.parse(localStorage.getItem(LOCAL_KNOWLEDGE_LIST) || '{}').createdKnowledgeList || [];
    });
    const [mineKnowledgeList, setMineKnowledgeList] = useState<any[]>(() => {
        return JSON.parse(localStorage.getItem(LOCAL_KNOWLEDGE_LIST) || '{}').mineKnowledgeList || [];
    });

    useEffect(() => {
        setJoinedLoading(true)
        setCreatedLoading(true)
        getKnowledgeList({ type: TYPE_MAP.JOINED.value, page: 1, page_size: PAGE_SIZE })
        .then((res) => {
            res?.length > 0 && setJoinedKnowledgeList(res);
            setJoinedLoading(false);
        }).catch(err => {
            setJoinedLoading(false);
        });

        getKnowledgeList({ type: TYPE_MAP.CREATED.value, page: 1, page_size: PAGE_SIZE })
        .then((res) => {
            res?.length > 0 && setCreatedKnowledgeList(res);
            if (res?.length > 0) {
                const mineList = res.filter((item) => item.is_default === 1);
                setMineKnowledgeList(mineList);
                const otherList = res.filter((item) => item.is_default === 0);
                setCreatedKnowledgeList(otherList);
            }
            setCreatedLoading(false);
        })
        .catch(err => {
            setCreatedLoading(false);
        })

    }, []);

    // 缓存知识库列表
    useEffect(() => {
        if (!joinedLoading && !createdLoading) {
            localStorage.setItem(LOCAL_KNOWLEDGE_LIST, JSON.stringify({ joinedKnowledgeList, createdKnowledgeList, mineKnowledgeList })); 
        }
    }, [joinedLoading, createdLoading])


    return  {
      joinedKnowledge: {
        name: TYPE_MAP.JOINED.text,
        list: joinedKnowledgeList,
      },
      createdKnowledge: {
        name: TYPE_MAP.CREATED.text,
        list: createdKnowledgeList,
      },
      mineKnowledgeList: {
        name: TYPE_MAP.MINE.text,
        list: mineKnowledgeList,
      },
      isLoading: createdLoading && joinedLoading,
    }

}


export default useKnowledgeList
