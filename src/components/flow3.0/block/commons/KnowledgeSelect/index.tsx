import { Select } from "antd";
import React, { useMemo } from "react";
import styles from "./index.module.scss";
import useKnowledgeList from './useKnowledgeList'
import BlockItemWrap from "../BlockItemWrap";

export default function KnowledgeSelect({ onChange, value }: { onChange: (value: string[]) => void, value: string[] | undefined }) {
    const { joinedKnowledge, createdKnowledge, mineKnowledgeList, isLoading } = useKnowledgeList();

    const options = useMemo(() => {
        const res = []
        if (mineKnowledgeList?.list?.length > 0) {
            res.push({
                label: <span className={styles.selectLabel}>选择</span>,
                title: mineKnowledgeList.name,
                options: mineKnowledgeList.list.map((item) => ({
                    label: <span>个人知识库</span>,
                    value: item.folder_id,
                })),
            })  
        }
        if (createdKnowledge?.list?.length > 0) {
            res.push({
                label: <span className={styles.selectLabel}>{createdKnowledge.name}</span>,
                title: createdKnowledge.name,
                options: createdKnowledge.list.map((item) => ({
                label: <span>{item.name?.replace(/\//ig, '')}</span>,
                value: item.folder_id,
                })),
            })
        }
        if (joinedKnowledge?.list?.length > 0) {
            res.push({
                label: <span className={styles.selectLabel}>{joinedKnowledge.name}</span>,
                title: joinedKnowledge.name,
                options: joinedKnowledge.list.map((item) => ({
                    label: <span>{item.name?.replace(/\//ig, '')}</span>,
                    value: item.folder_id,
                })),
            })
        }
        return res
    }, [joinedKnowledge, createdKnowledge, mineKnowledgeList])

    const handleChange = (value) => {
        typeof onChange === 'function' && onChange([value].filter(Boolean))
    }
    

    const knowledgeItems = [
        {
            key: "1",
            label: "知识库",
            children: <div className={styles.knowledgeSelectWrapper}>
                    <Select
                    allowClear
                    loading={isLoading}
                    style={{ width: "100%" }}
                    placeholder="请选择知识库"
                    value={value && !isLoading ? value[0] : ''}
                    className={styles.knowledgeSelect}
                    optionLabelProp="label"
                    options={options}
                    onChange={handleChange}
                >
                </Select>
            </div>
        },
    ];

    return (
        <BlockItemWrap  items={knowledgeItems} borderBottom />
    );
}