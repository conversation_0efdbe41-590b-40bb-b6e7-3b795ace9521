.container {
	display: flex;
	overflow: hidden;
	
	.left {
		:global{
			::--webkit-scrollbar {
				display: none; /* 对于webkit浏览器 */
			}
		}

		width: 320px;
		padding: 16px;
		border-bottom: 1px solid #EEEFF2;
		overflow-y: scroll;
	
		.listItem {
			border-bottom: 1px solid #EEEFF2;
			padding: 12px;
			cursor: pointer;

			&Title {
				font-size: 14px;
				font-weight: 600;
				line-height: 22px;
				color: #1B2532;
			}

			&Desc {
				font-size: 12px;
				color: #626F84;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
				line-height: 20px;
			}

			&.current {
				background: #EDF1F5;
				border-radius: 8px;
				border-bottom: none;
			}
		}
	}

	.right {
		flex: 1;
		padding: 16px;
		border: 1px solid #EDF1F5;
		border-radius: 8px;
	}
}