import { useState, useEffect, useRef, useMemo } from "react";
import { Popover, Input, Button, Modal } from "antd";
import cls from 'classnames';
import { ExclamationCircleFilled } from '@ant-design/icons';
import beautifyStyles from "./index.module.scss";
import starIcon from "@/images/flow3.0/star.svg";
import stopIcon from "@/images/stopIcon.png";
import { sseRequest } from "@/utils/sseRequest";
import SendIcon from '@/images/flow3.0/send.svg?url'
import { getPromptReplaceLog, getPromptStopLog } from "@/service/nami_agent";
import {
  formatStringToHtml,
  smartSlice,
  formatStringToJson,
} from "./formatPrompt";

export default function PromptBeautifyModal(props) {
  const { children, onClose, flowInfo, curBlock, handleChange, containerWidth } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [content, setContent] = useState("");
  const contentRef = useRef(null);
  const sliceIndex = useRef(0);
  const contentDom = useRef(null);
  // 用于显示的内容
  const [isOptimize, setIsOptimize] = useState(false);
  // 防止重复触发确认弹窗
  const [isShowingConfirm, setIsShowingConfirm] = useState(false);
  // const [sessionId, setSessionId] = useState("");
  // const [messageId, setMessageId] = useState("");
  const msgIdRef = useRef({
    session_id: "",
    message_id: ""
  });
  const cancelRef = useRef(null);

  const contentHtml = useMemo(() => {
    const str = smartSlice(content);
    sliceIndex.current = str.length;
    const strHtml = formatStringToHtml(str);
    return strHtml
  }, [content])

  const fetchPrompt = async (query: string) => {
    setIsOptimize(true);
    contentRef.current = "";
    sliceIndex.current = 0;

    const data = {
      query,
      addition_info: {
        flow_name: flowInfo?.title,
        flow_description: flowInfo?.desc,
        name: curBlock?.name,
        description: curBlock?.desc,
        original_prompt: curBlock?.prompts?.user,
        original_params: curBlock?.prompts?.user_params,
        mcp: curBlock?.mcp_list.map(({ display_name, intro, server_name }) => ({
          display_name,
          intro,
          server_name,
        })),
      },
      session_id: msgIdRef.current.session_id,
      template_type: 0,
      template_id: flowInfo?.id, // flow_id
      teamId: flowInfo?.teamId
    };
    // dev环境sse请求会缓存，所以需要本地转发一下
    const url = `/api/v2/nami_agent_flow/prompt_optimization_gen`;
    const dev = '/api/stream'
    const sseUrl = window.location.hostname === 'localhost' ? dev : url
    
    const cbs = {
      onOpenCb: (data) => {
        // console.log("-ddd-onOpenCb", data);
        msgIdRef.current = {
          session_id: '',
          message_id: ''
        }
      },
      onSuccessCb: (data) => {
        msgIdRef.current.message_id = data.message_id;
        msgIdRef.current.session_id = data.session_id;
        setIsOptimize(true);
        setContent(state => state + data.content)
      },
      onFinishCb: (data) => {
        setIsOptimize(false);
      },
      onFailCb: (data) => {
        // console.log("-ddd-onFailCb", content);
        setIsOptimize(false);
      },
    };

    try {
      const cancelRequest = await sseRequest(data, sseUrl, cbs, "POST");
      // 保存取消函数
      cancelRef.current = cancelRequest;
    } catch (error) {
      console.error("SSE request failed:", error);
      setIsOptimize(false);
    }
  };

  const handleSubmit = (e) => {
    e.stopPropagation();
    if (inputValue.trim() === "") return;
    resetStatus();
    fetchPrompt(inputValue);
  };

  const handleReplace = (e) => {
    e.stopPropagation();
    // console.log("🚀 ~ handleReplace ~ contentRef.current:", content)
    if (!content) return;
    // const str =  `### 工作流程：\n1. 接收用户输入：{#LibraryBlock id="111" type="userInput"#}用户输入{#/LibraryBlock#}\n2. 分析用户需求，必要时调用工具\n{#LibraryBlock id="754755" type="agentOutput"#}Agent{#/LibraryBlock#}\n3. 根据用户需求，生成响应内容\n{#LibraryBlock id="754755" type="agentOutput"#}Agent{#/LibraryBlock#}\n4. 将响应内容返回给用户{#LibraryBlock id="754755" type="agentOutput"#}Agent{#/LibraryBlock#}`
    // const str = 'aaaa{#LibraryBlock type="userInput"#}用户输入{#/LibraryBlock#}\nbbbb{#LibraryBlock id="658914" type="agentOutput"#} Agent_1{#/LibraryBlock#}\nmmmm{#LibraryBlock id="10010" type="mcp"#}纳米AI 搜索-搜索引擎工具{#/LibraryBlock#}\n'
    const json = formatStringToJson(content);
    handleChange(json);
    getPromptReplaceLog({ adopt_type: 1, message_id: msgIdRef.current.message_id })
    setIsOpen(false);
  };

  const handleExit = (e) => {
    e.stopPropagation();
    setIsOpen(false);
    resetStatus();
    // 退出处理逻辑
    onClose && onClose();
    // 退出时，记录日志
  };

  // 自动优化
  const handleOptimize = (e) => {
    e.stopPropagation();
    fetchPrompt("自动优化");
  };

  const resetStatus = () => {
    contentRef.current = "";
    setIsOptimize(false);
    setInputValue("");
    setContent("");
    sliceIndex.current = 0;

    // 关闭弹窗时取消请求 和 定时器
    cancelRef.current && cancelRef.current();
  }

  const handleOpenChange = (newOpen: boolean) => {
    // 如果是要关闭弹窗，且当前有内容或正在优化，且没有正在显示确认弹窗，则弹出确认弹窗
    if (!newOpen && (content || isOptimize) && !isShowingConfirm) {
      setIsShowingConfirm(true);
      Modal.confirm({
        title: (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            fontSize: '16px',
            fontWeight: 400,
            color: '#1D2531'
          }}>
            确认退出并舍弃AI生成的内容吗？
          </div>
        ),
        icon: <ExclamationCircleFilled />,
        okText: '确定',
        cancelText: '取消',
        centered: true,
        width: 356,
        onOk() {
          setIsShowingConfirm(false);
          setIsOpen(false);
          resetStatus();
          onClose && onClose();
        },
        onCancel() {
          setIsShowingConfirm(false);
          // 点击取消，不做任何操作，保持弹窗打开状态
        },
      });
    } else if (!newOpen && isShowingConfirm) {
      // 如果正在显示确认弹窗，忽略这次关闭事件
      return;
    } else {
      // 如果没有内容或不是关闭操作，直接执行原逻辑
      setIsOpen(newOpen);
      resetStatus();
    }
  };

  const handleStop = () => {
    setIsOptimize(false);
    if (cancelRef.current) {
      cancelRef.current();
    }
    if (msgIdRef.current.message_id) {
      getPromptStopLog({ adopt_type: 1, message_id: msgIdRef.current.message_id, broken_pos:  contentRef.current?.length })
    }
  };

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 确保在组件卸载时取消请求
      if (cancelRef.current) {
        cancelRef.current();
      }
      sliceIndex.current = 0;
    };
  }, []);

  // 监听strHtml字符变化，将contentDom滚动到底部
  useEffect(() => {
    if (contentDom.current) {
      contentDom.current.scrollTo({
        top: contentDom.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [contentHtml]);


  return (
    <Popover
      className={beautifyStyles.promptPopover}
      title=""
      trigger="click"
      open={isOpen}
      onOpenChange={handleOpenChange}
      placement="left"
      align={{ offset: [-containerWidth + 247,90] }}
      arrow={false}
      getPopupContainer={() => document.getElementById('blockDetail')}
      content={
        <div className={beautifyStyles.container}>
          {/* 自动优化按钮 */}
          {contentHtml || isOptimize ? null : (
            <Button
              className={beautifyStyles.optimizeButton}
              onClick={handleOptimize}
            >
              自动优化
            </Button>
          )}
          {contentHtml ? (
            <div
              className={beautifyStyles.content}
              ref={contentDom}
              dangerouslySetInnerHTML={{ __html: contentHtml }}
            ></div>
          ) : null}
          <div className={beautifyStyles.footer}>
          {isOptimize ? (
            <div className={beautifyStyles.loading}>
              <i></i>
              <i></i>
              <i></i>
            </div>
          ) : contentHtml ? (
            <div className={beautifyStyles.btnWrapper}>
              <Button
                className={beautifyStyles.replaceButton}
                onClick={handleReplace}
              >
                替换
              </Button>
              <Button
                className={beautifyStyles.exitButton}
                onClick={handleExit}
              >
                退出
              </Button>
            </div>
          ) : null}
          <div className={beautifyStyles.textAreaWrapper}>
            <img
              src={starIcon.src}
              alt="star"
              className={beautifyStyles.prefixIcon}
            />
            <Input.TextArea
              className={beautifyStyles.input}
              value={inputValue}
              placeholder="你希望如何编写或优化提示词?"
              onChange={(e) => setInputValue(e.target.value)}
              autoSize={{ minRows: 1, maxRows: 6 }}
              onPressEnter={(e) => {
                e.preventDefault();
                handleSubmit(e);
              }}
            />
            <div className={beautifyStyles.suffixIcon}>
              {isOptimize ? (
                <Button
                  icon={
                    <img
                      src={stopIcon.src}
                      alt="stop"
                      className={beautifyStyles.stopIcon}
                    />
                  }
                  className={beautifyStyles.stopBtn}
                  onClick={handleStop}
                >
                  停止响应
                </Button>
              ) : (
                <SendIcon className={cls([beautifyStyles.sendIcon, {
                  [beautifyStyles.inputActive]: inputValue.trim()
                }])} onClick={handleSubmit} />
              )}
            </div>
          </div>
          </div>
        </div>
      }
    >
      {children}
    </Popover>
  );
}
