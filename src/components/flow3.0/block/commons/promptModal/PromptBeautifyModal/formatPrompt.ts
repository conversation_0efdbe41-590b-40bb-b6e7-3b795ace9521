const inputIcon = "https://s4.ssl.qhres2.com/static/f04c4192cd2fbc81.svg"

/**
 * 将特定格式的字符串转换为JSON数据
 * @param text 输入的文本字符串
 * @returns 转换后的JSON数据，如果转换失败则返回null
 */

interface MentionPayload {
  name: string;
  id?: string;
  type: string;
  icon?: string;
  color?: string;
}

interface MentionNode {
  type: "mention";
  payload: MentionPayload;
  children: Array<{ text: string }>;
}

interface ParagraphNode {
  type: "paragraph";
  children: Array<{ text: string } | MentionNode>;
}

type BlockNode = ParagraphNode;

/**
 * 从节点数组中提取所有payload值
 * @param nodes 节点数组
 * @returns payload数组
 */
export const getMediaNode = (nodes) => {
  try {
    const payloads: MentionPayload[] = [];

    // 递归遍历所有节点
    const traverseNode = (node: any) => {
      // 如果节点有payload属性，则添加到结果数组
      if (node.payload) {
        payloads.push(node.payload);
      }

      // 如果节点有children属性，则递归遍历
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(traverseNode);
      }
    };

    // 遍历所有根节点
    nodes.forEach(traverseNode);

    return payloads;
  } catch (error) {
    console.error("提取payload失败:", error);
    return [];
  }
};
// 将字符串转换为JSON
export const formatStringToJson = (
  text: string,
  // iconUrls: Record<string | number, {icon: string, color?: string}>
): BlockNode[] | null => {
  try {
    // 如果文本已经是JSON格式，直接解析返回
    try {
      const directJson = JSON.parse(text);
      if (Array.isArray(directJson)) {
        return directJson;
      }
    } catch (e) {
      // 继续使用自定义解析逻辑
    }

    // 将文本按行分割
    const lines = text.split("\n");
    const result: BlockNode[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // 创建基本段落节点
      const paragraphNode: ParagraphNode = {
        type: "paragraph",
        children: [],
      };

      // 解析行内容
      let currentIndex = 0;
      while (currentIndex < line.length) {
        // 查找LibraryBlock标记
        const blockStart = line.indexOf("{#LibraryBlock", currentIndex);

        if (blockStart === -1) {
          // 如果没有找到标记，将剩余文本作为普通文本节点
          if (currentIndex < line.length) {
            paragraphNode.children.push({
              text: line.slice(currentIndex),
            });
          }
          break;
        }

        // 添加标记前的文本
        if (blockStart > currentIndex) {
          paragraphNode.children.push({
            text: line.slice(currentIndex, blockStart),
          });
        }

        // 查找标记结束位置
        const blockEnd = line.indexOf("#}", blockStart);
        if (blockEnd === -1) {
          throw new Error(`未找到LibraryBlock标记的结束位置: ${line}`);
        }

        // 解析LibraryBlock属性
        const blockContent = line.slice(blockStart + 2, blockEnd).trim();
        const attributes = parseAttributes(blockContent);

        // 查找闭合标签
        const closeTag = `{#/LibraryBlock#}`;
        const closeTagStart = line.indexOf(closeTag, blockEnd);
        if (closeTagStart === -1) {
          throw new Error(`未找到LibraryBlock的闭合标签: ${line}`);
        }

        // 获取标签内的文本内容
        const innerText = line.slice(blockEnd + 2, closeTagStart);

        const { type, id } = attributes || {};
        // const { icon, color } = iconUrls[innerText] || {};

        // 创建mention节点
        const mentionNode: MentionNode = {
          type: "mention",
          payload: {
            name: innerText,
            // icon,
            // color,
            id,
            type: type || "default", // 确保type属性始终存在
          },
          children: [{ text: "" }],
        };

        paragraphNode.children.push(mentionNode);
        currentIndex = closeTagStart + closeTag.length;
      }

      paragraphNode.children.push({ text: "" });
      result.push(paragraphNode);
    }

    return result;
  } catch (error) {
    console.error("格式化字符串到JSON失败:", error);
    return null;
  }
};

// 所有的{#LibraryBlock}xxx{#/LibraryBlock#}的开始和结束位置
const getLibraryBlockPos = (content, reg) => {
  // 参数校验
  if (!content || typeof content !== "string") {
    return [];
  }
  if (!(reg instanceof RegExp)) {
    return [];
  }

  const positions = [];
  let match;

  // 重置正则表达式的lastIndex，确保从头开始匹配
  reg.lastIndex = 0;

  // 循环获取所有匹配项
  while ((match = reg.exec(content)) !== null) {
    positions.push({
      start: match.index,
      end: match.index + match[0].length,
      content: match[0], // 完整匹配内容
      innerContent: match[1] || "", // 匹配的内部内容
    });
  }

  return positions;
};

/**
 * 智能切片函数，确保不会切割LibraryBlock结构
 * 当遇到不完整的LibraryBlock结构时，返回到该结构开始前的所有内容
 * @param {string} text - 输入文本
 * @returns {string} - 处理后的文本
 */
export function smartSlice(text) {
  // console.log('🚀 ~ smartSlice ~ text:', text)
  const LIBRARY_BLOCK_REGEX = /{#LibraryBlock[^#]*#}([^[#]*){#\/LibraryBlock#}/g;
  
  // 收集所有完整的LibraryBlock匹配及其位置
  const completeMatches = [];
  let match;
  while ((match = LIBRARY_BLOCK_REGEX.exec(text)) !== null) {
    completeMatches.push({
      start: match.index,
      end: match.index + match[0].length,
      content: match[0]
    });
  }
  
  // 查找不完整的LibraryBlock开始标记
  const startTagRegex = /{#LibraryBlock[^#]*(?!#})/g;
  let incompleteStart = null;
  while ((match = startTagRegex.exec(text)) !== null) {
    // 检查这个开始标记是否属于完整匹配的一部分
    const isPartOfComplete = completeMatches.some(
      complete => match.index >= complete.start && match.index < complete.end
    );
    
    if (!isPartOfComplete) {
      incompleteStart = match.index;
      break;
    }
  }
  
  // 如果存在不完整的开始标记，返回它之前的所有内容
  if (incompleteStart !== null) {
    return text.substring(0, incompleteStart);
  }
  
  return text;
}

/**
 * 解析LibraryBlock标记中的属性
 * @param content 标记内容
 * @returns 属性对象
 */
function parseAttributes(content: string): Partial<MentionPayload> {
  const attributes: Partial<MentionPayload> = {};
  const matches = content.match(/(\w+)="([^"]*?)"/g);

  if (matches) {
    matches.forEach((match) => {
      const [key, value] = match.split("=").map((s) => s.replace(/"/g, ""));
      if (key === "id" || key === "type" || key === "icon" || key === "color") {
        attributes[key] = value;
      }
    });
  }

  return attributes;
}

/**
 * 将特定格式的字符串转换为HTML
 * @param text 输入的文本字符串
 * @returns 转换后的HTML字符串
 */
export const formatStringToHtml = (text: string): string => {
  // 处理HTML特殊字符转义
  const escapeHtml = (str: string): string => {
    const htmlEscapes: { [key: string]: string } = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#39;",
    };
    return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
  };

  try {
    // 将文本按行分割并处理
    const lines = text.split("\n");
    let html = "";

    for (let line of lines) {
      if (!line.trim()) {
        continue;
      }

      // 开始处理每一行
      let currentIndex = 0;
      let lineHtml = "<p>";

      while (currentIndex < line.length) {
        const blockStart = line.indexOf("{#LibraryBlock", currentIndex);

        if (blockStart === -1) {
          // 添加剩余的普通文本
          lineHtml += escapeHtml(line.slice(currentIndex));
          break;
        }

        // 添加LibraryBlock前的文本
        lineHtml += escapeHtml(line.slice(currentIndex, blockStart));

        // 解析LibraryBlock
        const blockEnd = line.indexOf("#}", blockStart);
        if (blockEnd === -1) {
          throw new Error(`未找到LibraryBlock标记的结束位置: ${line}`);
        }

        // 解析属性
        const blockContent = line.slice(blockStart + 2, blockEnd).trim();
        const attributes = parseAttributes(blockContent);

        // 查找闭合标签
        const closeTag = "{#/LibraryBlock#}";
        const closeTagStart = line.indexOf(closeTag, blockEnd);
        if (closeTagStart === -1) {
          // throw new Error(`未找到LibraryBlock的闭合标签: ${line}`);
        }

        // 获取内部文本
        const innerText = line.slice(blockEnd + 2, closeTagStart);
        const className = `prompt_${attributes.type}`;

        // 不需要变色和图标icon
        // const { icon = '', color = '' } = iconUrls[innerText] || {}

        // 创建带样式的span标签
        lineHtml += `<span class="${className}"">${escapeHtml(innerText)}</span>`;

        currentIndex = closeTagStart + closeTag.length;
      }

      lineHtml += "</p>";
      html += lineHtml;
    }

    return html;
  } catch (error) {
    console.error("格式化字符串到HTML失败:", error);
    return escapeHtml(text);
  }
};

/**
 * 从节点数组中提取所有payload值
 * @param nodes 节点数组
 * @returns payload数组
 */
export const extractPayloads = (nodes: BlockNode[]): MentionPayload[] => {
  try {
    const payloads: MentionPayload[] = [];

    // 递归遍历所有节点
    const traverseNode = (node: any) => {
      // 如果节点有payload属性，则添加到结果数组
      if (node.payload) {
        payloads.push(node.payload);
      }

      // 如果节点有children属性，则递归遍历
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(traverseNode);
      }
    };

    // 遍历所有根节点
    nodes.forEach(traverseNode);

    return payloads;
  } catch (error) {
    console.error("提取payload失败:", error);
    return [];
  }
};

/**
 * 从节点数组中提取id和icon的映射关系
 * @param nodes 节点数组
 * @returns 一个对象，key是id，value是icon
 */
export const extractIconMap = (
  nodes: any[]
): Record<string | number, {icon: string, color?: string}> => {
  try {
    const iconMap: Record<string | number, {icon: string, color?: string}> = {};

    // 遍历所有根节点
    nodes.forEach((node) => {
      // 检查是否是段落节点
      if (node.type === "paragraph" && Array.isArray(node.children)) {
        // 遍历段落中的所有子节点
        node.children.forEach((child) => {
          // 检查是否是mention类型的节点
          if (child.type === "mention" && child.payload) {
            const { id = '', icon, color, type, name } = child.payload;
            if (type === 'userInput') {
              iconMap[name] = {
                icon: inputIcon,
                color
              }
            } else {
              iconMap[name] = {
                icon: icon, // 如果没有icon才使用默认值
                color
              };
            }
          }
        });
      }
    });

    return iconMap;
  } catch (error) {
    console.error("提取id-icon映射失败:", error);
    return {};
  }
};
