
.container {
	:global {
		.prompt_userInput, .prompt_agentOutput, .prompt_mcp {
			color: #006BFF;
			display: inline-block;
			// padding-left: 20px;
			padding: 0 8px;
			background-size: 16px 16px;
			background-repeat: no-repeat;
			background-position: left center;
			background-color: #F7F9FA;	
			border-radius: 8px;
		}
		.prompt_icon {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			border-radius: 4px;
			padding: 0 4px;
			width: 16px;
			height: 16px;
		}
	}
	padding: 16px 16px 24px;
	width: 482px;
	max-height: 424px;
	display: flex;
	flex-direction: column;
	.optimizeButton {
		box-sizing: border-box;
		height: 32px;
		width: 88px;
		text-align: center;
		border-radius: 12px;
		background-color: #F7F9FA;
		border: 1px solid #E6E8EB;
		box-shadow: none;
		font-family: 'PingFang SC', sans-serif;
		font-weight: 400;
		font-size: 14px;
		line-height: 24px;
		color: #1D2531;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		margin-bottom: 12px;

		&:hover {
			border-color: #D1D7DD;
		}

		&:focus {
			border-color: #D1D7DD;
			box-shadow: none;
		}
	}

	.content {
		flex: 1;
		overflow-y: auto;
		font-size: 14px;
		font-weight: 400;
		line-height: 24px;
		color: #1D2531;
	}
	.loading{
		display: flex;
		gap: 4px;
		padding: 15px 7px;
		i{
			display: block;
			width: 6px;
			height: 6px;
			border-radius: 50%;
			background-color: #9EA7B8;
			animation: loading 1.5s infinite linear;

			&:nth-child(1) {
				animation-delay: 0s;
			}
			
			&:nth-child(2) {
				animation-delay: 0.15s;
			}
			
			&:nth-child(3) {
				animation-delay: 0.3s;
			}
		}
	}
	.footer{

	}
	.btnWrapper{
		display: flex;
		flex-direction: row;
		gap: 12px;
		padding: 12px 0;
	}
	.replaceButton {
		box-sizing: border-box;
		font-weight: 400;
		justify-content: center;
		line-height: 22px;
		transform: scale(1, 1);
		border: none;
		transition: background-color 0 cubic-bezier(0.4,0,1,1) 0,border 0 cubic-bezier(0.4,0,1,1) 0;
		transition-property: color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-text-decoration-color,-webkit-filter,-webkit-backdrop-filter;
		transition-duration: .15s;
		transition-timing-function: cubic-bezier(.4,0,.2,1);
		color: rgba(81, 71, 255,1);
		border-radius: 8px;
		font-size: 14px;
		height: 32px;
		min-width: 32px;
		cursor: pointer;
		padding-bottom: 6px;
		padding-left: 16px;
		padding-right: 16px;
		padding-top: 6px;
		background: linear-gradient(90deg,rgba(171, 181, 255,0.3) 0,rgba(224,163,255,0.3) 100%);
		&:hover{
			background:linear-gradient(90deg,rgba(161, 170, 255,0.38) 0,rgba(216,170,255,0.38) 100%) !important;
			color: rgb(81, 71, 255) !important;
		}
	}

	.exitButton {
		cursor: pointer;
		box-sizing: border-box;
		height: 34px;
		padding: 4px 16px;
		border-radius: 8px;
		background-color: #ffffff;
		border: 1px solid #E6E8EB;
		box-shadow: none;
		font-family: 'PingFang SC', sans-serif;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #1D2531;
		display: flex;
		align-items: center;
		justify-content: center;

		&:hover {
			border-color: #D1D7DD;
		}

		&:focus {
			border-color: #D1D7DD;
			box-shadow: none;
		}
	}

	.textAreaWrapper {
		box-sizing: border-box;
		position: relative;
		display: flex;
		align-items: flex-start;

		.prefixIcon {
			position: absolute;
			top: 17px;
			left: 12px;
			z-index: 1;
		}

		.suffixIcon {
			position: absolute;
			bottom: 12px;
			right: 12px;
			z-index: 1;
			
			.stopBtn {
				position: relative;
				top: 2px;
				cursor: pointer;
				text-indent: 0;
				line-height: 0;
				border: none;
				letter-spacing: normal; 
				.stopIcon {
					display: inline-block;
					width: 20px;
					height: 20px;
				}
			}

			.sendIcon {
				color: #A4ACBC;
				cursor: pointer;
			}
			.inputActive{
				color: #006BFF;
			}
		}
	}

	.input {
		box-sizing: border-box;
		font-size: 14px;
		padding-top: 11px;
		padding-bottom: 11px;
		padding-left: 36px;
		padding-right: 36px;
		border-radius: 12px;
		border: 1px solid #2A76FF;
		background: rgba(0, 107, 255, 0.04);
		line-height: 24px;
		::-webkit-input-placeholder {
			color:#9EA7B8;
		}
	}

	.tip {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: #9EA7B8;
		text-align: center;
	}
}


@keyframes loading {
	0% {
		opacity: 0;
		background-color: rgba(158, 167, 184, 0.3);
	}
	50% {
		opacity: 0.5;
		background-color: rgba(158, 167, 184, 0.6);
	}
	100% {
		opacity: 1;
		background-color: rgba(158, 167, 184, 1);
	}
}
