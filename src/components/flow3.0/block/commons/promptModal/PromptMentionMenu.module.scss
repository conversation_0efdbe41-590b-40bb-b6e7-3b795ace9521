.menu {
	width: 260px;
	max-height: 476px;
	overflow: auto;
	padding: 12px 16px;
	background-color: #fff;
	border-radius: 12px;
	::-webkit-scrollbar {
		display: none;
	}

	&Group {
		margin-top: 16px;
		&:first-child {
			margin-top: 0;
		}

		&Title {
			font-size: 14px;
			color: #1D2531;
			font-weight: 600;
			line-height: 32px;
			margin-bottom: 10px;
			display: flex;

			&Icon {
				width: 16px;
				margin-right: 8px;
			}
		}

		&List {
			.menuGroupItem {
				font-size: 14px;
				color: #1D2531;
				font-weight: 400;
				
				margin: 2px 0;
				padding: 8px 12px;
				border: 1px solid #E1E7ED;
				border-radius: 12px;
				cursor: pointer;
				margin-bottom: 8px;

				
				&Header {
					display: flex;
					align-items: center;
					line-height: 22px;
					position: relative;
					&:hover {
						color: rgb(0, 107, 255);
					}
					
					&Name {
						flex: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						padding-right: 10px;
					}
				}

				&Arrow {
					width: 14px;
					position: absolute;
					right: 3px;
					top: 6px;
					transition: 0.5s all ease;

					&Open {
						transform: rotate(90deg);
					}
				}

				.menuGroupItemArrowWrap{
					padding: 12px 12px;
				}

				.menuGroupItemIconWrap {
					margin-right: 8px;
					width: 24px;
					height: 24px;
					vertical-align: middle;
					text-align: center;
					line-height: 24px;
					border-radius: 6px;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				&Content {
					background: #F7F9FA;
					border-radius: 8px;
					padding-left: 26px;
					padding-right: 8px;
					margin-top: 8px;

					&Item {
						font-size: 14px;
						line-height: 32px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;

						&:hover {
							color: rgb(0, 107, 255);
						}
					}
				}
			}
		}

		&Empty {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 31px 0;
			
			&Img {
				width: 55px;
				margin-bottom: 4px;
			}

			&Text {
				font-size: 14px;
				color: #9EA7B8;
			}
		}
	}
}