import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, But<PERSON> } from 'antd'
import InfiniteScroll from "react-infinite-scroll-component";
import styles from './PromptLibraryModal.module.scss'
import { copyToClipboard } from '@/utils/utils'

export default function PromptLibraryModal(props) {
	const { open, setOpen, handleInsert } = props
	const [ list, setList ] = useState([
		{
			update_time: '2022-03-10 10:00:00',
			content: '你好，请问有什么可以帮到您？',
		},
		{
			update_time: '2022-03-10 10:00:00',
			content: '你好，请问有什么可以222帮到您？你好，请问有什么可以222帮到您？你好，请问有什么可以222帮到您？',
		},
		{
			update_time: '2022-03-10 10:00:00',
			content: '你好，请问有什么可以帮到您？',
		},
		{
			update_time: '2022-03-10 10:00:00',
			content: '你好，请问有什么可以222帮到您？',
		},
		{
			update_time: '2022-03-10 10:00:00',
			content: '你好，请问有什么可以帮到您？',
		},
		{
			update_time: '2022-03-10 10:00:00',
			content: '你好，请问有什么可以222帮到您？',
		}
	])
	const [ page, setPage ] = useState(1)
	const [ hasMore, setHasMore ] = useState(true)
	const [ currentIndex, setCurrentIndex ] = useState(0)

	const fetchData = async () => {
		// const response = await fetch(`http://localhost:3000/api/prompt/library?page=${page}`)
		// const data = await response.json()
		// setList(list.concat(data.data))
		// setHasMore(data.hasMore)
		setList(list.concat(list))
		setHasMore(false)
	}
	
	const onInsert = () => {
		console.log('insert')
		//TODO update_block
		handleInsert(list[currentIndex].content)
		setOpen(false)
	}

	const onCopy = () => {
		console.log('copy')
		copyToClipboard(list[currentIndex].content)
		setOpen(false)
	}

	const onChoose = (index) => {
		setCurrentIndex(index)
	}

	useEffect(() => {
		fetchData()
	}, [])

	return (
		<Modal
			title="提示词库"
			open={open}
			onCancel={() => setOpen(false)}
			width={1080}
			footer={
				<div>
					<Button type="default" onClick={onCopy} style={{ marginRight: '8px' }}>复制提示词</Button>
					<Button type="primary" onClick={onInsert}>插入提示词</Button>
				</div>
			}
		>
			<div className={styles.container} style={{ height: 'calc(100vh - 250px)'}}>
				<div className={styles.left} id="scrollableDiv">
					<InfiniteScroll
						dataLength={list.length}
						next={fetchData}
						hasMore={hasMore}
						loader={<>Loading...</>}
						endMessage={<Divider plain>没有更多数据了</Divider>}
						scrollableTarget="scrollableDiv"
					>
						{
							list?.map((item, index) => (
								<div onClick={() => { onChoose(index) }} className={styles.listItem + ` ${currentIndex === index ?  styles.current: ''}`}>
									<div className={styles.listItemTitle}>{item.update_time}</div>
									<div className={styles.listItemDesc}>{item.content}</div>
								</div>
							))
						}
					</InfiniteScroll>
				</div>
				<div className={styles.right}>
					{ list?.[currentIndex].content }
				</div>
			</div>
		</Modal>
	)
}