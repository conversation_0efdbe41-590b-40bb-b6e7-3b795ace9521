import { Popover } from 'antd';
import PromptMentionMenu from './PromptMentionMenu';
import { useState } from 'react'
import styles from './PromptMentionMenuPopover.module.scss';

export default function PromptMentionMenuPopover(props) {
	const { type = "all", onSelect, blocks, activeBlockId, loopConfigs, mcpToolList, placement = "bottom", handleOpenChange, children } = props;
	const [ open, setOpen ] = useState(false)

	const onOpenChange = (value: boolean) => {
		setOpen(value)
		handleOpenChange && handleOpenChange(value)
	}
	return (
		<Popover
			overlayClassName={styles.popover}
			content={
				<PromptMentionMenu
					type={type}
					onSelect={onSelect}
					blocks={blocks}
					activeBlockId={activeBlockId}
					loopConfigs={loopConfigs}
					mcpToolList={mcpToolList}
					handleOpenChange={onOpenChange}
				/>
			}
			trigger="hover"
			placement={placement}
			arrow={false}
			open={open}
			onOpenChange={onOpenChange}
		>
			{children}
		</Popover>
	)
}