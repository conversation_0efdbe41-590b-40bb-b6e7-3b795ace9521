.promptHistoryModal {
	:global {
		.ant-modal-content {
			height: 80vh;
			max-height: 800px;
		}
		.ant-modal-body {
			height: calc(100% - 75px) !important;
		}
	}
}

.container {
	display: flex;
	overflow: hidden;
	padding: 12px 0;
	gap: 12px;
	height: 100%;
	
	.left {
		width: 220px;
		height: 100%;
		overflow-y: auto;
		
		.listItem {
			border-bottom: 1px solid #EEEFF2;
			padding: 12px;
			cursor: pointer;

			&:last-child {
				border-bottom: none;
			}

			&Title {
				font-size: 14px;
				font-weight: 600;
				line-height: 22px;
				color: #1B2532;
			}

			&Desc {
				font-size: 12px;
				color: #626F84;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
				line-height: 20px;
				height: 20px;
			}

			&.current {
				background: #EDF1F5;
				border-radius: 8px;
				border-bottom: none;
			}
		}
	}

	.right {
		flex: 1;
		padding: 16px 20px;
		border: 1px solid #EDF1F5;
		border-radius: 8px;
		overflow: auto;
	}
}