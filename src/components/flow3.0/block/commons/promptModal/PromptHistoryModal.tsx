import { useState, useEffect, useMemo, useRef } from 'react'
import { <PERSON><PERSON>, Di<PERSON><PERSON>, <PERSON><PERSON> } from 'antd'
import InfiniteScroll from "react-infinite-scroll-component";
import styles from './PromptHistoryModal.module.scss'
import { useRouter } from 'next/router'
import { reqGetPromptHistory } from '@/service/flow3.0'
import ListEmpty from '@/components/commonComponents/listEmpty/ListEmpty';
import { extractTextUsingDOMParser } from '@/utils/utils'

export default function PromptHistoryModal(props) {
	const {  activeBlockId, open, setOpen, handleInsert } = props
	const router = useRouter()
	const [ list, setList ] = useState([])
	const [ page, setPage ] = useState(1)
	const [ total, setTotal ] = useState(0)
	const [ isInit, setIsInit ] = useState(false)
	const pageSize = 10
	const [ currentIndex, setCurrentIndex ] = useState(0)
	const scrollRef = useRef(null)

	const hasMore = useMemo(() => {
		return !isInit || (isInit && list.length < total)
	}, [ page, pageSize, total, isInit ])

	const fetchData = async () => {
		if(!hasMore) return
		const data = await reqGetPromptHistory({
			page,
			page_size: pageSize,
			template_id: router.query.id,
			block_index: activeBlockId
		})
		!isInit && setIsInit(true)
		setList(list.concat(data.data))
		setTotal(data.total)
		setPage(page+1)
	}
	
	const onInsert = () => {
		handleInsert(list[currentIndex].user_params)
		setOpen(false)
	}

	const onCancel = () => {
		setOpen(false)
	}

	const onChoose = (index) => {
		setCurrentIndex(index)
	}

	const onReset = () => {
		setPage(1)
		setList([])
		setTotal(0)
		setIsInit(false)
	}

	useEffect(() => {
		if(open) {
			fetchData()
		}
		return () => {
			onReset()
		}
	}, [ open ])

	return (
		<Modal
			wrapClassName={styles.promptHistoryModal}
			title="历史记录"
			centered={true}
			open={open}
			onCancel={() => setOpen(false)}
			width={1200}
			footer={
				<div>
					<Button type="default" onClick={onCancel} style={{ marginRight: '8px' }}>取消{hasMore}</Button>
					{
						list?.[currentIndex]?.prompt && <Button type="primary" onClick={onInsert}>恢复使用</Button>
					}
				</div>
			}
		>
			<div className={styles.container}>
				<div className={styles.left} ref={scrollRef}>
					{
						isInit ?
						(
							!list.length ?
								<ListEmpty desc="暂无历史记录" /> 
								:
								<InfiniteScroll
									dataLength={list.length}
									next={fetchData}
									hasMore={hasMore}
									loader={<div style={{ textAlign: 'center' }}>加载中...</div>}
									// endMessage={<Divider plain>没有更多数据了</Divider>}
									scrollableTarget={scrollRef.current}
								>
									{
										list?.map((item, index) => (
											<div onClick={() => { onChoose(index) }} className={styles.listItem + ` ${currentIndex === index ?  styles.current: ''}`}>
												<div className={styles.listItemTitle}>{item.create_time}</div>
												<div 
													className={styles.listItemDesc}
												>
													{extractTextUsingDOMParser(item.prompt || '').substring(0, 50)}
												</div>
											</div>
										))
									}
								</InfiniteScroll>
						) : null
					}
				</div>
				<div className={styles.right} dangerouslySetInnerHTML={{ __html: list?.[currentIndex]?.prompt }}>
				</div>
			</div>
		</Modal>
	)
}