import { useCallback, useEffect, useState } from "react";
import { Button, Collapse, Input, Tooltip } from "antd";
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import styles from './styles/BlockBranch.module.scss';
import { reqAddBranch, reqDeleteBranch } from "@/service/flow3.0";

import { deletedBranchInfoAtom } from '@/atoms/flowEditorAtoms' 
import { useSetRecoilState } from "recoil";

export default function BlockBranchList(props: any) {
    const { getFlowDetail, chainId, blocks, activeBlockId, setBlocks, borderTop = false, borderBottom = true, text = '场景' } = props;

    const setDeletedBranchInfo = useSetRecoilState(deletedBranchInfoAtom)

    const handleAddBranch = async () => {
        if (blocks[activeBlockId].branches.length >= 50) {
            return;
        }
        const branch_config = {
            branch_id: Math.ceil(Math.random() * 1000000) + "",
            content: "",
        };

        // 前端更新数据
        blocks[activeBlockId].branches.push(branch_config);
        setBlocks(prev => ({ ...blocks }));
    }

    const handleRemoveItem = async (item: any, index: number) => {

        // 需要删除边 以及 修改点 不能setblocks
        // blocks[activeBlockId].branches.splice(index, 1);
        setDeletedBranchInfo({
            blockid: activeBlockId,
            branch_id: blocks[activeBlockId].branches?.[index]?.branch_id,
            branch_index: index
        })
        // setBlocks(prev => ({...blocks}));
    }

    const handleChange = (e: any, index: number) => {
        blocks[activeBlockId].branches[index].content = e.target.value.trim();
        setBlocks(prev => ({ ...blocks }));
    }

    return <div className={[flowStyles.blockContainerWrapper, borderTop && flowStyles.borderTop, borderBottom && flowStyles.borderBottom].filter(Boolean).join(' ')}>
        <Collapse
            destroyOnHidden
            accordion={false}
            defaultActiveKey={['1']}
            ghost
            className={flowStyles.collapseMaskWrapper}
            items={[
                {
                    key: '1',
                    label:
                        <div className={flowStyles.collapseMaskTitle}>
                            <div className={flowStyles.collapseTitle}>{text}列表</div>
                        </div>,
                    children: <>
                        <div className={styles.list}>
                            {
                                blocks[activeBlockId].branches?.map((item, index) => {
                                    return <div key={item.branch_id} className={styles.listItem}>
                                        <Input
                                            className={styles.input}
                                            placeholder={`请输入${text}`}
                                            prefix={<div className={styles.inputIcon}>{text}{index}</div>}
                                            value={item.content}
                                            onChange={(e) => handleChange(e, index)}
                                        />
                                        {
                                            blocks[activeBlockId].branches.length > 1 ?
                                                <Button
                                                    type="text"
                                                    className={styles.delBtn}
                                                    onClick={() => { handleRemoveItem(item, index) }}
                                                >
                                                    <Tooltip placement="top" title="删除" >
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M13.4707 5.14258C14.2532 5.14258 14.8927 5.75418 14.9375 6.52539L14.9404 6.6123V7.10156H18.3691C18.6396 7.10163 18.8584 7.32133 18.8584 7.5918C18.8583 7.84287 18.6693 8.04984 18.4258 8.07812L18.3691 8.08105H16.8994V17.3877C16.8993 18.1701 16.2879 18.8098 15.5166 18.8545L15.4307 18.8564H8.57324C7.7907 18.8564 7.15114 18.245 7.10645 17.4736L7.10352 17.3877V8.08105H5.63477C5.36434 8.08105 5.14466 7.86219 5.14453 7.5918C5.14453 7.34065 5.3336 7.13381 5.57715 7.10547L5.63477 7.10156H9.0625V6.6123C9.0625 5.82977 9.67495 5.1902 10.4463 5.14551L10.5322 5.14258H13.4707ZM8.08301 17.3877C8.08312 17.6387 8.27227 17.8456 8.51562 17.874L8.57324 17.877H15.4307C15.6817 17.8767 15.8887 17.6878 15.917 17.4443L15.9199 17.3877V8.08105H8.08301V17.3877ZM10.5322 10.041C10.7834 10.041 10.9903 10.23 11.0186 10.4736L11.0225 10.5303V15.4287C11.0222 15.699 10.8026 15.918 10.5322 15.918C10.2811 15.9179 10.0742 15.7289 10.0459 15.4854L10.043 15.4287V10.5303C10.043 10.2598 10.2618 10.041 10.5322 10.041ZM13.4707 10.041C13.7219 10.041 13.9297 10.23 13.958 10.4736L13.9609 10.5303V15.4287C13.9607 15.699 13.7411 15.918 13.4707 15.918C13.2197 15.9178 13.0127 15.7288 12.9844 15.4854L12.9814 15.4287V10.5303C12.9815 10.2599 13.2004 10.0412 13.4707 10.041ZM10.5322 6.12207C10.2811 6.1221 10.0743 6.31119 10.0459 6.55469L10.043 6.6123V7.10156H13.9609V6.6123C13.9609 6.36115 13.7719 6.15333 13.5283 6.125L13.4707 6.12207H10.5322Z" fill="#657083" />
                                                        </svg>
                                                    </Tooltip>
                                                </Button>
                                                : ''
                                        }

                                    </div>
                                })
                            }
                        </div>
                        <div className={flowStyles.collapseAddItemBtnWrap}>
                            <Button
                                type="text"
                                className={flowStyles.collapseAddItemBtn}
                                onClick={handleAddBranch}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="#1D2531" />
                                </svg>
                                {text}
                            </Button>
                        </div>
                    </>


                }
            ]}
        />
    </div>
}