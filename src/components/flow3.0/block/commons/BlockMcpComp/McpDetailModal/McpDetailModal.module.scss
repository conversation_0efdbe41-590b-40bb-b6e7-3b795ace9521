.modalHeader {
  // position: absolute;
  // top: 0;
  // left: 0;
  // right: 0;
  // z-index: 100;
  background: transparent;
  border-color: transparent;

  .backBtn {
    display: flex;
    align-items: center;
    color: #1B2532;

    .backBtnIcon {
      cursor: pointer;
    }

    .backBtnText {
      margin-left: 10px;
      font-size: 16px;

      font-weight: 600;
      line-height: 24px;
    }
  }

  &.modalHeaderList {
    padding: 16px 24px;
    background: #F7F8FA !important;
    border-bottom: 1px solid #EBF0F5 !important;
  }
}

.mcpContent {
  padding: 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  // max-height: 800px;
  // overflow-y: auto;

  .header {
    flex-shrink: 0;

    .headerInfo {
      display: flex;
      gap: 8px;
      position: relative;
    }

    .addBtn {
      position: absolute;
      right: 0;
      top: 0;
    }

    .logoWrapper {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      border: 1px solid #EDF1F5;
      background: #FFF;
      overflow: hidden;
    }

    .logo {
      display: block;
      width: 100%;
    }

    .titleContent {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-right: 100px;
      margin-left: 16px;
    }

    .title {
      font-family: 'PingFang SC';
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: #1d2531;
      margin: 0;
    }

    .description {
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #657083;
      margin: 0;
    }

    .statusBtn {
      position: absolute;
      right: 0px;
      top: 0;
    }

  }

  .tags {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-left: -12px;

    .tag {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 0 12px;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        width: 1px;
        height: 12px;
        background-color: #EDF1F5;
      }

      img {
        width: 12px;
        height: 12px;
      }

      span {
        font-family: 'PingFang SC';
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #657083;
        margin-left: 2px;
      }

      .certified {
        color: #006bff;
      }
    }

    .tagHide {
      display: none;
    }
  }
}

.mcpContentList {
  // padding-top: 77px;
}

.detailTabsWrapper {
  margin-top: 12px;
  padding: 0 0 16px;
  border-radius: 16px;
  border: 1px solid #EBF0F5;
  background: #FFF;
  height: 100%;
  overflow: hidden;
  // overflow-y: auto;
}

.detailTabs {
  height: 100%;
  overflow: hidden;

  & :global(.ant-tabs-nav) {
    margin: 0 16px 16px;
  }

  & :global(.ant-tabs-content-holder) {
    padding: 0 16px;
    height: 100%;
    overflow-y: auto;
  }

  .tabLabel {
    display: flex;
    align-items: center;
    line-height: 1;



    .tabLabelText {
      display: inline-block;
      margin-left: 8px;
    }
  }
}

.closeBtn {
  position: absolute;
  right: 12px;
  top: 12px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #657083;
  font-size: 14px;
  z-index: 100;
}

.overviewContent {
  font-size: 14px;
  color: #1d2531;

  .overviewItem {
    margin-bottom: 16px;
  }

  h4 {
    font-size: 16px;
    line-height: 24px;
    color: #1d2531;
    font-weight: 600;
    margin-bottom: 8px;
  }

  p {
    margin: 0;
    line-height: 22px;
  }
}

.toolsList {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.toolItemWrapper {
  border: 1px solid #e1e7ed;
  border-radius: 8px;
  overflow: hidden;

  .toolItemHeader {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f7f9fa;
    cursor: pointer;
    width: 100%;

    &:hover {
      background: #f0f2f5;
    }

    .keyIcon {
      width: 14px;
      height: 14px;
      margin-right: 8px;
    }

    .toolName {
      flex: 1;
      font-family: 'PingFang SC';
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
      color: #1d2531;
    }

    .arrowIcon {
      width: 14px;
      height: 14px;
    }
  }

  .toolDetail {
    padding: 0 12px 12px;
    background: #fff;
    border-top: 1px solid #e1e7ed;

    .labelValuePair {
      display: flex;
      padding: 10px 0;
      font-family: 'PingFang SC';
      font-size: 12px;
      line-height: 20px;

      .labelKey {
        width: 70px;
        color: #9ea7b8;
        flex-shrink: 0;
      }

      .labelValue {
        flex: 1;
        color: #1d2531;
      }
    }

    .paramCard {
      background: #f7f9fa;
      border-radius: 8px;
      margin-top: 10px;
      overflow: hidden;

      .paramHeader {
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 10px;

        .paramName {
          font-family: 'PingFang SC';
          font-weight: 600;
          font-size: 14px;
          line-height: 22px;
          color: #1d2531;
        }

        .requiredTag {
          background: #ffece8;
          border-radius: 2px;
          padding: 0 4px;
          font-family: 'PingFang SC';
          font-size: 14px;
          line-height: 22px;
          color: #cb272d;
        }

        .typeTag {
          background: rgba(0, 0, 0, 0.04);
          border-radius: 2px;
          padding: 0 4px;
          font-family: 'PingFang SC';
          font-size: 14px;
          line-height: 22px;
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .dividerLine {
        height: 1px;
        background: #edf1f5;
        width: 100%;
      }

      .labelValuePair {
        padding: 12px 16px;
      }
    }
  }
}

// 移除不再需要的旧样式
.toolItem {
  display: none;
}

.toolCascader {
  display: none;
}