import React, { useState, useImperativeHandle, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Button, ModalProps } from "antd";
import classNames from "classnames";
import { CloseOutlined, LeftOutlined } from "@ant-design/icons";
import styles from "./McpDetailModal.module.scss";
import detailStyles from '../../styles/BlockMCP.module.scss'
import userIcon from "@/images/user.png";
import websiteIcon from "@/images/website.png";
import safetyIcon from "@/images/safety.png";
import packageIcon from "@/images/package.png";
import keyIcon from "@/images/key.png";
import arrowDownIcon from "@/images/arrowDown.png";
import arrowUpIcon from "@/images/arrowUp.png";
import OverviewIcon from "@/icons/OverviewIcon";
import ToolIcon from "@/icons/ToolIcon";
import { getMcpDetail } from "@/service/nami_agent";
import { MCPCardType } from "../enum";
import ModalContentLoading from "../ModalContentLoading";


// 图片资源
const IMAGES = {
  logo: "http://localhost:3845/assets/5a543207673103a78be16b4c962f343a1350ac11.png",
  safetyIcon: safetyIcon.src,
  websiteIcon: websiteIcon.src,
  userIcon: userIcon.src,
  packageIcon: packageIcon.src,
  keyIcon: keyIcon.src,
  arrowDownIcon: arrowDownIcon.src,
  arrowUpIcon: arrowUpIcon.src,
};

// 定义Payload类型
type Payload = {
  data: any;
  from?: string;
  isAdded?: boolean;
  onClose?(): void;
  addMcp?(id: string): void;
  itemStatusBtn?: Function,
  modalProps?: ModalProps,
  [others: string]: any;
};

// 定义Modal的Ref类型
export type McpDetailModalRefType = {
  show(payload: Payload): void;
};

// 定义工具项的接口
interface ToolOption {
  value: string;
  label: React.ReactNode;
  description?: string;
  children?: ToolOption[];
  isLeaf?: boolean;
  params?: ToolParam[];
}

// 定义参数接口
interface ToolParam {
  name: string;
  required: boolean;
  type: string;
  description: string;
}

// 工具项组件
interface ToolItemProps {
  tool: {
    name: string;
    name_ui: string;
    description?: string;
    inputSchema: {
      properties: Record<
        string,
        { description: string; type: string;[key: string]: any }
      >;
      required?: string[];
      type: string;
    };
  };
  isActive: boolean;
  onClick: () => void;
}

/**
 * MCP工具项组件
 * @param tool
 * @param isActive
 * @param onClick
 * @returns
 */
const ToolItem: React.FC<ToolItemProps> = ({ tool, isActive, onClick }) => {
  // 动态生成参数列表
  const params =
    tool.inputSchema && tool.inputSchema.properties
      ? Object.entries(tool.inputSchema.properties).map(([key, value]) => ({
        name: key,
        required: tool.inputSchema.required?.includes(key) || false,
        type: value.type,
        description: value.description,
      }))
      : [];

  return (
    <div className={styles.toolItemWrapper}>
      <div className={styles.toolItemHeader} onClick={onClick}>
        <img src={IMAGES.keyIcon} alt="key" className={styles.keyIcon} />
        <span className={styles.toolName}>{tool.name_ui}</span>
        <img
          src={isActive ? IMAGES.arrowUpIcon : IMAGES.arrowDownIcon}
          alt="arrow"
          className={styles.arrowIcon}
        />
      </div>
      {isActive && params.length > 0 && (
        <div className={styles.toolDetail}>
          <div className={styles.labelValuePair}>
            <div className={styles.labelKey}>工具标识符:</div>
            <div className={styles.labelValue}>{tool.name}</div>
          </div>
          {tool.description && (
            <div className={styles.labelValuePair}>
              <div className={styles.labelKey}>工具描述:</div>
              <div className={styles.labelValue}>{tool.description}</div>
            </div>
          )}
          {params.map((param, index) => (
            <div key={index} className={styles.paramCard}>
              <div className={styles.paramHeader}>
                <div className={styles.paramName}>{param.name}</div>
                {param.required && (
                  <div className={styles.requiredTag}>Required</div>
                )}
                <div className={styles.typeTag}>{param.type}</div>
              </div>
              <div className={styles.dividerLine}></div>
              <div className={styles.labelValuePair}>
                <div className={styles.labelKey}>参数描述:</div>
                <div className={styles.labelValue}>{param.description}</div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface McpDetail {
  is_local?: number;
  tools?: Array<{
    name: string;
    name_ui: string;
    description?: string;
    inputSchema: {
      properties: Record<string, { description: string; type: string;[key: string]: any }>;
      required?: string[];
      type: string;
    };
  }>;
  ext?: {
    icon?: string;
    intro?: string;
    auth?: boolean;
    owner?: string;
    overview?: Array<{
      title: string;
      items: string[];
    }>;
  };
}

/**
 * MCP工具详情弹框
 */
const McpDetailModal = React.forwardRef<McpDetailModalRefType>(
  (_props, ref) => {
    // 状态管理
    const [visible, setVisible] = useState(false);
    const [activeToolIndex, setActiveToolIndex] = useState<number | null>(null);
    const [activeTab, setActiveTab] = useState<string>("overview");
    const [payload, setPayload] = useState<Payload>({
      data: {},
      from: "",
      isAdded: false,
      modalProps: {},
    });
    const [mcpDetail, setMcpDetail] = useState<McpDetail>({});
    const [isLoading, setIsLoading] = useState(false);

    // 暴露给父组件的方法
    useImperativeHandle(
      ref,
      () => ({
        show: (payload) => {
          setPayload(payload);
          setVisible(true);
        },
      }),
      []
    );

    // 处理关闭事件
    const handleClose = () => {
      setVisible(false);
      if (payload?.onClose) {
        payload.onClose();
      }
    };

    // 处理工具点击事件
    const handleToolClick = (index: number) => {
      setActiveToolIndex(activeToolIndex === index ? null : index);
    };

    // 处理Tab切换
    const handleTabChange = (key: string) => {
      setActiveTab(key);
      setActiveToolIndex(null);
    };

    const { data, from = "", isAdded = false } = payload;

    useEffect(() => {
      if (payload.data?.server_name && visible) {
        setIsLoading(true);
        getMcpDetail({
          server_name: payload.data.server_name,
          flow_id: payload?.param?.flow_id,
          block_key: payload?.param?.block_key,
        }).then((res) => {
          const detail: any = res?.detail;
          setMcpDetail({ ...detail });
          setIsLoading(false);
        });
      }
    }, [visible]);

    return <>
      {
        visible ?
          <Modal
            {
            ...{
              ...payload.modalProps,
              width: '70%',
              style: {
                maxWidth: '1200px',
                minWidth: '680px',
              },
              open: visible,
              onCancel: handleClose,
              footer: null,
              maskClosable: true,
              centered: true,
              closable: false,
              wrapClassName: detailStyles.addMCPModalWrap,
              classNames: {
                'content': detailStyles.addMCPModalContent,
                'body': detailStyles.addMCPModalBodyWrap,
              },
            }
            }
          >
            {
              isLoading ?
                <ModalContentLoading />
                :
                <div style={{ height: '100%', display: 'flex', flexDirection: 'column'}}>
                  {/* 弹窗头部 */}
                  <div
                    className={classNames(
                      styles.modalHeader,
                      from === "list" ? styles.modalHeaderList : ""
                    )}
                  >
                    {from === "list" ? (
                      <div className={styles.backBtn}>
                        <LeftOutlined className={styles.backBtnIcon} onClick={handleClose} />
                        <span className={styles.backBtnText}>工具详情</span>
                      </div>
                    ) : null}
                    <div className={styles.closeBtn} onClick={handleClose}>
                      <CloseOutlined />
                    </div>

                  </div>
                  <div className={classNames([styles.mcpContent, { [styles.mcpContentList]: from === "list" }])}>
                    {/* mcp信息 */}
                    <div className={styles.header}>
                      <div className={styles.headerInfo}>
                        <div className={styles.logoWrapper}>
                          <img
                            src={mcpDetail?.ext?.icon}
                            alt="logo"
                            className={styles.logo}
                          />
                        </div>
                        <div className={styles.titleContent}>
                          <h3 className={styles.title}>{data.display_name}</h3>
                          <p className={styles.description}>{mcpDetail?.ext?.intro}</p>
                        </div>
                        <div className={styles.statusBtn}>
                          {
                            payload?.itemStatusBtn?.(data)
                          }
                        </div>

                      </div>
                      {/* 标签信息 */}
                      <div className={styles.tags}>
                        {
                          mcpDetail?.ext?.auth ? (
                            <div
                              className={classNames([
                                styles.tag,
                              ])}
                            >
                              <img src={IMAGES.safetyIcon} alt="safety" />
                              <span className={styles.certified}>已认证</span>
                            </div>
                          ) : null
                        }
                        {
                          mcpDetail.is_local == 2 ? (
                            <div className={styles.tag}>
                              <img src={IMAGES.websiteIcon} alt="website" />
                              <span>云端</span>
                            </div>
                          ) : null
                        }
                        {
                          mcpDetail.is_local == 1 ? (
                            <div className={styles.tag}>
                              <img src={IMAGES.userIcon} alt="local" />
                              <span>本地</span>
                            </div>
                          ) : null
                        }
                        {
                          mcpDetail?.ext?.owner ? (
                            <div className={styles.tag}>
                              <img src={IMAGES.userIcon} alt="user" />
                              <span>{mcpDetail?.ext?.owner}</span>
                            </div>
                          ) : null
                        }
                      </div>
                    </div>

                    {/* 详情信息Tab切换 */}
                    <div className={styles.detailTabsWrapper}>
                      <Tabs
                        activeKey={activeTab}
                        onChange={handleTabChange}
                        className={styles.detailTabs}
                        items={[
                          {
                            key: "overview",
                            label: (
                              <span className={styles.tabLabel}>
                                <OverviewIcon />
                                <span className={styles.tabLabelText}>概览</span>
                              </span>
                            ),
                            children: (
                              <div className={styles.overviewContent}>
                                {mcpDetail?.ext?.overview?.map((item, index) => {
                                  return (
                                    <div key={item.title} className={styles.overviewItem}>
                                      <h4>{item.title}</h4>
                                      {item.items.map((i, iidx) => {
                                        return <p key={iidx}>{i}</p>;
                                      })}
                                    </div>
                                  );
                                })}
                              </div>
                            ),
                          },
                          {
                            key: "tools",
                            label: (
                              <span className={styles.tabLabel}>
                                <ToolIcon />
                                <span className={styles.tabLabelText}>工具</span>
                              </span>
                            ),
                            children: (
                              <div className={styles.toolsList}>
                                {mcpDetail?.tools && mcpDetail?.tools.length > 0 ? (
                                  mcpDetail?.tools.map((tool, idx) => (
                                    <ToolItem
                                      key={tool.name}
                                      tool={tool}
                                      isActive={activeToolIndex === idx}
                                      onClick={() => handleToolClick(idx)}
                                    />
                                  ))
                                ) : (
                                  <div>暂无工具</div>
                                )}
                              </div>
                            ),
                          },
                        ]}
                      />
                    </div>
                    {/* // 详情信息 */}
                  </div>

              </div>
            }
          </Modal>
          : ''
      }
    </>;
  }
);

export default McpDetailModal;
