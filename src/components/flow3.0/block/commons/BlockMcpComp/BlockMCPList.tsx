import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import styles from '../styles/BlockMCP.module.scss';

import AddMCPModal from './AddMCPModal';
import McpDetailModal from "./McpDetailModal/McpDetailModal";
import { MCPCardType } from "./enum";
import { reqDelUIMCPAllData } from "@/service/flow3.0";
import { deepClone } from "@/utils/utils";
import { default_model_params } from "@/utils/defaultLLMConfig";

export default function BlockMCPList(props: any) {
    const { isUnEdit, chainId, blocks, activeBlockId, setBlocks, borderTop = false, borderBottom = true } = props;

    const mcpDetailModalRef = useRef(null);
    const [openCustomUICard, setOpenCustomUICard] = useState(false);
    const [showAddMCPModal, setShowAddMCPModal] = useState(false); // 添加mcp工具弹框显示

    const handlePreview = (e: any, item: any) => {
        e.stopPropagation();
        mcpDetailModalRef.current?.show({
            data: item,
            param: {
                flow_id: chainId,
                block_key: activeBlockId,
            },
            onClose: () => {
                console.log("🚀 ~ onClose ~ onClose:")
            },
            addMcp: () => {
                console.log("🚀 ~ addMcp ~ addMcp:")
            }
        })
    }

    /**
     * 自定义UI卡片类型MCP编辑列表后更新当前卡片状态
     * @param formListLength >0 --> 当前mcpitem已绑定form
     */
    const updateUIMCPStatus = (formListLength: number) => {

    }

    // 更新MCPList
    const handleUpdateMCPList = async (list: any) => {
        blocks[activeBlockId].mcp_list = [...list];
        setBlocks(prev => ({ ...blocks }));
    }

    const handleRemoveMCPItem = async (item: any) => {
        // 自定义UI MCP卡片删除再调一下删除卡片对应的所有form编表单接口
        if (item.from == MCPCardType.UIMCP) {
            reqDelUIMCPAllData({ "flow_id": chainId, "block_key": activeBlockId })
        }
        handleUpdateMCPList(blocks[activeBlockId].mcp_list.filter(i => i.server_id !== item.server_id))
    }

    const onGUIFormCardListChange = (list: any) => {
        const temp = blocks[activeBlockId].mcp_list
        const index = blocks[activeBlockId].mcp_list?.findIndex(i => i.from == MCPCardType.UIMCP)
        if (list?.length == 0 && index > -1) {
            blocks[activeBlockId].mcp_list[index]['unedit'] = true
        }
        if (list?.length > 0 && index > -1) {
            blocks[activeBlockId].mcp_list[index]['unedit'] = false
        }
        setBlocks(prev => ({ ...blocks }));
    }

    useEffect(() => {
        if (!blocks[activeBlockId]?.model_params) {
            blocks[activeBlockId]["model_params"] = default_model_params;
            setBlocks(prev => ({ ...blocks }));
        }
    }, [])

    return <>
        <div className={[flowStyles.blockContainerWrapper, borderTop && flowStyles.borderTop, borderBottom && flowStyles.borderBottom].filter(Boolean).join(' ')}>
            <Collapse
                destroyOnHidden
                accordion={false}
                defaultActiveKey={['1']}
                ghost
                className={flowStyles.collapseMaskWrapper}
                items={[
                    {
                        key: '1',
                        label:
                            <div className={flowStyles.collapseMaskTitle}>
                                <div className={flowStyles.collapseTitle}>MCP工具</div>
                            </div>,
                        children:
                            <>
                                {
                                    blocks[activeBlockId]?.mcp_list?.length > 0 ?
                                        <div className={styles.mcpList}>
                                            {
                                                blocks[activeBlockId].mcp_list?.filter(i => i.from != MCPCardType.UIMCP)?.map((item, index) => {
                                                    return <div key={index} className={styles.mcpListItem} >
                                                        <div className={styles.itemLeft}>
                                                            <img className={styles.icon} src={item.icon} alt="" />
                                                            <div className={styles.name}>{item.display_name}</div>
                                                            {/* 自定义UI卡片类型未定义（未绑定过数据）时 unedit= true, 其他情况=false */}
                                                            {
                                                                item?.unedit ?
                                                                    <div className={styles.inedited}>
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                                                            <path d="M7.00098 0.730469C8.68635 0.730469 10.2674 1.39925 11.4355 2.56738C12.6035 3.73544 13.2724 5.31581 13.2725 7.00098C13.2725 8.68635 12.6037 10.2674 11.4355 11.4355C10.2674 12.6037 8.68635 13.2725 7.00098 13.2725C5.31581 13.2724 3.73545 12.6035 2.56738 11.4355C1.39925 10.2674 0.730469 8.68635 0.730469 7.00098C0.730555 5.31573 1.39932 3.73546 2.56738 2.56738C3.73546 1.39933 5.31573 0.730555 7.00098 0.730469ZM7.00098 1.60547C5.55018 1.60555 4.19115 2.17996 3.18555 3.18555C2.17995 4.19116 1.60555 5.55018 1.60547 7.00098C1.60547 8.45175 2.18008 9.81077 3.18555 10.8164C4.19116 11.822 5.55018 12.3974 7.00098 12.3975C8.4519 12.3975 9.81073 11.8221 10.8164 10.8164C11.8221 9.81073 12.3975 8.4519 12.3975 7.00098C12.3974 5.55018 11.822 4.19116 10.8164 3.18555C9.81077 2.18008 8.45175 1.60547 7.00098 1.60547ZM7.00098 9.33496C7.40357 9.33496 7.73029 9.66092 7.73047 10.0635C7.73047 10.4662 7.40368 10.793 7.00098 10.793C6.59842 10.7928 6.27246 10.4661 6.27246 10.0635C6.27264 9.66103 6.59853 9.33514 7.00098 9.33496ZM7.00098 3.06348C7.22398 3.06348 7.4085 3.23062 7.43555 3.44629L7.43848 3.50098V8.16797C7.43848 8.40959 7.2426 8.60547 7.00098 8.60547C6.77809 8.60531 6.59437 8.43827 6.56738 8.22266L6.56348 8.16797V3.50098C6.56365 3.25961 6.75961 3.06365 7.00098 3.06348Z" fill="#FF9A2E" />
                                                                        </svg>
                                                                        未定义
                                                                    </div>
                                                                    : ''
                                                            }

                                                        </div>
                                                        <div className={styles.itemBtns}>
                                                            {/* 编辑按钮只有自定义UI卡片类型有 */}
                                                            {
                                                                item.from == MCPCardType.UIMCP ?
                                                                    <>
                                                                        <Button
                                                                            type="text"
                                                                            className={styles.MCPItemBtn}
                                                                            onClick={(e) => { e.stopPropagation(); setOpenCustomUICard(true) }}
                                                                        >
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                                                <path d="M1.93359 14.3984H14.7336" stroke="currentColor" strokeWidth="1.19467" strokeLinecap="round" strokeLinejoin="round" />
                                                                                <path d="M3.35547 8.96864V11.5571H5.95714L13.311 4.2L10.7137 1.60156L3.35547 8.96864Z" stroke="currentColor" strokeWidth="1.19467" strokeLinejoin="round" />
                                                                            </svg>
                                                                            编辑
                                                                        </Button>
                                                                        <div className={styles.btnSplitLine}></div>
                                                                    </>
                                                                    : ''
                                                            }

                                                            {/* 自定义UI卡片类型下，未定义（未绑定过数据）不显示查看按钮 */}
                                                            {
                                                                !item?.unedit ?
                                                                    <>
                                                                        <Button
                                                                            type="text"
                                                                            className={styles.MCPItemBtn}
                                                                            onClick={(e) => handlePreview(e, item)}
                                                                        >
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                                                                <path fill-rule="evenodd" clipRule="evenodd" d="M2.96547 5.61505C4.20409 4.58836 5.98358 3.5 7.99859 3.5C10.0136 3.5 11.7931 4.58836 13.0317 5.61505C13.6581 6.13427 14.1638 6.65235 14.5131 7.0406C14.688 7.23505 14.8245 7.39778 14.918 7.51296C14.9648 7.57057 15.0008 7.61635 15.0257 7.64833C15.0381 7.66432 15.0477 7.67687 15.0545 7.68572L15.0624 7.69621L15.0648 7.69932L15.0655 7.70033C15.0656 7.70048 15.066 7.70098 14.6653 8C15.066 8.29902 15.0659 8.29915 15.0658 8.2993L15.0648 8.30068L15.0624 8.30379L15.0545 8.31428C15.0477 8.32313 15.0381 8.33568 15.0257 8.35167C15.0008 8.38365 14.9648 8.42943 14.918 8.48704C14.8245 8.60222 14.688 8.76495 14.5131 8.9594C14.1638 9.34765 13.6581 9.86573 13.0317 10.385C11.7931 11.4116 10.0136 12.5 7.99859 12.5C5.98358 12.5 4.20409 11.4116 2.96547 10.385C2.33906 9.86573 1.83338 9.34765 1.48411 8.9594C1.30918 8.76495 1.17273 8.60222 1.07921 8.48704C1.03243 8.42943 0.996341 8.38365 0.971498 8.35167C0.959075 8.33568 0.949459 8.32313 0.94272 8.31428L0.934776 8.30379L0.932438 8.30068L0.93168 8.29967C0.93157 8.29952 0.931196 8.29902 1.33193 8C0.931196 7.70098 0.931294 7.70085 0.931404 7.7007L0.932438 7.69932L0.934776 7.69621L0.94272 7.68572C0.949459 7.67687 0.959075 7.66432 0.971498 7.64833C0.996341 7.61635 1.03243 7.57057 1.07921 7.51296C1.17273 7.39778 1.30918 7.23505 1.48411 7.0406C1.83338 6.65235 2.33906 6.13427 2.96547 5.61505ZM1.33193 8L0.931404 7.7007C0.799045 7.87808 0.798838 8.12164 0.931196 8.29902L1.33193 8ZM1.97419 8C2.04445 8.08331 2.12924 8.18131 2.22755 8.2906C2.55298 8.65235 3.02361 9.13427 3.60363 9.61505C4.77786 10.5884 6.33171 11.5 7.99859 11.5C9.66548 11.5 11.2193 10.5884 12.3936 9.61505C12.9736 9.13427 13.4442 8.65235 13.7696 8.2906C13.868 8.18131 13.9527 8.08331 14.023 8C13.9527 7.91669 13.868 7.81869 13.7696 7.7094C13.4442 7.34765 12.9736 6.86573 12.3936 6.38495C11.2193 5.41164 9.66548 4.5 7.99859 4.5C6.33171 4.5 4.77786 5.41164 3.60363 6.38495C3.02361 6.86573 2.55298 7.34765 2.22755 7.7094C2.12924 7.81869 2.04445 7.91669 1.97419 8ZM14.6653 8L15.0658 8.2993C15.1981 8.12192 15.1984 7.87836 15.066 7.70098L14.6653 8Z" fill="currentColor" />
                                                                                <path fill-rule="evenodd" clipRule="evenodd" d="M5.83203 7.9987C5.83203 6.80209 6.80209 5.83203 7.9987 5.83203C9.19531 5.83203 10.1654 6.80209 10.1654 7.9987C10.1654 9.19531 9.19531 10.1654 7.9987 10.1654C6.80209 10.1654 5.83203 9.19531 5.83203 7.9987ZM7.9987 6.83203C7.35437 6.83203 6.83203 7.35437 6.83203 7.9987C6.83203 8.64302 7.35437 9.16536 7.9987 9.16536C8.64302 9.16536 9.16536 8.64302 9.16536 7.9987C9.16536 7.35437 8.64302 6.83203 7.9987 6.83203Z" fill="currentColor" />
                                                                            </svg>
                                                                            查看
                                                                        </Button>
                                                                        <div className={styles.btnSplitLine}></div>
                                                                    </>
                                                                    : ''
                                                            }


                                                            <Button
                                                                type="text"
                                                                className={`${styles.MCPItemBtn} ${styles.default}`}
                                                                onClick={(e) => { e.stopPropagation(); handleRemoveMCPItem(item) }}
                                                            >
                                                                <Tooltip placement="top" title="删除" >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                                                                        <path d="M8.4707 0.144531C9.25315 0.144531 9.89268 0.756134 9.9375 1.52734L9.94043 1.61426V2.10352H13.3691C13.6396 2.10358 13.8584 2.32328 13.8584 2.59375C13.8583 2.84483 13.6693 3.05179 13.4258 3.08008L13.3691 3.08301H11.8994V12.3896C11.8993 13.1721 11.2879 13.8118 10.5166 13.8564L10.4307 13.8584H3.57324C2.7907 13.8584 2.15114 13.2469 2.10645 12.4756L2.10352 12.3896V3.08301H0.634766C0.364339 3.08301 0.14466 2.86415 0.144531 2.59375C0.144531 2.3426 0.333604 2.13576 0.577148 2.10742L0.634766 2.10352H4.0625V1.61426C4.0625 0.83172 4.67495 0.192153 5.44629 0.147461L5.53223 0.144531H8.4707ZM3.08301 12.3896C3.08312 12.6406 3.27227 12.8475 3.51562 12.876L3.57324 12.8789H10.4307C10.6817 12.8787 10.8887 12.6897 10.917 12.4463L10.9199 12.3896V3.08301H3.08301V12.3896ZM5.53223 5.04297C5.78341 5.04297 5.99026 5.23199 6.01855 5.47559L6.02246 5.53223V10.4307C6.02224 10.701 5.80259 10.9199 5.53223 10.9199C5.28107 10.9199 5.07419 10.7309 5.0459 10.4873L5.04297 10.4307V5.53223C5.043 5.26177 5.26177 5.043 5.53223 5.04297ZM8.4707 5.04297C8.72189 5.04297 8.92972 5.23199 8.95801 5.47559L8.96094 5.53223V10.4307C8.96071 10.701 8.74107 10.9199 8.4707 10.9199C8.21968 10.9197 8.01265 10.7308 7.98438 10.4873L7.98145 10.4307V5.53223C7.98148 5.26187 8.20038 5.04316 8.4707 5.04297ZM5.53223 1.12402C5.28113 1.12405 5.07427 1.31315 5.0459 1.55664L5.04297 1.61426V2.10352H8.96094V1.61426C8.96094 1.3631 8.77187 1.15528 8.52832 1.12695L8.4707 1.12402H5.53223Z" fill="currentColor" />
                                                                    </svg>
                                                                </Tooltip>
                                                            </Button>

                                                        </div>
                                                    </div>
                                                })
                                            }

                                        </div>
                                        : ''
                                }
                                <div className={flowStyles.collapseAddItemBtnWrap}>
                                    <Button
                                        type="text"
                                        className={flowStyles.collapseAddItemBtn}
                                        onClick={() => setShowAddMCPModal(true)}
                                        disabled={!blocks[activeBlockId].model_params?.support_mcp}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="#1D2531" />
                                        </svg>
                                        MCP工具
                                    </Button>
                                    {
                                        !blocks[activeBlockId].model_params?.support_mcp ?
                                            <Tooltip placement="top" title="请先更换模型，再选MCP工具">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                                    <g clip-path="url(#clip0_24928_56902)">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.40381 2.4038C3.57959 1.22804 5.20521 0.5 7 0.5C8.79479 0.5 10.4204 1.22804 11.5962 2.4038L11.3103 2.6897L11.5962 2.40381C12.772 3.57959 13.5 5.20521 13.5 7C13.5 8.79479 12.772 10.4204 11.5962 11.5962C10.4204 12.772 8.79479 13.5 7 13.5C5.20521 13.5 3.57959 12.772 2.40381 11.5962L2.6897 11.3103L2.4038 11.5962C1.22804 10.4204 0.5 8.79479 0.5 7C0.5 5.20521 1.22804 3.57959 2.4038 2.40381L2.40381 2.4038ZM7 1.40698C5.45542 1.40698 4.05778 2.0325 3.04513 3.04514C2.0325 4.05779 1.40698 5.45542 1.40698 7C1.40698 8.54458 2.0325 9.94222 3.04513 10.9549L2.72447 11.2755L3.04513 10.9549C4.05779 11.9675 5.45542 12.593 7 12.593C8.54458 12.593 9.94221 11.9675 10.9549 10.9549C11.9675 9.94221 12.593 8.54458 12.593 7C12.593 5.45542 11.9675 4.05779 10.9549 3.04513C9.94222 2.0325 8.54458 1.40698 7 1.40698Z" fill="#626F84" />
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.8125 5.42969C4.8125 4.22157 5.79188 3.24219 7 3.24219C8.20812 3.24219 9.1875 4.22157 9.1875 5.42969C9.1875 6.48798 8.43598 7.37075 7.4375 7.57343V8.34635C7.4375 8.58798 7.24162 8.78385 7 8.78385C6.75838 8.78385 6.5625 8.58798 6.5625 8.34635V7.17969C6.5625 6.93806 6.75838 6.74219 7 6.74219C7.72487 6.74219 8.3125 6.15456 8.3125 5.42969C8.3125 4.70482 7.72487 4.11719 7 4.11719C6.27513 4.11719 5.6875 4.70482 5.6875 5.42969C5.6875 5.67131 5.49162 5.86719 5.25 5.86719C5.00838 5.86719 4.8125 5.67131 4.8125 5.42969Z" fill="#626F84" />
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.0026 10.974C7.40531 10.974 7.73177 10.6475 7.73177 10.2448C7.73177 9.84209 7.40531 9.51562 7.0026 9.51562C6.5999 9.51562 6.27344 9.84209 6.27344 10.2448C6.27344 10.6475 6.5999 10.974 7.0026 10.974Z" fill="#626F84" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_24928_56902">
                                                            <rect width="14" height="14" fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                            </Tooltip>
                                            : ''
                                    }
                                </div>
                            </>

                    }
                ]}
            />
        </div>
        {
            showAddMCPModal ?
                <AddMCPModal
                    {...{
                        ...props,
                        showAddMCPModal,
                        setShowAddMCPModal,
                        setOpenCustomUICard,
                        mcpDetailModalRef,
                        addedList: blocks[activeBlockId].mcp_list,
                        onUpdateMCPList: handleUpdateMCPList,
                        onRemoveMCPList: handleRemoveMCPItem,
                    }}
                />
                : ''
        }

        <McpDetailModal ref={mcpDetailModalRef} />
        {/* {
            openCustomUICard ?
                <GUIFormCardListModal
                    open={openCustomUICard}
                    flowId={chainId}
                    blockKey={activeBlockId}
                    onCancel={() => setOpenCustomUICard(false)}
                    onGUIFormCardListChange={onGUIFormCardListChange}
                />
                : ''
        } */}

    </>
}