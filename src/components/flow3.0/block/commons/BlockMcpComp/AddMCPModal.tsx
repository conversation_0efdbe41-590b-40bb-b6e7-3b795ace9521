import { useEffect, useState } from "react";
import { Button, Input, message, Modal } from "antd";
import { CheckOutlined, CloseOutlined, MinusCircleOutlined, SearchOutlined } from "@ant-design/icons";
import styles from '../styles/BlockMCP.module.scss';
import ListEmpty from "@/components/commonComponents/listEmpty/ListEmpty";
import { reqGetNmAgentMcpList } from "@/service/flow3.0";
import { MCPCardType } from "./enum";
import ModalContentLoading from "./ModalContentLoading";
import { MCPTabIconConfig } from "./MCPTabIconConfig";


export default function AddMCPModal(props: any) {
    const { blocks, activeBlockId, chainId, showAddMCPModal, setShowAddMCPModal, onUpdateMCPList, onRemoveMCPList, setOpenCustomUICard, mcpDetailModalRef } = props;

    const [modalLoading, setModalLoading] = useState(false);
    const [curClickTabId, setCurClickTabId] = useState<any>(-1);
    const [curMCPList, setCurMCPList] = useState<any>([]);
    const [curHoverMCPId, setCurHoverMCPId] = useState<any>('');
    const [searchKey, setSearchKey] = useState(''); // 搜索框关键字
    const [list, setList] = useState<any>([]);

    const initMCPList = async () => {
        setModalLoading(true);
        const res = await reqGetNmAgentMcpList({});
        const temp = res?.filter(item => !(item?.list?.findIndex(i => i?.from == MCPCardType.UIMCP) > -1))
        setList([...temp])
        setModalLoading(false);
        updataCurTabMCPList(-1, temp);
    }

    const updataCurTabMCPList = (id: any, initList?: any) => {
        setCurMCPList([]);
        let temp = [];
        if (id == -1) {
            (initList ? initList : list).forEach(item => {
                temp = temp.concat(item.list)
            });
        } else {
            const found = list.find(item => item.cid === id);
            if (found?.list) {
                temp = found.list;
            }
        }

        setCurMCPList(temp);
    }

    const filterMCPList = (key: string) => {
        let temp = [];
        setCurMCPList(temp);
        setCurClickTabId(-1);
        list.forEach(item => {
            item.list.forEach(i => {
                if (i.display_name.indexOf(key) > -1) temp.push(i)
            })
        })
        setCurMCPList(temp);
    }

    const handleTabClick = (id: any) => {
        setSearchKey('');
        setCurClickTabId(id);
        updataCurTabMCPList(id);
    }

    const handleToNamiMCP = () => {
        if (/NAMIAI|QIHU 360SE/i.test(navigator.userAgent)) {
            window.open('https://browser.360.cn/ai/mcpsquare.html', '_blank');
            return;
        }
        message.warning('请前往纳米AI搜索客户端进行添加');
    }

    /**
     * 判断是不是自定义UI类型的 ？item['unedit'] = true : item['unedit'] = false 记录自定义UI卡片是否添加过卡片内容
     */
    const handleAddMCPItem = (item: any) => {
        if (blocks[activeBlockId].mcp_list?.findIndex(i => i.server_id == item.server_id) > -1) {
            return;
        }
        item['unedit'] = false;
        if (item.from == MCPCardType.UIMCP) {
            item['unedit'] = true;
            onUpdateMCPList([...blocks[activeBlockId].mcp_list, item]);
            setShowAddMCPModal(false);
            setOpenCustomUICard(true);
            return;
        }
        onUpdateMCPList([...blocks[activeBlockId].mcp_list, item]);
    }

    const MCPItemOperateStatus = (item: any) => {

        const add = () => {
            return <Button
                type="text"
                className={styles.addMCPItemBtn}
                onClick={(e) => {
                    e.stopPropagation();
                    handleAddMCPItem(item);
                }}
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="#1D2531" />
                </svg>
                添加
            </Button >
        }

        const remove = () => {
            return <Button
                type="text"
                className={styles.removeMCPItemBtn}
                onClick={(e) => {
                    e.stopPropagation();
                    onRemoveMCPList(item);
                }}
            >
                <MinusCircleOutlined />
                取消添加
            </Button>
        }
        const added = () => {
            return <Button
                type="link"
                style={{ color: "#4EC01E" }}
            >
                <CheckOutlined color="#4EC01E" />
                已添加
            </Button>
        }

        const isAdded = blocks[activeBlockId].mcp_list?.findIndex(i => i.server_id == item.server_id) > -1;
        if (isAdded && curHoverMCPId != item.server_id) {
            return added();
        }

        if (curHoverMCPId == item.server_id) {
            if (isAdded) {
                return remove();
            }
            return add();
        }

        return null;
    }

    const handleSearchMCP = (e: any) => {
        if (e.target.value?.trim()?.length == 0) {
            setSearchKey('')
            updataCurTabMCPList(-1);
            return;
        }
        setSearchKey(e.target.value);
        filterMCPList(e.target.value);
    };

    const handleOpenMCPDetail = (e: any, item) => {
        e.stopPropagation();
        mcpDetailModalRef.current?.show({
            data: item,
            from: 'list',
            param: {
                flow_id: chainId,
                block_key: activeBlockId,
            },
            modalProps: {
                mask: false,
                transitionName: "",
                maskTransitionName: ""
            },
            itemStatusBtn: (item) => MCPItemOperateStatus(item)
        })
    }


    const MCPTotal = () => {
        let total = 0;
        if (list?.length > 0) {
            list.forEach(item => {
                total += item?.list?.length || 0;
            })
        }

        return total
    }

    useEffect(() => {
        initMCPList();
    }, [])

    return <>
        <Modal
            title={null}
            open={showAddMCPModal}
            footer={null}
            centered
            closable={false}
            wrapClassName={styles.addMCPModalWrap}
            classNames={{
                'content': styles.addMCPModalContent,
                'body': styles.addMCPModalBodyWrap,
            }}
            width={'70%'}
            style={{
                maxWidth: '1200px',
                minWidth: '680px',
            }}
        >
            {
                modalLoading ?
                    <ModalContentLoading /> :
                    <div className={styles.addMCPModalBody}>
                        <div className={styles.tabWrap}>
                            <div className={styles.modalName}>MCP工具</div>
                            <div className={styles.tabList}>
                                <div className={[styles.tabListItem, curClickTabId === -1 && styles.active].filter(Boolean).join(' ')} onClick={() => handleTabClick(-1)}>{MCPTabIconConfig.all}全部 （{MCPTotal()}）</div>
                                {
                                    list?.find(i => i.cid == 0) ?
                                        <div className={[styles.tabListItem, curClickTabId === list.find(i => i.cid == 0).cid && styles.active].filter(Boolean).join(' ')} onClick={() => handleTabClick(list.find(i => i.cid == 0).cid)}>{MCPTabIconConfig.mine}我的MCP（{list.find(i => i.cid == 0).list?.length}）</div>
                                        : ''
                                }
                                {
                                    list?.filter(i => i.cid != 0).map(tabitem => {
                                        return <div
                                            key={tabitem.cid}
                                            className={[styles.tabListItem, curClickTabId == tabitem.cid && styles.active].filter(Boolean).join(' ')}
                                            onClick={() => handleTabClick(tabitem.cid)}
                                        >
                                            {MCPTabIconConfig?.[tabitem.cid] ?? MCPTabIconConfig.default}{tabitem.cname} ({tabitem?.list?.length})
                                        </div>
                                    })
                                }
                            </div>

                            <div className={styles.mcpToolBox}>
                                <div
                                    className={styles.tabListItem}
                                    onClick={handleToNamiMCP}
                                >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <g clip-path="url(#clip0_24976_40072)">
                                            <path d="M14.6673 5.33203C14.6673 7.54116 12.8765 9.33203 10.6673 9.33203C9.99175 9.33203 9.35528 9.16453 8.79718 8.86883L3.00065 14.6654L1.33398 12.9987L7.13052 7.20216C6.83482 6.64406 6.66732 6.0076 6.66732 5.33203C6.66732 3.12289 8.45818 1.33203 10.6673 1.33203C11.3429 1.33203 11.9794 1.49951 12.5375 1.79523L10.0007 4.33203L11.6673 5.9987L14.2041 3.4619C14.4998 4.02 14.6673 4.65646 14.6673 5.33203Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_24976_40072">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    MCP工具箱
                                </div>
                            </div>
                        </div>
                        <div className={styles.tabContainerWrap}>
                            <div className={styles.modalOperate}>
                                <Input
                                    style={{
                                        width: "240px",
                                    }}
                                    placeholder="搜索MCP名称"
                                    value={searchKey}
                                    onChange={handleSearchMCP}
                                    prefix={<SearchOutlined style={{ color: "#9BA7BA" }} />}
                                />
                                <CloseOutlined width={16} height={16} color="#626F84" onClick={() => setShowAddMCPModal(false)} />
                            </div>
                            <div className={styles.mpcListWrap} onMouseLeave={() => { setCurHoverMCPId('') }}>
                                {
                                    curMCPList.length > 0 ?
                                        <>
                                            {
                                                curMCPList.map(item => {
                                                    return <div
                                                        key={item.server_id}
                                                        className={styles.mpcListItem}
                                                        onClick={(e) => handleOpenMCPDetail(e, item)}
                                                        onMouseEnter={(e) => { e.stopPropagation(); setCurHoverMCPId(item.server_id) }}
                                                    >
                                                        <div className={styles.mcpItemInfoWrap}>
                                                            <img className={styles.mcpItemIcon} src={item.icon} alt="" />
                                                            <div className={styles.mcpItemInfo}>
                                                                <div className={styles.mcpItemName}>{item.display_name}</div>
                                                                <div className={styles.mcpItemDesc}>{item.intro}</div>
                                                            </div>
                                                        </div>
                                                        <>
                                                            {MCPItemOperateStatus(item)}
                                                        </>
                                                    </div>
                                                })
                                            }
                                        </>
                                        :
                                        <ListEmpty desc="抱歉，没有找到相关MCP～" />
                                }
                            </div>
                        </div>
                    </div>
            }
        </Modal>
    </>
}