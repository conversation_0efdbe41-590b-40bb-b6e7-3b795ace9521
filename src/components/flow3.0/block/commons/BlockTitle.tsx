import React, { useState, useRef, useEffect } from 'react';
import { Input, message } from 'antd';
import type { InputRef } from 'antd';
import flowStyles from '@/styles/flow3.0/Flow.module.scss'

import editBtn from '@/images/editBtn.png';
import { iconList } from '@/components/flowEditor3.0/flowConfig';

interface BlockTitleProps {
  text: string;
  className?: string;
  style?: React.CSSProperties;
  onSave?: (newTitle: string) => void; // 可选，编辑完成回调
  iconUrl?: string; // 新增，修复类型错误
  meta?: any;
}

const BlockTitle: React.FC<BlockTitleProps> = ({ text, className = '', style = {}, onSave, iconUrl, meta }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [titleValue, setTitleValue] = useState(text);
  const inputRef = useRef<InputRef>(null);

  useEffect(() => {
    setTitleValue(text);
  }, [text]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleDoubleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    console.log('-dd--handleDoubleClick')
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitleValue(e.target.value);
  };

  const handleSave = () => {
    if (!titleValue.trim()) {
      message.error('标题不能为空');
      return;
    }
    setIsEditing(false);
    if (titleValue !== text && onSave) {
      try {
        onSave(titleValue);
      } catch (err) {
        message.error('保存失败');
      }
    }
  };

  const handleCancel = () => {
    setTitleValue(text);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  return (
    <div className={flowStyles.blockTitleLeftWrapper} style={style}>
      {iconUrl ? <span className={flowStyles.flowIcon}>
          <img src={iconUrl} width='26' />
        </span> : <span className={flowStyles.flowIcon} style={{background: meta?.color,borderRadius: '8px',textAlign: 'center',lineHeight: '32px'}}>
          <img src={iconList?.[meta?.iconIndex]?.image?.src} width='18' />
        </span>}
      <span
        className={flowStyles.blockTitle}
        // onDoubleClick={handleDoubleClick}
      >
        {isEditing ? (
          <Input
            ref={inputRef}
            value={titleValue}
            onChange={handleInputChange}
            onBlur={handleSave}
            onKeyDown={handleKeyDown}
            maxLength={50}
            size="small"
            style={{ width: 'auto', minWidth: 60 }}
          />
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <div className={flowStyles.blockTitleContent}>{titleValue}</div>
            {titleValue ? <div
            className={flowStyles.editTitleIcon}
              onClick={handleDoubleClick}
            >
              {/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M1.93359 14.3984H14.7336" stroke="#657083" strokeWidth="1.19467" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M3.35547 8.96864V11.5571H5.95714L13.311 4.2L10.7137 1.60156L3.35547 8.96864Z" stroke="#657083" strokeWidth="1.19467" strokeLinejoin="round" />
              <isvg> */}
              <img src={editBtn.src} alt="edit" width={16} height={16} />
            </div> : ''}
          </div>
        )}
      </span>
    </div >
  );
};

export default BlockTitle;
