import { Segmented, Tabs } from "antd";
import React, { useEffect, useState } from "react";
import styles from './styles/BlockTabs.module.scss';
import GUIFormCardList from "@/components/flow3.0/GUIFormCardModal/GUIFormCardList";
import { deepClone } from "@/utils/utils";
import { getNamiUIServer } from "@/service/nami_agent";
import { MCPCardType } from "./BlockMcpComp/enum";

enum AlignType {
    BASICCONFIG = '基础配置',
    USERUICARD = '用户界面',
}
export default function BlockTabs(props: any) {
    const { activeBlockId, chainId, blocks, setBlocks, basicConfigRender, onChangeTab, borderBottom=true } = props;
    const [alignValue, setAlignValue] = React.useState<AlignType>(AlignType.BASICCONFIG);

    const addBlockNamiUIServer = async () => {
        const res = await getNamiUIServer({});
        blocks[activeBlockId].mcp_list.push(res.list[0]);
        handleUpdateMCPList(blocks[activeBlockId].mcp_list);
    }

    // 更新MCPList
    const handleUpdateMCPList = async (list: any) => {
        blocks[activeBlockId].mcp_list = [...list];
        setBlocks(prev => ({...blocks}));
    }

    const onGUIFormCardListChange = async (list: any) => {
        const index = blocks[activeBlockId].mcp_list?.findIndex(i => i.from == MCPCardType.UIMCP)
        if (list?.length == 0) {
            handleUpdateMCPList(blocks[activeBlockId].mcp_list.filter(i => i.from !== MCPCardType.UIMCP))
            return
        }

        if (index == -1) addBlockNamiUIServer()
    }

    const handleChangeTab = async (value: AlignType) => {
        await onChangeTab?.();
        setAlignValue(value);
    }

    useEffect(() => {
        if (activeBlockId) {
            setAlignValue(AlignType.BASICCONFIG)
        }
    }, [activeBlockId])

    return <div className={styles.blockTabsWrapper}>
        <div className={[styles.segmentedWrapper, borderBottom && styles.borderBottom].filter(Boolean).join(' ')}>
            <Segmented
                className={styles.segmented}
                block
                value={alignValue}
                onChange={handleChangeTab}
                options={[AlignType.BASICCONFIG, AlignType.USERUICARD]}
            />
        </div>
        <Tabs className={styles.contentWrapper} activeKey={alignValue} renderTabBar={() => null} items={[
            {
                key: AlignType.BASICCONFIG,
                label: AlignType.BASICCONFIG,
                children: alignValue == AlignType.BASICCONFIG ? basicConfigRender?.() : '',
            },
            {
                key: AlignType.USERUICARD,
                label: AlignType.USERUICARD,
                children: <GUIFormCardList
                    key={activeBlockId}
                    flowId={chainId}
                    blockKey={activeBlockId}
                    onGUIFormCardListChange={onGUIFormCardListChange}
                />,
            }
        ]} />
    </div>
}