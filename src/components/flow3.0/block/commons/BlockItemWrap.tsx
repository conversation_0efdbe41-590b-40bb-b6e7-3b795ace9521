import { Collapse } from 'antd';
import type { CollapseProps } from 'antd';
import classNames from 'classnames';
import styles from '@/styles/flow3.0/Flow.module.scss';


export default function BlockItemWrap({ items, borderTop, borderBottom }: { items: CollapseProps['items'], borderTop?: boolean, borderBottom?: boolean }) {
  const keys = items.map(item => item.key)
  return (
    <div className={classNames([styles.blockItemWrap, {
      [styles.borderTop]: borderTop,
      [styles.borderBottom]: borderBottom,
    }])}>
      <Collapse ghost items={items} defaultActiveKey={keys} key={keys.join(',')}  />
    </div>
  )
}