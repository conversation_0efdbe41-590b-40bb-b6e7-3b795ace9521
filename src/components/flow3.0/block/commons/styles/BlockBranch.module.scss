.list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 480px;
    overflow-y: auto;

    .listItem {
        display: flex;
        align-items: center;
        gap: 4px;

        .input {
            flex: 1;
        }

        .inputIcon {
            color: #657083;
            margin-right: 10px;
            padding-right: 10px;
            position: relative;

            &::after {
                content: '';
                display: block;
                width: 1px;
                height: 12px;
                background: #EDF1F5;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        .delBtn {
            flex-shrink: 0;
            padding: 4px !important;

            &:hover {
                path {
                    fill: #006BFF !important;
                }
            }
        }
    }
}