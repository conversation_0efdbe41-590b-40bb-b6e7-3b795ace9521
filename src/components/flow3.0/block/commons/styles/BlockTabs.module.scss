.blockTabsWrapper {
	display: flex;
	height: 100%;
	overflow: hidden;
	background: #FFF;
	flex-direction: column;

	.segmentedWrapper {
		padding: 4px 16px 16px;
		flex-shrink: 0;

		&.borderBottom {
			border-bottom: 1px solid #EDF1F5;
		}

		.segmented {
			border-radius: 8px;
			background: #EDF1F5;
			color: #1D2531;

			&:global(.ant-segmented-group) {
				background: #EDF1F5;
			}


			&:global(.ant-segmented .ant-segmented-item) {
				border-radius: 8px;
				cursor: pointer;

				&:hover {
					background: transparent !important;
					color: #006BFF !important;
				}

				&::after {
					display: none;
				}
			}

			&:global(.ant-segmented .ant-segmented-item-selected) {
				color: #006BFF !important;
				background: #fff !important;

				&:hover {
					background: #fff !important;
				}

			}
		}
	}

	.contentWrapper {
		flex: 1;
		height: 100%;
		overflow-y: auto;
	}
}