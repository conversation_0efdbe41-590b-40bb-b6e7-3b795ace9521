.runTestPage {
    width: 100%;
    position: absolute;
    top: 55px;
    left: 0;
    background: #ffffff;
    height: calc(100% - 55px);
    z-index: 500;
    // border-left: 1px solid #E1E7ED;
    /* 投影/上/弹出投影（4级） */
    box-shadow: 0px -10px 30px -3px rgba(75, 85, 105, 0.10), 0px -15px 45px 7px rgba(27, 37, 50, 0.06);
}

.runTestPageAll {
    top: 0;
    height: 100%;
}

.runTestPageBtnRun {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    border-top: 1px solid rgba(82, 100, 154, 0.13);
    display: flex;
    justify-content: center;
    align-items: center;
}

.runTestPageHeader {
    padding: 0 8px 0 12px;
    position: relative;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(82, 100, 154, 0.13);
    width: 100%;
    height: 60px;
}

.runTestPageContent {
    // padding: 12px 8px 12px 12px;
    width: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    bottom: 0;
    overflow: auto;
    background: #fff;
    z-index: 100;
}

.runTestPageBox {
    padding: 12px 8px 12px 12px;
    border-bottom: 1px solid rgba(82, 100, 154, 0.13);
}

.runTestEasyRenderWrapper {
    padding: 16px 16px 24px 16px;
    border-radius: 12px;
    background: #F7F9FA;

    .confirmbtn{
        margin: 16px 0;
        width: 100%;
    }
}