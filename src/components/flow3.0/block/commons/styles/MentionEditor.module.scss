.container {
	height: 100%;
	display: flex;
	flex-direction: column;
	border: 1px solid #BFC9D5;
	border-radius: 8px;
	height: 100%;
	position: relative;

	&.focused {
		border: 1px solid rgb(0, 107, 255);
	}
	
	.operate {
		padding: 8px 8px;
		display: flex;
		border-bottom: 1px solid #EDF1F5;
		gap: 5px;

		&Btn {
			flex: 1;
			height:28px;
			gap: 2px;
			padding-left: 2px;
			padding-right: 2px;

			&Icon {
				width: 16px;
				position: relative;
				top: 1px;
			}
		}
	}

	.fullScreenBtn {
		display: flex;
		width: 28px;
		height: 28px;
		padding: 4px;
		justify-content: center;
		align-items: center;
		border-radius: 8px;
		position: absolute;
		top: 6px;
		right: 6px;
		border: 1px solid #ddd;
		box-shadow: 0px 10px 30px -3px rgba(75, 85, 105, 0.10), 0px 15px 45px 7px rgba(27, 37, 50, 0.06);
		cursor: pointer;
		z-index: 10000;
		background: rgba(255, 255, 255, 0.8);

		&Icon {
			width: 14px;
		}
	}

}

.errorTipPopover {
	position: absolute;
	left: -9999px;
	top: -9999px;
	color: #fff;
	background-color: #1D2531;
	border-radius: 4px;
	padding: 5px 12px;
	z-index: 9999;
	font-size: 14px;
	box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
	opacity: 0;
	transition: opacity 0.3s ease-in;

	&::after {
		content: "";
		position: absolute;
		top: calc(100% - 1px);
		left: 50%;
		transform: translateX(-50%);
		width: 8px;
		height: 4px;
		background: url('../../../../../images/flow3.0/ArrowDownBlack.svg') no-repeat;
	}
}