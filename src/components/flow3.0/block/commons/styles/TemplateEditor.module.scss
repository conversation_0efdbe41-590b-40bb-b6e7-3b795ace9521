.container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: #40a9ff;
  }

  &.focused {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.heading {
  font-size: 16px;
  font-weight: 600;
  color: #1890FF;
  margin: 12px 0 8px 0;
  line-height: 1.4;
  
  &:first-child {
    margin-top: 0;
  }
}

.paragraph {
  margin: 8px 0;
  line-height: 1.6;
  color: #262626;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.editablePlaceholder {
  display: inline-block;
  padding: 4px 12px;
  margin: 2px 4px;
  background-color: #e6f4ff;
  color: #1890ff;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #b8e0ff;
  min-width: 150px;
  text-align: center;
  font-size: 13px;
  font-style: italic;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background-color: #bae0ff;
    border-color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}

// 占位符编辑状态
.editablePlaceholder.editing {
  background-color: #fff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

// 模板选择栏样式
.templateBar {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 6px 6px 0 0;
  position: relative;
}

.templateOptions {
  position: absolute;
  top: 100%;
  left: 12px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 4px;
  
  button {
    display: block;
    width: 100%;
    text-align: left;
    margin: 2px 0;
    
    &:hover {
      background-color: #f0f2ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    margin: 0 -12px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .editablePlaceholder {
    min-width: 120px;
    font-size: 12px;
    padding: 3px 8px;
  }
}