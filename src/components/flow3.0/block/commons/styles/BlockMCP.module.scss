.mcpList {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 480px;
    overflow-y: auto;

    .mcpListItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        padding: 5px 12px;
        align-items: center;
        gap: 8px;
        flex: 1 0 0;
        border-radius: 8px;
        border: 1px solid #BFC9D5;

        .itemLeft {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            .icon {
                display: block !important;
                width: 16px;
                height: 16px;
                border-radius: 2px;
                border: 0.5px solid #EDF1F5;
                flex-shrink: 0;
                border-radius: 50%;
            }

            .name {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .inedited {
                display: flex;
                height: 22px;
                padding: 4px 8px;
                justify-content: center;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #FF7D00;
                background: #FFF7E8;
                border-radius: 8px;
            }
        }

        .itemBtns {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            cursor: pointer;

            .MCPItemBtn {
                gap: 4px !important;
                font-size: 12px !important;
                padding: 4px !important;
                color: #006BFF;

                &.default {
                    color: #657083;
                }

                &:hover {
                    color: #2E8CFF !important;
                    background: transparent !important;
                }
            }

            .btnSplitLine {
                margin: 0 8px;
                width: 1px;
                height: 12px;
                background: #EDF1F5;
            }
        }
    }
}

.addMCPModalWrap {
    // padding: 50px 0;

    .addMCPModalContent {
        padding: 0 !important;
        overflow: hidden;
    }

    .addMCPModalBodyWrap {
        height: 80vh !important;
        max-height: 800px !important;
        // overflow-y: auto;
    }

    .addMCPModalBody {
        height: 100%;
        display: flex;

        .tabWrap {
            display: flex;
            width: 200px;
            flex-direction: column;
            align-items: flex-start;
            flex-shrink: 0;
            align-self: stretch;
            border-right: 1px solid var(---g-20, #EEEFF2);
            background: #EDF1F5;



            .mcpToolBox {
                padding: 4px 16px 16px;
                width: 100%;
            }

            .modalName {
                display: flex;
                align-items: center;
                font-size: 16px;
                font-weight: 600;
                padding: 15px 16px;
            }

            .tabList {
                width: 100%;
                flex: 1;
                overflow-y: auto;
                padding: 0 16px;
            }

            .tabListItem {
                display: flex;
                padding: 10px 12px;
                align-items: center;
                gap: 8px;
                border-radius: 8px;
                font-size: 14px;
                cursor: pointer;

                &.active {
                    background: #E1E7ED;
                    color: #006BFF;
                    font-weight: 600;
                }

                &:hover {
                    color: #006BFF;
                }
            }
        }

        .tabContainerWrap {
            flex: 1;
            padding: 0px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            .modalOperate {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding: 12px 24px;
                gap: 24px;

                .modalOperateDesc {
                    flex: 1;
                    overflow: hidden;
                    color: #657083;
                    font-feature-settings: 'liga' off, 'clig' off;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px;
                }
            }

            .mpcListWrap {
                flex: 1;
                padding: 12px 24px;
                overflow-y: auto;

                .mpcListItem {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 16px;
                    gap: 24px;
                    border-radius: 8px;
                    border-bottom: 1px solid #EEEFF2;
                    cursor: pointer;

                    &:last-child {
                        border-color: transparent;
                    }

                    &:hover {
                        background: #F7F8FA;
                    }

                    .mcpItemInfoWrap {
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        display: flex;
                        align-items: center;
                        flex-shrink: 0;
                        gap: 20px;

                        .mcpItemIcon {
                            display: block !important;
                            width: 48px;
                            height: 48px;
                            border-radius: 50%;
                            border: 1px solid #EDF1F5;
                        }

                        .mcpItemInfo {
                            flex: 1;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            display: flex;
                            flex-direction: column;

                            .mcpItemName {
                                font-size: 14px;
                                font-weight: 600;
                            }

                            .mcpItemDesc {
                                color: #626F84;
                                font-size: 12px;
                                flex: 1;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                        }
                    }



                }

            }
        }
    }
}

.addMCPItemBtn {
    padding: 5px 16px;
    border-radius: 8px;
    border: 1px solid #2A76FF;
    background: #F8F9FF !important;
    gap: 4px !important;
    margin-top: 10px;
    height: 28px !important;
    color: #006BFF;

    &:hover {
        color: #2E8CFF !important;
        border: 1px solid #2E8CFF;

        path {
            fill: #2E8CFF !important;
        }
    }

    path {
        fill: #006BFF !important;
    }
}

.removeMCPItemBtn {
    padding: 5px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    background: #EEEFF2;
}