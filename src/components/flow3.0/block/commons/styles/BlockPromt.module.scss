.popover {
	:global {
		.ant-popover-arrow {
			&:after {
				background: #000;
			}
			&:before{
				background: #000;
			}
		}
		.ant-popover-inner-content {
			background: #000; /* 黑色背景 */
			color: #fff; /* 文字颜色 */
			border-radius: 8px;
		}
	}
}

.container {
	:global(.ant-collapse-header){
		display: flex;
		align-items: center !important;
	}
	
	.fullScreenBtn {
		display: flex;
		width: 28px;
		height: 28px;
		padding: 4px;
		justify-content: center;
		align-items: center;
		border-radius: 8px;
		border: 1px solid #ddd;
		cursor: pointer;
		margin-left: 10px;

		&:hover {
			border: 1px solid #006BFF;
			color: #006BFF;
		}
		
		&Icon {
			width: 14px;

		}
	}

}