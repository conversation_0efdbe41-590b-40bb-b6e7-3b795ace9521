.modelSelectWrap {
    position: relative;

    .modelSelect {
        & :global(.ant-select-selector) {
            border: 1px solid #BFC9D5;
        }
    }
}

.modelSelecIcon {
    cursor: pointer;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    z-index: 1;
    top: 0;
    bottom: 0;
    cursor: pointer;
    right: 30px;
    padding-right: 8px;
    color: #006BFF;
    user-select: none;

    &::after {
        content: '';
        display: block;
        width: 1px;
        height: 12px;
        background: #EDF1F5;
        position: absolute;
        right: 0;
    }

    &:hover {
        color: #2E8CFF;
    }
}

.modelSelect {
    width: 100% !important;
    cursor: pointer;

    & :global(.ant-select-selection-item) {
        .optionItem {
            background: #fff;
        }
    }

}


.optionItem {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    // background: #fff;

    .optionIcon {
        display: block !important;
        width: 14px;
        height: 14px;
        flex-shrink: 0;
        border-radius: 50%;
        overflow: hidden;
    }

    .optiontitle {
        flex: 1;
        color: #1D2531;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .mcpIcon {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            background: #E9EEFE;
            font-size: 12px;
            color: #006BFF;
            margin-left: 8px;
        }
    }

    .optionparamstitle {
        padding-right: 70px;
    }
}

.NmLLMConfigWrap {
    position: absolute;
    width: 400px;
    // top: 0;
    // left: 0;
    bottom: 80px;
    right: 45px;
    transform: translateX(-100%);
    height: auto;
    display: flex;
    padding: 16px 16px 24px;
    flex-direction: column;
    box-sizing: border-box;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 7px 17px -2px rgba(75, 85, 105, .12), 0 2px 25px 4px rgba(27, 37, 50, .07);
    font-size: 14px;
    color: #1D2531;

    .NmLLMConfigWrapTittle {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .solidDivider {
        width: 100%;
        height: 1px;
        background: rgb(238, 239, 242);
        margin: 24px 0;
    }

    .aiModuleLabelTitle {
        display: flex;
        align-items: center;
        gap: 4px;

        svg {
            cursor: pointer;
        }
    }

    .configSlider {
        display: flex;
        align-items: center;
        gap: 12px;

        .left {
            flex: 1;
            height: 40px;

            .mask {
                display: none;
                align-items: center;
                justify-content: space-between;
                color: #657083;
                margin-top: -14px;
                user-select: none;
            }

            &:hover {
                .mask {
                    display: flex;
                }
            }

            .aiModuleSlider {
                margin-right: 0;
                margin-left: 0;
            }


        }

        .input {
            flex-shrink: 0;
            height: 32px;
            width: 62px;
        }

        & :global(.ant-input-number-outlined) {
            border-color: #BFC9D5;
        }

        & :global(.ant-input-number-outlined:focus-within) {
            border-color: #1677ff;
        }

        & :global(.ant-input-number .ant-input-number-input) {
            text-align: center !important;
            cursor: pointer;
        }

    }

}