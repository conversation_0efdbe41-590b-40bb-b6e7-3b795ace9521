export const TemplateSelect = ({ attributes, children, element, onCustomEvent}: any) => {
  const baseStyle: React.CSSProperties = {
    color: '#A6C0FA',
    backgroundColor: '#F5F9FE',
    padding: '4px 8px',
    margin: '2px',
    display: 'inline-block',
    fontSize: '12px',
    borderRadius: '4px',
    cursor: 'pointer',
  }

  const handleClick = (event) => {
    event.stopPropagation()
    const domTarget = event.target;

    onCustomEvent({
      type: 'showMentionMenu',
      element: element,
      target: domTarget,
    })

  }

  return (
    <span
      onClick={handleClick}
      {...attributes}
      style={baseStyle}
      contentEditable={false}
    >
      { element.placeholder }
    </span>
  )
}