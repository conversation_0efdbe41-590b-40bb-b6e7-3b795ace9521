import { Collapse, Select, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import styles from '@/components/flow3.0/block/commons/styles/BlockLLMCollapseSelect.module.scss';
import useClickOutside from "@/hooks/useClickOutside";
import BlockNmLLMConfig from "./BlockNmLLMConfig";
import { parameterRules } from "@/utils/defaultLLMConfig";


export default function BlockNmLLMSelect(props: any) {
    const { isShowLlmParam, setIsShowLlmParam, LLMList, hasParamsIcon, blocks, activeBlockId, setBlocks } = props;
    const llmConfigRef = useRef<HTMLDivElement>(null);
    const [aiModuleConfig, setAiModuleConfig] = useState(new Array());

    const selectedRoleId = blocks[activeBlockId]?.model_params?.role_id;
    const selectedItem = LLMList.find(item => item.role_id === selectedRoleId);

    const OptionItem = ({ showMCPIcon = true, ...label }: any) => {
        return <div className={styles.optionItem}>
            <img className={styles.optionIcon} src={label.icon} alt="" />
            <div className={[styles.optiontitle, hasParamsIcon && styles.optionparamstitle].filter(Boolean).join(' ')}>
                {label.title}
                {
                    showMCPIcon && label?.support_mcp ?
                        <span className={styles.mcpIcon}>MCP</span>
                        : ''
                }
            </div>

        </div>
    }

    const handleChange = (value: any) => {
        blocks[activeBlockId].model_params.role_id = value.value;
        blocks[activeBlockId].model_params.title = value.title;
        blocks[activeBlockId].model_params.support_mcp = value.label.props?.support_mcp || false;
        initModelParam(true);
        setBlocks(pre => ({ ...blocks }))
    }

    const handleChangeParams = (e: any) => {
        e.stopPropagation();
        setIsShowLlmParam(!isShowLlmParam);
    }

    // 使用点击外部关闭hook
    useClickOutside(llmConfigRef, () => {
        if (isShowLlmParam) {
            setIsShowLlmParam(false);
        }
    }, ['.llm-select-params-icon',]); // 排除参数button元素


    const initModelParam = (isChange: any) => {
        const modelParamList = new Array();
        const modelParam: any = new Object();
        parameterRules.forEach((p: any) => {
            modelParam[p.name] = p.default
            let component = 'slider'
            if (p.type === 'float' || p.type === 'int') {
                if ((p.min || p.min === 0) && (p.max || p.max === 0)) {
                    component = 'slider'
                } else {
                    component = 'inputNumber'
                }
            } else if (p.type === 'string') {
                if (p.options.length) {
                    component = 'selector'
                } else {
                    component = 'input'
                }
            } else if (p.type === 'boolean') {
                component = 'radio'
            }

            modelParamList.push({
                required: p.required,
                name: p.label.zh_Hans,
                help: p.help,
                component: component,
                key: p.name,
                value: p.default,
                step: p.type === 'int' ? 1 : 0.1,
                min: p.min,
                max: p.max,
                options: p.options,
                type: p.type,
                scopeDesc: p.scopeDesc
            })
        })

        // 更改模型 参数赋初始值
        if (isChange) {
            blocks[activeBlockId].model_params = {
                ...blocks[activeBlockId].model_params,
                ...modelParam
            };
            setBlocks(pre => ({ ...blocks }))
        }
        setAiModuleConfig(modelParamList)
    }


    useEffect(() => {
        initModelParam(false)
    }, [])

    return <div className={styles.modelSelectWrap}>
        <Select
            className={styles.modelSelect}
            placeholder="选择一个模型"
            optionFilterProp="title"
            showSearch
            labelInValue
            // value={{ value: blocks[activeBlockId]?.model_params?.role_id || '' }}
            options={LLMList.map(item => ({
                label: <OptionItem {...item} />,
                value: item.role_id,
                title: item.title,
            }))}
            optionLabelProp="label"
            value={
                selectedItem
                    ? { value: selectedItem.role_id, label: <OptionItem {...selectedItem} showMCPIcon={false} /> }
                    : { value: blocks[activeBlockId]?.model_params?.role_id, label: blocks[activeBlockId]?.model_params?.role_id }
            }

            onChange={handleChange}
        />
        {
            hasParamsIcon ?
                <div className={[styles.modelSelecIcon, 'llm-select-params-icon'].join(' ')} onClick={handleChangeParams}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                        <path fillRule="evenodd" clipRule="evenodd" d="M9.91406 2.91406C9.91406 2.67244 10.1099 2.47656 10.3516 2.47656H12.1016C12.3432 2.47656 12.5391 2.67244 12.5391 2.91406C12.5391 3.15569 12.3432 3.35156 12.1016 3.35156H10.3516C10.1099 3.35156 9.91406 3.15569 9.91406 2.91406Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M8.02344 1.3125C8.26506 1.3125 8.46094 1.50838 8.46094 1.75V4.08333C8.46094 4.32496 8.26506 4.52083 8.02344 4.52083C7.78181 4.52083 7.58594 4.32496 7.58594 4.08333V1.75C7.58594 1.50838 7.78181 1.3125 8.02344 1.3125Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M1.16406 2.91406C1.16406 2.67244 1.35994 2.47656 1.60156 2.47656H8.01823C8.25985 2.47656 8.45573 2.67244 8.45573 2.91406C8.45573 3.15569 8.25985 3.35156 8.01823 3.35156H1.60156C1.35994 3.35156 1.16406 3.15569 1.16406 2.91406Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M1.16406 7C1.16406 6.75838 1.35994 6.5625 1.60156 6.5625H3.9349C4.17652 6.5625 4.3724 6.75838 4.3724 7C4.3724 7.24162 4.17652 7.4375 3.9349 7.4375H1.60156C1.35994 7.4375 1.16406 7.24162 1.16406 7Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M6.27344 5.39844C6.51506 5.39844 6.71094 5.59431 6.71094 5.83594V8.16927C6.71094 8.41089 6.51506 8.60677 6.27344 8.60677C6.03181 8.60677 5.83594 8.41089 5.83594 8.16927V5.83594C5.83594 5.59431 6.03181 5.39844 6.27344 5.39844Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M5.83594 7C5.83594 6.75838 6.03181 6.5625 6.27344 6.5625H12.6901C12.9317 6.5625 13.1276 6.75838 13.1276 7C13.1276 7.24162 12.9317 7.4375 12.6901 7.4375H6.27344C6.03181 7.4375 5.83594 7.24162 5.83594 7Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M9.91406 11.0859C9.91406 10.8443 10.1099 10.6484 10.3516 10.6484H12.1016C12.3432 10.6484 12.5391 10.8443 12.5391 11.0859C12.5391 11.3276 12.3432 11.5234 12.1016 11.5234H10.3516C10.1099 11.5234 9.91406 11.3276 9.91406 11.0859Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M8.02344 9.47656C8.26506 9.47656 8.46094 9.67244 8.46094 9.91406V12.2474C8.46094 12.489 8.26506 12.6849 8.02344 12.6849C7.78181 12.6849 7.58594 12.489 7.58594 12.2474V9.91406C7.58594 9.67244 7.78181 9.47656 8.02344 9.47656Z" fill="currentColor" />
                        <path fillRule="evenodd" clipRule="evenodd" d="M1.16406 11.0859C1.16406 10.8443 1.35994 10.6484 1.60156 10.6484H8.01823C8.25985 10.6484 8.45573 10.8443 8.45573 11.0859C8.45573 11.3276 8.25985 11.5234 8.01823 11.5234H1.60156C1.35994 11.5234 1.16406 11.3276 1.16406 11.0859Z" fill="currentColor" />
                    </svg>
                    参数
                </div>
                : ''
        }

        {
            hasParamsIcon ?
                <BlockNmLLMConfig
                    {...
                    {
                        ...props,
                        LLMList,
                        isShowLlmParam,
                        pref: llmConfigRef,
                        aiModuleConfig,
                    }
                    }
                />
                : ''

        }
    </div>
}