import ReactDOM from 'react-dom';
import { Input<PERSON><PERSON><PERSON>, Slider, Spin, Tooltip } from 'antd';
import BlockNmLLMSelect from "./BlockNmLLMSelect";
import styles from '@/components/flow3.0/block/commons/styles/BlockLLMCollapseSelect.module.scss';
import flowStyles from '@/styles/flow3.0/Flow.module.scss';

interface ModelIcon {
    [key: string]: string;
}

export default function BlockNmLLMConfig(prop: any) {
    const { aiModuleConfig, pref, isShowLlmParam, blocks, activeBlockId, setBlocks } = prop;
    const target = document.getElementById('blockDetail');


    const Tips = (props: any) => {
        const { title } = props;
        return <Tooltip placement="topLeft" title={title} >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                <g clip-path="url(#clip0_23790_4917)">
                    <path fillRule="evenodd" clipRule="evenodd" d="M2.40381 2.4038C3.57959 1.22804 5.20521 0.5 7 0.5C8.79479 0.5 10.4204 1.22804 11.5962 2.4038L11.3103 2.6897L11.5962 2.40381C12.772 3.57959 13.5 5.20521 13.5 7C13.5 8.79479 12.772 10.4204 11.5962 11.5962C10.4204 12.772 8.79479 13.5 7 13.5C5.20521 13.5 3.57959 12.772 2.40381 11.5962L2.6897 11.3103L2.4038 11.5962C1.22804 10.4204 0.5 8.79479 0.5 7C0.5 5.20521 1.22804 3.57959 2.4038 2.40381L2.40381 2.4038ZM7 1.40698C5.45542 1.40698 4.05778 2.0325 3.04513 3.04514C2.0325 4.05779 1.40698 5.45542 1.40698 7C1.40698 8.54458 2.0325 9.94222 3.04513 10.9549L2.72447 11.2755L3.04513 10.9549C4.05779 11.9675 5.45542 12.593 7 12.593C8.54458 12.593 9.94222 11.9675 10.9549 10.9549C11.9675 9.94222 12.593 8.54458 12.593 7C12.593 5.45542 11.9675 4.05779 10.9549 3.04513C9.94222 2.0325 8.54458 1.40698 7 1.40698Z" fill="#626F84" />
                    <path fillRule="evenodd" clipRule="evenodd" d="M4.8125 5.42969C4.8125 4.22157 5.79188 3.24219 7 3.24219C8.20812 3.24219 9.1875 4.22157 9.1875 5.42969C9.1875 6.48798 8.43598 7.37075 7.4375 7.57343V8.34635C7.4375 8.58798 7.24162 8.78385 7 8.78385C6.75838 8.78385 6.5625 8.58798 6.5625 8.34635V7.17969C6.5625 6.93806 6.75838 6.74219 7 6.74219C7.72487 6.74219 8.3125 6.15456 8.3125 5.42969C8.3125 4.70482 7.72487 4.11719 7 4.11719C6.27513 4.11719 5.6875 4.70482 5.6875 5.42969C5.6875 5.67131 5.49162 5.86719 5.25 5.86719C5.00838 5.86719 4.8125 5.67131 4.8125 5.42969Z" fill="#626F84" />
                    <path fillRule="evenodd" clipRule="evenodd" d="M7.0026 10.974C7.40531 10.974 7.73177 10.6475 7.73177 10.2448C7.73177 9.84209 7.40531 9.51562 7.0026 9.51562C6.5999 9.51562 6.27344 9.84209 6.27344 10.2448C6.27344 10.6475 6.5999 10.974 7.0026 10.974Z" fill="#626F84" />
                </g>
                <defs>
                    <clipPath id="clip0_23790_4917">
                        <rect width="14" height="14" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </Tooltip>
    }

    const roundByStep = (num: number | string | null | undefined, step: number): number => {
        const value = Number(num);
        if (!isFinite(value) || !isFinite(step)) return value;

        const stepStr = step.toString();
        const decimalPlaces = stepStr.includes('.')
        ? stepStr.split('.')[1].length
        : 0;

        return parseFloat(value.toFixed(decimalPlaces));
    }

    const setNewBlocks = (key: string, value: number) => {
        blocks[activeBlockId]['model_params'][key] = value;
        setBlocks(pre => ({...blocks}))
    } 


    const handleChageValue = (key: string, step: number, min: number, max: number, value: any) => {
        if(value<min) return setNewBlocks(key, min);
        if(value>max) return setNewBlocks(key, max);
        setNewBlocks(key, roundByStep(value, step))
    }


    const Content: React.FC = () => {
        return <div ref={pref} className={[isShowLlmParam && styles.NmLLMConfigWrap].filter(Boolean).join(' ')}>
            <div className={styles.NmLLMConfigWrapTittle}>模型</div>
            <BlockNmLLMSelect  {...{ ...prop, hasParamsIcon: false }} />
            <div className={styles.solidDivider}></div>
            <div className={styles.NmLLMConfigWrapTittle} style={{ marginBottom: '12px' }}>参数</div>
            {/* <Spin spinning={pLoading}> */}
            <Spin spinning={false}>
                <div onClick={(e) => e.stopPropagation()} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {aiModuleConfig?.map((config: any) => {
                        if (config.component === 'slider') {
                            return (
                                <div key={config.key} className={styles.aiModuleItem}>
                                    <div className={styles.aiModuleLabel + ' ' + flowStyles.aiModuleLabel}>
                                        <div className={styles.aiModuleLabelTitle}>
                                            {config.required && <span style={{ color: "red" }}>*</span>}
                                            {config.name}
                                            <Tips title={config.help.zh_Hans} />
                                        </div>
                                        {/* <span>{blocks[activeBlockId]['model_params']?.[config.key] || config.value || 0}</span> */}
                                    </div>
                                    <div className={styles.configSlider}>
                                        <div className={styles.left}>
                                            <Slider
                                                min={config.min}
                                                max={config.max}
                                                defaultValue={blocks[activeBlockId]['model_params']?.[config.key] || config.value || 0}
                                                className={styles.aiModuleSlider + ' commonSlider'}
                                                step={config.step}
                                                onChange={(v: any) => {
                                                    blocks[activeBlockId]['model_params'][config.key] = v;
                                                }}
                                                onChangeComplete={(v: any) => {
                                                    setBlocks(pre => ({ ...blocks }))
                                                }}
                                            />
                                            <div className={styles.mask}><span>{config.scopeDesc.min}</span><span>{config.scopeDesc.max}</span></div>
                                        </div>
                                        <InputNumber
                                            type='number'
                                            className={styles.input}
                                            controls={false}
                                            min={config.min}
                                            max={config.max}
                                            value={blocks[activeBlockId]['model_params']?.[config.key] || config.value || 0}
                                            onPressEnter={(e: any) => {
                                                handleChageValue(config.key, config.step, config.min, config.max, e.target.value)
                                            }}
                                            onBlur={(e: any) => {
                                                handleChageValue(config.key, config.step, config.min, config.max, e.target.value)
                                            }}
                                            onChange={(e: any) => {
                                                blocks[activeBlockId]['model_params'][config.key] = e;
                                            }}
                                        />
                                    </div>
                                </div>
                            )
                        }
                        return null;
                    })}
                </div>
            </Spin>
        </div>
    }

    return <>
        {
            isShowLlmParam && ReactDOM.createPortal(
                <Content />,
                target
            )
        }
    </>
}