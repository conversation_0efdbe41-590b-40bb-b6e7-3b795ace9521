class ModelIcon {
  modelIconMap: Map<string, string> = new Map();
  iconCache: Map<string, string> = new Map();

  constructor() {
    this.modelIconMap = new Map();
    this.iconCache = new Map();
  }

  public getModelIcon(provider: string): string {
    return this.modelIconMap.get(provider) || '';
  }

  public setModelIcon(provider: string, iconPath: string): void {
    this.modelIconMap.set(provider, iconPath);
  }

  public getModelIconCache(provider: string): string {
    return this.iconCache.get(provider) || '';
  }

  public setModelIconCache(provider: string, imageUrl: string): void {
    this.iconCache.set(provider, imageUrl);
  }
  
}

const modelIcon = new ModelIcon();

export default modelIcon;