import BlockPrompt from './BlockPrompt';
import { useMemo } from 'react';

interface BlockUserPromptProps {
  isUserFullScreen: boolean;
  setIsUserFullScreen: (value: boolean) => void;
  activeBlockId: string;
  blocks: any;
  loopConfigs?: any;
  setBlocks: (blocks: any) => void;
  showTooltip?: boolean;
  title?: string;
  tooltipText?: string;
  chainId?: string;
  flowInfo?: any;
	resizeWidth: () => void;
	borderTop?: boolean;
	borderBottom?: boolean;
}

export default function BlockUserPrompt({
  isUserFullScreen,
  setIsUserFullScreen,
  activeBlockId,
  blocks,
  loopConfigs,
  setBlocks,
  showTooltip = true,	
  title = '提示词(Prompt)',
  tooltipText = '在这里写你的提示词，输入“/” 绑定“用户输入、智能体输出、已配置MCP工具、用户界面”',
  flowInfo,
	resizeWidth,
	borderTop,
	borderBottom
}: BlockUserPromptProps) {
  return (
    <BlockPrompt
      flowInfo={flowInfo}
      key={activeBlockId}
      isFullScreen={isUserFullScreen}
      setIsFullScreen={setIsUserFullScreen}
      title={title}
      tooltipText={tooltipText}
      showTooltip={showTooltip}
			activeBlockId={activeBlockId}
			blocks={blocks}
      loopConfigs={loopConfigs}
			resizeWidth={resizeWidth}
			setBlocks={setBlocks}
			borderTop={borderTop}
			borderBottom={borderBottom}
    />
  );
}
