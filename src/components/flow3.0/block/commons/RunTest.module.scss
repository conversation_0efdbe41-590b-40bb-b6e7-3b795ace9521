.runTestPage {
    width: 100%;
    position: absolute;
    top: 53px;
    left: 0;
    background: #ffffff;
    height: calc(100% - 55px);
    z-index: 500;
    // border-left: 1px solid #E1E7ED;
    /* 投影/上/弹出投影（4级） */
    box-shadow: 0px -10px 30px -3px rgba(75, 85, 105, 0.10), 0px -15px 45px 7px rgba(27, 37, 50, 0.06);
    padding: 0 5px;
    border-radius: 0 0 8px 8px;
}
.runTestPageAll {
    top: 0;
    height: 100%;
    border-radius: 8px;
}
.runTestPageBtnRun {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: #ffffff;
    border-top: 1px solid rgba(82,100,154, 0.13);
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 24px 16px;
    border-radius: 0 0 8px 8px;
}
.runTestPageHeader {
    padding: 0 18.5px 0 20px; 
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(82,100,154, 0.13);
    width: 100%;
    height: 60px;

    img {
        cursor: pointer;
    }
}
.runDebugTitle {
    color: #1B2532;
    font-weight: 600;
}
.runFlowPageTitle {
    color: #1B2532;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
}
.runTestPageContent {
    padding-bottom: 40px;
    margin-top: 60px;
    width: 100%;
    height: calc(100% - 120px);
    max-height: calc(100% - 120px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;

}
.runTestPageBox {
    padding: 12px 8px 12px 12px;
    border-bottom: 1px solid rgba(82,100,154, 0.13);
}
.htmlBox {
    border: 1px solid #EDF1F5;
    padding: 8px 12px;
    box-sizing: border-box;
    font-size: 14px;
    border-radius: 8px;
    min-height: 100px;
    p,span {
        display: inline-block;
    }
    span {
        line-height: 24px;
        font-size: 14px;
        vertical-align: unset !important;
    }
}
.runBtn {
    width: 100%;
    display: flex;
    height: 32px;
    padding: 5px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid #006BFF;
    color: #006BFF;
    background: rgba(0, 107, 255, 0.04);
    cursor: pointer;
    font-size: 14px;
    img {
        margin-top: 1px;
    }
}
.runBtnGray,.runBtnGrayNo {
    width: 100%;
    display: flex;
    height: 32px;
    padding: 5px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid #D8E0E8;
    color: #1B2532;
    background: #EBF0F5;
    cursor: pointer;
    font-size: 14px;
    img {
        margin-top: 1px;
    }
}
.runBtnGrayNo {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
    background-color: rgba(0, 0, 0, 0.04);
}
.runBtnGray {
    &:hover {
        color: #333333;
    }
}
.runFlowEditBox {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    // height: 112px;
    box-sizing: border-box;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 24px 16px 16px 16px;
    border-radius: 0 0 8px 8px;
}
.runFlowBtnContent {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    // height: 80px;
    padding: 12px;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    flex: 1 0 0;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    background: #FFF;
}
.runFlowBtnContentActive {
    border: 1px solid rgb(0, 107, 255);
}
.runFlowBtnTextAreaBox {
    width: 100%;
}
.runFlowBtnArea {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.runFlowBtn {
    width: 24px;
    padding: 0;
    border: 0;
    background-color: unset;
     
    img {
        width: 24px;
        height: 24px;
    }
}
.runFlowNodeDelete {
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
}
.runFlowNodeHeader {
    display: flex;
    align-items: center;
    gap: 8px;
    color:#1D2531;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    
    &:hover {
        .runFlowNodeDelete {
            display: flex;
        }
    }
}
.runFlowNodeHeaderTitle {
    max-width: 200px;
    font-size: 14px;
    // width: 200px;
    // min-width: 200px;
}
.runFlowNodeContentTitle {
    max-width: 200px;
    // width: 200px;
    min-width: 200px;
}
.runFlowNodeHeaderStatus {
    display: flex;
    height: 20px;
    padding: 0px 6px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    
    font-family: "PingFang SC";
    font-size: 13px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    border-radius: 4px;
    color: #006AFF;
    background: rgba(0, 106, 255, 0.10);
}
.runFlowNodeHeaderStatus2 {
    color: #006AFF;
    background: rgba(0, 106, 255, 0.10);
}
.runFlowNodeHeaderStatus3 {
    color: #1DAB69;
    background: rgba(29, 171, 105, 0.10);
}
.runFlowNodeHeaderStatus4 {
    color: #F53F3F;
    background: rgba(245, 63, 63, 0.10);
}
.runFlowNodeHeaderStatus6 {
    color: #657083;
    background: rgba(158, 167, 184, 0.20);
}
.runFlowNodeLine {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.runFlowNodeContent {
    gap: 0 !important;
    padding: 12px 16px;
    border-radius: 8px;
    background: #FFF;
    display: flex;
}
.runFlowNodeContentHidden {
    display: none;
}
.runFlowNodeContentDetail {
    display: flex;
    align-items: center;
    justify-content: space-between;

    color: #1D2531;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; 

    // img {
    //     width: 20px;
    //     height: 20px;
    // }
}
.runFlowNodeContentDetailError {
    color: red;
}
.runFlowNodeContentDetailBtn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #006BFF;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    cursor: pointer;
}
.runFlowNodeFooter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #657083;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    width: 100%;
}
.runFlowNodeFooterLeft {
    width: 60%;
    display: flex;
    align-items: center;
    div {
        display: flex;
        align-items: center;
    }
}
.runFlowNodeFooterRight {
    width: 40%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    div {
        display: flex;
        align-items: center;
    }
}

.rotate {
    animation: circle 2s linear infinite;
}
@keyframes circle {
  0% {
     transform: rotate(0deg);
  }
  100% {
     transform: rotate(360deg);
  }
}
.runFlowNodeQuery {
    display: flex;
    // justify-content: flex-end;
    padding: 10px 16px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 12px;
    background: #006BFF;

    color:#FFF;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.debugHistoryBox {
    position: absolute;
    top: -53px;
    right: 450px;
    display: flex;
    width: 400px;
    height: 424px;
    box-sizing: border-box;
    padding: 16px 16px 24px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;
    background: #FFF;
    box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
}
.historyLine {
    width: 100%;
    height: 1px;
    background: #EEEFF2;
}
.debugHistoryList {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    max-height: 330px;
    position: relative;
}

.debugHistoryTitle {
    color: #1B2532;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
}
.debugHistoryItem {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 0 12px;
    justify-content: space-between;
    height: 38px;
    padding: 8px 12px;
    box-sizing: border-box;

    color: #1D2531;
    text-align: right;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;

    &:hover {
        border-radius: 8px;
        background: #F7F9FA;
        cursor: pointer;
        color: #006BFF;
    }
}
.runTestPageDebugResult {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
}