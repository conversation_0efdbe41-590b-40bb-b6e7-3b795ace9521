import { useState, useEffect, useRef } from 'react';
import style from '@/components/flow3.0/block/commons/RunTest.module.scss';
import { Button, Input, InputNumber, Radio, Spin, Collapse, message, Modal, Divider } from 'antd'
import CloseImg from '@/images/newFlow/close.svg';
import JsonViewCom from '@/components/commonComponents/jsonView/index';
import ImgRunStop from '@/images/flow3.0/runStop.svg';
const { TextArea } = Input;
import ImgRunStopDisabled from '@/images/flow3.0/runStopDisabled.png';
import ImgRunFlowSend from '@/images/flow3.0/runFlowSend.svg';
import ImgRunFlowSendDisabled from '@/images/flow3.0/runFlowSendDisabled.svg';
import Fail from "@/images/newFlow/icon-status-fail.svg";
import Success from "@/images/newFlow/icon-status-success.svg";
import Noupdate from "@/images/newFlow/icon-status-noupdate.svg";
import Running from "@/images/newFlow/icon-status-running.svg";
import Cancel from "@/images/newFlow/icon-status-cancel.svg";
const statusMapping = {
//   "-1": { label: "异常", color: "#FFECE8", icon: Exception },
//   "1": { label: "未调试", color: "#FFECE8", icon: Unused },
  "2": { label: "运行中", color: "#E8F7FF", icon: Running.src },
  "3": { label: "运行成功", color: "#E8FFEA", icon: Success.src },
  "4": { label: "运行失败", color: "#FFECE8", icon: Fail.src },
  "5": { label: "已编辑待更新", color: "#FFF7E8", icon: Noupdate.src },
  "6": { label: "已取消", color: "#EDF1F5", icon: Cancel.src },
//   "7": { label: "暂停", color: "#FFF7E8", icon: Pause },
//   "8": { label: "未调用", color: "#EDF1F5", icon: Unused },
};
import ExpandPage from '@/components/commonComponents/expand'
import useElementSize from "@/hooks/useElementSize";
import ImgClear from '@/images/flow3.0/clear.svg';
import NodeDetail from '@/components/flow3.0/block/commons/Run/nodeDetail';
import {reqGetRunFlowHistory, reqGetRunFlowDetail, reqDelRunFlowNodeHistory} from '@/service/flow3.0'
import { iconList } from "@/components/flowEditor3.0/flowConfig";
import dayjs from 'dayjs';
import ImgDelete from '@/images/flow3.0/delete.svg';
import ImgNMLogo from '@/images/flow3.0/namilogo.svg';
import ImgSeaFactoryLogo from '@/images/flow3.0/seafactorylogo.svg';
// const defaultValue = {
//     string: '',
//     number: 0,
//     boolean: false,
//     array: [],
//     'arr[string]': [],
//     'arr[number]': [],
//     'arr[object]': [],
//     object: {},
// };
// const objMap = ['arr[number]', 'arr[string]', 'arr[object]', 'object', 'array'];
const titleJsonStyle = {
    color: '#657083',
    fontSize: '12px',
    fontWeight: '400'
};
function RunTestPage(props: any) {
    const { setIsRunTest, blocks, activeBlockId, runTestModel, flowLoading, blockLoading, onRun, onStop, runNodeResultValue, currentRunCallBackId, chainId, flowInfo } = props;
    const [formList, setFormList] = useState([]);
    const [runTitle, setRunTitle] = useState('运行');
    const [flowHistoryList, setFlowHistoryList] = useState([]);
    const flowRunningObjRef = useRef(null);
    const [queryValue, setQueryValue] = useState('');
    const runFlowEditRef = useRef(null);
    const runFlowEditRefObj = useElementSize(runFlowEditRef);
    const historyEndRef = useRef(null);
    const [openNodeDetail, setOpenNodeDetail] = useState(false);

    const [nodeDetailData, setNodeDetailData] = useState({});
    const [hasMore, setHasMore] = useState(false);
    const [page, setPage] = useState(1);
    const [isLoadingData, setIsLoadingData] = useState(false);
    const [pageSize, setPageSize] = useState(10);
    const scrollContainerRef = useRef(null);
    const [loadingFlowDetailObj, setLoadingFlowDetailObj] = useState({});
    const [isQueryFocus, setIsQueryFocus] = useState(false);

    useEffect(() => {
        handleGetFlowHistoryList();
        return () => {
            handleClearRunning()
        }
    }, []);

    useEffect(() => {
        if (flowLoading) {
            setFlowHistoryList((prev) => [...prev, {
                title: flowInfo.title,
                desc: flowInfo.desc,
                images: flowInfo.image,
                inputs: {
                    query: queryValue,
                },
                nodes: [],
                results: [],
                chain_status: 2,
                isFromAgent: true
            }])
            setQueryValue('');
            let timer = setTimeout(() => {
                scrollToHistoryBottom();
                clearTimeout(timer);
            }, 1000);
        }
    }, [flowLoading]);


    useEffect(() => {
        if (!currentRunCallBackId && runNodeResultValue?.flow_status == 4) {
            setFlowHistoryList((prev) => {
                return prev.map(item => {
                    if (!item.call_back_id && item?.inputs?.query === runNodeResultValue.query) {
                        let obj = {
                            ...item,
                            chain_status: 4,
                            nodes: [],
                            results: [],
                            call_back_id: '',
                            start_time: runNodeResultValue.start_time,
                            starttime: runNodeResultValue.start_time ? dayjs(runNodeResultValue.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '',
                            exec_time: runNodeResultValue.exec_time,
                            isFromAgent: true
                        };
                        return obj;
                    }
                    return item;
                });
            })
        }
        if (currentRunCallBackId) {
            let blockEndListResult: any = [];
            let nodes = [];
            Object.keys(runNodeResultValue).map((item: any) => {
                if (!['flow_status', 'query', 'start_time', 'exec_time'].includes(item)) {
                    let curNode = runNodeResultValue[item];
                    if (curNode?.is_end) {
                        blockEndListResult.push(
                            {
                                title: '输出-'+ curNode?.name,
                                value: {output: curNode?.result?.output || ''}
                            }
                        );
                    }
                    if ([2, 3, 4, 5, 6].includes(curNode?.status)) {
                        nodes.push(
                            {
                                ...curNode,
                                imgUrl: iconList[curNode?.meta?.iconIndex]?.image.src,
                                imgUrlBgColor: curNode?.meta?.color,
                            }
                        );
                    }
                }
            });
            nodes = nodes.sort((a: any, b:any) => a?.update_time_sort - b?.update_time_sort);
            setFlowHistoryList((prev) => {
                return prev.map(item => {
                    if (item.call_back_id === currentRunCallBackId || (!item.call_back_id && item?.inputs?.query === runNodeResultValue.query)) {
                        let obj = {...item, chain_status: runNodeResultValue.flow_status,
                            nodes, results: blockEndListResult,
                            call_back_id: currentRunCallBackId,
                            start_time: runNodeResultValue.start_time,
                            starttime: runNodeResultValue.start_time ? dayjs(runNodeResultValue.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '',
                            exec_time: runNodeResultValue.exec_time,
                            isFromAgent: true
                        };
                        return obj;
                    }
                    return item;
                });
            });
        }
    }, [runNodeResultValue, flowLoading, currentRunCallBackId]);

    const handleClearRunning = () => {
        if (flowRunningObjRef.current) {
            Object.keys(flowRunningObjRef.current).forEach(key => {
                if (flowRunningObjRef.current[key] != 3 && flowRunningObjRef.current[key] != 4) {
                    clearInterval(flowRunningObjRef.current[key]);
                }
            });
        }
    }

    const scrollToHistoryBottom = () => {
        if (historyEndRef.current) {
            historyEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    };

    const handleGetFlowDetail = async (params: any) => {
        try {
            let res = await reqGetRunFlowDetail({
                log_id: params.log_id
            });
            console.error('-------------detail------------');
            console.error(res)
            if (res) {
                setFlowHistoryList((prev) => {
                    let list = [...prev];
                    list = list.map((item: any) => {
                        if (item.log_id === res.log_id) {
                            let obj = {...item, ...res};
                            let logs = res.logs;
                            logs = logs.map((logItem: any) => {
                                logItem.msg = JSON.parse(logItem.message);
                                return logItem;
                            });
                            obj.logs = logs;
                            obj.results = [];
                            obj.nodes = [];

                            let logsInputs = logs.filter((log: any) => log?.msg?.log_type === 'nami_engine_log_input');
                            if (logsInputs.length) {
                                logsInputs.forEach((logItem: any) => {
                                    let block_id = logItem?.msg?.block_id || '';
                                    if (block_id) {
                                        let nodeObj = obj?.chain_config?.blocks[block_id] || {};
                                        let curLogResults = logs.find((log: any) => log.msg.block_id == block_id && log?.msg?.log_type === 'nami_engine_log_output');
                                        nodeObj = {
                                            ...nodeObj,
                                            result: curLogResults?.msg?.result || '',
                                            inputs: {...(nodeObj?.inputs || {}), ...(logItem?.msg?.inputs || {})},
                                            status: curLogResults?.msg?.status || 2,
                                            imgUrl: iconList[nodeObj?.meta?.iconIndex]?.image.src,
                                            imgUrlBgColor: nodeObj?.meta?.color,
                                            block_index: block_id
                                        }
                                        obj.nodes.push(nodeObj);
                                        
                                        if (obj?.inputs?.flow_end_blocks && obj?.inputs?.flow_end_blocks.length > 0 && obj?.inputs?.flow_end_blocks?.includes(block_id)) {
                                            obj.results.push({
                                                title: '输出-' + nodeObj?.name,
                                                value: {
                                                    output: nodeObj?.result?.output || '',
                                                }
                                            })
                                        }
                                    }
                                })
                            }
                            return obj;
                        }
                        return item;
                    });
                    console.log('----------list', list);
                    return list;
                })
                if (res?.chain_status !== 2 && flowRunningObjRef.current && flowRunningObjRef.current[res?.log_id]) {
                    clearInterval(flowRunningObjRef.current[res?.log_id]);
                    // delete flowRunningObjRef.current[res?.log_id];
                }
                setLoadingFlowDetailObj((prev) => {
                    return {
                        ...prev,
                        [res?.log_id]: false
                    }
                });
            }
        } catch (error) {
            
        }
    };


    const handleGetFlowHistoryList = async() => {
        setIsLoadingData(true);
        try {
            let res = await reqGetRunFlowHistory({
                template_id: chainId,
                run_type: 'debugflow',
                page,
                page_size: pageSize,
                task_type: 1,
            });
            console.error('---------获取运行历史记录列表---------');
            console.log(res);
            if (res.data && Array.isArray(res.data)) {
                let list = res.data.reverse();
                let resultList: any = [];
                list.forEach((item: any) => {
                    let obj = {...item, starttime: item.start_time ? dayjs(item.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''};
                    // 节点详情
                    obj.nodes = [];
                    // 运行结果
                    obj.results = [];
                    
                    resultList.push(obj);
                })
                setFlowHistoryList((prev) => {
                    return [...resultList, ...prev,]
                });

                if (page == 1) {
                    let timer = setTimeout(() => {
                        scrollToHistoryBottom();
                        clearTimeout(timer);
                       
                    }, 500);
                } else {
                    let timer = setTimeout(() => {
                        if (scrollContainerRef.current) {
                            scrollContainerRef.current.scrollTop = 270 * list.length;
                        }
                        clearTimeout(timer);
                    }, 500);
                }

                if (res.page * res.page_size < res.total) {
                    setHasMore(true);
                    setPage(() => page + 1);
                } else {
                    setHasMore(false);
                }
                setIsLoadingData(false);
            }

        } catch (error) {
            setHasMore(false);
            setIsLoadingData(false);
        }
    };
     
    const handleRunTest = () => {
        let runParams: any = {};
        if (runTestModel === 'all') {
            runParams.query = queryValue;
        } else {
            formList.forEach((item: any) => {
                runParams[item.name] = item.value;
            });
        }
        console.log(runParams, 'on run params');
        onRun(runParams)
    };
    const handleRunStop = () => {
        onStop && onStop('all');
    }

    const handleTogglePanel = (open, type, item: any = {}) => {
        if (open && type) {
            if (item[type] && item[type].length) {
                return;
            }
            if (!item?.call_back_id && !item?.log_id) {
                message.warning('无log_id !');
                return;
            }
            if (item?.call_back_id && flowLoading && !item?.log_id) {
                return;
            }
            if (item?.call_back_id && !flowLoading && !item?.log_id) {
                item.log_id = item?.call_back_id;
                setFlowHistoryList((prev) => {
                    return prev.map(itemPrev => {
                        return itemPrev.call_back_id === item.log_id ? { ...itemPrev, log_id: item.call_back_id } : itemPrev
                    })
                })
            }
            if (!item?.log_id || (item?.log_id && flowRunningObjRef.current && flowRunningObjRef.current[item.log_id])) {
                return;
            }
            
            handleGetFlowDetail(item);
            if (item?.chain_status == 2) {
                let timer = setInterval(() => {
                    handleGetFlowDetail(item);
                }, 3000);
                flowRunningObjRef.current = {
                    ...(flowRunningObjRef.current || {}),
                    [item.log_id]: timer,
                };
            } else {
                flowRunningObjRef.current = {
                    ...(flowRunningObjRef.current || {}),
                    [item.log_id]: item?.chain_status,
                };
            }
            setLoadingFlowDetailObj((prev) => {
                return {
                    ...prev,
                    [item.log_id]: true
                }
            });
        }
    }
    // 处理键盘按下事件
    const handleKeyDown = (e: any) => {
        console.log(e)
        // 判断是否按下 Enter 键（key 可能为 'Enter' 或 'Return'，兼容不同设备）
        const isEnter = e.key === 'Enter' || e.key === 'Return';
        if (isEnter) {
            // 若未按 Shift 键，则触发发送
            if (!e.shiftKey && !flowLoading) {
                e.preventDefault(); // 阻止默认换行行为
                handleRunTest();
                return;
            }
            if (!e.shiftKey && flowLoading) {
                e.preventDefault();
                return;
            }
            // 若按了 Shift + Enter，则不干预，保留默认换行
        }
    };


    return (
        <div className={style.runTestPage + ' ' + (runTestModel === 'all' ? style.runTestPageAll : '')}>
            <div className={style.runTestPageHeader}>
                <div className={style.runFlowPageTitle}>{runTitle}</div>
                <div className='h-full flex gap-3 items-center'>
                    {/* <img src={ImgClear.src} onClick={() => {
                        setFlowHistoryList([]);
                        setHasMore(false);
                        handleClearRunning();
                    }} />  */}
                     <img src={CloseImg.src} onClick={() => {
                        if (!flowLoading) {
                            setIsRunTest(false);
                        }
                    }} /> 
                </div>
            </div>

            <div className={style.runTestPageContent} style={{
                height: `calc(100% - 120px - ${runFlowEditRefObj.height + 'px'})`,
                maxHeight: `calc(100% - 120px - ${runFlowEditRefObj.height + 'px'})`
            }}  ref={scrollContainerRef}>
                {
                    hasMore ? <div className='p-4 relative'>
                        <div className={style.runBtn} onClick={() => {
                            handleGetFlowHistoryList()
                        }}>
                            加载更多
                        </div>
                        {
                            isLoadingData && <div className='w-full h-full flex justify-center items-center absolute top-0 left-0 z-101 bg-[rgba(255,255,255,0.9)] rounded-lg'>
                                <Spin wrapperClassName="runTestLoading">
                                </Spin>
                            </div>
                        }
                    </div> : <></>
                }
                 
                {
                    flowHistoryList.map((item: any, index: number) => {
                        return <div className='flex flex-col gap-3 p-4'>
                            <div className='flex justify-end'>
                                <div className={style.runFlowNodeQuery}>{item?.inputs?.query}</div>
                            </div>
                            <ExpandPage
                                isLoading={loadingFlowDetailObj[item?.log_id || item?.call_back_id || '']}
                                header={<div className={style.runFlowNodeHeader}>
                                    <div><img src={item.images} className='rounded-[4px]' width={24} height={24}/></div>
                                    <div className={'singleLineEllipsis ' + style.runFlowNodeHeaderTitle}>
                                        {item.title}
                                    </div>
                                    {/* <img src={item?.task_type == 3 ? ImgNMLogo.src : ImgSeaFactoryLogo.src} width={16} height={16} /> */}
                                    <div className={style.runFlowNodeDelete}>
                                        {item?.chain_status != 2 ? <img className="cursor-pointer" src={ImgDelete.src} width={16} height={16} onClick={async (e: any) => {
                                            e.stopPropagation();
                                            try {
                                                let res = await reqDelRunFlowNodeHistory({log_id: item.log_id});
                                                if (res && res.log_id) {
                                                    setFlowHistoryList((prev) => {
                                                        return prev.filter(item => item.log_id !== res.log_id);
                                                    });
                                                    message.success('删除成功!')
                                                }
                                            } catch (error) {
                                            }
                                        }}/> : <></>}
                                    </div>
                                </div>}
                                arrowLeft={
                                    item?.chain_status ? <div className={style.runFlowNodeHeaderStatus + ' ' + (style['runFlowNodeHeaderStatus' + item.chain_status])}>{statusMapping[item.chain_status]?.label}</div> : <></>
                                }
                                defaultExpanded={item?.isFromAgent || false}
                                contentClass={item?.nodes?.length ? style.runFlowNodeContent : style.runFlowNodeContentHidden}
                                children={
                                    item?.nodes?.length ? item?.nodes?.map((itemCur: any, index: any) => {
                                        return (
                                            <div>
                                                <div className={style.runFlowNodeContentDetail + ' ' + (itemCur.status == 4 ? style.runFlowNodeContentDetailError : '')}>
                                                    <div className='flex items-center gap-4'>
                                                        <img src={statusMapping[itemCur.status]?.icon} className={itemCur.status == 2 ? style.rotate : ''} width={20} height={20} />
                                                        <div className='flex items-center gap-x-1'>
                                                            {
                                                                !itemCur?.imgUrlBgColor ? <img src={statusMapping[itemCur.status]?.icon} className={itemCur.status == 2 ? style.rotate : ''} width={20} height={20} /> :
                                                                <div className='flex items-center justify-center w-[20px] h-[20px] rounded-[6px]' style={{
                                                                    backgroundColor: itemCur.imgUrlBgColor,
                                                                }}>
                                                                    <img src={itemCur.imgUrl} width={12} height={12} />
                                                                </div>
                                                            }
                                                            <div className={'singleLineEllipsis ' + style.runFlowNodeContentTitle}>{itemCur.name || ''}</div>
                                                        </div>
                                                    </div>
                                                    {itemCur.status == 2 ? <div></div> :<div className={style.runFlowNodeContentDetailBtn} onClick={() => {
                                                        setNodeDetailData({...itemCur, log_id: item.log_id || item.call_back_id || ''})
                                                        setOpenNodeDetail(true);
                                                    }}>详情</div>}
                                                </div>
                                                {index === item.nodes.length - 1 ? <></> : <div className={style.runFlowNodeLine}>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="2" height="18" viewBox="0 0 2 18" fill="none">
                                                        <path d="M1 1V17" stroke="#BFC9D5" stroke-linecap="round"/>
                                                    </svg>
                                                    </div>}
                                            </div>
                                        )
                                    }) : <></>
                                }
                                footer={
                                    <div className={style.runFlowNodeFooter}>
                                        <div className={style.runFlowNodeFooterLeft}><div>开始时间：</div><div>{item.starttime}</div></div>
                                        <div className={style.runFlowNodeFooterRight}><div>运行时长： </div><div>{item.exec_time ? item.exec_time + 's' : ''}</div></div>
                                    </div>
                                }
                                onTogglePanel={(open) => {
                                    handleTogglePanel(open, 'nodes', item);
                                }}
                            />
                            {[3, 4].includes(item?.chain_status) ? <ExpandPage
                                isLoading={loadingFlowDetailObj[item?.log_id || item?.call_back_id || '']}
                                header={<div>结果</div>}
                                defaultExpanded={item?.isFromAgent || false}
                                children={
                                    item?.results?.length ? item.results.map((itemRes: any, index: any) => {
                                        return (
                                            <JsonViewCom key={index} data={itemRes.value || {}} title={itemRes.title} style={{ backgroundColor: '#ffffff' }} titleStyle={titleJsonStyle} preview={'output'} />
                                        )
                                    }) : <></>
                                }
                                onTogglePanel={() => {
                                    handleTogglePanel(open, 'results', item);
                                }}
                            /> : <></>}
                        </div>
                    })
                }
                <div ref={historyEndRef} />
            </div>

            {/* {
                (blockLoading || flowLoading) && (
                    <div className='w-full h-full flex justify-center items-center absolute top-0 left-0 z-101 bg-[rgba(255,255,255,0.9)] rounded-lg'>
                        <Spin tip={runTitle + '中......'} wrapperClassName="runTestLoading">
                            <div style={{
                                padding: '4px 50px',
                            }} />
                        </Spin>
                    </div>
                )
            } */}
            <div className={style.runFlowEditBox} ref={runFlowEditRef}>
                {
                    runTestModel === 'all' ? <div className={style.runFlowBtnContent + ' ' + (isQueryFocus ? style.runFlowBtnContentActive : '')}>
                        <div className={'runFlowBtnTextArea ' + style.runFlowBtnTextAreaBox}>
                            <TextArea
                                placeholder={'请输入内容，Enter发送，Shift+Enter换行'}
                                value={queryValue}
                                onChange={(e) => {
                                    setQueryValue(e.target.value);
                                    setFormList([
                                        {
                                            ...formList[0],
                                            value: e.target.value,
                                        }
                                    ]);
                                }}
                                autoSize={{ minRows: 1, maxRows: 4 }}
                                onPressEnter={(e) => {
                                    handleKeyDown(e);
                                }}
                                onFocus={() => setIsQueryFocus(true)}
                                onBlur={() => setIsQueryFocus(false)}
                            />
                        </div>
                        <div className={style.runFlowBtnArea}>
                            {flowLoading && !currentRunCallBackId && <div className={style.runBtnGrayNo + ' ' + style.runFlowBtn}><img src={ImgRunStopDisabled.src} /></div>}
                            {flowLoading && currentRunCallBackId && <div className={style.runBtnGray + ' ' + style.runFlowBtn} onClick={handleRunStop}><img src={ImgRunStop.src} /></div>}
                            {!flowLoading && queryValue && <div className={style.runBtn + ' ' + style.runFlowBtn} onClick={handleRunTest}><img src={ImgRunFlowSend.src} /></div>}
                            {!flowLoading && !queryValue && <div className={style.runBtn + ' ' + style.runFlowBtn}><img src={ImgRunFlowSendDisabled.src} /></div>}
                        </div>
                    </div> : <></>
                }
            </div>
            <Modal
                title="节点详情"
                open={openNodeDetail}
                onCancel={() => setOpenNodeDetail(false)}
                width={1152}
                footer={false}
                style={{
                    padding: 0
                }}
                classNames={{
                    content: 'nodeDetailContent'
                }}
                styles={{
                    header: {
                        padding: '16px 30px 16px 24px',
                        borderBottom: '1px solid #EBF0F5',
                        background: '#F7F8FA',
                        borderRadius: '16px 16px 0px 0px',
                        margin: '0'
                    },
                    content: {
                        padding: '0',
                        borderRadius: '16px'
                    },
                    footer: {
                        display: 'none'
                    }
                    
                }}
            >
                {openNodeDetail ? <NodeDetail nodeDetailData={nodeDetailData} key={(nodeDetailData as any)?.log_id || ''}></NodeDetail> : <></>}
            </Modal>
        </div>
    );
}
export default RunTestPage;