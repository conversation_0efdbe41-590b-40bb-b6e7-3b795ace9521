import {useState, useEffect} from 'react';
import style from './nodeDetail.module.scss';
import ImgAllDefault from '@/images/flow3.0/nodeDetail/allDefault.svg';
import ImgAllActive from '@/images/flow3.0/nodeDetail/allActive.svg';
import ImgMCPDefault from '@/images/flow3.0/nodeDetail/mcpDefault.svg';
import ImgMCPActive from '@/images/flow3.0/nodeDetail/mcpActive.svg';
import ImgUserDefault from '@/images/flow3.0/nodeDetail/userDefault.svg';
import ImgUserActive from '@/images/flow3.0/nodeDetail/userActive.svg';
import ImgKnowledgeDefault from '@/images/flow3.0/nodeDetail/knowledgeDefault.svg';
import ImgKnowledgeActive from '@/images/flow3.0/nodeDetail/knowledgeActive.svg';
import JsonViewCom from '@/components/commonComponents/jsonView/index';
import ExpandPage from '@/components/commonComponents/expand';
import {reqGetRunFlowNodeDetail} from '@/service/flow3.0';
import {Spin} from 'antd';
import ImgEmpty from '@/images/flow3.0/nodeDetail/empty.svg';
const titleJsonStyle = {
    color: '#657083',
    fontSize: '12px',
    fontWeight: '400'
};
const tabList = [
    {
        icon: ImgAllDefault,
        iconActive: ImgAllActive,
        title: '整体',
        name: 'all'
    },
    {
        icon: ImgMCPDefault,
        iconActive: ImgMCPActive,
        title: 'MCP',
        name: 'mcp'
    },
    {
        icon: ImgUserDefault,
        iconActive: ImgUserActive,
        title: '用户界面',
        name: 'uimcp'
    },
    {
        icon: ImgKnowledgeDefault,
        iconActive: ImgKnowledgeActive,
        title: '知识库',
        name: 'knowledge'
    },
];
const allListDefault = [
    {
        title: '输入',
        value: [
            {
                title: '提示词',
                name: 'user_prompt',
                value:  {user_prompt: ''}
            },
        ]
    },
    {
        title: '输出',
        value: [
            {
                title: '智能体1',
                name: 'output',
                value:  {
                    output: {
                        content: '这是输出内容',
                        type: 'text',
                        ddd: '这是输出内容',
                    }
                }
            },
        ]
    },
];
const NodeDetail = ({nodeDetailData = {}}: any) => {
    const [activeTab, setActiveTab] = useState('all');
    const [allList, setAllList] = useState([]);
    const [mcpList, setMcpList] = useState([]);
    const [uiMcpList, setUiMcpList] = useState([]);
    const [knowledgeList, setKnowledgeList] = useState([]);
    const [isLoadingData, setIsLoadingData] = useState(false);
    const [listObj, setListObj] = useState({
        mcp: false,
        uiMcp: false,
        knowledge: false
    });

    useEffect(() => {
        console.error('nodeDetailData', nodeDetailData)
        if (nodeDetailData && Object.keys(nodeDetailData).length && activeTab) {
            if (activeTab === 'all') {
                let all_list: any = [...allListDefault];
                all_list[0].value[0].value.user_prompt = nodeDetailData?.inputs?.user_prompt || '';
                all_list[1].value[0].value.output = nodeDetailData?.result?.output || '';
                all_list[1].value[0].title = nodeDetailData?.name || '';
                setAllList(all_list);
            } else {
                handleGetFlowNodeDetail(activeTab)
            }
        } else {
            setAllList([]);
            setMcpList([]);
            setUiMcpList([]);
            setKnowledgeList([]);
        }

    }, [nodeDetailData, activeTab])

    const handleGetFlowNodeDetail = async (type) => {
        if (!type) {
            return;
        }
        if (type && listObj[type]) {
            return;
        }
        if (type === 'mcp' && mcpList.length) {
            return;
        }
        else if (type === 'uimcp' && uiMcpList.length) {
            return;
        }
        else if (type === 'knowledge' && knowledgeList.length) {
            return;
        }
        setIsLoadingData(true);
        try {
            let res = await reqGetRunFlowNodeDetail({
                block_key: nodeDetailData.block_index,
                log_type: type,
                log_id: nodeDetailData.log_id
            });
            console.log('--------------', type, res);
            let result = [];
            if (res) {
                
                result = res.map((item, index) => {
                    let output: any = {};
                    try {
                        output = JSON.parse(item?.message);
                    } catch (error) {
                        output = item?.message;
                    }
                    return {
                        title: '输出-' + (index + 1),
                        name: 'output',
                        value: {
                            output
                        }
                    }
                })
            }
            
            if (type === 'mcp' && res) {
                setMcpList(result);
            }
            else if (type === 'uimcp' && res) {
                setUiMcpList(result);
            }
            else if (type === 'knowledge' && res) {
                setKnowledgeList(result);
            }
            if (type && res) {
                setListObj((prev) => {
                    return {...prev, [type]: true};
                });
            }
            setIsLoadingData(false);

            
        } catch (error) {
            setIsLoadingData(false);
            if (type === 'mcp') {
                setMcpList([]);
                return;
            }
            if (type === 'uimcp') {
                setUiMcpList([]);
                return;
            }
            if (type === 'knowledge') {
                setKnowledgeList([]);
                return;
            }
        } 
    }

    return (
        <div className={style.nodeDetailBox}>
            <div className='flex gap-4 items-center'>
                <div className={style.headerImg}>
                    <div className='flex items-center justify-center w-[48px] h-[48px] rounded-[12px]' style={{
                        backgroundColor: nodeDetailData.imgUrlBgColor,
                    }}>
                        <img src={nodeDetailData.imgUrl} width={28} height={28} />
                    </div>
                </div>
                <div className='flex flex-col gap-1 flex-1'>
                    <div className={'w-full singleLineEllipsis ' + style.headerTitle + ' ' + style.titleDescLineWidth}>{nodeDetailData.name || ''}</div>
                    <div className={'flex gap-7 items-center ' + style.headerDesc}>
                        <div className='flex gap-2 items-center'><div>ID: </div><div>{nodeDetailData.block_index || ''}</div></div>
                        <div className='w-full flex gap-2 items-center'><div>描述:</div><div className={'singleLineEllipsis ' + style.titleDescLineWidth}>{nodeDetailData.desc || ''}</div></div>
                    </div>
                </div>
            </div>
            <div className={style.ndbTabContent}>
                {
                    isLoadingData && (
                        <div className='w-full h-full flex justify-center items-center absolute top-0 left-0 z-101 bg-[rgba(255,255,255,0.9)] rounded-lg'>
                            <Spin tip={'加载中......'} wrapperClassName="runTestLoading">
                                <div style={{
                                    padding: '4px 50px',
                                }} />
                            </Spin>
                        </div>
                    )
                }
                <div className={style.ndbTabBox}>
                    {
                        tabList.map((item, index) => {
                            return (
                                <div key={index}>
                                    <img src={activeTab === item.name ? item.iconActive.src : item.icon.src} alt="" onClick={() => setActiveTab(item.name)} />
                                </div>
                            )
                        })
                    }
                </div>
                <div className={style.ndbContentBox}>
                    {
                        activeTab === 'all' ? <>
                            <div className='w-full flex flex-col gap-4'>
                                {
                                    allList.map((itemAll: any, indexAll: any) => {
                                        return (
                                            <ExpandPage
                                                header={<div className={style.headerTitle}>{itemAll.title}</div>}
                                                defaultExpanded={true}
                                                children={
                                                    itemAll.value.map((item: any, index: any) => {
                                                        return (
                                                            <JsonViewCom isBgWrite={true} isShowHeader={itemAll.title !== '输入'} key={index} data={item.value || {}} title={item.title} style={{ backgroundColor: '#ffffff', border: '0' }} titleStyle={titleJsonStyle} preview={item.name} />
                                                        )
                                                    })
                                                } />
                                        )
                                    })
                                }
                                
                            </div>
                        </> : <></>
                    }
                    {
                        activeTab === 'mcp' ? <>
                            {
                                mcpList.length ? <ExpandPage
                                    header={<div className='flex items-center gap-1'>
                                        <div className={style.headerTitle}>输出</div>
                                        {/* <div className={style.headerContent}>({itemAll.name})</div> */}
                                    </div>}
                                    defaultExpanded={true}
                                    children={
                                        mcpList.length ? mcpList.map((item: any, index: any) => {
                                            return (
                                                <JsonViewCom isBgWrite={true} isShowHeader={item.title !== '输入'} key={index} data={item.value || {}} title={item.title} style={{ backgroundColor: '#ffffff', border: '0' }} titleStyle={titleJsonStyle} preview={item.name} />
                                            )
                                        }) : <div className='w-full h-[300px]'></div>
                                    } /> : <div className='w-full h-full flex items-center justify-center'>
                                    <img src={ImgEmpty.src} />
                                </div>
                            }
                        </> :<></>
                    }
                    {
                        activeTab === 'uimcp' ? <>
                            {
                                uiMcpList.length ? <ExpandPage
                                    header={<div className='flex items-center gap-1'>
                                        <div className={style.headerTitle}>输出</div>
                                        {/* <div className={style.headerContent}>({itemAll.name})</div> */}
                                    </div>}
                                    defaultExpanded={true}
                                    children={
                                        uiMcpList.length ? uiMcpList.map((item: any, index: any) => {
                                            return (
                                                <JsonViewCom isBgWrite={true} isShowHeader={item.title !== '输入'} key={index} data={item.value || {}} title={item.title} style={{ backgroundColor: '#ffffff', border: '0' }} titleStyle={titleJsonStyle} preview={item.name} />
                                            )
                                        }) : <div className='w-full h-[300px]'></div>
                                    } /> : <div className='w-full h-full flex items-center justify-center'>
                                        <img src={ImgEmpty.src} />
                                    </div>
                            }
                        </> :<></>
                    }
                    {
                        activeTab === 'knowledge' ? <>
                            {/* <div className='w-full flex flex-col gap-4'>
                                {
                                    knowledgeList.map((itemAll: any, indexAll: any) => {
                                        return (
                                            <ExpandPage
                                                header={<div className='flex items-center gap-6'>
                                                    <div className={style.headerTitle}>{itemAll.title}</div>
                                                    {itemAll.title == '输入' && <><div className={style.headerContent}>知识库: <span className='ml-2'>个人知识库</span></div>
                                                    <div className={style.headerContent}>ID: <span className='ml-2'>32413421432</span></div></>}
                                                </div>}
                                                defaultExpanded={true}
                                                children={
                                                    itemAll.value.map((item: any, index: any) => {
                                                        return (
                                                            <JsonViewCom isBgWrite={true} isShowHeader={itemAll.title !== '输入'} key={index} data={item.value || {}} title={item.title} style={{ backgroundColor: '#ffffff', border: '0' }} titleStyle={titleJsonStyle} preview={item.name} />
                                                        )
                                                    })
                                                } />
                                        )
                                    })
                                }
                            </div> */}

                            {
                                knowledgeList.length ? <ExpandPage
                                    header={<div className='flex items-center gap-1'>
                                        <div className={style.headerTitle}>输出</div>
                                        {/* <div className={style.headerContent}>({itemAll.name})</div> */}
                                    </div>}
                                    defaultExpanded={true}
                                    children={
                                        knowledgeList.length ? knowledgeList.map((item: any, index: any) => {
                                            return (
                                                <JsonViewCom isBgWrite={true} isShowHeader={item.title !== '输入'} key={index} data={item.value || {}} title={item.title} style={{ backgroundColor: '#ffffff', border: '0' }} titleStyle={titleJsonStyle} preview={item.name} />
                                            )
                                        }) : <div className='w-full h-[300px]'></div>
                                    } /> : <div className='w-full h-full flex items-center justify-center'>
                                        <img src={ImgEmpty.src} />
                                    </div>
                            }
                        </> :<></>
                    }
                </div>
            </div>
        </div>
    )
}
export default NodeDetail;