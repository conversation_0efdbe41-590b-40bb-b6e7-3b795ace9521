.nodeDetailBox {
    width: 100%;
    height: 696px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    box-sizing: border-box;
    padding: 24px;
}
.headerImg {
    width: 48px;
    height: 48px;
}
.headerTitle {
    color:#1B2532;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;  
}
.headerDesc {
    color: #657083;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; 
}
.ndbTabContent {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 16px;
    box-sizing: border-box;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 16px;
    border: 1px solid #E1E7ED;
    position: relative;
}
.ndbTabBox {
    display: flex;
    gap: 16px;
    border-bottom: 1px solid #EDF1F5;
    img {
        width: auto;
        height: 100%;
        object-fit: contain;
        cursor: pointer;
    }
}
.ndbContentBox {
    flex: 1;
    width: 100%;
    overflow: auto;
    max-height: 490px;
    padding-bottom: 16px;
}
.headerTitle {
    color: #1D2531;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;  
}
.headerContent {
    color: #1D2531;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; 
}
.titleDescLineWidth {
    max-width: 600px;
}