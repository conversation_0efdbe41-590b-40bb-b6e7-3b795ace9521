import { Collapse, Select, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import { reqGetNmAgentModelList } from "@/service/flow3.0";
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import styles from './styles/BlockLLMCollapseSelect.module.scss';
import BlockNmLLMSelect from "./BlockNmLLMSelect/BlockNmLLMSelect";
import { default_model_params } from "@/utils/defaultLLMConfig";


export default function BlockLLMCollapseSelect(props: any) {
    const { setBlocks, blocks, activeBlockId, borderTop = false, borderBottom = false, hasParamsIcon = true } = props;
    const prop = { ...{ borderTop: false, borderBottom: false, hasParamsIcon: true, ...props } }

    const [LLMAllList, setLLMAllList] = useState<any>([]);
    const [LLMList, setLLMList] = useState<any>([]);
    const [isShowLlmParam, setIsShowLlmParam] = useState(false);

    const apiENV = process.env.NEXT_PUBLIC_API_ENV ?? 'test'
    const role_id = (apiENV == 'production' || apiENV == 'pre')? '917ec92d6f5146cb9dc7789114b468ee' : "a53701f73ebf930a13ca778995df63cb"

    const getLLMList = async () => {
        const res = await reqGetNmAgentModelList({});
        setLLMAllList([...res]);
        initLLMList([...res]);
    }

    const initLLMList = async (list?: any) => {
        let temp: any = list ? list : LLMAllList;
        const curBlock = blocks[activeBlockId];
        if (curBlock?.mcp_list?.length == 0) {
            setLLMList([...temp]);
            return
        }
        // 暂时隐藏
        // if(curBlock?.mcp_list.filter((m) => {
        //     return m.server_name == 'custom-ui-card'
        // }).length > 0) {
        //     // 有用户界面 只能用通义千问
        //     setLLMList([...temp.filter(i => i.role_id == role_id)]);
        //     // 默认需要选中
        //     blocks[activeBlockId].model_params.role_id = role_id
        //     setBlocks(prev => ({...blocks}));
        //     return;
        // }
        setLLMList([...temp.filter(i => i.support_mcp)]);

        if (!curBlock?.model_params?.support_mcp && blocks[activeBlockId]?.model_params) {
            blocks[activeBlockId].model_params = { ...default_model_params }
            setBlocks(prev => ({...blocks}));
        }
    }

    useEffect(() => {
        getLLMList()
    }, [])

    useEffect(() => {
        initLLMList();
    }, [blocks[activeBlockId].mcp_list])


    return <div key={activeBlockId} className={[flowStyles.blockContainerWrapper, borderTop && flowStyles.borderTop, borderBottom && flowStyles.borderBottom].filter(Boolean).join(' ')}>
        <Collapse
            destroyOnHidden
            accordion={false}
            defaultActiveKey={['1']}
            ghost
            className={flowStyles.collapseMaskWrapper}
            items={[
                {
                    key: '1',
                    label:
                        <div className={flowStyles.collapseMaskTitle}>
                            <div className={flowStyles.collapseTitle}>模型</div>
                        </div>,
                    children: <BlockNmLLMSelect
                        {...
                        {
                            ...prop,
                            LLMList,
                            isShowLlmParam,
                            setIsShowLlmParam
                        }
                        }
                    />
                }
            ]}
        />
    </div>
}