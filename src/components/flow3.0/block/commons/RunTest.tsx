import { useState, useEffect, useRef} from 'react';
import style from './RunTest.module.scss';
import { Button, Input, InputNumber, Radio, Spin, Collapse, message, Modal } from 'antd'
import CloseImg from '@/images/newFlow/close.svg';
import JsonViewCom from '@/components/commonComponents/jsonView/index';
import JsonEditorPage from '@/components/commonComponents/jsonEditor';
import ImgRunPlay from '@/images/flow3.0/runPlayBlue.svg';
import ImgRunStop from '@/images/flow3.0/runStop.svg';
const { TextArea } = Input;
import ImgRunStopDisabled from '@/images/flow3.0/runStopDisabled.png';
import ImgHistory from '@/images/flow3.0/nodeDetail/history.svg';
import NodeDetail from '@/components/flow3.0/block/commons/Run/nodeDetail';
import ImgDelete from '@/images/flow3.0/delete.svg';
import ImgDetail from '@/images/flow3.0/nodeDetail/detail.svg';
import { useRecoilCallback, useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { runNodeResultAtom } from '@/atoms/flowEditorAtoms';
import useClickOutside from '@/hooks/useClickOutside';
import {reqGetRunFlowHistory, reqDelRunFlowNodeHistory, reqGetRunFlowDetail} from '@/service/flow3.0'
import dayjs from 'dayjs';
import Fail from "@/images/newFlow/icon-status-fail.svg";
import Success from "@/images/newFlow/icon-status-success.svg";
import Noupdate from "@/images/newFlow/icon-status-noupdate.svg";
import Running from "@/images/newFlow/icon-status-running.svg";
import { iconList } from "@/components/flowEditor3.0/flowConfig";
import ImgEmpty from '@/images/flow3.0/nodeDetail/empty.svg';

const statusMapping = {
//   "-1": { label: "异常", color: "#FFECE8", icon: Exception },
//   "1": { label: "未调试", color: "#FFECE8", icon: Unused },
  "2": { label: "运行中", color: "#E8F7FF", icon: Running.src },
  "3": { label: "运行成功", color: "#E8FFEA", icon: Success.src },
  "4": { label: "运行失败", color: "#FFECE8", icon: Fail.src },
  "5": { label: "已编辑待更新", color: "#FFF7E8", icon: Noupdate.src },
//   "6": { label: "已取消", color: "#EDF1F5", icon: Cancel },
//   "7": { label: "暂停", color: "#FFF7E8", icon: Pause },
//   "8": { label: "未调用", color: "#EDF1F5", icon: Unused },
};
const defaultValue = {
    string: '',
    number: 0,
    boolean: false,
    array: [],
    'arr[string]': [],
    'arr[number]': [],
    'arr[object]': [],
    object: {},
};
const objMap = ['arr[number]', 'arr[string]', 'arr[object]', 'object', 'array'];
const titleJsonStyle = {
    color: '#657083',
    fontSize: '12px',
    fontWeight: '400'
};
function RunTestPage(props: any) {
    const { setIsRunTest, blocks, activeBlockId, runTestModel, flowLoading, blockLoading, onRun, onStop, runNodeResultValue, currentRunCallBackId, resizableWidth, chainId } = props;
    const [formList, setFormList] = useState([]);
    const [originObj, setOriginObj] = useState({});
    const [runTitle, setRunTitle] = useState('调试');
    const [resultList, setResultList] = useState([]);
    const [runAllQueryValue, setRunAllQueryValue] = useState('');
    const [openNodeDetail, setOpenNodeDetail] = useState(false);
    const [historyList, setHistoryList] = useState([]);
    const [openHistoryList, setOpenHistoryList] = useState(false);
    const runNodeResultAtomValue = useRecoilValue(runNodeResultAtom);
    const [nodeDetailData, setNodeDetailData] = useState({});
    const historyBoxRef = useRef<HTMLDivElement>(null);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [isLoadingData, setIsLoadingData] = useState(false);
    const scrollContainerRef = useRef(null);
    const [hasMore, setHasMore] = useState(true);

    // 使用点击外部关闭hook
    useClickOutside(historyBoxRef, () => {
        if (historyBoxRef) {
          setOpenHistoryList(false);
          setPage(1);
          setHistoryList([]);
        }
    }, ['.nodeDetailContent', '.ant-modal-mask', '.ant-modal-wrap', '.debug-history-btn']); // 排除LLM选择器触发元素
    
    useEffect(() => {
        if (runTestModel === 'all') {
            setRunTitle('运行');
        } else {
            setRunTitle('调试');
        }
        if (flowLoading || blockLoading) {
            return;
        }
        if (runTestModel === 'all') {
            let obj = {
                prompt: [
                    {
                        type: 'string',
                        name: 'query',
                        title: '用户输入',
                        value: runAllQueryValue,
                        isTextarea: true
                    }
                ]
            };
            // setOriginObj(obj);
            setFormList(obj.prompt);
        } else {
            let keyIds = [];
            let promptList: any = [];
            if (activeBlockId && blocks[activeBlockId]) {
                // 处理是否显示用户输入
                if (blocks[activeBlockId]?.prompts?.user_params) {
                    let user_params = blocks[activeBlockId]?.prompts?.user_params || [];

                    user_params.forEach((uItem: any) => {
                        if (uItem?.children) {
                            uItem.children.forEach((item: any) => {
                                if (item && item?.type === 'mention' && item?.payload?.type === 'userInput' && !keyIds.includes('userInput')) {
                                    promptList.push({
                                        type: 'string',
                                        name: 'query',
                                        title: '用户输入',
                                        value: '',
                                        isTextarea: true
                                    });
                                    keyIds.push('userInput');
                                } else if (item && item?.payload?.id && item?.type === 'mention' && ['agentOutput', 'iteratorItem'].includes(item?.payload?.type) && !keyIds.includes(item?.payload?.id)) {
                                    let output = runNodeResultValue[item?.payload?.id]?.result?.output || '';
                                    // 迭代体 默认显示空
                                    if (item?.payload?.type == 'iteratorItem') {
                                        output = '';
                                    }
                                    
                                    promptList.push({
                                        type: 'string',
                                        name:  item?.payload?.type == 'iteratorItem' ? 'iteratorItem' : item?.payload?.id,
                                        title: item?.payload?.name,
                                        value: typeof output === 'string' ? output : JSON.stringify(output),
                                        isTextarea: true
                                    });
                                    keyIds.push(item?.payload?.id);
                                }
                            })
                        }
                    })
                }
            }
            let obj: any = {
                prompt:  promptList
            };
            setFormList(obj.prompt);
        }
    }, [runTestModel]);

    useEffect(() => {
        if (flowLoading || blockLoading) {
            return;
        }
        if (runTestModel === 'single' && activeBlockId && runNodeResultValue[activeBlockId]?.result) {
            let result = runNodeResultValue[activeBlockId]?.result;
            setResultList([
                {
                    title: '输出',
                    value: {output: result.output || ''}
                }
            ])
        }

        if (runTestModel === 'all') {
            let blockEndListResult: any = [];
            Object.keys(runNodeResultValue).map((item: any) => {
                if (runNodeResultValue[item]?.is_end) {
                    blockEndListResult.push(
                        {
                            title: '输出-'+ runNodeResultValue[item]?.name,
                            value: {output: runNodeResultValue[item]?.result?.output || ''}
                        }
                    )
                }
            });
            setResultList(blockEndListResult);
        }
        
    }, [activeBlockId, runNodeResultValue, runTestModel, flowLoading, blockLoading]);

    const handleGetFlowHistoryList = async () => {
        setIsLoadingData(true);
        try {
            let res = await reqGetRunFlowHistory({
                template_id: chainId,
                run_type: 'debug',
                page,
                page_size: pageSize,
                block_index: activeBlockId
            });
            console.error('---------获取调试历史记录列表---------');
            console.log(res);
            if (res.data && Array.isArray(res.data)) {
                let list = res.data.map((item: any) => {
                    return {
                            ...item,
                            title: '调试',
                            id: 'debug',
                            time: item?.start_time ? dayjs(item.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '',
                        };
                });
                setHistoryList((prev: any) => [...prev, ...list])
            }
            if (res.page * res.page_size < res.total) {
                setHasMore(true);
                setPage(() => page + 1);
            } else {
                setHasMore(false);
            }
            setIsLoadingData(false);

        } catch (error) {
            setIsLoadingData(false);
            setHasMore(false);
        }
    };
    const handleRunTest = () => {
        let runParams: any = {};
        if (runTestModel === 'all') {
            let queryObj = formList.find((item: any) => item.name === 'query');
            if (queryObj) {
                runParams.query = queryObj.value;
            }
        } else {
            let debug_nodes = {};
            formList.forEach((item: any) => {
                if (item.name === 'query') {
                    runParams[item.name] = item.value;
                } else {
                    debug_nodes[item.name] = item.value;
                }
            });
            runParams = {
                ...runParams,
                debug_nodes,
            }
        }
        console.log(runParams, 'on run params');
        onRun(runParams)
    };
    const handleRunStop = () => {
        onStop && onStop('all');
    }
    const handleDebugRunStop = () => {
        onStop && onStop('single');
    }

    const getIsTextarea = (item: any) => {
        let isFlag = false;
        // 文本分割
        let nodeTypeList: any = ['split_text', 'random_value'];
        if (nodeTypeList.includes(blocks[activeBlockId]?.type) || item?.isTextarea) {
            isFlag = true;
        }
        return isFlag;
    }

    const onHistoryScroll = (e: any) => {
        if (!scrollContainerRef.current || isLoadingData || !hasMore) return;

        const { scrollTop, clientHeight, scrollHeight } = scrollContainerRef.current;
        
        // 计算距离底部的距离
        const distanceToBottom = scrollHeight - (scrollTop + clientHeight);
        
        // 当距离底部小于等于100px时加载更多
        if (distanceToBottom <= 100) {
            handleGetFlowHistoryList();
        }
    }


    const collapseList: any = [
        ...(formList.length ? [{
            key: 'input',
            label: <div className='font-[600]'>输入</div>,
            children: (
                <div className='flex flex-col gap-4 text-[#657083] mb-1'>
                    {
                        formList.map((item: any, index: number) => {
                            return <div className='flex flex-col gap-2 text-[12px]' key={index}>
                                {runTestModel === 'single' ? <div>
                                    <div className='flex gap-2'>{item.title}
                                        {/* <div className='px-[4px] py-[2px] bg-[#eef2f6] rounded-md'>{item?.type?.slice(0, 1).toUpperCase() || 'S'}{item?.type?.slice(1) || 'tring'}</div> */}
                                    </div>
                                </div> : ''}
                                <div>
                                    {
                                        item.type === 'string' || !item?.type ? (
                                            getIsTextarea(item) ?
                                                <TextArea
                                                    placeholder={item.placeholder || item.title || ''}
                                                    disabled={item?.disabled || false}
                                                    value={item.value}
                                                    onChange={(e) => {
                                                        item.value = e.target.value;
                                                        setFormList([...formList]);
                                                        if (runTestModel === 'all' && item.name === 'query') {
                                                            setRunAllQueryValue(e.target.value);
                                                        }
                                                    }}
                                                    rows={4}
                                                    autoSize={{ minRows: 4, maxRows: 21 }}
                                                />
                                                :
                                                <Input
                                                    placeholder={item.placeholder}
                                                    value={item.value}
                                                    onChange={(e) => {
                                                        item.value = e.target.value;
                                                        setFormList([...formList]);
                                                    }}
                                                />) : ('')
                                    }
                                    {
                                        item.type === 'number' ? (
                                            <InputNumber
                                                style={{
                                                    width: '100%'
                                                }}
                                                placeholder={item.placeholder}
                                                value={item.value}
                                                onChange={(value) => {
                                                    item.value = value;
                                                    setFormList([...formList]);
                                                }}
                                            />) : ('')
                                    }
                                    {
                                        item.type === 'boolean' ? (
                                            <Radio.Group
                                                name={item.name}
                                                value={item.value}
                                                options={[
                                                    { value: true, label: 'true' },
                                                    { value: false, label: 'false' },
                                                ]}
                                                onChange={(event: any) => {
                                                    item.value = event.target.value;
                                                    setFormList([...formList]);
                                                }}
                                            />) : ('')
                                    }
                                    {
                                        item.type === 'html' ? (
                                            <div className='w-full relative'>
                                                <div
                                                    className={style.htmlBox}
                                                    dangerouslySetInnerHTML={{ __html: item.value }}
                                                />
                                                { item.disabled ? <div style={{
                                                    // color: '#BCCAD6',
                                                    opacity: 0.6,
                                                    borderRadius: '8px',
                                                    background: '#F7F9FA',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    width: '100%',
                                                    height: '100%'
                                                }}></div> : <></>}
                                            </div>
                                        ): ''
                                    }
                                    {
                                        objMap.includes(item.type) ? (
                                            <JsonEditorPage
                                                id={'run-test-' + index}
                                                data={item.value}
                                                isReadOnly={false}
                                                onChange={(value: any) => {
                                                    // console.log('value', value, item.value)
                                                    formList[index].value = value;
                                                    setFormList([...formList]);
                                                }}
                                            />
                                        ) : ('')
                                    }
                                </div>
                            </div>
                        })
                    }
                </div>
            )
        }] : []),
        ...(resultList.length ? [
        {
            key: 'result',
            label: <div className={style.runTestPageDebugResult}><div className='font-[600]'>运行结果</div><img src={ImgDetail.src} onClick={() => {
                console.log(runNodeResultAtomValue[activeBlockId])
                let curItem = {...(runNodeResultAtomValue[activeBlockId] || {})};
                let meta = curItem?.meta || "";
                curItem = {
                    ...curItem,
                    desc: curItem?.content?.desc || '',
                    imgUrl: meta ? iconList[meta?.iconIndex]?.image.src : '',
                    imgUrlBgColor: meta ? meta?.color : ''
                }
                setNodeDetailData(curItem)
                setOpenNodeDetail(true);
            }} /></div>,
            children: (
                <div className='flex flex-col gap-2'>
                    {
                        resultList.map((item: any, index: any) => {
                            return (
                                <JsonViewCom key={index} data={item.value || {}} title={item.title} style={{ backgroundColor: '#F7F9FA' }} titleStyle={titleJsonStyle} preview={'output'} />
                            )
                        })
                    }
                </div>
            )
        }] : [])
    ];


    return (
        <div className={style.runTestPage + ' ' + (runTestModel === 'all' ? style.runTestPageAll : '')}>
            <div className={style.runTestPageHeader}>
                <div className={style.runDebugTitle}>{runTitle}</div>
                <div className='flex items-center gap-3'>
                    <img className="debug-history-btn" src={ImgHistory.src} onClick={(e: any) => {
                        e.stopPropagation();
                        if (openHistoryList) {
                            setOpenHistoryList(false);
                            setPage(1);
                            setHistoryList([]);
                        } else {
                            setOpenHistoryList(true);
                            handleGetFlowHistoryList();
                        }
                    }} /> 
                    <img src={CloseImg.src} onClick={() => {
                        setIsRunTest(false);
                    }} /> 
                </div>
            </div>

            <div className={style.runTestPageContent}>
                <Collapse items={collapseList} bordered={false} ghost defaultActiveKey={['input', 'result']} />
            </div>

            {
                (blockLoading || flowLoading) && (
                    <div className='w-full h-full flex justify-center items-center absolute top-0 left-0 z-101 bg-[rgba(255,255,255,0.9)] rounded-lg'>
                        <Spin tip={runTitle + '中......'} wrapperClassName="runTestLoading">
                            <div style={{
                                padding: '4px 50px',
                            }} />
                        </Spin>
                    </div>
                )
            }
            <div className={style.runTestPageBtnRun}>
                {
                    runTestModel === 'single' && (blockLoading ? (currentRunCallBackId ?
                        <div className={style.runBtnGray} onClick={handleDebugRunStop}><img src={ImgRunStop.src} width={16} height={16} />停止</div>
                        : <div className={style.runBtnGrayNo}><img src={ImgRunStopDisabled.src} width={16} height={16} />停止</div>) : <div className={style.runBtn} onClick={handleRunTest}><img src={ImgRunPlay.src} width={16} height={16} />调试</div>)
                }
                {
                    runTestModel === 'all' ? (flowLoading ? (currentRunCallBackId ? 
                    <div className={style.runBtnGray} onClick={handleRunStop}><img src={ImgRunStop.src} width={16} height={16} />停止</div>
                    : <div className={style.runBtnGrayNo}><img src={ImgRunStopDisabled.src} width={16} height={16} />停止</div>) : <div className={style.runBtn} onClick={handleRunTest}><img src={ImgRunPlay.src} width={16} height={16} />运行</div>) : <></>
                }
            </div>
            {
               openHistoryList ? (
                <div className={style.debugHistoryBox} style={{
                    right: (resizableWidth + 8) + 'px'
                }} ref={historyBoxRef}>
                    <div className={style.debugHistoryTitle}>调试历史</div>
                    <div className={style.historyLine}></div>
                    <div className={style.debugHistoryList} ref={scrollContainerRef} onScroll={onHistoryScroll}>
                        {
                            isLoadingData && (
                                <div className='w-full h-full flex justify-center items-center absolute top-0 left-0 z-101 bg-[rgba(255,255,255,0.9)] rounded-lg'>
                                    <Spin tip={'加载中......'} wrapperClassName="runTestLoading">
                                        <div style={{
                                            padding: '4px 50px',
                                        }} />
                                    </Spin>
                                </div>
                            )
                        }
                        {
                            historyList.length ? historyList.map((item: any, index: number) => {
                                return <div className='flex gap-1'>
                                    <div className={'w-[16px] flex flex-col items-center' + (index === 0 ? ' justify-end' : (index === historyList.length - 1 ? ' justify-start' : ' justify-center'))}>
                                        {index !== 0 &&  
                                            <div style={{
                                                width: '1px',
                                                height: '12px',
                                                background: '#BFC9D5'
                                            }}></div>
                                        }
                                        <img src={statusMapping[item?.chain_status].icon} width={16} height={16} className={item?.chain_status == 2 ? style.rotate : ''} />
                                        {index !== historyList.length - 1 &&  
                                            <div style={{
                                                width: '1px',
                                                height: '18px',
                                                background: '#BFC9D5'
                                            }}></div>
                                        }
                                        {historyList.length === 1 &&  
                                            <div style={{
                                                width: '0px',
                                                height: '18px',
                                                background: '#BFC9D5'
                                            }}></div>
                                        }
                                    </div>
                                    <div className='pb-2 flex-1 flex'>
                                        <div className={style.debugHistoryItem}>
                                            <div onClick={async () => {
                                                if (item.chain_status == 2) {
                                                    message.warning('运行中,不支持查看!')
                                                    return;
                                                }
                                                let nodeDetail: any = {};
                                                setIsLoadingData(true);
                                                try {
                                                    let resDetail = await reqGetRunFlowDetail({
                                                        log_id: item.log_id,
                                                        block_key: item.block_index
                                                    });
                                                    console.log('-----flow node detail ----', resDetail)
                                                    if (resDetail?.logs) {
                                                        resDetail?.logs.forEach(item => {
                                                            let msg: any = {};
                                                            try {
                                                                msg = JSON.parse(item.message);
                                                            } catch (error) {
                                                                
                                                            }
                                                            
                                                            if (msg?.log_type === 'nami_engine_log_input') {
                                                                nodeDetail.inputs = msg?.inputs || {};
                                                            }

                                                            if (msg?.log_type === 'nami_engine_log_output') {
                                                                nodeDetail.result = msg?.result || {};
                                                            }
                                                        });
                                                    }
                                                    setIsLoadingData(false);
                                                } catch (error) {
                                                    setIsLoadingData(false);
                                                }
                                                
                                                let curItem = {...item, ...nodeDetail};
                                                let meta = item?.chain_config?.blocks[item?.block_index]?.meta || "";
                                                curItem = {
                                                    ...curItem,
                                                    name: item?.chain_config?.blocks[item?.block_index]?.name || '',
                                                    desc: item?.chain_config?.blocks[item?.block_index]?.desc || '',
                                                    imgUrl: meta ? iconList[meta?.iconIndex]?.image.src : '',
                                                    imgUrlBgColor: meta ? meta?.color : ''
                                                }
                                               setNodeDetailData(curItem)
                                               setOpenNodeDetail(true);
                                            }}>{item.time}</div>
                                            <img src={ImgDelete.src} width={16} height={16} onClick={async (e: any) => {
                                                e.stopPropagation();
                                                try {
                                                    let res = await reqDelRunFlowNodeHistory({log_id: item.log_id});
                                                    console.log(res)
                                                    if (res && res.log_id) {
                                                        setHistoryList((prev) => {
                                                            return prev.filter(item => item.log_id !== res.log_id);
                                                        });
                                                        message.success('删除成功!')
                                                    }
                                                } catch (error) {
                                                    
                                                }
                                            }}/>
                                        </div>
                                    </div>
                                </div>
                            }) : <div className='w-full h-full flex items-center justify-center'>
                                    <img src={ImgEmpty.src} />
                                </div>
                        }
                    </div>
                </div>
               ) : <></>
            }
            
            <Modal
                title="节点详情"
                open={openNodeDetail}
                onCancel={() => {
                    setOpenNodeDetail(false);
                    setNodeDetailData({});
                }}
                width={1152}
                footer={false}
                style={{
                    padding: 0
                }}
                classNames={{
                    content: 'nodeDetailContent'
                }}
                styles={{
                    header: {
                        padding: '16px 30px 16px 24px',
                        borderBottom: '1px solid #EBF0F5',
                        background: '#F7F8FA',
                        borderRadius: '16px 16px 0px 0px',
                        margin: '0'
                    },
                    content: {
                        padding: '0',
                        borderRadius: '16px'
                    },
                    footer: {
                        display: 'none'
                    }
                    
                }}
            >
                {openNodeDetail ? <NodeDetail nodeDetailData={nodeDetailData} key={(nodeDetailData as any)?.log_id || ''}></NodeDetail> : <></>}
            </Modal>
        </div>
    );
}
export default RunTestPage;