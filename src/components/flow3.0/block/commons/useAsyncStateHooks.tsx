import { useState, useEffect, useRef, useCallback } from'react';

function useAsyncState(initialState) {
  const [state, setState] = useState(initialState);
  const callbackRef = useRef(null);

  const setAsyncState = useCallback((newState, callback) => {
    callbackRef.current = callback;
    setState(newState);
  }, []);

  useEffect(() => {
    if (callbackRef.current) {
      callbackRef.current(state); // 状态更新后执行回调
      callbackRef.current = null;
    }
  }, [state]);

  return [state, setAsyncState];
}

export default useAsyncState;