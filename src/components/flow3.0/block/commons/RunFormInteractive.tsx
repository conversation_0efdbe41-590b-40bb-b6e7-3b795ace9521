/*
 * @Author: yh
 * @Date: 2025-06-12 10:24:17
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-12 15:42:51
 * @FilePath: \prompt-web\src\components\flow3.0\block\commons\RunFormInteractive.tsx
 */
import { EasyForm } from '@q/easy-render';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import style from './styles/RunFormInteractive.module.scss';
import CloseImg from '@/images/close.svg';
import { useEffect, useState } from 'react';
import { copyToClipboard, easyFormInputsData } from '@/utils/utils';
import { useRouter } from 'next/router';

export default function RunFormInteractive(props: any) {
  const { showFormInteractive, formInteractiveConfirm, setShowFormInteractive, blocks, activeFormInteractionBlockId, runTestModel, flowLoading, blockLoading, onRun, onCloseFormRun, setBlocks, saveFlow, formList } = props;
  const [inputsData, setInputsData] = useState<any>({});
  const router = useRouter();

  useEffect(() => {
    // const timer = setTimeout(() => {
    // const data = {}
    // formList?.forEach(i => {
    //   data[i.name] = i.value;
    // });
    // console.log("🚀 ~ timer ~ data:", data)
    // // setInputsData(data);
    //   clearTimeout(timer)
    // }, 0)
  }, [formList, activeFormInteractionBlockId])

  useEffect(() => {
    if (runTestModel == 'all') {
      // const timer = setTimeout(() => {
      //   setInputsData(easyFormInputsData(blocks[activeFormInteractionBlockId], 'run'));
      //   clearTimeout(timer)
      // }, 0)
      setInputsData(easyFormInputsData(blocks[activeFormInteractionBlockId], 'run'));
    } else {
      const data = {}
      formList?.forEach(i => {
        data[i.name] = i.value;
      });
      setInputsData(data);
    }

  }, [runTestModel, activeFormInteractionBlockId])

  const handleFormChange = (value: any) => {
    blocks[activeFormInteractionBlockId].result = value;
    setBlocks(prev => ({...blocks}));
    // setInputsData(value)
  }

  const onOk = () => {
    // blocks[activeFormInteractionBlockId].buttons = blocks[activeFormInteractionBlockId].status == 3 ? ['reset'] : ['ok'];
    // blocks[activeFormInteractionBlockId].buttons = ['reset', 'ok', 'cancel'];
    setBlocks(prev => ({...blocks}));
    // const timer = setInterval(() => {
    //   saveFlow();
    // }, 0)
    // clearInterval(timer);
    formInteractiveConfirm();
    setShowFormInteractive(false);
  }


  return <div className={style.runTestPageContent}>
    <div className={style.runTestPageHeader}>
      <div className={style.runTestPageHeaderLeft}>试运行</div>
      {
        runTestModel == 'all' ?
          <div className={style.runTestPageHeaderRight} onClick={() => onCloseFormRun()}><img src={CloseImg.src} /></div>
          : ''
      }
    </div>
    <div style={{ padding: '16px' }}>
      <div className={style.runTestEasyRenderWrapper}>
        {
          router.query?.showformdata == '1' ?
            <div>
              整体调试|单点调试 数据：
              <div style={{ cursor: 'pointer' }} onClick={() => copyToClipboard(JSON.stringify(inputsData))}>点击复制inputsData数据{` -整体运行用["inputs"]`}，节点调试{`用["var_params"]`}: </div>
              <div style={{ cursor: 'pointer' }} onClick={() => copyToClipboard(JSON.stringify(blocks[activeFormInteractionBlockId].form_data))}>点击复制form_data数据{`["form_data"]`}: </div>
            </div>
            : ''
        }

        <EasyForm formData={JSON.parse(JSON.stringify(blocks[activeFormInteractionBlockId].form_data))} inputsData={inputsData} onChange={handleFormChange} />
        <Button className={style.confirmbtn} type="primary" onClick={onOk}>确定</Button>
      </div>
    </div>
  </div>
}