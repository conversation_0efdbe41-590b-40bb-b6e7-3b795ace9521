
import { useEffect, useState } from 'react';
import BlockMCPList from './commons/BlockMcpComp/BlockMCPList';
import BlockBranchList from './commons/BlockBranchList';
import KnowledgeSelect from './commons/KnowledgeSelect';
import BlockUserPrompt from "./commons/BlockUserPrompt";
import BlockLLMCollapseSelect from './commons/BlockLLMCollapseSelect';
import BlockTabs from './commons/BlockTabs';

export default function BlockAgentCondition(prop: any) {
    const { blocks, activeBlockId, setBlocks, flowInfo, loopConfigs, resizeWidth, isUserFullScreen, setIsUserFullScreen} = prop;

    const handleKnowledgeChange = (value: string[]) => {
        blocks[activeBlockId].knowledge_list = value;
        setBlocks(prev => ({...blocks}));
    }


    return (
        <>
            {
                blocks[activeBlockId].type == 'nami_agent_condition' && blocks[activeBlockId].id == activeBlockId ?
                    <BlockTabs
                        {...{
                            ...prop,
                            onChangeTab: async() => {
                                return  await setIsUserFullScreen(pre => {
                                    if (pre) {
                                        return !pre
                                    }
                                    return pre
                                })
                            },
                            basicConfigRender: () => {
                                return <>
                                    {
                                        !isUserFullScreen ?
                                            <BlockBranchList  {...{ ...prop, text: '分支', borderBottom: false, borderTop: false }} />
                                            : ''
                                    }
                                    <BlockUserPrompt
                                        isUserFullScreen={isUserFullScreen}
                                        setIsUserFullScreen={setIsUserFullScreen}
                                        activeBlockId={activeBlockId}
                                        blocks={blocks}
                                        setBlocks={setBlocks}
                                        flowInfo={flowInfo}
                                        loopConfigs={loopConfigs}
                                        resizeWidth={resizeWidth}
                                    />
                                    {
                                        !isUserFullScreen ?
                                            <>
                                                <BlockMCPList {...{ ...prop, borderTop: true }} />
                                                <KnowledgeSelect onChange={handleKnowledgeChange} value={blocks[activeBlockId].knowledge_list} />
                                                <BlockLLMCollapseSelect  {...{ ...prop }} />
                                            </>
                                            : ''
                                    }

                                </>
                            }
                        }}
                    />
                    : ''
            }
        </>
    )
}
