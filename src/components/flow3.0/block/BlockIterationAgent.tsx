import { useRef, useState } from "react";
import { Select, message, Collapse } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useRecoilValue } from "recoil";
import { edgesAtom } from "@/atoms/flowEditorAtoms";

import KnowledgeSelect from "./commons/KnowledgeSelect";

// import BlockPrompt from "./BlockPrompt";
import BlockUserPrompt from "./commons/BlockUserPrompt";
import BlockLLMCollapseSelect from './commons/BlockLLMCollapseSelect';


import inputStyles from "@/styles/flow3.0/Inputs.module.scss";
import BlockMCPList from "./commons/BlockMcpComp/BlockMCPList";
import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import BlockTabs from "./commons/BlockTabs";


/**
 * 迭代Agent
 * @param prop 
 * @returns 
 */
export default function BlockIterationAgent(prop: any) {
  const { flowInfo, flowLoading, promptList, modelList,
    getType, isWaiWang, setOpenMarkDown, setContentValue, activeLlm, isShowLlmParam,
    setIsShowLlmParam, isUnEdit, activeBlockId, blocks, blockIds, setBlocks, teamId, loopConfigs, resizeWidth, isUserFullScreen, setIsUserFullScreen } = prop;

  const handleKnowledgeChange = (value: string[]) => {
    console.log('🚀 ~dd ~ handleKnowledgeChange ~ value:', value)
    blocks[activeBlockId].knowledge_list = value;
    setBlocks(prev => ({...blocks}));
  }

  // console.log('🚀 ~dd ~ blocks[activeBlockId].knowledge_list:', blocks[activeBlockId])
  return (
    <BlockTabs
      {
      ...{
        ...prop,
        borderBottom: true,
        onChangeTab: async () => {
          return await setIsUserFullScreen(pre => {
            if (pre) {
              return !pre
            }
            return pre
          })
        },
        basicConfigRender: () => {
          return <>
            <div className={flowStyles.maskWrapper}>
              {isUnEdit && <div className={flowStyles.mask}></div>}
              <BlockUserPrompt
                isUserFullScreen={isUserFullScreen}
                setIsUserFullScreen={setIsUserFullScreen}
                activeBlockId={activeBlockId}
                blocks={blocks}
                setBlocks={setBlocks}
                loopConfigs={loopConfigs}
                flowInfo={flowInfo}
                resizeWidth={resizeWidth}
								borderTop={false}
								borderBottom={false}
              />
            </div>
            <div className={isUserFullScreen ? flowStyles.hideWrapper : ''}>
              <BlockMCPList {...prop} borderTop />
              <KnowledgeSelect onChange={handleKnowledgeChange} value={blocks[activeBlockId].knowledge_list} />
              <BlockLLMCollapseSelect {...prop} />
            </div>
          </>
        }
      }
      }
    />
  );
}

