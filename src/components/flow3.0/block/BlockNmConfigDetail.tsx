import React, { forwardRef, ForwardRefRenderFunction, useState, useEffect } from 'react';
import { Input, Select, Slider, InputNumber, Radio, Spin } from 'antd';
import BlockTitle from '@/components/flow3.0/block/commons/BlockTitle';
import Divider from '@/components/common/Divider';
import styles from '@/styles/flow3.0/Common.module.scss';
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import BlockLlmSelect from './BlockLlmSelect';
import modelIcon from './commons/modelIcon';

interface BlockConfigDetailProps {
  isWaiWang: boolean;
  waiwangLlmList: any[];
  llmList: any[];
  activeBlockId: string;
  blocks: any;
  setBlocks: (blocks: any) => void;
  teamId: string;
  isShowLlmParam: boolean;
  setIsShowLlmParam: (isShow: boolean) => void;
  setChangeStamp: (stamp: number) => void;
  aiModuleConfig: any[];
  pLoading: boolean;
  isFunctionCall?: boolean;
}

interface ModelIcon {
  [key: string]: string;
}

const BlockConfigDetail: ForwardRefRenderFunction<HTMLDivElement, BlockConfigDetailProps> = (
  {
    isWaiWang,
    waiwangLlmList,
    llmList,
    activeBlockId,
    blocks,
    setBlocks,
    teamId,
    isShowLlmParam,
    setIsShowLlmParam,
    setChangeStamp,
    aiModuleConfig,
    pLoading,
    isFunctionCall = false
  },
  ref
) => {
  const [modelIcons, setModelIcons] = useState<ModelIcon>({});

  // 获取所有模型提供商的图标
  useEffect(() => {
    // 从modelIcon中获取已缓存的图标
    const iconCache: ModelIcon = {};
    Array.from(modelIcon.modelIconMap.keys()).forEach((provider) => {
      const cachedIcon = modelIcon.getModelIconCache(provider);
      if (cachedIcon) {
        iconCache[provider] = cachedIcon;
      }
    });
    setModelIcons(iconCache);
  }, []);

  return (
    <div className={(isShowLlmParam ? (flowStyles.llmConfigWrapper + ' ' + flowStyles.namillmConfigWrapper) : flowStyles.hideLlmParamsWrapper)} ref={ref}>
      {/* <div>
        <BlockTitle text='模型' style={{ marginBottom: '5px' }} />
        <BlockLlmSelect
          isWaiWang={isWaiWang}
          waiwangLlmList={waiwangLlmList}
          llmList={llmList}
          activeBlockId={activeBlockId}
          blocks={blocks}
          setBlocks={setBlocks}
          teamId={teamId}
          isShowLlmParam={isShowLlmParam}
          setIsShowLlmParam={setIsShowLlmParam}
          setChangeStamp={setChangeStamp}
          isShowIcon={false}
          isFunctionCall={isFunctionCall}
        />
      </div> */}
      {/* <Divider color='#EEEFF2' /> */}
      <div>
        <BlockTitle text='参数' style={{ marginBottom: '12px' }} />
        <Spin spinning={pLoading}>
          <div onClick={(e) => e.stopPropagation()}>
            {aiModuleConfig?.map((config: any) => {
              if (config.component === 'slider') {
                return (
                  <div key={config.key} className={styles.aiModuleItem}>
                    <div className={styles.aiModuleLabel + ' ' + flowStyles.aiModuleLabel}>
                      <span> {config.required ? <span style={{ color: "red", marginRight: '4px' }}>*</span> : <span style={{ width: '7px', height: '1px', display: 'inline-block' }}></span>}{config.name}</span>
                      <span>{blocks[activeBlockId]['llm_params']?.[config.key] || config.value || 0}</span>
                    </div>
                    <Slider
                      min={config.min}
                      max={config.max}
                      value={blocks[activeBlockId]['llm_params']?.[config.key] || config.value || 0}
                      className={styles.aiModuleSlider + ' commonSlider'}
                      style={{ width: '98%' }}
                      step={config.step}
                      onChange={(v: any) => {
                        blocks[activeBlockId]['llm_params'][config.key] = v;
                        setBlocks(prev => ({...blocks}));
                      }}
                    />
                  </div>
                )
              } else if (config.component === 'inputNumber') {
                return (
                  <div key={config.key} className={styles.aiModuleItem} style={{
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <div className={styles.aiModuleLabel + ' ' + flowStyles.aiModuleLabel}>
                      {config.required ? <span style={{ color: "red", marginRight: '4px' }}>*</span> : <span style={{ width: '7px', height: '1px', display: 'inline-block' }}></span>}
                      {config.name}
                    </div>
                    <InputNumber
                      min={config.min}
                      max={config.max}
                      value={blocks[activeBlockId]['llm_params']?.[config.key] || config.value || 0}
                      defaultValue={config.value}
                      style={{ width: '40%' }}
                      onChange={(v) => {
                        blocks[activeBlockId]['llm_params'][config.key] = v;
                        setBlocks(prev => ({...blocks}));
                      }}
                    />
                  </div>
                )
              } else if (config.component === 'selector') {
                return (
                  <div key={config.key} className={styles.aiModuleItem} style={{
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <div className={styles.aiModuleLabel + ' ' + flowStyles.aiModuleLabel}>
                      {config.required ? <span style={{ color: "red", marginRight: '4px' }}>*</span> : <span style={{ width: '7px', height: '1px', display: 'inline-block' }}></span>}
                      {config.name}
                    </div>
                    <Select
                      style={{ width: '40%' }}
                      options={config.options.map((o: any) => {
                        return {
                          label: o,
                          value: o
                        }
                      })}
                      value={blocks[activeBlockId]['llm_params']?.[config.key] || config.value || ''}
                      suffixIcon={null}
                      labelRender={(option) => {
                        // 这里使用当前选中的值
                        const currentValue = blocks[activeBlockId]['llm_params']?.[config.key] || config.value || '';
                        const provider = blocks[activeBlockId].provider_name;
                        if (provider && modelIcons[provider]) {
                          return (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <img 
                                src={modelIcons[provider]} 
                                alt={provider}
                                style={{ 
                                  width: '16px', 
                                  height: '16px', 
                                  aspectRatio: '1/1', 
                                  marginRight: '8px' 
                                }} 
                              />
                              <span>{currentValue}</span>
                            </div>
                          );
                        }
                        return <span>{currentValue}</span>;
                      }}
                      optionRender={(option) => {
                        const provider = blocks[activeBlockId].provider_name;
                        if (provider && modelIcons[provider]) {
                          return (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <img 
                                src={modelIcons[provider]} 
                                alt={provider}
                                style={{ 
                                  width: '16px', 
                                  height: '16px', 
                                  aspectRatio: '1/1', 
                                  marginRight: '8px' 
                                }} 
                              />
                              <span>{option.label}</span>
                            </div>
                          );
                        }
                        return <span>{option.label}</span>;
                      }}
                      onChange={(v: any) => {
                        blocks[activeBlockId]['llm_params'][config.key] = v;
                        setBlocks(prev => ({...blocks}));
                      }}
                    ></Select>
                  </div>
                )
              } else if (config.component === 'radio') {
                return (
                  <div key={config.key} className={styles.aiModuleItem} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignContent: 'center'
                  }}>
                    <div className={styles.aiModuleLabel + ' ' + flowStyles.aiModuleLabel}>
                      {config.required ? <span style={{ color: "red", marginRight: '4px' }}>*</span> : <span style={{ width: '7px', height: '1px', display: 'inline-block' }}></span>}
                      {config.name}
                    </div>
                    <Radio.Group
                      onChange={(e) => {
                        blocks[activeBlockId]['llm_params'][config.key] = e.target.value;
                        setBlocks(prev => ({...blocks}));
                      }}
                      value={blocks[activeBlockId]['llm_params']?.[config.key] !== null || blocks[activeBlockId]['llm_params']?.[config.key] !== undefined ? blocks[activeBlockId]['llm_params']?.[config.key] : config.value}
                    >
                      <Radio value={true}>True</Radio>
                      <Radio value={false}>False</Radio>
                    </Radio.Group>
                  </div>
                )
              } else if (config.component === 'input') {
                return (
                  <div key={config.key} className={styles.aiModuleItem}>
                    <div className={styles.aiModuleLabel + ' ' + flowStyles.aiModuleLabel}>
                      {config.required ? <span style={{ color: "red", marginRight: '4px' }}>*</span> : <span style={{ width: '7px', height: '1px', display: 'inline-block' }}></span>}
                      {config.name}
                    </div>
                    <Input
                      min={config.min}
                      max={config.max}
                      value={blocks[activeBlockId]['llm_params']?.[config.key] || config.value || ''}
                      style={{ width: '40%' }}
                      onChange={(e) => {
                        blocks[activeBlockId]['llm_params'][config.key] = e.target.value;
                        setBlocks(prev => ({...blocks}));
                      }}
                    />
                  </div>
                )
              }
              return null;
            })}
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default forwardRef(BlockConfigDetail);
