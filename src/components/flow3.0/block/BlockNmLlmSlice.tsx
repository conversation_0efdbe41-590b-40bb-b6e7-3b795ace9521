import React, { useRef } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip, Collapse } from 'antd';
import styles from '@/styles/flow3.0/Common.module.scss';
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import BlockNmLlmSelect from './BlockNmLlmSelect';
import BlockNmConfigDetail from './BlockNmConfigDetail';
import useClickOutside from '@/hooks/useClickOutside';

interface BlockLlmSliceProps {
  isWaiWang: boolean;
  waiwangLlmList: any[];
  llmList: any[];
  activeBlockId: string;
  blocks: any;
  setBlocks: (blocks: any) => void;
  teamId: string;
  isShowLlmParam: boolean;
  setIsShowLlmParam: (show: boolean) => void;
  setChangeStamp: (stamp: number) => void;
  aiModuleConfig: any[];
  pLoading: boolean;
  isUserFullScreen?: boolean;
  isSystemFullScreen?: boolean;
  isFunctionCall?: boolean;
}

const BlockLlmSlice: React.FC<BlockLlmSliceProps> = ({
  isWaiWang,
  waiwangLlmList,
  llmList,
  activeBlockId,
  blocks,
  setBlocks,
  teamId,
  isShowLlmParam,
  setIsShowLlmParam,
  setChangeStamp,
  aiModuleConfig,
  pLoading,
  isUserFullScreen = false,
  isSystemFullScreen = false,
  isFunctionCall = false
}) => {
  const llmConfigRef = useRef<HTMLDivElement>(null);

  // 使用点击外部关闭hook
  useClickOutside(llmConfigRef, () => {
    if (isShowLlmParam) {
      setIsShowLlmParam(false);
    }
  }, ['.llm-select-trigger']); // 排除LLM选择器触发元素

  return (
    // <div className={flowStyles.llmWrapper + ' ' + flowStyles.commonWrapper + ' ' + (isUserFullScreen || isSystemFullScreen ? flowStyles.hideWrapper : '')}>
        // <div className={flowStyles.llmParamsWrapper}>
          <div className={flowStyles.namiAiModule}>
            <div className={styles.aiModuleItem + ' ' + flowStyles.namiAiModule}>
              {/* llm下拉选择 */}
              <BlockNmLlmSelect
                isWaiWang={isWaiWang}
                waiwangLlmList={waiwangLlmList}
                llmList={llmList}
                activeBlockId={activeBlockId}
                blocks={blocks}
                setBlocks={setBlocks}
                teamId={teamId}
                isShowLlmParam={isShowLlmParam}
                setIsShowLlmParam={setIsShowLlmParam}
                setChangeStamp={setChangeStamp}
                isFunctionCall={isFunctionCall}
              />
            </div>
            {/* llm详情配置 */}
            <BlockNmConfigDetail
              ref={llmConfigRef}
              isWaiWang={isWaiWang}
              waiwangLlmList={waiwangLlmList}
              llmList={llmList}
              activeBlockId={activeBlockId}
              blocks={blocks}
              setBlocks={setBlocks}
              teamId={teamId}
              isShowLlmParam={isShowLlmParam}
              setIsShowLlmParam={setIsShowLlmParam}
              setChangeStamp={setChangeStamp}
              aiModuleConfig={aiModuleConfig}
              pLoading={pLoading}
              isFunctionCall={isFunctionCall}
            />
          </div>
        // </div>
    // </div>
  );
};

export default BlockLlmSlice;
