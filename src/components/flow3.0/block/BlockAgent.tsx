import { useState, useEffect, useRef } from 'react';

import { Input, Select, Slider, Button, Tooltip, Switch, InputNumber, Radio, Spin, Checkbox } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import styles from '@/styles/flow3.0/Common.module.scss'
import { useRecoilValue } from "recoil";

import doubleArrowUp from '@/images/doubleArrowUp.png'
import doubleArrowDown from '@/images/doubleArrowDown.svg'

import fullscreen from '@/images/fullScreen.svg'
import exitfullscreen from '@/images/exitfullscreen.svg'

import { initJsonEditor } from '../flowFunc';

import BlockUserPrompt from './commons/BlockUserPrompt';

import BlockMCPList from "./commons/BlockMcpComp/BlockMCPList";
import KnowledgeSelect from "./commons/KnowledgeSelect";
import BlockLLMCollapseSelect from './commons/BlockLLMCollapseSelect';
import BlockTabs from './commons/BlockTabs';

export default function BlockAgent(prop: any) {
	const { resizeWidth, flowInfo, flowLoading, promptList, modelList, getType, isWaiWang, setOpenMarkDown, setContentValue, activeLlm, isShowLlmParam, setIsShowLlmParam, isUnEdit, activeBlockId, blocks, blockIds, setBlocks, teamId, loopConfigs, isUserFullScreen, setIsUserFullScreen } = prop;
	// const [isUserFullScreen, setIsUserFullScreen] = useState(false)

	const handleKnowledgeChange = (value: string[]) => {
		console.log('🚀 ~dd ~ handleKnowledgeChange ~ value:', value)
		blocks[activeBlockId].knowledge_list = value;
        setBlocks(prev => ({...blocks}));
	}

	return (
		<>
			{blocks[activeBlockId]?.type === 'nami_agent' ?
				<BlockTabs
					{
					...{
						...prop,
						borderBottom: true,
						onChangeTab: async () => {
							return await setIsUserFullScreen(pre => {
								if (pre) {
									return !pre
								}
								return pre
							})
						},
						basicConfigRender: () => {
							return <>
								<>

									<div className={flowStyles.maskWrapper}>
										{isUnEdit && <div className={flowStyles.mask}></div>}
										<BlockUserPrompt
											isUserFullScreen={isUserFullScreen}
											setIsUserFullScreen={setIsUserFullScreen}
											activeBlockId={activeBlockId}
											blocks={blocks}
											setBlocks={setBlocks}
											flowInfo={flowInfo}
											loopConfigs={loopConfigs}
											resizeWidth={resizeWidth}
											borderTop={false}
											borderBottom={false}
										/>
									</div>


									<div className={isUserFullScreen ? flowStyles.hideWrapper : ''}>
										<BlockMCPList borderTop={true} borderBottom={true} {...prop} />
										<KnowledgeSelect onChange={handleKnowledgeChange} value={blocks[activeBlockId].knowledge_list} />
										<BlockLLMCollapseSelect borderTop={false} {...prop} />
									</div>
								</>
							</>
						}
					}
					}
				/>

				: ''}
		</>
	)
}
