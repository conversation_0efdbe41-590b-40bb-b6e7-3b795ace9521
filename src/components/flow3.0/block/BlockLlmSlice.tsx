import React, { useRef } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip, Collapse } from 'antd';
import styles from '@/styles/flow3.0/Common.module.scss';
import flowStyles from '@/styles/flow3.0/Flow.module.scss';
import BlockLlmSelect from './BlockLlmSelect';
import BlockConfigDetail from './BlockConfigDetail';
import useClickOutside from '@/hooks/useClickOutside';

interface BlockLlmSliceProps {
  isWaiWang: boolean;
  waiwangLlmList: any[];
  llmList: any[];
  activeBlockId: string;
  blocks: any;
  setBlocks: (blocks: any) => void;
  teamId: string;
  isShowLlmParam: boolean;
  setIsShowLlmParam: (show: boolean) => void;
  setChangeStamp: (stamp: number) => void;
  aiModuleConfig: any[];
  pLoading: boolean;
  isUserFullScreen?: boolean;
  isSystemFullScreen?: boolean;
  isFunctionCall?: boolean;
}

const BlockLlmSlice: React.FC<BlockLlmSliceProps> = ({
  isWaiWang,
  waiwangLlmList,
  llmList,
  activeBlockId,
  blocks,
  setBlocks,
  teamId,
  isShowLlmParam,
  setIsShowLlmParam,
  setChangeStamp,
  aiModuleConfig,
  pLoading,
  isUserFullScreen = false,
  isSystemFullScreen = false,
  isFunctionCall = false
}) => {
  const llmConfigRef = useRef<HTMLDivElement>(null);

  // 使用点击外部关闭hook
  useClickOutside(llmConfigRef, () => {
    if (isShowLlmParam) {
      setIsShowLlmParam(false);
    }
  }, ['.llm-select-trigger']); // 排除LLM选择器触发元素

  const items = [
    {
      key: '1',
      label: <div className={flowStyles.collapseKeyLabel}>模型</div>,
      children: (
        <div className={flowStyles.llmParamsWrapper}>
          <div className={flowStyles.aiModule}>
            <div className={styles.aiModuleItem}>
              {/* llm下拉选择 */}
              <BlockLlmSelect
                isWaiWang={isWaiWang}
                waiwangLlmList={waiwangLlmList}
                llmList={llmList}
                activeBlockId={activeBlockId}
                blocks={blocks}
                setBlocks={setBlocks}
                teamId={teamId}
                isShowLlmParam={isShowLlmParam}
                setIsShowLlmParam={setIsShowLlmParam}
                setChangeStamp={setChangeStamp}
                isFunctionCall={isFunctionCall}
              />
            </div>
            {/* llm详情配置 */}
            <BlockConfigDetail
              ref={llmConfigRef}
              isWaiWang={isWaiWang}
              waiwangLlmList={waiwangLlmList}
              llmList={llmList}
              activeBlockId={activeBlockId}
              blocks={blocks}
              setBlocks={setBlocks}
              teamId={teamId}
              isShowLlmParam={isShowLlmParam}
              setIsShowLlmParam={setIsShowLlmParam}
              setChangeStamp={setChangeStamp}
              aiModuleConfig={aiModuleConfig}
              pLoading={pLoading}
              isFunctionCall={isFunctionCall}
            />
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className={flowStyles.llmWrapper + ' ' + flowStyles.commonWrapper + ' ' + (isUserFullScreen || isSystemFullScreen ? flowStyles.hideWrapper : '')}>
      <Collapse 
        items={items} 
        defaultActiveKey={['1']} 
        ghost 
      />
    </div>
  );
};

export default BlockLlmSlice;
