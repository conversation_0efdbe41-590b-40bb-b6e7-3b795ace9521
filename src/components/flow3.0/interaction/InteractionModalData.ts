// 生成RFC4122版本4兼容的UUID
function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0, 
              v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 为对象数组中的每个对象添加ID
function addIdsToComponents(components) {
    return components.map(component => ({
        ...component,
        id: uuidv4()
    }));
}
function addIdsToComponent(component) {
    return {
        ...component,
        id: uuidv4()
    };
}

const templateData0 = addIdsToComponents([
        {
            type: 'Input',
            props: {
                label: '',
                value: '',
            },
            mode: 'variable',
            variable: {
                label: '',
                defaultValue: ''
            },
            immutable: {
                label: '来访主题',
                defaultValue: '',
            },
           originalProps: {
                label: '来访主题',
                placeholder: '请填写来访主题',
            },
        },
        {
            type: 'DateSelect',
            props: {
                label: '',
                picker: 'datetime',
                value: '',
            },
            mode: 'variable',
            variable: {
                label: '',
            },
            immutable: {
                label: '来访时间',
            },
            originalProps: {
                value: '',
                label: '来访时间',
                picker: 'datetime',
                placeholder: '请选择来访时间',
            },
        },
        {
            type: 'Input',
            props: {
                label: '',
                value: '',
            },
            mode: 'variable',
            variable: {
                label: '',
                defaultValue: ''
            },
            immutable: {
                label: '来访人员',
                defaultValue: '',
            },
            originalProps: {
                label: '来访人员',
                placeholder: '请填写来访人员姓名',
            },
        }
])

const templateData1 = addIdsToComponents([
        {
            type: 'Input',
            props: {
                label: '',
                value: '',
            },
            mode: 'variable',
            variable: {
                label: '',
                defaultValue: ''
            },
            immutable: {
                label: '接待地点',
                defaultValue: '',
            },
            originalProps: {
                label: '接待地点',
                placeholder: '请填写接待地点',
            },
        },
        {
            type: 'Input',
            props: {
                label: '',
                value: '',
            },
            mode: 'variable',
            variable: {
                label: '',
                defaultValue: ''
            },
            immutable: {
                label: '所在楼宇',
                defaultValue: '',
            },
             originalProps: {
                label: '所在楼宇',
                placeholder: '请填写所在楼宇',
            },
        },
        {
            type: 'Radio',
            props: {
                label: '',
                value: '',
                options:[]
            },
            mode: 'variable',
            variable: {
                label: '',
                options:''
            },
            immutable: {
                label: '访问事由',
                options: ['商务浅谈','面试','培训学习','新员工入职'],
            },
             originalProps: {
                label: '访问事由',
                value: '',
                options: ['商务浅谈','面试','培训学习','新员工入职'],
            },
        },
        {
            type: 'Radio',
            props: {
                label: '',
                value: '',
                options: []
            },
            mode: 'variable',
            variable: {
                label: '',
                options:''
            },
            immutable: {
                label: '是否风险性公司',
                options: ['是','否'],
            },
             originalProps: {
               label: '是否风险性公司',
                options: ['是','否'],
            },
        },
        {
            type: 'Input',
            props: {
                label: '',
                value: '',
            },
            mode: 'variable',
            variable: {
                label: '',
                defaultValue: ''
            },
            immutable: {
                label: '来访人员',
                defaultValue: '',
            },
             originalProps: {
                label: '来访人员',
                placeholder: '请填写来访人员',
            },
        },
])

const componentsData = addIdsToComponents([
    {
        type: 'Radio',
        props: {
            label: '',
            value: '',
            options: []
        },
        mode: 'variable',
        variable: {
            label: '',
            options:''
        },
        immutable: {
            label: '单选按钮',
            options: ['选项1', '选项2','选项3'],
        },
          originalProps: {
            label: '单选按钮',
            options: ['选项1', '选项2','选项3'],
            defaultValue: '选项1',
        },
    },
    {
        type: 'Checkbox',
        props: {
            label: '',
            value: '',
            options:[]
        },
        mode: 'variable',
        variable: {
            label: '',
            options: ''
        },
        immutable: {
            label: '多选按钮',
            options: ['选项1', '选项2','选项3'],
        },
        originalProps: {
            label: '多选按钮',
            options: ['选项1', '选项2','选项3'],
            defaultValue: ['选项1'],
        },
    },
    {
        type: 'Input',
        props: {
            label: '',
            value: '',
        },
        mode: 'variable',
        variable: {
            label: '',
            defaultValue: ''
        },
        immutable: {
            label: '文本输入',
            defaultValue: '',
        },
        originalProps: {
            label: '文本输入',
        },
    },
    {
        type: 'TextList',
        props: {
            label: '',
            value: '',
            options: ''
        },
        mode: 'variable',
        variable: {
            label: '',
            options: ''
        },
        immutable: {
            label: '文本列表',
            options: ['内容1', '内容2', '内容3'],
        },
        originalProps: {
            label: '文本列表',
            options: ['内容1', '内容2', '内容3'],
        },
    },
        {
          type: 'MarkdownView',
          props: {
              content: '',
          },
          mode: 'variable',
          variable: {
              content: '',
          },
          immutable: {
              content: '**集团开发大赛背景** \n\n \n 详细阐述举办Agent开发大赛的背景和市场需求，大赛目的旨在推动Agent开发技术发展及挖掘人才',
          },
          originalProps: {
              content: '**集团开发大赛背景** \n\n \n 详细阐述举办Agent开发大赛的背景和市场需求，大赛目的旨在推动Agent开发技术发展及挖掘人才'
          },
      },
      {
        type: 'Textarea',
        props: {
            value: '',
            defaultValue: '',
            placeholder: "文本框"
        },
        mode: 'variable',
        variable: {
            value: '',
            defaultValue: ''
        },
        immutable: {
            value: '',
            defaultValue: '',
        },
        originalProps: {
            defaultValue: '',
            value: '',
            placeholder: "文本框"
        },
        
      },
      {
        type: 'DateSelect',
        props: {
            label: '',
            picker: 'datetime',
            value: '',
        },
        mode: 'variable',
        variable: {
            label: '',
        },
        immutable: {
            label: '日期选择器',
        },
        originalProps: {
            label: '日期选择器',
            placeholder: '请选择日期',
        },
    },
      {
        type: 'LocationSelect',
        props: {
            label: '',
            value: '',
        },
        mode: 'variable',
        variable: {
            label: '',
        },
        immutable: {
            label: '地址选择器',
        },
        originalProps: {
            label: '地址选择器',
            defaultValue: '',
        },
    }
])

export {
  templateData0,
  templateData1,
  componentsData,
  addIdsToComponent,
  uuidv4,
}

