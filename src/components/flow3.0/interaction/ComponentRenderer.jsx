import React from 'react';
import {
    Radio,
    Input,
    DateSelect,
    Checkbox,
    LocationSelect,
    TextList,
    Textarea,
    MarkdownView,
} from '@q/easy-form-components';

// 创建组件映射表，用于直接访问导入的组件
const ImportedComponents = {
  Radio,
  Input,
  DateSelect,
  Checkbox,
  LocationSelect,
  TextList,
  Textarea,
  MarkdownView,
};

// 自定义占位组件
const PlaceholderComponent = ({ type, ...props }) => {
  return (
    <div style={{ border: '1px dashed #ccc', padding: '10px', borderRadius: '4px' }}>
      <div style={{ fontWeight: 'bold' }}>{props.label || '组件'}</div>
      <div style={{ color: '#666', fontSize: '12px' }}>类型: {type}</div>
    </div>
  );
};

// 渲染单个组件
const RenderComponent = ({ componentData, onChange }) => {
  const { type, props = {} } = componentData;
  
  console.log('渲染组件类型:', type, ImportedComponents[type]);
  console.log('ImportedComponents:=====>', ImportedComponents);
   let Component =  null;
  // 首先尝试从导入的组件中获取
  if (ImportedComponents[type]) {
     Component = ImportedComponents[type]?.component;

    try {
       if(type === 'Textarea') {
          return <div style={{background:'transparent'}}>
              {/* <div style={{color: '#1d2129', fontSize: '14px',fontWeight:'600',textAlign:'left',marginBottom:'6px'}}>
                <span>{type === 'Textarea' ? '输入框' : '展示信息'}</span>
              </div> */}
            {
              React.createElement(Component, {
                ...props,
                onChange: (value) => onChange && onChange(value)
              })
            }
          </div>
       } else if( type === "MarkdownView" ){
        return <div style={{background:'transparent', padding: '8px'}}>
          <div style={{paddingBottom: '8px', fontSize: '14px', fontWeight: '600'}}>展示信息</div>
          <div style={{paddingBottom: '8px', fontSize: '12px', fontWeight: '500'}}>集团开发大赛背景</div>
          <div style={{paddingBottom: '8px', fontSize: '12px', fontWeight: '400'}}>详细阐述举办Agent开发大赛的背景和市场需求，大赛目的旨在推动Agent开发技术发展及挖掘人才</div>
        </div>
       } else {
          return React.createElement(Component, {
            ...props,
            onChange: (value) => onChange && onChange(value)
          });
       }
    } catch (err) {
      console.error(`渲染导入组件 ${type} 时出错:`, err);
      return <PlaceholderComponent type={type} {...props} error={err.message} />;
    }
  }
  
  if (typeof window === 'undefined') {
    return <PlaceholderComponent type={type} {...props} />;
  }
  
  if (!Component) {
    return <PlaceholderComponent type={type} {...props} />;
  }
};

// 新增: 渲染配置组件
const ConfigRenderer = ({ componentType, configType = 'variable', configData, onChange, variableList = [] }) => {
  if (typeof window === 'undefined') {
    return <div>配置器加载中...</div>;
  }
  // 安全获取配置组件
  let ConfigComponent = null;
  
   console.log('ImportedComponents[componentType]====',ImportedComponents[componentType],componentType);
  try {
    // 尝试从导入的组件获取配置器
    if (ImportedComponents[componentType] && 
        ImportedComponents[componentType].setter && 
        ImportedComponents[componentType].setter[configType === 'variable' ? 'dynamicSetter' : 'staticSetter']) {
      ConfigComponent = ImportedComponents[componentType].setter[configType === 'variable' ? 'dynamicSetter' : 'staticSetter'];
    }
  } catch (err) {
    console.error('获取配置组件时发生错误:', err);
  }
  
  // 如果没有找到有效配置组件，显示提示信息
  if (!ConfigComponent) {
    return <div>该组件暂不支持{configType === 'variable' ? '动态' : '静态'}配置</div>;
  }
  
  console.log(`渲染 ${componentType} 的配置器，参数是：`, configData);
  // 尝试使用React.createElement创建配置组件
  try {
    return React.createElement(ConfigComponent, {
      value: {
        ...configData,
        options: configType === 'variable' ? 
          (configData.options ? configData.options.replace(/{{/g, '').replace(/}}/g, '') : '') : 
          configData.options
      },
      variableList: variableList,
      onChange: (value) => {
        console.log(`配置 ${componentType} 的值已更改:`, value);
        onChange && onChange(configType === 'variable' ? {
          ...value,
          options: value.options ? `{{${value.options}}}` : undefined
        } : value)
      }
    });
  } catch (err) {
    console.error(`渲染 ${componentType} 的配置器时出错:`, err);
    return <div>配置器渲染错误: {err.message}</div>;
  }
};

// 渲染多个组件
const ComponentRenderer = ({ components = [], onChange, dataType = 'immutable', isTemplate = false }) => {
  if (!Array.isArray(components) || components.length === 0) {
    return <div>无组件数据</div>;
  }

  return (
    <div>
      {components.map((comp, index) => {
        // 准备props
        const displayProps = {
          ...comp.originalProps,
        };
        
        return (
          <div key={index}>
            <RenderComponent 
              componentData={{
                type: comp.type,
                props: displayProps
              }} 
              onChange={(value) =>{
                 console.log(`组件 ${comp.type} 的值已更改:`, value);
                 onChange && onChange(index, value)
              }} 
            />
          </div>
        );
      })}
    </div>
  );
};

// 导出新增的配置渲染器
export { ComponentRenderer as default, ConfigRenderer, ImportedComponents };
