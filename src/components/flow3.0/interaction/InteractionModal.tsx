import React, { useEffect, useState, forwardRef, useImperativeHandle, useCallback } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { Modal, Button, Tabs, Empty, Input, Segmented } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import styles from './InteractionModal.module.scss';
import InterClose from '@/images/flow3.0/interaction_close.svg';
import InterFull from '@/images/flow3.0/interaction_full.svg';
import { templateData0, templateData1, componentsData, addIdsToComponent } from './InteractionModalData';
import EasyFormWrapper from './EasyFormWrapper';
import ComponentRenderer, { ConfigRenderer } from './ComponentRenderer';

declare global {
    interface Window {
        easyRenderLoaded?: boolean;
        EasyRender?: any;
    }
}

interface InteractionModalProps {
    options: any[]; // 变量选项
    modalData?: any; // 模态框数据
    visible?: boolean;
    onCancel?: () => void;
    onOk?: (data: any) => void;
}

// 定义暴露给父组件的方法接口
export interface InteractionModalRef {
    openModal: () => void;
}

const InteractionModal = forwardRef<InteractionModalRef, InteractionModalProps>(({ options = [], modalData = [], visible = false, onCancel, onOk }, ref) => {
    // 标签页状态
    const [activeTab, setActiveTab] = useState('component');
    const [isOpenModel, setIsOpenModel] = useState(visible);
    const [dataTypeValue, setDataTypeValue] = useState('variable');
    const [isFullScreen, setIsFullScreen] = useState(false);
    const [templateList, setTemplateList] = useState([templateData0, templateData1, componentsData]);
    const [variableOptions, setVariableOptions] = useState(options);
    const [componentsOriginData, setComponentsOriginData] = useState(componentsData);// 原始组件数据

    // 添加一个状态用于触发重新渲染
    const [modalKey, setModalKey] = useState(0);
    const [currentEasyFormData, setCurrentEasyFormData] = useState(modalData);//正在编辑的内容
    const [selectTemplateIndex, setSelectTemplateIndex] = useState(0);
    const [currentEasyFormDataId, setCurrentEasyFormDataId] = useState(-1);

    // 添加标记，跟踪初始化状态
    const initializedRef = React.useRef(false);
    // 存储之前的modalData用于比较
    const prevModalDataRef = React.useRef(modalData);

    useEffect(() => {
        setIsOpenModel(visible);
    }, [visible]);

    useEffect(() => {
        const optionsArray = Object.keys(options) || [];
        setVariableOptions(optionsArray);
    }, [options]);

    useEffect(() => {
        // 首次渲染初始化
        if (!initializedRef.current) {
            initializedRef.current = true;

            // 优先使用modalData，如果有效
            if (modalData && Array.isArray(modalData) && modalData.length > 0) {
                console.log('使用modalData初始化表单:', modalData);
                const deepCopiedData = JSON.parse(JSON.stringify(modalData));
                setCurrentEasyFormData(deepCopiedData);
                setCurrentEasyFormDataId(-1);
                setSelectTemplateIndex(-1);
            } else if (templateList.length > 0) {// 如果modalData无效，使用默认模板
                console.log('使用默认模板初始化表单:', templateList[0]);
                selectTemplate(templateList[0], 0);
                const deepCopyTemplate = JSON.parse(JSON.stringify(templateList[0]));
                setCurrentEasyFormData(deepCopyTemplate);
            }
            prevModalDataRef.current = modalData;
            return;
        }

        // 后续更新：监测modalData变化
        const hasModalDataChanged = () => {
            if (prevModalDataRef.current !== modalData) {
                const prevJson = JSON.stringify(prevModalDataRef.current);
                const currentJson = JSON.stringify(modalData);
                return prevJson !== currentJson;
            }
            return false;
        };

        if (hasModalDataChanged()) {
            console.log('modalData 发生实质性变化，更新状态:', modalData);
            prevModalDataRef.current = modalData;

            if (modalData && Array.isArray(modalData) && modalData.length > 0) {
                const deepCopiedData = JSON.parse(JSON.stringify(modalData));
                setCurrentEasyFormData(deepCopiedData);
                setCurrentEasyFormDataId(-1);
                setSelectTemplateIndex(-1);
            }
        }
    }, [modalData, templateList]);

    useEffect(() => {
        if (isOpenModel) {
            resetModalState();
            setModalKey(prevKey => prevKey + 1); // 更新key触发重新渲染
        }
    }, [isOpenModel]);

    const resetModalState = () => {
        setActiveTab('component');
        setDataTypeValue('variable');
        setIsFullScreen(false);
        // 优先使用modalData，如果有效
        if (modalData && Array.isArray(modalData) && modalData.length > 0) {
            console.log('使用modalData初始化表单:', modalData);
            const deepCopiedData = JSON.parse(JSON.stringify(modalData));
            setCurrentEasyFormData(deepCopiedData);
            setCurrentEasyFormDataId(-1);
            setSelectTemplateIndex(-1);
        } else if (templateList.length > 0) {// 如果modalData无效，使用默认模板
            console.log('使用默认模板初始化表单:', templateList[0]);
            selectTemplate(templateList[0], 0);
            const deepCopyTemplate = JSON.parse(JSON.stringify(templateList[0]));
            setCurrentEasyFormData(deepCopyTemplate);
        }
        prevModalDataRef.current = modalData;
    };

    useImperativeHandle(ref, () => ({
        openModal: () => {
            setIsOpenModel(true);
        }
    }));

    // 处理Modal关闭
    const handleCancel = () => {
        initializedRef.current = false;

        setIsOpenModel(false);
        onCancel && onCancel();
    };

    const handlerOk = () => {
        console.log('当前EasyForm数据:======>', currentEasyFormData);
        setIsOpenModel(false);
        onOk && onOk(JSON.parse(JSON.stringify(currentEasyFormData)));
    };

    const onChangFullScreenHandle = () => {
        setIsFullScreen(!isFullScreen);
        // 更新key以触发重新渲染
        setModalKey(prevKey => prevKey + 1);
    };

    // 根据全屏状态确定模态框宽度和样式
    const getModalWidth = () => {
        return isFullScreen ? '100vw' : 1200;
    };

    const getModalStyles = () => {
        const baseBodyStyle = {
            overflow: 'auto',
            padding: '0',
        };

        const baseContentStyle = {
            paddingBottom: 0,
            margin: '0 auto',
        };

        if (isFullScreen) {
            return {
                body: {
                    ...baseBodyStyle,
                    height: 'calc(100vh - 130px)',
                    maxHeight: 'calc(100vh - 130px)',
                },
                content: {
                    ...baseContentStyle,
                    top: 0,
                    maxWidth: '100vw',
                    width: '100vw',
                    height: '100vh',
                },
                mask: {
                    backgroundColor: 'rgba(0, 0, 0, 0.85)', // 全屏模式背景色更深
                },
                wrapper: {
                    top: 0,
                    padding: 0,
                }
            };
        } else {
            return {
                body: {
                    ...baseBodyStyle,
                    height: '607px',
                    maxHeight: 'calc(100vh - 110px)',
                },
                content: {
                    ...baseContentStyle,
                    top: 0,
                    maxWidth: '100vw',
                }
            };
        }
    };

    // 选择模板
    const selectTemplate = (template, index) => {
        if (index === selectTemplateIndex) {
            console.log('已选中该模板，跳过刷新');
            return;
        }
        console.log('选中模板:======>', template);
        const deepCopyTemplate = JSON.parse(JSON.stringify(template));
        setCurrentEasyFormData(deepCopyTemplate);
        setSelectTemplateIndex(index);
    };

    const handleComponentClick = (component) => {
        console.log('选中组件:', component);

        setCurrentEasyFormData(prevTemplate => {
            // 深拷贝当前模板，避免直接修改原数据
            const updatedTemplate = JSON.parse(JSON.stringify(prevTemplate));
            // 添加选中的组件到模板中
            updatedTemplate.push(addIdsToComponent(component));
            return updatedTemplate;
        });
        setSelectTemplateIndex(-1);
    };

    // 判断是否为当前选中的模板
    const isTemplateSelected = (template, index) => {
        if (!currentEasyFormData) return false;
        return index === selectTemplateIndex;
    };

    const onEasyFormWrapperHandler = (keyName, data) => {
        console.log('EasyFormWrapper data:', keyName, data);

        if (keyName === 'keydown') {
            // 处理键盘事件
            const { keyboard, id } = data;
            if (!currentEasyFormData || id === -1 || !id) {
                setCurrentEasyFormDataId(-1);
                return;
            };
            setCurrentEasyFormDataId(id);
            const componentIndex = currentEasyFormData.findIndex(comp => comp.id === id);
            console.log('当前组件索引:', componentIndex);
            if (componentIndex === -1) return;
            const updatedTemplate = JSON.parse(JSON.stringify(currentEasyFormData));
            if (keyboard === 'ArrowDown') {
                if (componentIndex >= updatedTemplate.length - 1) {
                    console.log('已经是最后一个组件，无法继续下移');
                    return;
                }
                const temp = updatedTemplate[componentIndex];
                updatedTemplate[componentIndex] = updatedTemplate[componentIndex + 1];
                updatedTemplate[componentIndex + 1] = temp;
                console.log(`处理 ${keyboard} 键，组件ID: ${id} 下移成功`);
            } else if (keyboard === 'ArrowUp') {
                if (componentIndex <= 0) {
                    console.log('已经是第一个组件，无法继续上移');
                    return;
                }
                // 交换当前组件和上一个组件的位置
                const temp = updatedTemplate[componentIndex];
                updatedTemplate[componentIndex] = updatedTemplate[componentIndex - 1];
                updatedTemplate[componentIndex - 1] = temp;

                console.log(`处理 ${keyboard} 键，组件ID: ${id} 上移成功`);
            } else if (keyboard === 'Delete') {
                // 从模板中移除当前组件
                if (updatedTemplate.length === 1) {
                    console.log('模板中只有一个组件，无法删除');
                    return;
                }
                // 删除指定ID的组件
                updatedTemplate.splice(componentIndex, 1);
                console.log(`处理 Delete 键，组件ID: ${id} 已删除`);
                setCurrentEasyFormDataId(-1); // 清除当前编辑组件ID
            }
            setCurrentEasyFormData(updatedTemplate);
        } else if (keyName === 'selected') {
            const { id } = data;
            if (!currentEasyFormData || !id || id === -1) {
                setCurrentEasyFormDataId(-1);
                return;
            };
            setCurrentEasyFormDataId(id);
            const component = findComponentById(id);
            console.log('选中组件:>>>>>>>>', component);
            if (component.mode === 'variable') {
                setDataTypeValue('variable');
            } else {
                setDataTypeValue('immutable');
            }
        }
    }

    // 根据ID查找组件
    const findComponentById = (id) => {
        if (!currentEasyFormData || !id) return null;
        return currentEasyFormData.find(comp => comp.id === id) || null;
    };

    // 处理配置更改
    const handleConfigChange = (configData) => {
        console.log('配置更改:', configData, dataTypeValue);
        if (!currentEasyFormDataId || currentEasyFormDataId === -1) return;

        setCurrentEasyFormData(prevData => {
            const updatedData = JSON.parse(JSON.stringify(prevData));
            const componentIndex = updatedData.findIndex(comp => comp.id === currentEasyFormDataId);

            if (componentIndex === -1) return prevData;

            // 更新相应的配置数据
            if (dataTypeValue === 'variable') {
                updatedData[componentIndex].variable = {
                    ...updatedData[componentIndex].variable,
                    ...configData
                };
            } else {
                updatedData[componentIndex].immutable = {
                    ...updatedData[componentIndex].immutable,
                    ...configData
                };
            }
            console.log('更新后的EasyForm数据:======>', updatedData);
            return updatedData;
        });
    };

    return (
        <div className={styles.modalWrapper}>
            <Modal
                key={modalKey}
                title={
                    <div className={styles.modalTitleContent}>
                        <div className={styles.modalTitleContentTitle}>
                            卡片
                        </div>
                        <div className={styles.modalTitleContentRight}>
                            <img src={InterFull.src} alt='全屏' onClick={onChangFullScreenHandle} className={styles.modalTitleContentIcon} />
                            <img src={InterClose.src} alt='关闭' className={styles.modalTitleContentIcon} onClick={handleCancel} />
                        </div>
                    </div>
                }
                open={isOpenModel}
                closable={false}
                onCancel={handleCancel}
                footer={
                    <div className={styles.modalFooter}>
                        <Button onClick={handleCancel} className={styles.modalFooterButton}>
                            取消
                        </Button>
                        <Button type='primary' onClick={handlerOk} className={styles.modalFooterButton}>
                            确认
                        </Button>
                    </div>
                }
                width={getModalWidth()}
                centered={!isFullScreen} // 全屏模式取消居中
                destroyOnClose={true}
                maskClosable={false}
                styles={getModalStyles()}
                modalRender={(modal) => (
                    <div style={{
                        position: 'relative',
                        width: '100%',
                        maxWidth: isFullScreen ? '100vw' : '1200px',
                        margin: '0 auto'
                    }}>
                        {modal}
                    </div>
                )}
                className='interaction-custom-modal'
                wrapClassName={styles.cardModalWrap}
            >
                <div className={`${styles.cardModalContainer} ${isFullScreen ? styles.fullScreenContainer : ''}`}>
                    {/* 左侧 - 标签页区域 */}
                    <div className={styles.cardLeftSection}>
                        <Tabs
                            activeKey={activeTab}
                            centered
                            className='interaction-tabs'
                            onChange={(key) => {
                                setActiveTab(key)
                                console.log('当前选中的标签页:======>', componentsOriginData);
                            }}
                            indicator={{ size: (origin) => origin + 20, align: 'center' }}
                            items={[
                                {
                                    key: 'component',
                                    label: '组件',
                                    children: (
                                        <div className={styles.componentContent}>
                                            {componentsOriginData.length === 0 ? (
                                                <Empty description="暂无组件" />
                                            ) : (
                                                <div className={styles.componentsList}>
                                                    {componentsOriginData.map((component, index) => (
                                                        <div
                                                            key={component.id || index}
                                                            className={styles.componentItem}
                                                            onClick={() => handleComponentClick(component)}
                                                        >
                                                            <div className={styles.componentOverlay}>
                                                                <ComponentRenderer
                                                                    components={[component]}
                                                                    isTemplate={false}
                                                                    onChange={() => { }}
                                                                />
                                                                {/* 透明覆盖层，阻止所有点击事件 */}
                                                                <div className={styles.overlayBlocker}></div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    ),
                                },
                                {
                                    key: 'template',
                                    label: '模板',
                                    children: (
                                        <div className={styles.templateContent}>
                                            {templateList.length === 0 ? (
                                                <Empty description="暂无模板" />
                                            ) : (
                                                <div className={styles.templateList}>
                                                    <div className={styles.templateListHeader}>
                                                        卡片模板
                                                    </div>
                                                    {templateList.map((template, index) => (
                                                        <div
                                                            key={index}
                                                            className={`${styles.templateListItem} ${isTemplateSelected(template, index) ? styles.templateListItemSelected : ''}`}
                                                            onClick={() => selectTemplate(template, index)}
                                                        >
                                                            <div className={styles.componentOverlay}>
                                                                <ComponentRenderer
                                                                    components={template}
                                                                    isTemplate={true}
                                                                    onChange={() => { }}
                                                                />
                                                                {/* 透明覆盖层，阻止所有点击事件 */}
                                                                <div className={styles.overlayBlocker}></div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )
                                            }
                                        </div>
                                    ),
                                }
                            ]}
                        />
                    </div>

                    {/* 中间 - 预览与配置区域 */}
                    <div className={styles.cardMiddleSection}>
                        <div className={styles.cardPreviewArea}>
                            <EasyFormWrapper
                                formData={currentEasyFormData}
                                inputsData={options}
                                selectedId={currentEasyFormDataId}
                                mode='editable'
                                onChange={() => { }}
                                onHandler={onEasyFormWrapperHandler}
                            />
                        </div>
                    </div>

                    {/* 右侧 - 数据配置区域 */}
                    <div className={styles.cardRightSection}>
                        <div className={styles.cardRightSectionHeader}>数据配置</div>
                        <div className={styles.cardRightSectionContent}>
                            <Segmented
                                block
                                value={dataTypeValue}
                                style={{ marginBottom: 16 }}
                                onChange={(value) => {
                                    setDataTypeValue(value);
                                    setCurrentEasyFormData(prevData => {
                                        console.log('当前EasyForm数据prevData:====>', prevData);
                                        const updatedData = JSON.parse(JSON.stringify(prevData));
                                        const componentIndex = updatedData.findIndex(comp => comp.id === currentEasyFormDataId);
                                        if (componentIndex !== -1) {
                                            updatedData[componentIndex].mode = value; // 更新模式
                                        }
                                        console.log('NextData当前EasyForm数据prevData:====>', updatedData);
                                        return updatedData;
                                    });
                                }}
                                options={[
                                    { label: '动态数据', value: 'variable' },
                                    { label: '固定数据', value: 'immutable' },
                                ]}
                                className='interaction-segmented'
                            />
                            <div className={styles.dataConfigContent}>
                                {dataTypeValue === 'variable' ? (
                                    <div className={styles.variableConfig}>
                                        {
                                            currentEasyFormDataId !== -1 && currentEasyFormData && currentEasyFormData.length > 0 ? (
                                                (() => {
                                                    console.log('当前EasyForm数据ID:====>', currentEasyFormDataId);
                                                    const currentComponent = findComponentById(currentEasyFormDataId);
                                                    if (!currentComponent) {
                                                        return <Empty description="无法找到所选组件" />;
                                                    }
                                                    return (
                                                        <div className={styles.configContainer}>
                                                            <div className={styles.configFormWrapper}>
                                                                <ConfigRenderer
                                                                    componentType={currentComponent.type}
                                                                    configType="variable"
                                                                    configData={currentComponent.variable || {}}
                                                                    variableList={variableOptions}
                                                                    onChange={(configData) => handleConfigChange(configData)}
                                                                />
                                                            </div>
                                                        </div>
                                                    );
                                                })()
                                            ) : <Empty description="请选择一个组件进行配置" />
                                        }
                                    </div>
                                ) : (
                                    <div className={styles.immutableConfig}>
                                        {currentEasyFormDataId !== -1 && currentEasyFormData && currentEasyFormData.length > 0 ? (
                                            (() => {
                                                const currentComponent = findComponentById(currentEasyFormDataId);
                                                if (!currentComponent) {
                                                    return <Empty description="无法找到所选组件" />;
                                                }
                                                return (
                                                    <div className={styles.configContainer}>
                                                        <div className={styles.configFormWrapper}>
                                                            <ConfigRenderer
                                                                componentType={currentComponent.type}
                                                                configType="immutable"
                                                                configData={currentComponent.immutable || currentComponent.originalProps || {}}
                                                                onChange={(configData) => handleConfigChange(configData)}
                                                            />
                                                        </div>
                                                    </div>
                                                );
                                            })()
                                        ) : <Empty description="请选择一个组件进行配置" />}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </Modal >
        </div >
    );
});

export default InteractionModal;
