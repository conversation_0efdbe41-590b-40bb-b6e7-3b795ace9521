import { useEffect, useState, useMemo } from 'react';
import React from 'react';
import { isEasyRenderInitialized } from '../../../utils/easyRenderInit';

import {
  EasyForm,
} from '@q/easy-render';

export default function EasyFormWrapper(props) {
  const { formData } = props;  // 提取formData属性
  const [isLoaded, setIsLoaded] = useState(isEasyRenderInitialized());
  const [formKey, setFormKey] = useState(Date.now());
  const [internalFormData, setInternalFormData] = useState(formData);

  const formDataSignature = useMemo(() => {
    return JSON.stringify(formData);
  }, [formData]);

  // 当formData变化时更新内部状态
  useEffect(() => {
    if (formData) {
      setInternalFormData(formData);
      // 生成新的key但不依赖于外部的formKey
      if (isLoaded) {
        // 轻微延迟以确保状态更新
        setTimeout(() => {
          setFormKey(Date.now());
        }, 0);
      }
    }
  }, [formDataSignature, isLoaded]);

  if (!EasyForm) {
    console.error('EasyForm component not found in window.EasyRender');
    // 尝试找出EasyRender的结构
    console.log('EasyRender structure:', window.EasyRender);
    return <div>组件加载失败</div>;
  }

  try {
    // 使用内部状态的formData和内部key，不再依赖外部的key
    const modifiedProps = {
      ...props,
      formData: JSON.parse(JSON.stringify(internalFormData)),
      key: formKey
    };

    return React.createElement(EasyForm, modifiedProps);
  } catch (err) {
    console.error('Error rendering EasyForm:', err);
    return <div>渲染EasyForm时出错: {err.message}</div>;
  }
}
