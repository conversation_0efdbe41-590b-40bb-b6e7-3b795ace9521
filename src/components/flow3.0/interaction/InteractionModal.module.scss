.cardModalContainer {
  display: flex;
  height: 600px;
}

.cardLeftSection {
  width: 320px;
  border-right: 1px solid #f0f0f0;
  padding: 10px 10px 0 10px;
  overflow-y: hidden;
}

.cardMiddleSection {
  flex: 1;
  overflow-x: auto;
  background-color: rgba(233, 237, 242, 1);
  padding: 48px 0;
  box-sizing: border-box;

  .cardPreviewArea {
    width: 400px;
    height: auto;
    margin: 0 auto;
    border-radius: 12px;
    border: 1px solid #F7F9FA;
    background: #FFF;
  }
}

.cardRightSection {
  width: 300px;
  border-left: 1px solid #f0f0f0;
  padding: 12px 16px;
  box-sizing: border-box;
  overflow-y: auto;
  height: 100%;
}
.cardRightSectionHeader {
  color:  #1D2531;
  font-feature-settings: 'liga' off, 'clig' off;
  /* 加粗/16px */
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  margin-bottom: 16px;
}

.cardRightSectionContent {
  height: calc(100% - 40px);
  overflow-y: hidden;
}
.modalTitleContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 16px;
  box-sizing: border-box;
  border-bottom: 1px solid  #EBF0F5;
  background:  #F7F8FA;
  border-radius: 4px 4px 0 0;

  .modalTitleContentTitle {
    color: #1B2532;
    font-feature-settings: 'liga' off, 'clig' off;
    /* 加粗/16px */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }

  .modalTitleContentRight {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 56px;
    gap: 24px;
  }
  .modalTitleContentIcon {
    width: 24px;
    height: 24px;
    padding: 4px;
    color: rgba(0, 0, 0, 0.45);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &:hover {
      cursor: pointer;
      background-color: #eee;
      color: #1B2532;
      width: 24px;
      height: 24px;
      padding: 4px;
    }
  }
}

.dataConfigContent {
  height: 100%;
}
.variableConfig {
  height: 100%;
}

.immutableConfig {
  max-height: 100%;
  overflow-y: auto;
  padding-bottom: 35px;
}

.modalFooter {
  height: 56px;
  padding: 12px 16px;
  box-sizing: border-box;
  border-radius: 0px 0px 4px 4px;
  border-top: 1px solid  #EEEFF2;
  background: #FFF;
}

.modalFooterButton {
  width: 68px;
  height: 32px;
  padding: 5px 16px;
  box-sizing: border-box;
  margin-left: 8px;
}
.templateContent {
  // 模板内容样式
  // height: calc(100% - 356px);
  overflow-y: auto;
  height: 100%;
}

.componentContent {
  padding: 8px 0;
  height: 100%;
  overflow-y: auto;
}

.componentsList {
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: flex-start;
 gap: 8px;
}

.componentItem {
  position: relative;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  border: 1px solid #E1E7ED;
  background: #F7F9FA;
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }
}

/* Modal样式 - 从modal.module.scss合并而来 */
.cardModalWrap {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.cardModalWrap :global(.ant-modal) {
  top: 0 !important;
  padding-bottom: 0;
  margin: 0 auto;
  max-height: 100vh;
}

.cardModalWrap :global(.ant-modal-content) {
  max-height: calc(100vh - 20px);
  margin: 10px 0;
}

@media (max-height: 800px) {
  .cardModalWrap :global(.ant-modal-content) {
    margin: 0;
    max-height: 100vh;
  }
}

@media (max-width: 1200px) {
  .cardModalWrap :global(.ant-modal-content) {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}

.templateList {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  background-color:#fff;

  .templateListHeader {
    color:#1D2531;
    font-feature-settings: 'liga' off, 'clig' off;
    text-align: left;
    /* 加粗/14px */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    align-self: flex-start;
  }

  .templateListItem {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #E1E7ED;
    background: #F7F9FA;
    position: relative;
    cursor: pointer;
    border: 1px solid #eaeaea;
    border-radius: 4px;
    // padding: 12px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
  
    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }
  }

  .templateListItemSelected {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }
}

.fullScreenContainer {
  // 全屏模式下容器的额外样式
  height: calc(100vh - 130px) !important;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.loadingSpinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #1890ff;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.componentOverlay {
  position: relative;
}

.overlayBlocker {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* 完全透明但会捕获所有鼠标事件 */
  background: transparent;
  z-index: 10;
}
