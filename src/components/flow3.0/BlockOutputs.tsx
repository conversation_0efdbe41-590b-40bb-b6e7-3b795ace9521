import { Input, TreeSelect, Tooltip } from 'antd';
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { notify } from "@/utils/messageManager";

import { PlusOutlined } from '@ant-design/icons';

import flowStyles from '@/styles/flow3.0/Flow.module.scss'

import inputsStyles from '@/styles/flow3.0/Inputs.module.scss'

import deleteBtn from '@/images/newFlow/delete.svg'
import * as constants from "@/constants/appConstants";

const keyReg = /^[a-zA-Z]+[a-zA-Z0-9_-]*$/;

const BlockOutputs:React.FC<any> = forwardRef((props, ref) => {
    const { type, activeBlockId, blocks, setBlocks, blockOutputsData, setUpdateStamp } = props;

    useImperativeHandle(ref, () => ({
        addOutputItem
    }))

    const isBatchWebCrawl = type === 'web-crawl-batch'
    const isBatchLlm = type === 'llm-batch'
    const isBatchWebSearch = type === 'web-search-batch'


    const [blockOutputsList, setBlockOutputsList] = useState([{
        key: '',
        isInputError: false,
        treeValue: '',
        // treeData: new Array(),
        valueType: '',
        treeLabel: ''
    }])
    const [selectStamp, setSelectStamp] = useState(0)
    const [treeData, setTreeData] = useState(new Array())
    const [reRenderTreeStamp, setReRenderTreeStamp] = useState(new Date().getTime())
    
    const getType = (str:any) => {
        if(typeof str === 'object' && str ) {
            if(str instanceof Array){
                // 数组
                return {
                    type: 'array',
                    obj: str
                };
            }else {
                // 对象
                return {
                    type: 'object',
                    obj: str
                };
            }
        }else {
            try {
                const obj = JSON.parse(str);
                if(typeof obj === 'object' && obj ) {
                    if(obj instanceof Array){
                        // 数组
                        return {
                            type: 'array',
                            obj: obj
                        };
                    }else {
                        // 对象
                        return {
                            type: 'object',
                            obj: obj
                        };
                    }
                }else {
                    // 字符串 数字 布尔
                    return {
                        type: typeof(str),
                        obj: str
                    };
                }
            } catch(e) {
                return {
                    type: 'string',
                    obj: str
                };
            }
        }
    }
    const getOriginalType = (str:any) => {
        if(typeof str === 'object' && str ) {
            if(str instanceof Array){
                // 数组
                return {
                    type: 'array',
                    obj: str
                };
            }else {
                // 对象
                return {
                    type: 'object',
                    obj: str
                };
            }
        }else {
            // 字符串 数字 布尔
            return {
                type: typeof(str),
                obj: str
            };
        }
    }

    const addOutputItem = () => {
        blockOutputsList.push({
            key: '',
            isInputError: false,
            treeValue: '',
            // treeData: new Array(),
            valueType: '',
            treeLabel: ''
        })
        setBlockOutputsList([...blockOutputsList])
    }
    
    const copyParamValue = (str:any, isNeedTip=true) => {
        const isLlmopsBoard = localStorage.getItem(constants.prompt_isLlmopsBoard);
        if(navigator.clipboard && !isLlmopsBoard){
            navigator.clipboard.writeText(str);
            isNeedTip && notify.success('复制成功');
        }else {
            // 创建text area
            let textArea = document.createElement("textarea");
            textArea.value = str;
            // 使text area不在viewport，同时设置不可见
            textArea.style.position = "absolute";
            textArea.style.opacity = '0';
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            return new Promise((resolve, reject) => {
                // 执行复制命令并移除文本框
                document.execCommand('copy') ? resolve(isNeedTip && notify.success('复制成功')) : reject(isNeedTip && notify.success('复制失败'));
                textArea.remove();
            });
        }
    }
    useEffect(()=>{
        // blockOutputsList 转化 block_output_key_mapping
        if(selectStamp) {
            const keyMap = new Array();
            // console.log(blockOutputsList)
            blockOutputsList.forEach(outputItem => {
                let treeLabel = outputItem.treeLabel;
                let treeLabelArray:any = treeLabel.split('/');

                treeLabelArray = treeLabelArray.map((label:any) => {
                    const isNum = parseInt(label)
                    // 选择的是数组的某一项 或者数组下是对象 继续下钻
                    if((isNum || isNum === 0) && isNum == label){
                        return isNum
                    }
                    return label
                })
                
                keyMap.push({
                    "source": [
                        ...treeLabelArray
                    ],
                    "target": outputItem.key
                })
            })
            blocks[activeBlockId].block_output_key_mapping = keyMap;
            setBlocks(prev => ({...blocks}));
            setUpdateStamp(new Date().getTime())
        }
    },[selectStamp])

    useEffect(()=> {
        // block_output_key_mapping 转化 blockOutputsList
        if(activeBlockId && blocks[activeBlockId].block_output_key_mapping?.length) {
            const outputsArr = new Array();
            // const block_output_key_mapping = [{
            //     target: '',
            //     source: []
            // }]
            blocks[activeBlockId].block_output_key_mapping.forEach((item:any, index:any) => {
                const treeLabel = item.source.join('/');

                const {treeResult, treeValue, valueType }:any = getTreeData(item.source)
                outputsArr.push({
                    key: item.target,
                    isInputError: false,
                    treeLabel: treeLabel,
                    treeValue: treeValue && typeof(treeValue) === 'object' ? JSON.stringify(treeValue) : treeValue,
                    // treeData: treeResult,
                    valueType,
                })
                            })
            setBlockOutputsList(outputsArr);
            setReRenderTreeStamp(new Date().getTime())
        }
    },[activeBlockId, JSON.stringify(blockOutputsData)])

    useEffect(() => {
        const {treeResult, treeValue, valueType }:any = getTreeData()
        setTreeData(treeResult)

    },[JSON.stringify(blockOutputsData)])


    const getTreeData = (treeLabelList=new Array()) => {
        const treeResult:any = [];
        if (blockOutputsData.length === 0) {
            return {
                treeResult,
                treeValue: '',
                valueType: ''
            } 
        }
        const curTreeNode = {
            label: treeLabelList,
            value: '',
            valueType: ''
        }
        if(isBatchLlm || isBatchWebSearch) {
            const isBatch = blocks[activeBlockId].type.indexOf('batch') > -1;
            if (isBatch) {
                const blockOutputsDataObject = blockOutputsData[0]
                if (!blockOutputsDataObject) {
                    return {
                        treeResult,
                        treeValue: '',
                        valueType: ''
                    }
                } else if (typeof blockOutputsDataObject !== 'object') {
                    const keys = Object.keys(blockOutputsData)
                    if(keys.length) {
                        if(keys[0].length) {
                            renderTreeItem({
                                [keys[0]]: blockOutputsData[keys[0]][0]
                            }, treeResult, curTreeNode, type, '0');
                        }
                    }
                } else {
                    const keys = Object.keys(blockOutputsDataObject)

                    if(keys.length) {
                        keys.forEach((key, index) => {
                            renderTreeItem({
                                [key]: blockOutputsDataObject[key]
                            }, treeResult, curTreeNode, type, '' + index); 
                        })
                    }
                }
            } else {
                const keys = Object.keys(blockOutputsData)
                if(keys.length) {
                    if(keys[0].length) {
                        renderTreeItem({
                            [keys[0]]: blockOutputsData[keys[0]][0]
                        }, treeResult, curTreeNode, type, '0');
                    }
                }
            }
        }else {
            renderTreeItem(blockOutputsData, treeResult, curTreeNode, type, '0');
        }

        console.log('treeResult', treeResult)

        return {
            treeResult,
            treeValue: curTreeNode.value,
            valueType: curTreeNode.valueType
        }
    }
    const renderTreeItem = (str:any, treeResult:any, curTreeNode:any, blockType:any, path:any, parentObj:any = null) => {
        // getType 会解析字符串 getOriginalType不会解析字符串
        const {type, obj} = getType(str)
        if(type === 'object' && obj) {
            const keys = Object.keys(obj || {});
            keys.forEach((key, index) => {
                const nextPath = path+'&&&&'+index;
                // 避免label名字一样 无法选中label的情况
                // 需要把整个label传进来 用路径去匹配

                if(key === curTreeNode.label[curTreeNode.label.length - 1]) {
                    // 获取value 效果是默认选中
                    // curTreeNode.value = (obj[key] && typeof(obj[key]) ==='object' ? JSON.stringify(obj[key]) : obj[key])
                    curTreeNode.value = (obj[key] && typeof(obj[key]) ==='object' ? JSON.stringify(obj[key]) : obj[key]) + nextPath
                    curTreeNode.valueType = getOriginalType(obj[key]).type
                }
                // value可以是 字符串 数组 对象 数字等 统一string化
                // console.log(getType(obj[key]).type)
                if(JSON.stringify(obj[key])){
                    const item = {
                        // value: obj[key] && typeof(obj[key]) === 'object' ? JSON.stringify(obj[key]) : obj[key],
                        value: obj[key] && typeof(obj[key]) === 'object' ? JSON.stringify(obj[key])+nextPath : obj[key]+nextPath,
                        title: key,
                        // disabled: (isBatchLlm || isBatchWebSearch) && key === 'output' ? true : false,
                        // disabled: (isBatchWebCrawl || isBatchLlm || isBatchWebSearch) && getType(obj[key]).type != 'array',
                        children: new Array(),
                        valueType: getOriginalType(obj[key]).type
                    };
                    treeResult.push(item)
                    
                    if(typeof(obj[key]) === 'object') {
                        renderTreeItem(obj[key], item.children, curTreeNode, blockType, nextPath, item);
                    }
                }
            })
        }else if( type === 'array') {
            // if(!isBatchWebCrawl && !isBatchLlm && !isBatchWebSearch) {
            // 数组需要下钻到某一项即可
            for(let i = 0;i < obj.length;i++){
                const nextPath = path+'&&&&'+i;
                console.log(parentObj?.title , curTreeNode.label[curTreeNode.label.length - 2])
                if(i === curTreeNode.label[curTreeNode.label.length - 1] && parentObj?.title == curTreeNode.label[curTreeNode.label.length - 2]) {
                    // console.log(i,curTreeNode,44,curTreeNode.label[curTreeNode.label.length - 1])
                    // curTreeNode.value = (obj[i] && typeof(obj[i]) ==='object' ? JSON.stringify(obj[i]) : obj[i]);
                    curTreeNode.value = (obj[i] && typeof(obj[i]) ==='object' ? JSON.stringify(obj[i]) : obj[i]) +nextPath;
                    curTreeNode.valueType = getOriginalType(obj[i]).type
                }
                

                if(obj[i]) {
                    const item = {
                        // value: obj[i] && typeof(obj[i]) === 'object' ? JSON.stringify(obj[i]) : obj[i],
                        value: obj[i] && typeof(obj[i]) === 'object' ? JSON.stringify(obj[i])+nextPath : obj[i]+nextPath,
                        title: i,
                        // disabled: (isBatchWebCrawl || isBatchLlm || isBatchWebSearch) && getType(obj[i]).type != 'array',
                        children: new Array(),
                        valueType: getOriginalType(obj[i]).type
                    };
                    treeResult.push(item)
                    if(typeof(obj[i]) === 'object') {
                        renderTreeItem(obj[i], item.children, curTreeNode, blockType, path+'&&&&'+i, item);
                    }
                }
            }
            
            
        }else if(type === 'string') {

        }
    }
    
    return (
        <div className={flowStyles.inputsWrapper + ' ' +flowStyles.commonWrapper} style={{padding: 0, marginBottom: 0, border: 'none'}}>
            <div className={inputsStyles.paramsInputList}>
                <div className={inputsStyles.paramsListHeader}>
                    <span className={inputsStyles.paramsFisrtKey}>名称</span>
                    <span>变量值</span>
                </div>
                {
                    blockOutputsList.map((inputItem, index) => {
                        // treeValue
                        // 1、长度大于200 需要截断
                        // 2、需要截断path
                        // 3、boolean需要转化string
                        // 普通节点的值和类型
                        let treeValue:any = inputItem.treeValue
                        // console.log(treeValue?.toString(), '----treeValue')
                        treeValue = typeof(treeValue) === 'boolean' ? treeValue?.toString() : treeValue;
                        let resTreeValue = treeValue?.toString();
                        if(resTreeValue?.indexOf('-name') > -1) {
                            resTreeValue = resTreeValue.substring(0,resTreeValue?.indexOf('-name'))
                            // console.log(resTreeValue,222222)
                        }
                        const tipTitle = resTreeValue?.length > 200 ? resTreeValue.substring(0,200)+'...' : resTreeValue?.indexOf('0&&') > -1 ? resTreeValue?.substring(0,resTreeValue?.indexOf('0&&')) : resTreeValue;
                        let tipDom = inputItem.valueType || tipTitle ? (<><div>类型：{inputItem.valueType}</div><div>值：{tipTitle}</div></>) : ''
                        
                        return  <div key={index} className={inputsStyles.inputItem}>
                            {/* result没有加path */}
                            
                            <Tooltip title={tipDom} placement='topLeft'>
                                <Input 
                                    className={inputsStyles.keyItem} 
                                    value={inputItem.key} 
                                    placeholder={'请输入key'}
                                    style={{ width: '27%', marginRight: '2%' }}
                                    onChange={(e)=> {
                                        const value = e.target.value;
                                        blockOutputsList[index].key = value;
                                        let isFindSame = false
                                        blockOutputsList.forEach((item:any,itemIndex) => {
                                            if(item.key === value && itemIndex != index) {
                                                notify.error(value+'命名重复，请重新命名');
                                                isFindSame = true;
                                            }
                                        })
                                        if(blocks[activeBlockId]?.inputs_key_mapping){
                                            blocks[activeBlockId]?.inputs_key_mapping.forEach((item:any) => {
                                                if(item.target === value && !isFindSame) {
                                                    notify.error(value+'命名重复，请重新命名');
                                                    isFindSame = true;
                                                }
                                            })
                                        }

                                        blockOutputsList[index].isInputError = isFindSame;

                                        if(!keyReg.test(value) && value) {
                                            notify.error('key只能包含大小写字母、数字、_或者-，并且以大小写字母开头。')
                                        }

                                        setBlockOutputsList([...blockOutputsList]);
                                        setSelectStamp(new Date().getTime());
                                    }}
                                    onBlur={() => {
                                        setSelectStamp(new Date().getTime());
                                    }}
                                    status={inputItem.key == '0' || (!keyReg.test(inputItem.key) && inputItem.key) || inputItem.isInputError ? 'error' : ''  }
                                    onClick={(e)=> {
                                        if(keyReg.test(inputItem.key) && inputItem.key) {
                                            // if(type === 'code') {
                                            //     copyParamValue("inputs['"+inputItem.key+"']")
                                            // }else {
                                                
                                            // }
                                            copyParamValue("{{"+inputItem.key+"}}")
                                        }
                                        e.currentTarget.focus()
                                    }}
                                />
                            </Tooltip>
                            
                            <TreeSelect
                                style={{ width: '70%' }}
                                dropdownStyle={{ maxHeight: 400, width: 400, overflow: 'auto' }}
                                placeholder="请选择"
                                allowClear
                                treeDefaultExpandAll
                                treeData={treeData}
                                key={reRenderTreeStamp}
                                placement={'bottomRight'}
                                className={'inputs-tree-selector'}
                                value={inputItem.treeValue}
                                onChange={(value:any, label:any, extra:any)=> {
                                    if(extra.allCheckedNodes.length) {
                                        const indexArr = extra.allCheckedNodes[0].pos.split('-').slice(1);
                                        let data = treeData;
                                        const titleArr = new Array();
                                        indexArr.forEach((item:any,i:any) => {
                                            titleArr.push(data[+item].title);
                                            // if(i === indexArr.length - 1 ){
                                            //     blockOutputsList[index].treeValue = data[+item].value
                                            // }
                                            data = data[+item].children;
                                        })

                                        blockOutputsList[index].treeLabel = titleArr.join('/')
                                        blockOutputsList[index].treeValue = value
                                        blockOutputsList[index].valueType = extra.triggerNode.props.valueType
                                        // console.log(blockOutputsList[index].treeLabel,'---',value,'---',extra.triggerNode.props.valueType)
                                    }else {
                                        blockOutputsList[index].treeLabel = ''
                                        blockOutputsList[index].treeValue = ' '
                                        blockOutputsList[index].valueType = ''
                                    }
                                    

                                    if(!blockOutputsList[index].key) {
                                        blockOutputsList[index].key = label[0]
                                    }

                                    let isFindSame = false
                                    blockOutputsList.forEach((item:any, itemIndex) => {
                                        if(item.key === inputItem.key && itemIndex != index) {
                                            notify.error(inputItem.key+'命名重复，请重新命名');
                                            isFindSame = true;
                                        }
                                    })
                                    if(blocks[activeBlockId]?.inputs_key_mapping){
                                        blocks[activeBlockId]?.inputs_key_mapping.forEach((item:any) => {
                                            if(item.target === inputItem.key && !isFindSame) {
                                                notify.error(inputItem.key+'命名重复，请重新命名');
                                                isFindSame = true;
                                            }
                                        })
                                    }

                                    blockOutputsList[index].isInputError = isFindSame;
                                    
                                    
                                    setBlockOutputsList([...blockOutputsList])
                                    setSelectStamp(new Date().getTime());
                                }}
                            />
                            <img src={deleteBtn.src} className={inputsStyles.deleteBtn} onClick={() => {
                                blockOutputsList.splice(index,1)
                                setBlockOutputsList([...blockOutputsList])
                                setSelectStamp(new Date().getTime());
                            }} />
                        </div>
                    })
                }
            </div>
        </div>
    )
})

export default BlockOutputs;