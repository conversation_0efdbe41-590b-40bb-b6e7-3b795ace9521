import { Input, TreeSelect, Select, message, Radio } from 'antd';
import { useEffect, useState } from 'react';

import flowStyles from '@/styles/flow3.0/Flow.module.scss'

import inputsStyles from '@/styles/flow3.0/Inputs.module.scss'

import deleteBtn from '@/images/delete.svg'

export interface IOutputsProps {
    blocks: any;
    activeBlockId: string;
}

const keyReg = /^[a-zA-Z]+[a-zA-Z0-9_-]*$/;

const Outputs:React.FC<IOutputsProps> = (props) => {
    const  { blocks, activeBlockId } = props;

    const [outputsList, setOutputsList] = useState([''])

    const [selectStamp, setSelectStamp] = useState(0)

    const addInputItem = () => {
        setOutputsList([...outputsList, ''])
    }

    useEffect(()=> {
        // result_params 转化 outputsList
        if(activeBlockId && blocks[activeBlockId].result_params.length) {
            setOutputsList(blocks[activeBlockId].result_params);
        }
    },[activeBlockId])

    useEffect(()=>{
        // outputsList 转化 result_params
        if(selectStamp) {
            blocks[activeBlockId].result_params = outputsList;
            // todo 待验证
            // setBlocks(blocks)
        }
    },[selectStamp])
    
    return (
        <div className={flowStyles.inputsWrapper + ' ' +flowStyles.commonWrapper}>

            <div className={inputsStyles.titleWrapper}>
                <span className={inputsStyles.title}>
                    Result（设定后续节点可选择的变量）
                </span>  
            </div>

            <div className={inputsStyles.inputList}>
                {
                    outputsList.map((inputItem, index) => {
                        return  <div key={index} className={inputsStyles.inputItem}>
                            <Input 
                                className={inputsStyles.keyItem} 
                                value={inputItem} 
                                style={{width: '100%'}}
                                placeholder={'请输入result key'}
                                onChange={(e)=> {
                                    const value = e.target.value;
                                    outputsList[index] = e.target.value;
                                    setOutputsList([...outputsList]);
                                    setSelectStamp(new Date().getTime());
                                    if(!keyReg.test(value) && value) {
                                        message.error('key只能包含大小写字母、数字、_或者-，并且以大小写字母开头。')
                                    }
                                }}
                                status={keyReg.test(inputItem) || !inputItem ? '' : 'error'  }
                            />
                            
                            <img src={deleteBtn.src} className={(index === 0 ? inputsStyles.deleteDisabledBtn : '') + ' '+inputsStyles.deleteBtn} onClick={() => {
                                if(index !=0) {
                                    outputsList.splice(index,1)
                                    setOutputsList([...outputsList])
                                }
                            }} />
        
                        </div>
                    })
                }
                
            </div>
            <span className={inputsStyles.addBtn} onClick={addInputItem}> + 添加</span>
        </div>
    )
}


export default Outputs;