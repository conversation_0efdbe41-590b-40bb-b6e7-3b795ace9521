import { useState, useEffect, useRef } from 'react';
import { Modal, Button, Spin } from 'antd';
import publicStyles from '@/styles/PublicModal.module.scss'
import Vditor from "vditor";
import "vditor/dist/index.css";


export default function MarkDownModal(prop:any) {
    const {openMarkDown, setOpenMarkDown, contentValue = '',} = prop;
    // 容器 DOM ref
    const containerRef = useRef(null);
    const [vd, setVd] = useState<Vditor>();
    const [loading, setLoading] = useState(false);
    useEffect(() => {
        setLoading(true);
    }, []);
 
    useEffect(() => {
        if(openMarkDown && containerRef && containerRef.current) {
            try {
                const vditor = new Vditor(containerRef.current, {
                    cache: {
                        enable: false
                    },
                    after: () => {
                        setLoading(false);
                        if (typeof contentValue === 'object' && contentValue !== null) {
                            const formattedJson = JSON.stringify(contentValue, null, 2);
                            vditor.setValue(`\`\`\`json\n${formattedJson}\n\`\`\``);
                        } else {
                            vditor.setValue(contentValue);
                        }
                        setVd(vditor);
                    }
                });
                
            } catch (error) {
                setLoading(false);
            }
        }
    }, [openMarkDown, containerRef]);

    return (
        <>
            <Modal 
                open={openMarkDown}
                width={1000}
                className={publicStyles.markDownModalBg + ' commonModal'}
                onCancel={() => setOpenMarkDown(false)}
                title={'预览'}
                footer={(<>
                    <Button className='defaultButton smallDefaultButton' onClick={() => setOpenMarkDown(false)}>关闭</Button>
                </>)}
            >
                <div ref={containerRef} id='mark-down-editor' style={{minHeight: '300px', maxHeight: '500px'}}></div>
                {loading ? <div className='absolute flex items-center justify-center w-full h-full top-0 left-0 bg-[rgba(255, 255, 255, 0.9)]'><Spin /></div> : <></>}
            </Modal>
        </>
    )
}
