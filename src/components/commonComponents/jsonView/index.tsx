import { useEffect, useState } from "react";
import JsonView from '@uiw/react-json-view';
import styleJson from './index.module.scss'
import copyImg from '@/images/copyCode.svg';
import { copyParamValue } from '@/utils/common';
import MarkDownModal from '@/components/flow3.0/MarkDownModal';
import FlowEyePreview from '@/images/flowEyePreview.svg';
import FlowEyePreviewBgWrite from '@/images/flow3.0/preview.svg';
const customJsonStyle: any = { 
  '--w-rjv-line-color': 'transparent',
  borderRadius: '8px',
  border: '1px solid #E1E7ED',
  minHeight: '20px',
  whiteSpace: 'pre-wrap',
  wordBreak: 'break-all',
  padding: '12px',
  color: '#1D2531',
  fontFamily: "PingFang SC",
  fontSize: '14px',
  fontWeight: 400
};
function JsonViewPage (props: any) {
    const { data = {}, title = 'title', preview = '', style = {}, titleStyle = {}, isShowHeader = true, isBgWrite = false} = props;
    const [customStyle, setCustomStyle] = useState(customJsonStyle);
    const [openMarkDown, setOpenMarkDown] = useState(false);
    const [contentValue, setContentValue] = useState('');
    useEffect(() => {
      setCustomStyle({...customJsonStyle, ...style});
    }, [JSON.stringify(style)]);

    useEffect(() => {
      if (data && preview) {
        let keys = preview.includes('.') ? preview.split('.') : [preview];
        let curValue = data;
        keys.forEach((k: any) => {
          curValue = typeof curValue === 'object' ? (curValue = curValue[k] || '') : curValue;
        });
        setContentValue(curValue);
      }
    }, [data, preview])
    return (
        <div className={"json-view-page" + ' ' + (isBgWrite ? "json-view-page-bgWrite " + styleJson.jsonBgWrite : '')}>
          {isShowHeader ? <>
            <div className={styleJson.jsonViewHeader}>
              <div className={styleJson.jsonViewTitle}>
                <div className="singleLineEllipsis" style={{
                  maxWidth: '200px',
                  ...titleStyle
                }}>{title}</div>
                <div className="flex items-center justify-center cursor-pointer" onClick={() => copyParamValue(JSON.stringify(data))}><img src={copyImg.src} width="12" height="12" /></div>
              </div>
            {preview && contentValue && typeof contentValue === 'string' ? <div className="flex items-center justify-center cursor-pointer" onClick={(e: any) => {
              e.stopPropagation();
              setOpenMarkDown(true);
            }}>
              <img src={FlowEyePreviewBgWrite.src} /></div> : <></>}
            </div>
            {isBgWrite && <div  className="w-full h-[1px] bg-[#E1E7ED] my-2"></div>}
          </>
          : ''}
          <JsonView 
            style={customStyle} 
            value={data} 
            enableClipboard={false} 
            displayDataTypes={false} 
            shortenTextAfterLength={0}
          >
              <JsonView.Quote render={() => <span />}/>
          </JsonView>
          <MarkDownModal
              openMarkDown={openMarkDown}
              setOpenMarkDown={setOpenMarkDown}
              contentValue={contentValue}
          />
        </div>
    );
}
export default JsonViewPage;