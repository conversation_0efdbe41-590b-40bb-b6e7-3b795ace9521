import React, { useEffect, useState } from 'react';
import { Button, Modal } from 'antd';
import { reqImportCreatedFlow } from "@/service/flow3.0"
import FlowImportFile from '@/components/commonComponents/flowImportFile/FlowImportFile';
import { notify } from '@/utils/messageManager';


// LastEditors: 给flow flow2.0组件里面使用，后面不区分版本后迁移到自己对应的文件夹下
export default function FlowDetailImportFile({ template_id, openImportModal, setOpenImportModal, onFinish }: any) {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);



  const handleOk = () => {
    if (!uploadedFile) return notify.warning('请选择您要上传的文件');
    const modal = Modal.confirm({
      content: '文件导入后，原有配置将被全部替换，是否继续？',
      async onOk() {
        modal.destroy();
        setConfirmLoading(true);
        try{
          await handleCheckUpload(uploadedFile, false);
          // 1. 更新成功，关闭弹框
          setOpenImportModal(false);
          setConfirmLoading(false);

          // 2. 重新获取当前页面数据并更新渲染
          onFinish();
        } catch(e) {
          // 失败
          setConfirmLoading(false);
        }

        return Promise.reject(); // 阻止 Modal 自动关闭
      },
      onCancel() {
        console.log('Cancel');
      },
    })

  };

  const handleCancel = () => {
    setOpenImportModal(false);
  };

  const handleCheckUpload = async (file: File, only_check: boolean): Promise<any> => {
    const formData: any = new FormData();
    formData.append('file', file);
    formData.append('template_id', template_id);
    formData.append('only_check', only_check); // only_check true:只是检查 false:既有检查也有导入
    const res = await reqImportCreatedFlow(formData) 
    if (!res) {
      throw new Error(`文件导入失败`)
    }
    return res
  }

  useEffect(() => {
    return () => {
      Modal.destroyAll(); // 通常用于路由监听当中，处理路由前进、后退不能销毁确认对话框的问题
    }
  }, [])

  return <Modal
    title="导入"
    open={openImportModal}
    onOk={handleOk}
    confirmLoading={confirmLoading}
    onCancel={handleCancel}>
    <div>
      <div>说明：导入之后将直接替换当前正在编辑版本</div>
      <FlowImportFile setUploadedFile={setUploadedFile} checkUpload={(file: File) => handleCheckUpload(file, true)} />
    </div>
  </Modal>
}