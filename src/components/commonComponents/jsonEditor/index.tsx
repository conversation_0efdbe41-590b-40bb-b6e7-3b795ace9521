import { initJsonEditor } from '@/components/flow3.0/flowFunc';
import { useEffect, useState } from 'react';
function JsonEditorPage (props: any) {
    const { id, data, isReadOnly = false, onChange } = props;
    const [value, setValue] = useState([]);
    useEffect(() => {
        setValue(data || []);
    }, []);

    useEffect(() => {
        let timer = setTimeout(() => {
            if (document.getElementById('jsoneditor-' + id)) {
                initJsonEditor(value, id, isReadOnly, (value: any) => {
                        try {
                            let res = JSON.parse(value);
                            onChange(res);
                        } catch (e) {
                            return
                        }
                    }, () => {
                })
            }
            clearTimeout(timer);
        }, 10);
    }, [id, value]);

    return (
        <div
            id={'jsoneditor-' + id}
            style={{
                border: "1px solid #D5D7DE",
                borderRadius: 4,
                overflow: 'auto',
                maxHeight: '480px'
            }}
            onBlur={() => {
            }}>
        </div>
    )
}
export default JsonEditorPage;