/*
 * @Author: yh
 * @Date: 2025-05-06 18:05:28
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-05-08 15:05:55
 * @FilePath: \prompt-web\src\components\commonComponents\flowImportFile\FlowImportFile.tsx
 */
import createStyles from "@/styles/ApiCreateModal.module.scss";
import { notify } from "@/utils/messageManager";
import { Upload, UploadProps } from "antd";
import { useEffect, useState } from "react";

type ImportFileComponentProps = {
  setUploadedFile: any,
  checkUpload?: (file: File) => Promise<any>;
};

export default function FlowImportFile({ setUploadedFile, checkUpload }: ImportFileComponentProps) {
  const { Dragger } = Upload;
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);

  const props: UploadProps = {
    name: 'file',
    multiple: false,
    showUploadList: false,
    beforeUpload(file: File) {
      // const isJson = file.type === 'application/json';
      // if (!isJson) {
      //   notify.error('请上传JSON格式文件');
      //   return Upload.LIST_IGNORE; // 阻止文件上传
      // }
      // return true; // 允许文件上传
    },
    customRequest({ file, onSuccess }) {
      const reader = new FileReader();
      reader.onload = async () => {
        const f = file as File;

        try {
          await checkUpload?.(f);
          setUploadedFileName(f.name); // 设置文件名
          setUploadedFile(f); // 设置文件对象
        } catch (e) {
          return;
        }

      };
      if (file instanceof Blob) {
        reader.readAsDataURL(file);
      } else {
        notify.error('文件类型错误');
      }
    },
    onChange(info) {
      const { status } = info.file;
      if (status === 'done') {
        // 处理成功上传的情况
        notify.success('上传成功');

      } else if (status === 'error') {
        notify.error(`${info.file.name} file upload failed.`);
      }
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  const handleReupload = () => {
    setUploadedFileName(null);
  };

  useEffect(() => {
    return () => {
      setUploadedFileName(null)
    }
  }, [])
  return <Dragger  {...props}>
    {uploadedFileName ?
      <div className={createStyles.importContent}>
        <div className={createStyles.importImg}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="20" viewBox="0 0 16 20" fill="#9EA7B8">
            <path d="M11.5556 0.400391L16 4.76403V18.7277C16 19.2097 15.602 19.6004 15.1111 19.6004H0.888889C0.397969 19.6004 0 19.2097 0 18.7277V1.27312C0 0.791124 0.397969 0.400391 0.888889 0.400391H11.5556Z" fill="url(#paint0_linear_12706_82803)" />
            <path d="M0.888889 0.618572H11.4664L15.7818 4.85558V18.7277C15.7818 19.0854 15.4853 19.3822 15.1111 19.3822H0.888889C0.514676 19.3822 0.218182 19.0854 0.218182 18.7277V1.27312C0.218182 0.915379 0.514676 0.618572 0.888889 0.618572Z" stroke="#477FFF" strokeOpacity="0.15" strokeWidth="0.436364" />
            <path d="M11.5547 0.400391V3.8913C11.5547 4.37329 11.9527 4.76403 12.4436 4.76403H15.9991L11.5547 0.400391Z" fill="#ABCAFF" />
            <path fillRule="evenodd" clipRule="evenodd" d="M3.40975 11.7729C3.40975 11.9919 3.58728 12.1694 3.80627 12.1694C4.02525 12.1694 4.20278 11.9919 4.20278 11.7729V10.5784C4.20278 10.4991 4.22472 10.4214 4.26616 10.3539L5.36248 8.56747C5.51483 8.31921 5.3362 8 5.04492 8C4.91062 8 4.78671 8.07228 4.7206 8.18918L3.85116 9.72661C3.84569 9.73628 3.83545 9.74225 3.82434 9.74225C3.81318 9.74225 3.80289 9.73621 3.79745 9.72647L2.94439 8.19938C2.87562 8.07628 2.74564 8 2.60463 8C2.30138 8 2.11464 8.33147 2.27177 8.59083L3.34765 10.3667C3.38828 10.4337 3.40975 10.5106 3.40975 10.589V11.7729ZM6.61082 12.1694C6.39847 12.1694 6.22632 11.9972 6.22632 11.7849V8.42913C6.22632 8.19213 6.41845 8 6.65545 8H7.19019C7.37513 8 7.53927 8.11849 7.5975 8.29402L8.31132 10.446C8.31472 10.4563 8.32432 10.4632 8.33513 10.4632C8.34586 10.4632 8.3554 10.4564 8.35887 10.4462L9.09498 8.29046C9.15429 8.11675 9.31752 8 9.50108 8H10.0026C10.2396 8 10.4318 8.19213 10.4318 8.42913V11.7819C10.4318 11.9959 10.2583 12.1694 10.0443 12.1694C9.83024 12.1694 9.65675 11.9959 9.65675 11.7819V8.79008C9.65675 8.77843 9.64731 8.76899 9.63567 8.76899C9.62671 8.76899 9.61873 8.77466 9.61577 8.78312L8.82609 11.0409C8.76587 11.213 8.60341 11.3283 8.42103 11.3283H8.22571C8.04299 11.3283 7.88031 11.2126 7.82035 11.04L7.03628 8.78315C7.03333 8.77467 7.02535 8.76899 7.01638 8.76899C7.00475 8.76899 6.99531 8.77842 6.99531 8.79006V11.7849C6.99531 11.9972 6.82317 12.1694 6.61082 12.1694ZM12.2047 11.0494C12.2047 11.2864 12.3969 11.4785 12.6339 11.4785H13.5715C13.7623 11.4785 13.917 11.6332 13.917 11.8239C13.917 12.0147 13.7623 12.1694 13.5715 12.1694H11.8469C11.6099 12.1694 11.4177 11.9773 11.4177 11.7403V8.39351C11.4177 8.17618 11.5939 8 11.8112 8C12.0286 8 12.2047 8.17618 12.2047 8.39351V11.0494Z" fill="white" />
            <defs>
              <linearGradient id="paint0_linear_12706_82803" x1="12.6667" y1="1.70948" x2="6.16183" y2="20.0574" gradientUnits="userSpaceOnUse">
                <stop stopColor="#477FFF" />
                <stop offset="1" stopColor="#E0EEFF" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div className={createStyles.importTitle}>
          <span>{uploadedFileName}</span>
          <span onClick={handleReupload} style={{ marginLeft: '10px', color: '#006BFF' }}>重新上传</span>
        </div>
      </div>
      :
      <div className={createStyles.importContent}>
        <svg className={createStyles.linkIcon} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#9EA7B8">
          <path fillRule="evenodd" clipRule="evenodd" d="M12.0002 0.949219L8.10017 5.68606H10.8002V17.6861H13.2002V5.68606H15.9002L12.0002 0.949219ZM2.4 15.4727H0V23.0516H24V15.4727H21.6V20.5253H2.4V15.4727Z" />
        </svg>
        <div className={createStyles.importText} style={{ marginTop: 10 }}>点击或拖拽文件到此区域上传</div>
      </div>
    }
  </Dragger>
}