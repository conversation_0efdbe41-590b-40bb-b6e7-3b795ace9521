/*
 * @Author: yh
 * @Date: 2024-04-17 10:13:00
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-02-27 17:26:43
 * @LastEditTime: 2024-04-23 15:08:20
 * @FilePath: \prompt-web\src\components\agent\answerMarkdown\index.tsx
 */
import React, { useEffect, useState } from "react"
import chatMdStyles from "./index.module.scss";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from "rehype-raw";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import remarkMath from 'remark-math';
import remarkBreaks from 'remark-breaks';
import rehypeMathjax from 'rehype-mathjax';
import { message } from "antd";

const customHighlighiter = (match: any, children: any, style: any, props: any, config: any = {}) => {
  let defaultConfig = {
    isVisibleHeader: true,
    ...config
  }
  const copyToClipboard = (text: any) => {
    // 创建一个临时的 textarea 元素
    const textarea = document.createElement("textarea");
    // 设置 textarea 的值为要复制的文本
    textarea.value = text;
    // 将 textarea 元素添加到文档中
    document.body.appendChild(textarea);
    // 选中 textarea 中的文本
    textarea.select();
    // 执行复制命令
    document.execCommand('copy');
    // 移除临时的 textarea 元素
    document.body.removeChild(textarea);
    message.success('复制成功');
  }
  return <>
    {defaultConfig?.isVisibleHeader ? <div className={chatMdStyles.tools}>
      <div>{match[1]}</div>
      <div style={{ cursor: 'pointer' }} onClick={() => copyToClipboard(children)}>复制</div>
    </div> : ''}
    <SyntaxHighlighter
      {...props}
      wrapLongLines
      children={String(children).replace(/\n$/, '')}
      language={match[1]}
      PreTag="div"
      style={style}
      showLineNumbers={false}
      className={chatMdStyles.highlighterCode}
    />
  </>
}


export default function AnswerMarkDown(props: any = {}) {
  const [style, setStyle] = useState({})
  let { markdown, className, isRunTest = false} = props;

  const markdownContent = (content: any) => {
    try {
      if (typeof JSON.parse(content) === 'string') return JSON.parse(content)
      return content
        // const data = JSON.parse(content).output
        // if(data === undefined) {
        //   return content
        // }
        // return typeof data === 'string' ? data : JSON.stringify(data)
    } catch (error) {
      return content
    }
  }

  useEffect(() => {
    import('react-syntax-highlighter/dist/esm/styles/prism')
      .then(module => {
        setStyle(module.atomDark)
      });
  }, [])

  return (
    <ReactMarkdown
      className={`${chatMdStyles.markdown} ${className || ''} overflow-hidden ` + (isRunTest ? chatMdStyles.runTestMarkdown : '')}
      linkTarget="_blank"
      children={markdownContent(markdown)}
      components={{
        pre({ children, ...props }) {
          return (
            <pre
              style={{
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word',
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
              }}
              {...props}
            >
              {children}
            </pre>
          );
        },
        code({ node, inline, className, children, ...props }) {
          const match = /language-(\w+)/.exec(className || '');
          return !inline && match ? <>
            <SyntaxHighlighter
              children={String(children).replace(/\n$/, '')}
              className={chatMdStyles.highlighterWrapper}
              {...props}
              wrapLongLines
              language={match[1]}
              PreTag="div"
              CodeTag="div"
              style={style}
              showLineNumbers={false}
              renderer={() => customHighlighiter(match, children, style, props, {
                isVisibleHeader: !isRunTest
              })}
            />
          </> : (
            <code
              {...props}
            >
              {children}
            </code>
          );
        },
      }}
      remarkPlugins={[[remarkGfm, { singleTilde: false }], remarkMath, remarkBreaks]}
      rehypePlugins={[rehypeRaw as any]} // 允许解析 HTML
    />
  )
}