import React, {useState, useEffect}from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import 'highlight.js/styles/atom-one-dark.css'; // 备选深色主题

// 黑色主题 Markdown 渲染器
const DarkMarkdownRenderer = ({ content, className='' }) => {
const [codeStyle, setCodeStyle] = useState({})
useEffect(() => {
    import('react-syntax-highlighter/dist/esm/styles/prism')
        .then(module => {
        setCodeStyle(module.atomDark)
        });
    }, [])
  return (
    <div className={"w-full bg-gray-900 text-gray-100 px-4 py-2 rounded-xl shadow-xl max-w-4xl mx-auto min-h-14 " + className} style={{
      maxWidth: '100%',
      overflow: 'auto'
    }}>
      <ReactMarkdown
        children={content}
        remarkPlugins={[remarkGfm]}
        components={{
          code: ({children, className}) => {
            const match = /language-(\w+)/.exec(className || '');
            return <>
             <SyntaxHighlighter
                style={codeStyle}
                language={match[1]}
                PreTag="div"
                showLineNumbers
                className={`scroll-y my-4 rounded-lg overflow-x-auto shadow-lg ${className || ''}`}
                children={String(children).replace(/\n$/, '')}
              >
              </SyntaxHighlighter>
            </>
          },  
          h1: ({ children }) => <h1 className="text-3xl font-bold text-white mb-4 border-b border-gray-700 pb-2">
            {children}
          </h1>,
          h2: ({ children }) => <h2 className="text-2xl font-bold text-white/90 mt-6 mb-3 border-b border-gray-700 pb-2">
            {children}
          </h2>,
          h3: ({ children }) => <h3 className="text-xl font-bold text-white/80 mt-5 mb-2">
            {children}
          </h3>,
          p: ({ children }) => <p className="text-gray-300 leading-relaxed my-3">
            {children}
          </p>,
          ul: ({ children }) => <ul className="list-disc pl-6 text-gray-300 my-3 space-y-1">
            {children}
          </ul>,
          ol: ({ children }) => <ol className="list-decimal pl-6 text-gray-300 my-3 space-y-1">
            {children}
          </ol>,
          li: ({ children }) => <li className="text-gray-300">{children}</li>,
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-500 bg-gray-800/50 pl-4 py-2 my-3 italic">
              <p className="text-gray-300">{children}</p>
            </blockquote>
          ),
          table: ({ children }) => (
            <table className="w-full border-collapse my-4 bg-gray-800/50 rounded-lg overflow-hidden">
              {children}
            </table>
          ),
          thead: ({ children }) => (
            <thead className="bg-gray-700/50">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="divide-y divide-gray-700">
              {children}
            </tbody>
          ),
          tr: ({ children }) => <tr className="hover:bg-gray-700/30 transition-colors">{children}</tr>,
          th: ({ children }) => (
            <th className="px-4 py-2 text-left font-semibold text-white/80 border-b border-gray-600">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-gray-300 border-b border-gray-700/50">
              {children}
            </td>
          ),
          a: ({ children, href }) => (
            <a href={href} className="text-blue-400 hover:text-blue-300 underline transition-colors">
              {children}
            </a>
          )
        }}
      />
    </div>
  );
};

export default DarkMarkdownRenderer;    