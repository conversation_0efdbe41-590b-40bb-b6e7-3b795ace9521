.markdown {
  word-break: break-all;
  // white-space: pre-wrap; 改成使用插件处理，兼容table 、|--|--|这种格式输出问题
  // p, span {
  // }

  .tools {
    color: #b4b4b4;
    background: #2f2f2f;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 14px;
  }

  .highlighterWrapper {
    padding: 0 !important;
  }

  .highlighterCode {
    margin: 0 !important;
    // padding: 0 !important;
  }

  table {
    margin: 16px 0;
    border-collapse: collapse;
    border: 1px solid #e3ecf9;
    font-size: 14px;
    line-height: 22px;
    background: #fff;
  }

  p,
  h2,
  h3,
  h1 {
    margin: 0;
  }

  th,
  td {
    padding: 6px 12px;
    border: 1px solid #e3ecf9;
  }


  ol,
  ul {
    list-style-position: inside; /* 设置序号位于列表项内部 */
    margin-left: 0; /* 移除默认的左侧外边距 */
    padding-left: 30px; /* 添加左侧内边距，用于显示序号 */
    white-space: normal;
  }

  ol li {
    list-style: decimal !important;
  }

  ul li {
    list-style: disc !important;
  }


  img {
    display: block !important;
    max-width: 100%;
  }

  a {
    color: blue;
    text-decoration: underline;
  }
}
.runTestMarkdown {
  // border-radius: 8px;
  // padding: 16px;
  // background-color: #000000 !important;
  // color: rgba(255,255,255, 0.8) !important;
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  .highlighterCode {
    // background-color: #000000 !important;
    // color: rgba(255,255,255, 0.8) !important;
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      code {
          white-space: pre !important;
          word-wrap: normal !important;
          word-break: normal !important;
          overflow-wrap: normal !important;
      }
  }
}