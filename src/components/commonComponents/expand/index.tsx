import React, { useState } from 'react';

/**
 * 可折叠面板组件
 * @param {Object} props
 * @param {ReactNode} props.header - 自定义头部内容
 * @param {ReactNode} props.children - 面板内容
 * @param {boolean} [props.defaultExpanded=true] - 默认是否展开
 * @param {string} [props.containerClass] - 容器类名
 * @param {string} [props.headerClass] - 头部类名
 * @param {string} [props.contentClass] - 内容类名
 * @param {string} [props.arrowClass] - 箭头类名
 * @param {string} [props.expandedArrowSrc] - 展开状态的箭头图标
 * @param {string} [props.collapsedArrowSrc] - 折叠状态的箭头图标
 */
import styles from './index.module.scss';
import ImgExpandedArrowDown from '@/images/flow3.0/arrowDown.svg';
import ImgExpandedArrowUp from '@/images/flow3.0/arrowLeft.svg'
import Running from "@/images/newFlow/icon-status-running.svg";
const CollapsiblePanel = ({
  header=<div>标题</div>,
  children=<></>,
  defaultExpanded = true,
  containerClass='',
  headerClass='',
  contentClass='',
  arrowClass='',
  arrowBoxClass='',
  expandedArrowSrc = ImgExpandedArrowDown.src,
  collapsedArrowSrc = ImgExpandedArrowUp.src,
  arrowLeft=<></>,
  footer=<></>,
  onTogglePanel=() => {},
  isLoading=false
}: any) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const togglePanel = () => {
    setIsExpanded(!isExpanded);
    onTogglePanel(!isExpanded);
  };

  return (
    <div className={styles.expandContainer + ' ' + containerClass}>
      <div className={styles.expandHeader + ' ' + headerClass}>
        {header}
        <div className={styles.expandArrowBox + ' ' + arrowBoxClass}>
            {arrowLeft}
            {
              isLoading ? <div className={styles.expandArrow}><img src={Running.src} className={styles.rotate} /></div> :
              <div className={styles.expandArrow + ' ' + arrowClass} onClick={togglePanel}>
                  <img 
                      src={isExpanded ? expandedArrowSrc : collapsedArrowSrc} 
                      alt={isExpanded ? "收起" : "展开"}
                  />
              </div>
            }
        </div>
      </div>
      
      {isExpanded && (
        <div className={styles.expandContent + ' ' + contentClass}>
          {children}
        </div>
      )}
      {footer}
    </div>
  );
};

// 添加 propTypes 验证
// CollapsiblePanel.propTypes = {
//   header: PropTypes.node.isRequired,
//   children: PropTypes.node.isRequired,
//   defaultExpanded: PropTypes.bool,
//   containerClass: PropTypes.string,
//   headerClass: PropTypes.string,
//   contentClass: PropTypes.string,
//   arrowClass: PropTypes.string,
//   expandedArrowSrc: PropTypes.string,
//   collapsedArrowSrc: PropTypes.string
// };

export default CollapsiblePanel;