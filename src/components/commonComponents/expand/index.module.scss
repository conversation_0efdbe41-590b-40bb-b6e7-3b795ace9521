.expandContainer {
    display: flex;
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    border-radius: 12px;
    border: 1px solid #E1E7ED;
    background: #F7F9FA;
}
.expandHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;

    color: #1D2531;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px; 
}
.expandArrowBox {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
}
.expandArrow {
    justify-content: flex-end;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        width: 18px;
        height: 18px;
    }
}
.expandArrow img {
  transition: transform 0.3s ease;
}
.expandContent {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

.rotate {
    animation: circle 2s linear infinite;
}
@keyframes circle {
  0% {
     transform: rotate(0deg);
  }
  100% {
     transform: rotate(360deg);
  }
}
