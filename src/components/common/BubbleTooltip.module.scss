.bubbleTooltip {
  position: absolute;
  z-index: 1000;
  display: flex;
  align-items: center;
  pointer-events: none;

  &.left {
    flex-direction: row;

    .bubbleContent {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -7px;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 7px 0 7px 7px;
        border-color: transparent transparent transparent #4F7EFF;
      }
    }

    .bubbleDot {
      margin-left: 12px;
    }
  }

  &.right {
    flex-direction: row-reverse;

    .bubbleContent {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: -7px;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 7px 7px 7px 0;
        border-color: transparent #4F7EFF transparent transparent;
      }
    }

    .bubbleDot {
      margin-right: 12px;
    }
  }
}

.bubbleContent {
  background: #4F7EFF;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 8px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  max-width: 260px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  position: relative;
  box-shadow: 0 2px 8px rgba(79, 126, 255, 0.3);
}



.bubbleDot {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

/* 动画效果 */
.bubbleTooltip {
  animation: bubbleFadeIn 0.3s ease-out;
}

@keyframes bubbleFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// /* 响应式设计 */
// @media (max-width: 768px) {
//   .bubbleContent {
//     font-size: 12px;
//     padding: 6px 10px;
//     max-width: 200px;
//   }
  
//   .bubbleArrow {
//     border-width: 5px;
//   }
// }

// @media (max-width: 480px) {
//   .bubbleContent {
//     font-size: 11px;
//     padding: 5px 8px;
//     max-width: 160px;
//   }
  
//   .bubbleArrow {
//     border-width: 4px;
//   }
// }
