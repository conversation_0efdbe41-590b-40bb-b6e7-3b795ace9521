import React, { useState, useEffect } from 'react';
import { Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import BubbleTooltip from './BubbleTooltip';
import styles from './ImageViewerModal.module.scss';

interface BubbleData {
  content: string;
  style: React.CSSProperties;
  direction: 'left' | 'right';
  showDot?: boolean;
}

interface ImageBubbleConfig {
  imageKey: number;
  bubbles: BubbleData[];
}

interface ImageViewerModalProps {
  visible: boolean;
  onClose: () => void;
  images: string[];
  initialIndex?: number;
  bubbleConfig?: ImageBubbleConfig[];
}

const ImageViewerModal: React.FC<ImageViewerModalProps> = ({
  visible,
  onClose,
  images,
  initialIndex = 0,
  bubbleConfig
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // Mock数据 - 如果没有传入bubbleConfig则使用默认数据
  const defaultBubbleConfig: ImageBubbleConfig[] = [
    {
      imageKey: 0,
      bubbles: [
        {
          content: "可以切换不同模型评估效果",
          style: {
            left: '10%',
            top: '20%'
          },
          direction: "left",
          showDot: true
        },
        {
          content: "这里可以查看详细信息",
          style: {
            right: '15%',
            top: '40%'
          },
          direction: "right",
          showDot: true
        },
        {
          content: "点击这里进行设置",
          style: {
            left: '20%',
            bottom: '25%'
          },
          direction: "left",
          showDot: true
        }
      ]
    },
    {
      imageKey: 1,
      bubbles: [
        {
          content: "第二张图片的功能介绍",
          style: {
            left: '25%',
            top: '30%'
          },
          direction: "left",
          showDot: true
        },
        {
          content: "右侧的操作按钮",
          style: {
            right: '20%',
            bottom: '20%'
          },
          direction: "right",
          showDot: false
        }
      ]
    }
  ];

  const activeBubbleConfig = bubbleConfig || defaultBubbleConfig;

  useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex, visible]);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (!visible) return;

    switch (e.key) {
      case 'ArrowLeft':
        handlePrevious();
        break;
      case 'ArrowRight':
        handleNext();
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  useEffect(() => {
    if (visible) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, currentIndex]);

  if (!visible || !images.length) return null;

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      closable={false}
      centered
      width="auto"
      className={styles.imageViewerModal}
      maskClosable={true}

      styles={{
        body: { padding: 0 },
        mask: { backgroundColor: 'rgba(0, 0, 0, 0.8)' },
        content: {
          background: 'transparent',
          boxShadow: 'none',
          borderRadius: 0
        }
      }}
      transitionName=""
      maskTransitionName=""
    >
      <div className={styles.modalContent}>
        {/* 关闭按钮 */}
        <div className={styles.closeButton} onClick={onClose}>
          <CloseOutlined />
        </div>

        {/* 页码指示器 */}
        {images.length > 1 && (
          <div className={styles.pageIndicator}>
            {currentIndex + 1} / {images.length}
          </div>
        )}

        {/* 图片容器 */}
        <div className={styles.imageContainer}>
          <img
            src={images[currentIndex]}
            alt={`Tutorial ${currentIndex + 1}`}
            className={styles.image}
            onError={(e) => {
              console.error('Image load error:', e);
            }}
          />

          {/* 气泡提示 */}
          {activeBubbleConfig
            .filter(config => config.imageKey === currentIndex)
            .map((config, configIndex) =>
              config.bubbles.map((bubble, bubbleIndex) => (
                <BubbleTooltip
                  key={`${configIndex}-${bubbleIndex}`}
                  content={bubble.content}
                  direction={bubble.direction}
                  showDot={bubble.showDot}
                  style={bubble.style}
                />
              ))
            )}
        </div>

        {/* 左箭头 */}
        {images.length > 1 && (
          <div className={styles.arrowLeft} onClick={handlePrevious}>
            <img
              src="https://s1.ssl.qhres2.com/static/63fd4a8b57de0863.svg"
              alt="Previous"
              className={styles.arrowIcon}
              style={{ transform: 'rotate(180deg)' }}
            />
          </div>
        )}

        {/* 右箭头 */}
        {images.length > 1 && (
          <div className={styles.arrowRight} onClick={handleNext}>
            <img
              src="https://s1.ssl.qhres2.com/static/63fd4a8b57de0863.svg"
              alt="Next"
              className={styles.arrowIcon}
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ImageViewerModal;
