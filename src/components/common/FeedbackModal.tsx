import { useState } from "react";
import { Modal, Checkbox, Button, message, Form,Input } from "antd";
const { TextArea } = Input;

import publicStyles from "@/styles/PublicModal.module.scss";
import { reqDeploy, reqReDeploy } from "@/service/common";
import { reqSubmitFeedbackApi } from "@/service/background/feedback";
import Router from "next/router";

export default function FeedbackModal(prop: any) {
  const { title, cancelHandle, okHandel, openModal, setOpenModal } = prop;
  const [isTermChecked, setIsTermChecked] = useState(true);
  const [form] = Form.useForm();

  const onFinish = () => {  
    form.validateFields().then(async (values) => {
        console.log(values);
        try {
          const res = await reqSubmitFeedbackApi({title:values.title,opinion:values.message,contact_information:values.tel});
          if(res){
            message.success("提交成功");
            setOpenModal(false);
          }
          form.resetFields();
        } catch (error) {
          message.error("提交失败");
        }
    });
  }
  return (
    <>
      <Modal
        open={openModal}
        style={{ width: 480 }}
        className={"commonModal"}
        onCancel={() => {setOpenModal(false);form.resetFields();}}
        title={title ? title : "感谢您做出评价"}
        footer={
          <>
            <Button onClick={() => {setOpenModal(false);form.resetFields();}}>取消</Button>
            <Button
              type="primary"
              onClick={
                okHandel
                  ? okHandel
                  : () => {
                      onFinish();
                 }
              }
            >
              确认
            </Button>
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
          onFinish={onFinish}
          style={{ width: "100%", padding: "0 8px"}}
        >
          <Form.Item required label="标题" name="title" rules={[{ required: true, message: '请输入标题' }]} >
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item required label="建议&意见" name="message" rules={[{ required: true, message: '请输入您的建议意见' }]}>
          <TextArea rows={4} placeholder="请输入您的建议意见" maxLength={500} />
          </Form.Item>
          <Form.Item  label="联系方式" name="tel">
            <Input placeholder="请输入联系方式" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
