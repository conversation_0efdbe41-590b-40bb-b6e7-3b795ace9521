.tutorialGuide {
  position: fixed;
  bottom: 24px;
  left: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  z-index: 1000;
  // transition: all 0.3s ease;
  user-select: none;
  border: 1px solid rgba(0, 0, 0, 0.04);
  border-radius:100%;
  overflow:hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: #fafafa;
    // animation: buttonPulse 0.6s ease-in-out;
  }

  &:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.06);
  }
}

// 按钮脉冲动画
@keyframes buttonPulse {
  0% {
    transform: translateY(-2px) scale(1);
  }
  50% {
    transform: translateY(-3px) scale(1.02);
  }
  100% {
    transform: translateY(-2px) scale(1);
  }
}

.iconContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  // transition: all 0.3s ease;

  .tutorialGuide:hover & {
    transform: scale(1.05);
  }
}

.icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  // transition: all 0.3s ease;

  .tutorialGuide:hover & {
    transform: scale(1.1);
  }
}

// // 响应式设计
// @media (max-width: 768px) {
//   .tutorialGuide {
//     bottom: 20px;
//     left: 20px;
//     width: 44px;
//     height: 44px;
//   }

//   .icon {
//     width: 20px;
//     height: 20px;
//   }
// }

// @media (max-width: 480px) {
//   .tutorialGuide {
//     bottom: 16px;
//     left: 16px;
//     width: 40px;
//     height: 40px;
//   }

//   .icon {
//     width: 18px;
//     height: 18px;
//   }
// }

// // 深色模式支持（如果需要）
// @media (prefers-color-scheme: dark) {
//   .tutorialGuide {
//     background: #2a2a2a;
//     border-color: rgba(255, 255, 255, 0.1);

//     &:hover {
//       background: #333333;
//     }
//   }
// }
