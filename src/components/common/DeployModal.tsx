import { useState } from 'react';
import { Modal, Checkbox, Button, message } from 'antd';

import publicStyles from '@/styles/PublicModal.module.scss'
import { reqDeploy, reqReDeploy } from '@/service/common';

import Router from 'next/router'


export default function DeployModal(prop:any) {
    const {openDeploy, setOpenDeploy, chainId, deployId, deployType, setIsDeploySuccess, isNotJump} = prop;
    const [isTermChecked, setIsTermChecked] = useState(true);

    return (
        <>
            <Modal 
                open={openDeploy}
                style={{width: 480}}
                className={'commonModal'}
                onCancel={() => setOpenDeploy(false)}
                title={'部署到Agent'}
                footer={(<>
                    <Button onClick={() => setOpenDeploy(false)}>取消</Button>
                    <Button type='primary' disabled={!isTermChecked} onClick={async() => {
                        let res;
                        if(deployType === 'deploy') {
                            res = await reqDeploy({
                                chain_no: chainId
                            })
                        }else {
                            res = await reqReDeploy({
                                id: deployId
                            })
                            if(res && res.id && setIsDeploySuccess) {
                                setIsDeploySuccess(new Date().getTime())
                            }
                        }
                        if(res && res.id && !isNotJump) {
                            setOpenDeploy(false)
                            message.success('部署成功');
                            Router.push('/operationDetail?id='+chainId+'&deployId='+res.id)
                        } 
                        
                    }}>确定</Button>
                </>)}
            >
                <div className={publicStyles.deployTermsTitle}>
                    部署成功后，若要修改文件内容，请先取消部署
                </div>
                <div className={publicStyles.deployTerms}>
                    <Checkbox checked={isTermChecked} onChange={() => {setIsTermChecked(!isTermChecked)}}>点击发布表示您同意我们的</Checkbox> <a className={publicStyles.fuWuA} href='https://easydoc.qihoo.net/doc?project=e876b0cca6462cde17f6694ba6b3b387&doc=1c3e06420ff9da363de17dfd93df503e&config=toc' target='_blank'>《服务条款》</a>
                </div>
            </Modal>
        </>
    )
}
