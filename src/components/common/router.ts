
import menuAgent from "@/images/menu/menu-agent.svg";
import menuFlow from "@/images/menu/menu-flow.svg";
import menuKnowledge from "@/images/menu/menu-kno.svg";
import menuProject from "@/images/menu/menu-proj.svg";
import menuPrompt from "@/images/menu/menu-tis.svg";
import menuTemplate from "@/images/menu/menu-main.svg";
import menuTool from "@/images/menu/menu-api.svg";
import menuTemplateApi from "@/images/svgr/template_api.svg";
import menuTemplateAgent from "@/images/svgr/template_agent.svg";
import menuAgentHelper from '@/images/menu/menu_agentHelper.svg'

import menuAgentSelect from "@/images/menu/menu-agent-select.svg";
import menuFlowSelect from "@/images/menu/menu-flow-select.svg";
import menuKnowledgeSelect from "@/images/menu/menu-kno-select.svg";
import menuProjectSelect from "@/images/menu/menu-proj-select.svg";
import menuPromptSelect from "@/images/menu/menu-tis-select.svg";
import menuTemplateSelect from "@/images/menu/menu-main-select.svg";
import menuToolSelect from "@/images/menu/menu-api-select.svg";

import backgroundProject from "@/images/menu/background_project.svg";
import backgroundPower from "@/images/menu/background_power.svg";
import backgroundOperation from "@/images/menu/background_operation.svg";
import backgroundAccount from "@/images/menu/background_account.svg";
import backgroundFeedback from "@/images/menu/background_feedback.svg";
import backgroundRelease from "@/images/menu/background_release.svg";
import backgroundAgentList from "@/images/menu/background_agent_list.svg";
import backgroundPlugin from "@/images/menu/background_plugin.svg";
import backgroundUserManage from "@/images/menu/background_userManage.svg"


import backgroundProjectSelect from "@/images/menu/background_project_select.svg";
import backgroundPowerSelect from "@/images/menu/background_power_select.svg";
import backgroundOperationSelect from "@/images/menu/background_operation_select.svg";
import backgroundAccountSelect from "@/images/menu/background_account_select.svg";
import backgroundFeedbackSelect from "@/images/menu/background_feedback_select.svg";
import backgroundReleaseSelect from "@/images/menu/background_release_select.svg";
import backgroundAgentListSelect from "@/images/menu/background_agent_list_select.svg";
import backgroundPluginSelect from "@/images/menu/background_plugin_select.svg";
import backgroundUserManageSelect from "@/images/menu/background_userManage_select.svg"



export const templateRouterList = [
  {
    name: "template-agents",
    path: "/templateList?templateTab=agents&",
    label: "智能体商店",
    icon: menuTemplateAgent,
    selectIcon: menuTemplateSelect,
  },
  {
    name: "template-api",
    path: "/templateList?templateTab=api&",
    label: "插件商店",
    icon: menuTemplateApi,
    selectIcon: menuTemplateSelect,
  }
]
export const routerList = [
    // {
    //   name: "agents",
    //   path: "/agentList",
    //   label: "智能体",
    //   icon: menuAgent,
    //   selectIcon: menuAgentSelect,
    //   children:[
    //     {
    //       name: "agentDetail",
    //       path: "/agentDetail",
    //       label: "智能体详情",
    //       icon: '',
    //       selectIcon: '',
    //     },
    //   ]
    // },
    {
      name: "flow",
      path: "/flowList",
      label: "多专家智能体",
      icon: menuFlow,
      selectIcon: menuFlowSelect,
      children:[
        {
          name: "flowDetail",
          path: "/flowDetailV3",
          label: "插件详情",
          icon: '',
          selectIcon: '',
        },
      ]
    },
    // {
    //   name: "tool",
    //   path: "/toolList",
    //   label: "插件",
    //   icon: menuTool,
    //   selectIcon: menuToolSelect,
    //   children:[
    //     {
    //       name: "apiDetail",
    //       path: "/apiDetail",
    //       label: "插件详情",
    //       icon: '',
    //       selectIcon: '',
    //     }
    //   ]
    // },
    // {
    //   name: "knowledge",
    //   path: "/knowledge",
    //   label: "知识库",
    //   icon: menuKnowledge,
    //   selectIcon: menuKnowledgeSelect,
    //   children:[
    //     {
    //       name: "knowledgeDataDetail",
    //       path: "/knowledge/data/detail",
    //       label: "知识库数据详情",
    //       icon: '',
    //       selectIcon: '',
    //     },
    //     {
    //       name: "knowledgeChatDetail",
    //       path: "/knowledge/chat/detail",
    //       label: "知识库问答详情",
    //       icon: '',
    //       selectIcon: '',
    //     },
    //     {
    //       name: "knowledgeDataFileDetail",
    //       path: "/knowledge/data/fileDetail",
    //       label: "知识库文档详情",
    //       icon: '',
    //       selectIcon: '',
    //     },
    //   ]
    // },
    // {
    //   name: "prompt",
    //   path: "/promptList",
    //   label: "提示词",
    //   icon: menuPrompt,
    //   selectIcon: menuPromptSelect,
    //   children:[
    //     {
    //       name: "imagePromptDetail",
    //       path: "/imagePromptDetail",
    //       label: "文生图详情",
    //       icon: '',
    //       selectIcon: '',
    //     },
    //     {
    //       name: "textPromptDetail",
    //       path: "/textPromptDetail",
    //       label: "文生文详情",
    //       icon: '',
    //       selectIcon: '',
    //     },
    //   ]
    // },
    {
      name: "project",
      path: "/projectList",
      label: "设置",
      icon: menuProject,
      selectIcon: menuProjectSelect,
      children:[
        {
          name: "monitor",
          path: "/monitor",
          label: "服务监控",
          icon: '',
          selectIcon: '',
        },
        {
          name: "operationDetail",
          path: "/operationDetail",
          label: "部署详情",
          icon: '',
          selectIcon: '',
        },
        {
          name: "operationDetailV3",
          path: "/operationDetailV3",
          label: "部署详情",
          icon: '',
          selectIcon: '',
        },
        {
          name: "monitorDetail",
          path: "/monitorDetail",
          label: "详情",
          icon: '',
          selectIcon: '',

        }
      ]
    },
    // {
    //   name: "template",
    //   path: "/templateList",
    //   label: "发现",
    //   icon: menuTemplate,
    //   selectIcon: menuTemplateSelect,
    // },
];

// 系统超级管理员
  export const  systemSuperManagerRouters = [
    {
      name: "backgroundProject",
      path: "/background/projectList",
      label: "项目管理",
      icon: backgroundProject,
      selectIcon: backgroundProjectSelect,
    },

    {
      name: "backgroundExamine",
      path: "/background/examineList",
      label: "市场审核",
      icon: backgroundOperation,
      selectIcon: backgroundOperationSelect
    },
    {
      name: "backgroundPower",
      path: "/background/powerList",
      label: "权限管理",
      icon: backgroundPower,
      selectIcon: backgroundPowerSelect,
    },
    {
      name: "userList",
      path: "/background/userList",
      label: "用户管理_外网",
      icon: backgroundUserManage,
      selectIcon: backgroundUserManageSelect,
    },
    // {
    //   name: "backgroundOperation",
    //   path: "/background/operationList",
    //   label: "账户审核",
    //   icon: backgroundAccount,
    //   selectIcon: backgroundAccountSelect,
    // },
    {
      name: "backgroundFeedback",
      path: "/background/feedbackList",
      label: "反馈信息",
      icon: backgroundFeedback,
      selectIcon: backgroundFeedbackSelect,
    },
    {
      name: "backgroundRelease",
      path: "/background/channelList",
      label: "发布渠道",
      icon: backgroundRelease,
      selectIcon: backgroundReleaseSelect,
      children:[
        {
          name: "channelClassify",
          path: "/background/channelClassify",
          label: "分类管理",
          icon: '',
          selectIcon: '',
        },
        {
          name: "agentPublishChannel",
          path: "/background/agentBgPublishChannel",
          label: "渠道数据查看",
          icon: '',
          selectIcon: '',
        },
      ]
    },
    {
      name: "backgroundAgentList",
      path: "/background/agentList",
      label: "Agent发布管理",
      icon: backgroundAgentList,
      selectIcon: backgroundAgentListSelect,
    },
    {
      name: "backgroundPlugin",
      path: "/background/pluginList",
      label: "插件市场管理",
      icon: backgroundPlugin,
      selectIcon: backgroundPluginSelect,
    }
  ]
// 系统管理员、普通用户
  export const systemManagerRouters = [
    {
      name: "backgroundProject",
      path: "/background/projectList",
      label: "项目管理",
      icon: backgroundProject,
      selectIcon: backgroundProjectSelect,
    },

    {
      name: "backgroundExamine",
      path: "/background/examineList",
      label: "市场审核",
      icon: backgroundOperation,
      selectIcon: backgroundOperationSelect
    },
    {
      name: "backgroundFeedback",
      path: "/background/feedbackList",
      label: "反馈信息",
      icon: backgroundFeedback,
      selectIcon: backgroundFeedbackSelect,
    },
    {
      name: "backgroundRelease",
      path: "/background/channelList",
      label: "发布渠道",
      icon: backgroundRelease,
      selectIcon: backgroundReleaseSelect,
      children:[
        {
          name: "channelClassify",
          path: "/background/channelClassify",
          label: "分类管理",
          icon: '',
          selectIcon: '',
        },
        {
          name: "agentPublishChannel",
          path: "/background/agentBgPublishChannel",
          label: "渠道数据查看",
          icon: '',
          selectIcon: '',
        },
      ]
    },
    {
      name: "backgroundAgentList",
      path: "/background/agentList",
      label: "Agent发布管理",
      icon: backgroundAgentList,
      selectIcon: backgroundAgentListSelect,
    }
  ]

  // 系统超级管理员，如果用户来源不是agent，则不展示发布渠道和Agent发布管理
  export const  IsNotAgentsystemSuperManagerRouters = [
    {
      name: "backgroundProject",
      path: "/background/projectList",
      label: "项目管理",
      icon: backgroundProject,
      selectIcon: backgroundProjectSelect,
    },

    {
      name: "backgroundExamine",
      path: "/background/examineList",
      label: "市场审核",
      icon: backgroundOperation,
      selectIcon: backgroundOperationSelect
    },
    {
      name: "backgroundPower",
      path: "/background/powerList",
      label: "权限管理",
      icon: backgroundPower,
      selectIcon: backgroundPowerSelect,
    },
    {
      name: "userList",
      path: "/background/userList",
      label: "用户管理_外网",
      icon: backgroundUserManage,
      selectIcon: backgroundUserManageSelect,
    },
    // {
    //   name: "backgroundOperation",
    //   path: "/background/operationList",
    //   label: "账户审核",
    //   icon: backgroundAccount,
    //   selectIcon: backgroundAccountSelect,
    // },
    {
      name: "backgroundFeedback",
      path: "/background/feedbackList",
      label: "反馈信息",
      icon: backgroundFeedback,
      selectIcon: backgroundFeedbackSelect,
    },
  ]
  // 系统管理员、普通用户，如果用户来源不是agent，则不展示发布渠道和Agent发布管理
  export const IsNotAgentsystemManagerRouters = [
    {
      name: "backgroundProject",
      path: "/background/projectList",
      label: "项目管理",
      icon: backgroundProject,
      selectIcon: backgroundProjectSelect,
    },

    {
      name: "backgroundExamine",
      path: "/background/examineList",
      label: "市场审核",
      icon: backgroundOperation,
      selectIcon: backgroundOperationSelect
    },
    {
      name: "backgroundFeedback",
      path: "/background/feedbackList",
      label: "反馈信息",
      icon: backgroundFeedback,
      selectIcon: backgroundFeedbackSelect,
    },
  ]