import { useState, useEffect } from "react";
import { Modal, Form, Button, Select, Input, message } from "antd";

import {
    reqCreateAuth,
    reqPluginAuthList
} from "@/service/auth";
import {
    reqApiDetailV2
} from "@/service/api";

import authStyle from "@/styles/AuthConfig.module.scss";
import warnBlue from '@/images/warnBlue.svg';

export default function AuthConfig(props: any) {
    const { open, setOpen, curPlugin, handleAuth, authId } = props;
    const initForm = {
        apiName: '',
        authName: '',
        authMethod: -1,
        appId: '',
        appSecret: '',
        dbUsername: '',
        dbPassword: '',
        dbIp: '',
        dbPort: '',
        dbDatabase: '',
        keyName: '',
        keyValue: ''
    }
    const [formData, setFormData] = useState(initForm);
    const [options, setOptions] = useState(new Array<any>());
    const [authType, setAuthType] = useState(0);

    const verifyInput = () => {
        if (!formData.authName.length) {
            message.warning("请输入授权配置名称");
            return false;
        }

        if (authType === 4) {
            if (!formData.dbUsername.length) {
                message.warning("请输入账号");
                return false;
            }

            if (!formData.dbPassword.length) {
                message.warning("请输入密码");
                return false;
            }

            if (!formData.dbIp.length) {
                message.warning("请输入IP地址");
                return false;
            }

            if (!formData.dbPort.length && !formData.dbPort.match(/^[0-9]+$/)) {
                message.warning("请输入端口号");
                return false;
            }

            if (!formData.dbDatabase.length) {
                message.warning("请输入数据库名称");
                return false;
            }

            return true;
        } else if (authType === 2) {
            if (!formData.appId.length) {
                message.warning("请输入AppID");
                return false;
            }

            if (!formData.appSecret.length) {
                message.warning("请输入AppSecret");
                return false;
            }

            return true;
        } else if (authType === 3) {
            const reg = new RegExp("[\\u4E00-\\u9FFF]", "g");

            if (!formData.keyName || formData.keyName.replace(' ', '') === '') {
                message.warning('请填写name');
                return false;
            } 
            
            if (!formData.keyValue || formData.keyValue.replace(' ', '') === '') {
                message.warning('请填写value');
                return false;
            } 
            
            if (formData.keyName.length > 50) {
                message.warning('name 最多50个字符');
                return false;
            } 
            
            if (formData.keyValue.length > 1000) {
                message.warning('value 最多1000个字符');
                return false;
            }

            if (reg.test(formData.keyName)) {
                message.warning('name 不能包含中文');
                return false;
            }

            if (reg.test(formData.keyValue)) {
                message.warning('value 不能包含中文');
                return false; 
            }

            return true;
        }
    }

    const onFinish = () => {
        if (formData.authMethod === -1) {
            if (!verifyInput()) return;

            let authInfo;
            if (authType === 4) {
                authInfo = {
                    db_username: formData.dbUsername,
                    db_password: formData.dbPassword,
                    db_ip: formData.dbIp,
                    db_port: formData.dbPort,
                    db_database: formData.dbDatabase
                }
            } else if (authType === 2) {
                authInfo = {
                    app_id: formData.appId,
                    secret: formData.appSecret
                }
            } else if (authType === 3) {
                authInfo = {
                    key_name: formData.keyName,
                    key_value: formData.keyValue
                }
            }
            
            reqCreateAuth({
                api_id: curPlugin.id,
                auth_name: formData.authName,
                auth_type: authType,
                auth_info: JSON.stringify(authInfo)
            }).then((res) => {
                if (res) {
                    handleAuth(res.auth_id);
                }
            })
        } else {
            handleAuth(formData.authMethod)
        }

        setOpen(false);
    }

    const handleCancel = () => {
        setOpen(false);
        setFormData(initForm);
    }

    useEffect(() => {
        if (open) {
            if (curPlugin.auth_type) {
                setAuthType(curPlugin.auth_type)
            } else {
                reqApiDetailV2({
                    api_id: curPlugin.id
                }).then((res) => {
                    setAuthType(res.auth_type)
                })
            }
 
            reqPluginAuthList({
                api_id: curPlugin.id,
                page: 1,
                // wlr-todo 暂用 1000 条搜索所有插件授权列表
                page_size: 1000,
                sort_time: 0
            }).then((res) => {
                if (res) {
                    const optionList = res.map((item: any) => ({
                        label: item.auth_name,
                        value: item.id
                    }))
                    const isAuthId = !optionList.filter((option: any) => option.value === authId).length;
                    setFormData({
                        ...initForm,
                        apiName: curPlugin.name,
                        authMethod: isAuthId ? -1 : authId
                    })

                    setOptions(optionList);
                }
            })
        }
    }, [open])

    return (
        <Modal
            open={open}
            style={{ width: 520, padding: 0 }}
            className={"commonModal"}
            wrapClassName={authStyle.authModal}
            onCancel={handleCancel}
            footer={
                <>
                    <Button onClick={handleCancel}>取消</Button>
                    <Button
                        type="primary"
                        onClick={
                            () => {
                                onFinish()
                            }
                        }
                    >
                        确认
                    </Button>
                </>
            }
        >
            <div className={authStyle.authHeader}>授权配置</div>
            <div className={authStyle.authWarn}>
                <img src={warnBlue.src} alt="" />
                授权信息项目组内成员均可使用，请谨慎添加授权
            </div>
            <div className={authStyle.authForm}>
                <Form
                    layout="vertical"
                >
                    <Form.Item
                        label="插件"
                        required
                    >
                        <Input
                            value={formData.apiName}
                            disabled
                            placeholder='请输入插件名称'
                            onChange={(event: any) => {
                                setFormData({
                                    ...formData,
                                    authName: event.target.value
                                })
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="选择授权"
                        required
                    >
                        <Select
                            value={formData.authMethod}
                            options={
                                [
                                    { label: '新建授权', value: -1 },
                                    ...options
                                ]
                            }
                            onChange={(value: number) => {
                                setFormData({
                                    ...formData,
                                    authMethod: value
                                })
                            }}
                        />
                    </Form.Item>
                    {formData.authMethod === -1 ? (<>
                        <Form.Item
                            label="授权配置名称"
                            required
                        >
                            <Input
                                value={formData.authName}
                                placeholder='请输入链接器配置名称'
                                onChange={(event: any) => {
                                    setFormData({
                                        ...formData,
                                        authName: event.target.value
                                    })
                                }}
                            />
                        </Form.Item>
                        {authType === 4 && <>
                            <Form.Item
                                label="数据库名称"
                                required
                            >
                                <Input 
                                    value={formData.dbDatabase}
                                    showCount
                                    maxLength={100}
                                    placeholder="请输入数据库名称"
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbDatabase: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="账号"
                                required
                            >
                                <Input
                                    value={formData.dbUsername}
                                    showCount
                                    maxLength={100}
                                    placeholder="请输入账号"
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbUsername: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="密码"
                                required
                            >
                                <Input
                                    value={formData.dbPassword}
                                    showCount
                                    maxLength={100}
                                    placeholder="请输入密码"
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbPassword: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="IP地址"
                                required
                            >
                                <Input
                                    value={formData.dbIp}
                                    showCount
                                    maxLength={100}
                                    placeholder="请输入IP地址"
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbIp: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="端口号"
                                required
                            >
                                <Input
                                    value={formData.dbPort}
                                    showCount
                                    maxLength={100}
                                    placeholder="请输入端口号"
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbPort: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                        </>}
                        {authType === 2 && <>
                            <Form.Item
                                label="AppID"
                                required
                            >
                                <Input.TextArea
                                    value={formData.appId}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入 AppID'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            appId: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="AppSecret"
                                required
                            >
                                <Input.TextArea
                                    value={formData.appSecret}
                                    showCount
                                    maxLength={400}
                                    placeholder='请输入 AppSecret'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            appSecret: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                        </>}
                        {authType === 3 && <>
                            <Form.Item
                                label="name"
                                required
                            >
                                <Input.TextArea 
                                    value={formData.keyName}
                                    showCount
                                    maxLength={50}
                                    placeholder="请输入name"
                                    onChange={(event: any) => setFormData({
                                        ...formData,
                                        keyName: event.target.value
                                    })}
                                />
                            </Form.Item>
                            <Form.Item
                                label="value"
                                required
                            >
                                <Input.TextArea 
                                    value={formData.keyValue}
                                    showCount
                                    maxLength={1000}
                                    placeholder="请输入value"
                                    onChange={(event: any) => setFormData({
                                        ...formData,
                                        keyValue: event.target.value
                                    })}
                                />  
                            </Form.Item>
                        </>}
                    </>) : ''}
                </Form>
            </div>
        </Modal>
    )
}

