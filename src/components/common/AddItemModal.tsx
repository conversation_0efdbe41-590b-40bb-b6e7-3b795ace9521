import { useEffect, useState } from 'react';
import { Tooltip, Modal, message, Switch } from 'antd';
import { useRecoilValue } from 'recoil';

import Router from 'next/router'

import commonListStyles from '@/styles/CommonList.module.scss'

import add from '@/images/commonList/add.png'

import { reqAddFlow, reqCopyFlow  } from '@/service/flow3.0'
import { reqCreateApi, reqCopyApi } from '@/service/api'

import { teamIdAtom } from '@/atoms/commonAtoms';

import { initBlockList } from '@/config/flowConfig';

export default function AddItemModal(prop:any) {
    const { openAddItem, setOpenAddItem, addNewTitle, addTitle, iconUrl, officalListData, pageName, length } = prop;
    const [activeIndex, setActiveIndex] = useState(-1)
    
    // 是否是文生图
    const isImg = addNewTitle.includes('文生图')
    const teamId = useRecoilValue(teamIdAtom)

    const addItem = async() => {
        if(pageName === 'flow') {
            interface iConfigObj {
                [key:string]: object;
            }
            const flowConfig:iConfigObj = {};
            const blockKeys = new Array();
            initBlockList.forEach(block => {
                flowConfig[block.id] = block;
                blockKeys.push(block.id);
            })
            const res = await reqAddFlow({
                title: 'Flow'+length,
                flow_config: {
                    blocks: flowConfig,
                    block_keys: blockKeys
                }
            })
            Router.push('./flowDetailV3?flowDetail='+res.chain_no+'&teamId='+teamId)
        }
    }
    return (
        <>
            <Modal 
                open={openAddItem}
                className={'commonModal addItemModal'}
                onCancel={() => setOpenAddItem(false)}
                title={addTitle}
                onOk={async() => {
                    const id = officalListData[activeIndex].itemId
                    // 复制类型 1:团队内部复制 2:复制官方模版 3:复制模版社区
                    if(pageName === 'flow') {
                        const res = await reqCopyFlow({
                            chain_no: id,
                            team_id: teamId,
                            type: 2
                        })
                        Router.push('./flowDetailV3?flowDetail='+res.chain_no+'&teamId='+teamId)
                    }else if(pageName === 'api') {
                        // 复制类型，1:从本项目复制，2:从官方项目复制，默认为1
                        const res = await reqCopyApi({
                            api_id: id,
                            copy_type: 2
                        })
                        Router.push('./apiDetail?apiId='+res.id+'&teamId='+teamId)
                    }
                }}
                okText="确定"
                cancelText="取消"
            >   
                <div className={commonListStyles.list + ' ' + commonListStyles.addItemlist + ' normalFont'}>
                    <div 
                        onClick={() => {
                            addItem();
                        }}
                        className={commonListStyles.item + ' ' + (isImg ? commonListStyles.imgItem : '') + ' ' + commonListStyles.addItem}
                    >
                        <div><img src={add.src} width={20} /></div>
                        <span>{addNewTitle}</span>  
                    </div>
                </div>
                <div className={commonListStyles.addTitle + ' boldFont'}>参考官方模板创建</div>
                <div className={commonListStyles.list + ' ' + commonListStyles.addItemlist + ' normalFont'}>
                {officalListData.map((item:any, index:any)=>{
                    if(!isImg) {
                        return  <div 
                            onClick={() => {
                                setActiveIndex(index)
                            }}
                            className={commonListStyles.item + ' ' +((index - 2) % 3 === 0 ? commonListStyles.lastItem : '') + " " + (activeIndex === index ? commonListStyles.activeItem : '')}>
                            <img src={iconUrl} width={24} />
                            <div className={commonListStyles.itemContent}>
                            <div className={commonListStyles.itemTitle}>
                                
                                <span className={commonListStyles.title + ' singleLineEllipsis'}>{item.itemTitle}</span>
                                {item.deployStatus && <span className={commonListStyles.successDeploy + ' ' + commonListStyles.status + ' normalFont'}>{item.deployStatus}</span>}
                                {item.publicStatus && <span className={(item.publicStatus === '审核中' ? commonListStyles.publicing : commonListStyles.successPublic) + ' ' + commonListStyles.status + ' normalFont'}>{item.publicStatus}</span> }
                            </div>
                            <div className={commonListStyles.itemDesc + ' multiLineEllipsis'} title={item.itemDesc}>{item.itemDesc}</div>
                            </div>
                        </div>
                    }else {
                        return <div 
                            onClick={() => {
                                setActiveIndex(index)
                            }}
                            className={commonListStyles.item + ' ' + commonListStyles.imgItem + ' ' +((index - 3) % 4 === 0 ? commonListStyles.lastItem : '') + " " + (activeIndex === index ? commonListStyles.activeItem : '')}>
                            <div className={commonListStyles.promptImgWrapper}>
                            <img src={item.imgUrl} className={commonListStyles.promptImg} />
                            </div>
                            <div className={commonListStyles.itemContent}>
                            <div className={commonListStyles.itemTitle + ' ' + commonListStyles.imgItemTitle}>
                                <span className={commonListStyles.title + ' singleLineEllipsis'}>{item.itemTitle}</span>
                                {item.deployStatus && <span className={commonListStyles.successDeploy + ' ' + commonListStyles.status + ' normalFont'}>{item.deployStatus}</span>}
                                {item.publicStatus && <span className={(item.publicStatus === '审核中' ? commonListStyles.publicing : commonListStyles.successPublic) + ' ' + commonListStyles.status + ' normalFont'}>{item.publicStatus}</span> }
                            </div>
                            </div>
                        </div>
                    }
                })}
                </div>
            </Modal>
        </>
    )
}
