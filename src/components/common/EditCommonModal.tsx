import React, { useState, useEffect } from "react";
import { Modal, Checkbox, Button, message, Form, Input, Upload } from "antd";
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';

const { TextArea } = Input;

import { reqKnowledgeDataUpload } from '@/service/knowledge'

import { isNullOrEmpty } from '@/utils/common'

import {
  reqKnowledgeRename,
  reqRenameKnowledgeChatV2
} from "@/service/knowledge";

import {
  reqUpdateFlowTitle,
  reqUpdateFlowDesc
} from "@/service/flow3.0";

import { reqUpdateAgent } from '@/service/agent'

import agentDefaultImg from '@/images/commonList/agent-list-robot.svg'
import flowDefaultImg from '@/images/commonList/common-flow-default.svg'
import knowledgeQaDefaultImg from '@/images/commonList/common-knowledgeQa-default.svg'
import knowledgeQdDefaultImg from '@/images/commonList/common-knowledgeQd-default.svg'
import promptDefaultImg from '@/images/commonList/common-prompt-default.svg'
import { agentIconList, flowIconList, knowledgeQdIconList, promptIconList } from "@/constants/appConstants";

export default function EditCommonModal(prop: any) {
  const { pageName, okHandel, openModal, setOpenModal, editTitle, editDesc, editImage, editId } = prop;
  const [isTermChecked, setIsTermChecked] = useState(true);
  const [title, setTitle] = useState("编辑");
  const [imageUrl, setImageUrl] = useState("");
  const [imageBgColor, setImageBgColor] = useState('linear-gradient(180deg, #90BEFF 0%, #1D7CFF 100%)');
  const [defaultImage, setDefaultImage] = useState(flowDefaultImg);
  const [uploading, setUploading] = useState(false);

  const [form] = Form.useForm();

  useEffect(() => {
    let urlImageList = [] as string[]
    if (pageName === "agents") {
      setTitle("智能体");
      setDefaultImage(agentDefaultImg);
      setImageBgColor('linear-gradient(180deg, #919FC3 0%, #525E7F 100%)');
      urlImageList = agentIconList
    } else if (pageName === "flow") {
      setTitle("技能");
      setDefaultImage(flowDefaultImg);
      setImageBgColor('linear-gradient(180deg, #90BEFF 0%, #1D7CFF 100%)');
      urlImageList = flowIconList
    } else if (pageName === 'knowledge-data') {
      setTitle("知识数据");
      setDefaultImage(knowledgeQdDefaultImg);
      setImageBgColor('linear-gradient(180deg, #FFD89C 0%, #F4B14C 100%)');
      urlImageList = knowledgeQdIconList
    } else if (pageName === 'knowledge-chat') {
      setTitle("知识问答");
      setDefaultImage(knowledgeQaDefaultImg);
      setImageBgColor('linear-gradient(180deg, #FFD89C 0%, #F4B14C 100%)');
      urlImageList = knowledgeQdIconList
    } else if (pageName === 'prompt-text') {
      setTitle("文生文");
      setDefaultImage(promptDefaultImg);
      setImageBgColor('linear-gradient(180deg, #AAE4A4 0%, #74BE6C 100%)');
      urlImageList = promptIconList
    } else if (pageName === 'prompt-image') {
      setTitle("文生图");
      setDefaultImage(promptDefaultImg);
      setImageBgColor('linear-gradient(180deg, #AAE4A4 0%, #74BE6C 100%)');
      urlImageList = promptIconList
    }

    if (openModal) {
      form.resetFields();
      if (editImage) {
        setImageUrl(editImage);
      } else {
        setImageUrl(urlImageList[Math.floor(Math.random() * urlImageList.length)]);
      }

      if (editTitle) {
        form.setFieldsValue({ name: editTitle })
      }
      if (editDesc) {
        form.setFieldsValue({ description: editDesc })
      }
    }
  }, [pageName, openModal, editTitle, editDesc, editImage]);



  const onFinish = () => {
    form.validateFields().then(async (values) => {
      // console.log(values);
      const { name, description } = values;
      // 调用接口 修改名字
      const req = pageName === 'knowledge-data' ? await reqKnowledgeRename({ id: editId, name, description }) :
        pageName === 'knowledge-chat' ? await reqRenameKnowledgeChatV2({ id: editId, name, description }) :
          pageName === 'flow' ? (await Promise.all([reqUpdateFlowTitle({ template_id: editId, title: name }), reqUpdateFlowDesc({ template_id: editId, desc: description })])) :
            pageName === 'agents' ? await reqUpdateAgent({ agent_id: editId, params: { system_info: { icon: editImage, name, description } } }) : null

      if (pageName === 'knowledge-data') {
        if (!req) {
          message.error(req.msg);
        } else {
          message.success('修改成功');
          okHandel && okHandel();
          setOpenModal(false);
        }
      } else if (pageName === 'knowledge-chat') {
        // console.log('req======>', req)
        if (req) {
          message.success('修改成功');
          okHandel && okHandel();
          setOpenModal(false);
        }
      } else {
        if (pageName === 'flow') {
          if (!isNullOrEmpty(req[0]) && !isNullOrEmpty(req[1])) {
            message.success('修改成功');
            okHandel && okHandel();
            setOpenModal(false);
          }
        } else {
          if (!isNullOrEmpty(req)) {
            message.success('修改成功');
            okHandel && okHandel();
            setOpenModal(false);
          }
        }
      }
    });
  }

  const props: UploadProps = {
    multiple: false,
    beforeUpload: (file) => {
      // console.log('file=======>', file)
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('请上传JPG或PNG图片');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('文件不能超过 2MB!');
        return false;
      }
      onUploadLocalFileHandle([file])
      return false;
    },
  };

  const onUploadLocalFileHandle = async (fileList: any[]) => {
    try {
      setUploading(true);
      const formData = new FormData();
      fileList.forEach((file) => {
        formData.append('file_list', file as RcFile);
      });
      const res = await reqKnowledgeDataUpload(formData)
      // console.log('res======>', res)
      if (res.code !== 200 || res.data.length === 0) {
        message.error(res.message)
        return
      }
      const { file_path, file_name, file_id } = res.data[0];

    } catch (e: any) {
      message.error(e.message)
    } finally {
      setUploading(false);
    }
  }


  return (
    <>
      <Modal
        open={openModal}
        style={{ width: 480 }}
        className={"commonModal"}
        onCancel={() => { setOpenModal(false); form.resetFields(); }}
        title={"编辑" + title}
        footer={
          <>
            <Button onClick={() => { setOpenModal(false); form.resetFields(); }}>取消</Button>
            <Button
              type="primary"
              onClick={
                () => {
                  onFinish();
                }
              }
            >
              确认
            </Button>
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
          onFinish={onFinish}
          style={{ width: "100%", padding: "0 8px" }}
        >
          <Form.Item required label={title + '名称'} name="name" rules={[
            { required: true, message: '请输入' + title + '名称' },
          ]} >
            <TextArea rows={2} placeholder={'请输入' + title + '名称'} showCount maxLength={50} />
          </Form.Item>
          <Form.Item required label="描述" name="description" rules={[
            { required: true, message: '请输入' + title + '相关描述' },
          ]}>
            <TextArea rows={4} placeholder={(pageName !== 'agents' && !pageName.startsWith('prompt')) ? ('请输入' + title + '描述，描述越详细，智能体引用时效果越好。建议30个字以上。') : ('请输入' + title + '相关描述')} maxLength={500} showCount />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
