/*
 * @Author: yh
 * @Date: 2024-05-21 20:53:09
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-03-15 11:04:08
 * @FilePath: \prompt-web\src\components\common\CommonPage.tsx
 */
import { useSetRecoilState } from 'recoil';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router'
import commonPageStyles from '@/styles/CommonPage.module.css'
import * as constants from '@/constants/appConstants'
import dynamic from 'next/dynamic'

const MenuNoSSR = dynamic(() => import('@/components/common/Menu'), {
  ssr: false
})
export default function CommonPage(prop: any) {
  const { children, isBackground, isFullScreen, propClassName = '' } = prop;
  const [isFromCompanyBrainBoard, setIsFromCompanyBrainBoard] = useState(false);
  const router = useRouter();
  const { pathname } = router;
  const handleCompanyBrainValueChange = (value: boolean) => {
    setIsFromCompanyBrainBoard(value);
  }

  const isTransparentBg = () => {
    if (pathname.indexOf('/agentOpen') > -1) return true
    if (pathname == '/agentSDK') return true
    if (pathname == '/deepseek') return true
    return false
  }

  return (
    <div className={commonPageStyles.bg + ' ' + propClassName + ' ' + (isTransparentBg() && commonPageStyles.transparentbg)}>
      <MenuNoSSR isBackground={isBackground || false} isFullScreen={isFullScreen || false} onCompanyBrainValue={handleCompanyBrainValueChange} />
      <div className={commonPageStyles.rightContent + " " + (isFromCompanyBrainBoard ? commonPageStyles.rightContentZhiNao : "") + ' ' + (pathname == '/agentSDK' && commonPageStyles.hidden)} >
        {children}
      </div>
    </div>
  )
}
