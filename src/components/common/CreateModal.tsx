import React, { useState, useEffect, InputHTMLAttributes } from "react";
import { Modal, Button, Form, Input, message, Select, Radio, Upload, UploadProps } from "antd";
import { notify } from "@/utils/messageManager";

const { TextArea } = Input;

import createStyles from "@/styles/ApiCreateModal.module.scss";
import apiDefaultImg from '@/images/commonList/common-api-default.svg'
import magicBarImg from '@/images/magicBar.svg';
import magicBarActiveImg from '@/images/magicBarActive.svg';
import aiLoadingWhite from '@/images/aiLoadingWhite.svg';
import repeatAiImg from '@/images/repeatAi.svg';
import repeatUploadImg from '@/images/repeatUpload.svg';
import aiLoading from '@/images/aiLoading.svg';
import localUpload from '@/images/localUpload.svg';
import agentDefaultImg from '@/images/commonList/agent-list-robot.svg'
import flowDefaultImg from '@/images/commonList/common-flow-default.svg'
import knowledgeQaDefaultImg from '@/images/commonList/common-knowledgeQa-default.svg'
import knowledgeQdDefaultImg from '@/images/commonList/common-knowledgeQd-default.svg'
import knowledgeExternalDefaultImg from '@/images/commonList/common-knowledgeExternal-default.svg'
import promptDefaultImg from '@/images/commonList/common-prompt-default.svg'
import taskIcon from '@/images/taskIcon.svg'
import interactionIcon from '@/images/interactionIcon.svg'

import FlowImportFile from '@/components/commonComponents/flowImportFile/FlowImportFile';
import { reqRenameExternalKnowledge } from "@/service/knowledgeExternal";

import {
    agentIconList,
    flowIconList,
    knowledgeQdIconList,
    promptIconList,
    knowledgeExternalIconList
} from "@/constants/appConstants";
import {
    reqGenerateWord,
    reqGenerateImg,
    reqImageUpload,
} from '@/service/api';
import {
    reqUpdateFlowTitle,
    reqUpdateFlowDesc
} from "@/service/flow3.0";
import {
    reqKnowledgeRename
} from "@/service/knowledge";
import { reqUpdateAgent, reqAiCreateAgent } from '@/service/agent'
import { isNullOrEmpty } from '@/utils/common'
import Show from '@/components/commonComponents/show/Show';
import Router, { useRouter } from 'next/router'
export default function CreateCommonModal(prop: any) {
    const {
        baseInfo,
        pageName,
        okHandel,
        openModal,
        setOpenModal,
        isImportSuccess,
        aiCreateAgent,
        // 定义一个标志，是否渲染外部知识库的底部文本框（同时涉及重命名校验）
        isRenderExternalApiFields = true,
        moreAgentTeams = false,
    } = prop;
    const initForm = {
        name: '',
        description: '',
        // prompt:'',
        greetingMessage: '',
        greetingQuestion: [],
        // knowledge-external有
        endpoint: '',
        secret: '',
        knowledge_id:''
    }
    const [isCreate, setIsCreate] = useState(false);
    const [title, setTitle] = useState('插件');
    const [formData, setFormData] = useState(initForm);
    const [imageUrl, setImageUrl] = useState("");
    const [imageBgColor, setImageBgColor] = useState('linear-gradient(180deg, #90BEFF 0%, #1D7CFF 100%)');
    const [defaultImage, setDefaultImage] = useState(apiDefaultImg);
    const [loading, setLoading] = useState({
        icon: false,
        desc: false
    });
    const [first, setFirst] = useState(false);
    const [uploadedJsonContent, setUploadedJsonContent] = useState(null);
    const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
    const [importMode, setImportMode] = useState(1);
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);
    const [flowType, setFlowType] = useState(1)
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [createAgentType, setCreateAgentType] = useState(1); // 创建智能体类型 1-标准创建 2-AI 创建
    const [aiAgentDesc, setAiAgentDesc] = useState('');
    const [aiAgentLoading, setAiAgentLoading] = useState(false);
    const [isMoreAgentTeams, setIsMoreAgentTeams] = useState(false);
    const [modalTitle, setModalTitle] = useState('');

    const [form] = Form.useForm();

    const router = useRouter();
    useEffect(() => {
        if (router?.pathname === '/flowDetailV4') {
            setIsMoreAgentTeams(true);
        }
    }, [router])

    useEffect(() => { 
        console.log('--------', title, isMoreAgentTeams, baseInfo)
        let name = baseInfo ? "编辑" : "创建";
        if (isMoreAgentTeams || moreAgentTeams) {
            name = name + '多智能体组队';
        } else {
            name = name + title;
        }
        setModalTitle(name);
    }, [title, isMoreAgentTeams, baseInfo, moreAgentTeams])

    useEffect(() => {
        let urlImageList = [] as string[]
        if (pageName === "agents") {
            setTitle("智能体");
            setDefaultImage(agentDefaultImg);
            setImageBgColor('linear-gradient(180deg, #919FC3 0%, #525E7F 100%)');
            urlImageList = agentIconList
        } else if (pageName === "flow") {
            setTitle("多专家智能体");
            setDefaultImage(flowDefaultImg);
            setImageBgColor('linear-gradient(180deg, #90BEFF 0%, #1D7CFF 100%)');
            urlImageList = flowIconList
        } else if (pageName === 'knowledge-data') {
            setTitle("知识数据");
            setDefaultImage(knowledgeQdDefaultImg);
            setImageBgColor('linear-gradient(180deg, #FFD89C 0%, #F4B14C 100%)');
            urlImageList = knowledgeQdIconList
        } else if (pageName === 'knowledge-chat') {
            setTitle("知识问答");
            setDefaultImage(knowledgeQaDefaultImg);
            setImageBgColor('linear-gradient(180deg, #FFD89C 0%, #F4B14C 100%)');
            urlImageList = knowledgeQdIconList
        } else if (pageName === 'knowledge-external') {
            setTitle("外部知识库API");
            setDefaultImage(knowledgeExternalDefaultImg);
            setImageBgColor('linear-gradient(180deg, #AAE4A4 0%, #74BE6C 100%)');
            urlImageList = knowledgeExternalIconList
        }else if (pageName === 'prompt-text') {
            setTitle("文生文");
            setDefaultImage(promptDefaultImg);
            setImageBgColor('linear-gradient(180deg, #AAE4A4 0%, #74BE6C 100%)');
            urlImageList = promptIconList
        } else if (pageName === 'prompt-image') {
            setTitle("文生图");
            setDefaultImage(promptDefaultImg);
            setImageBgColor('linear-gradient(180deg, #AAE4A4 0%, #74BE6C 100%)');
            urlImageList = promptIconList
        }

        if (openModal && !baseInfo) {
            setFirst(true)
            setFormData(initForm);
            setImageUrl(urlImageList[Math.floor(Math.random() * urlImageList.length)]);
        } else {
            setFirst(false)
        }

        if (baseInfo) {
            setIsCreate(false)

            if (pageName === 'agents') {
                setImageUrl(baseInfo.icon)
                setFormData({
                    name: baseInfo.name,
                    description: baseInfo.description,
                    // prompt:'',
                    greetingMessage: '',
                    greetingQuestion: [],
                    endpoint: baseInfo.endpoint || '',
                    secret: baseInfo.secret || '',
                    knowledge_id:baseInfo.knowledge_id || ''
                })
            } else if (pageName === 'prompt-text' || pageName === 'prompt-image') {
                setImageUrl(pageName === 'prompt-text' ? baseInfo.image : baseInfo.user_image)
                setFormData({
                    name: baseInfo.name,
                    description: baseInfo.desc,
                    // prompt:'',
                    greetingMessage: '',
                    greetingQuestion: [],
                    endpoint: baseInfo.endpoint || '',
                    secret: baseInfo.secret || '',
                    knowledge_id:baseInfo.knowledge_id || ''
                })
            } else if (pageName === 'flow') {
                setImageUrl(baseInfo.images)
                setFormData({
                    name: baseInfo.title,
                    description: baseInfo.desc,
                    // prompt:'',
                    greetingMessage: '',
                    greetingQuestion: [],
                    endpoint: baseInfo.endpoint || '',
                    secret: baseInfo.secret || '',
                    knowledge_id:baseInfo.knowledge_id || ''
                })
            } else if (pageName === 'knowledge-data') {
                setImageUrl(baseInfo.imageUrl)
                setFormData({
                    name: baseInfo.name,
                    description: baseInfo.description,
                    // prompt:'',
                    greetingMessage: '',
                    greetingQuestion: [],
                    endpoint: baseInfo.endpoint || '',
                    secret: baseInfo.secret || '',
                    knowledge_id:baseInfo.knowledge_id || ''
                })
            } else if (pageName === 'knowledge-external') {
                setImageUrl(baseInfo.imageUrl)
                setFormData({
                    name: baseInfo.name,
                    description: baseInfo.description,
                    greetingMessage: '',
                    greetingQuestion: [],
                    endpoint: baseInfo.endpoint || '',
                    secret: baseInfo.secret || '',
                    knowledge_id:baseInfo.knowledge_id || ''
                })
            }
        } else {
            setIsCreate(true)
        }
    }, [pageName, openModal, moreAgentTeams, isMoreAgentTeams]);

    const onFinish = async () => {
        if (loading.icon || loading.desc || aiAgentLoading) {
            notify.warning('AI生成中，请耐心等待')
            return
        }

        if (pageName == 'agents' && isCreate && createAgentType == 2) {
            setLoadingSubmit(true);
            setAiAgentLoading(true);
            const res: any = await reqAiCreateAgent({ description: aiAgentDesc });
            setCreateAgentType(1);
            setLoadingSubmit(false);
            setAiAgentLoading(false);
            setAiAgentDesc('');
            setOpenModal(false);
            aiCreateAgent?.(res.agent_id)
            return;
        }


        if (!imageUrl) {
            notify.warning('请上传或生成图片');
            return
        } else if (!formData.name || (formData.name.replace(' ', '') === '')) {
            notify.warning('请填写名称');
            return
        } else if (!formData.description || (formData.description.replace(' ', '') === '')) {
            notify.warning('请填写描述');
            return
        }

        if (isRenderExternalApiFields && pageName === 'knowledge-external') {
            if (!formData.endpoint || (formData.endpoint.replace(' ', '') === '')) {
                notify.warning('请填写API Endpoint或接口地址');
                return
            } else if (!formData.secret || (formData.secret.replace(' ', '') === '')) {
                notify.warning('请填写API KEY');
                return
            }else if (!formData.knowledge_id || (formData.knowledge_id.replace(' ', '') === '')) {
                notify.warning('请填写知识库ID');
                return
            }
        }

        setLoadingSubmit(true);
        if (isCreate) {
            if (importMode == 1) {
                okHandel && okHandel({ ...formData, imageUrl, importMode, flowType }) && setLoadingSubmit(false);
            } else if (importMode == 2) {
                okHandel && okHandel({ ...formData, imageUrl, importMode, uploadedFile, flowType }) && setLoadingSubmit(false);
                if (isImportSuccess == false) {
                    return
                } else {
                    handleClearForm()
                }
            }
        } else {
            let req;
            if (pageName === 'agents') {
                req = await reqUpdateAgent({
                    agent_id: baseInfo.id,
                    params: {
                        system_info: {
                            icon: imageUrl,
                            name: formData.name,
                            description: formData.description
                        }
                    }
                })
            } else if (pageName === 'flow') {
                let title = formData.name;
                if (moreAgentTeams || isMoreAgentTeams) {
                    title = '专家组队-' + title;
                }
                req = await Promise.all([
                    reqUpdateFlowTitle({ template_id: baseInfo.id, title, images: imageUrl }),
                    reqUpdateFlowDesc({ template_id: baseInfo.id, desc: formData.description })
                ])
            } else if (pageName === 'knowledge-data') {
                req = await reqKnowledgeRename({
                    id: baseInfo.id,
                    name: formData.name,
                    description: formData.description,
                    imageUrl
                })
            }else if (pageName === 'knowledge-external') {
                req = await reqRenameExternalKnowledge({
                    id: baseInfo.id,
                    name: formData.name,
                    description: formData.description,
                    imageUrl: imageUrl
                });
            }

            if (pageName.startsWith('knowledge')) {
                if (req.context.code !== 0) {
                    notify.error(req.msg);
                } else {
                    notify.success('修改成功');
                    okHandel && okHandel();
                    setOpenModal(false);
                }
            } else {
                if (pageName === 'flow') {
                    if (!isNullOrEmpty(req[0]) && !isNullOrEmpty(req[1])) {
                        notify.success('修改成功');
                        okHandel && okHandel();
                        setOpenModal(false);
                    }
                } else {
                    if (!isNullOrEmpty(req)) {
                        notify.success('修改成功');
                        okHandel && okHandel();
                        setOpenModal(false);
                    }
                }
            }
            setLoadingSubmit(false);
        }
        // setOpenModal(false);
    }

    // 文生文-图
    const aiGenerate = async (text: string) => {
        if (!formData.name || loading.icon || loading.desc) return

        first && setFirst(false)

        setLoading({ icon: true, desc: true })


        const reqArr = await Promise.all([
            reqGenerateImg({ text }),
            reqGenerateWord({ text }),
        ])

        const [image, word] = reqArr;
        setLoading({ icon: false, desc: false })

        if (!word && !image) {
            // setLoading(false)
            notify.warning('AI生成失败，请重新生成');
            return
        } else if (!image || !image.img_url) {
            notify.warning('文生图失败');
            setFormData({
                ...formData,
                description: word.text,
                // greetingMessage:greetingInfo.greeting_message,
                // greetingQuestion:greetingInfo.greeting_question
            })
            // setLoading(false)
        } else {
            setImageUrl(image.img_url);
            setFormData({
                ...formData,
                description: word.text,
                // greetingMessage:greetingInfo.greeting_message,
                // greetingQuestion:greetingInfo.greeting_question
            })
            // setLoading(false)
        }

    }

    // 文生图
    const aiImage = async (text: string) => {
        setLoading({ ...loading, icon: true })


        const image = await reqGenerateImg({ text })
        image && setImageUrl(image.img_url);

        setLoading({ ...loading, icon: false })
    }

    // 文生文
    const aiText = async (text: string) => {
        setLoading({ ...loading, desc: true })

        let params: any = { text };
        const word = await reqGenerateWord(params) as any;
        word && setFormData({
            ...formData,
            description: word.text,
        })

        setLoading({ ...loading, desc: false })
    }


    const handleImageLoad = () => {
        setLoading({ ...loading, icon: false })
    }




    const localImgUpload = async () => {
        const imgInput = document.getElementById('filepond') as any;
        const file = new FormData();
        const imgType = ['image/png', 'image/jpeg', 'image/bmp', 'image/webp'];

        imgInput?.click();
        await imgInput.addEventListener('change', () => {
            if (imgInput.files.length > 1) {
                notify.warning(`${title}图标只能设置一张`)
            }

            const img = imgInput.files[0];
            const fileType = img.name.split('.').slice(-1)[0];

            if (!['png', 'jpeg', 'bmp', 'webp'].includes(fileType)) {
                notify.warning('请上传png、jpeg、bmp、webp格式照片');
                return
            }

            const imgUrl = URL.createObjectURL(img);
            const newImg = new Image();
            newImg.src = imgUrl;
            newImg.onload = () => {
                if (newImg.width !== newImg.height) {
                    notify.warning('图片需要 1:1 宽高');
                } else {
                    file.append('file', img)

                    if (img.size > 2 * 1024 * 1024) {
                        notify.warning('图片文件不能大于 2MB');
                        return
                    }
                    if (imgType.includes(img.type)) {
                        reqImageUpload(file).then((res) => {
                            setImageUrl(res.image_url)
                        })
                    } else {
                        notify.warning('需要png、jpeg、bmp、webp文件');
                    }
                }
            }
        }, { once: true })
    }

    const [modeInfo] = Form.useForm()
    const { Dragger } = Upload;

    const handleReupload = () => {
        setUploadedFileName(null);
        setUploadedJsonContent(null);
    };
    const handleClearForm = () => {
        setUploadedFileName(null);
        setUploadedJsonContent(null);
        setImportMode(1);
        modeInfo.resetFields();
    }

    useEffect(() => {
        if(openModal && pageName == 'flow' && isCreate) {
            modeInfo?.setFieldsValue({ mode: importMode });
        }
    }, [importMode]);

    useEffect(() => {
        if (!isImportSuccess) {
            setImportMode(2);
        }
    }, [isImportSuccess]);

    const onRaidoChange = (e: any) => {
        setImportMode(e.target.value)
    }


    const handleOk = async () => {
        if (pageName == 'agents' && isCreate && createAgentType == 2 && aiAgentDesc.trim()?.length == 0) {
            notify.error('请描述你希望创建一个什么样的智能体')
            return
        }

        if (pageName == "flow" && importMode == 2 && !uploadedFile && isCreate) {
            notify.error('没有找到文件，请上传！');
            return
        }

        onFinish()
    }

    const renderExternalApiFields = () => {
        if (pageName !== 'knowledge-external' || !isRenderExternalApiFields) {
            return null;
        }
        
        return (
            <div className={createStyles.externalApiFields}>
                <div className={createStyles.fieldContainer}>
                    <div className={createStyles.fieldLabel}>
                        <span className={createStyles.required}>*</span>
                        <span>API Endpoint/接口地址</span>
                    </div>
                    <Input 
                        value={formData.endpoint}
                        onChange={(e) => setFormData({...formData, endpoint: e.target.value})}
                        placeholder="请输入API Endpoint或接口地址"
                        className={createStyles.fieldInput}
                    />
                </div>
                <div className={createStyles.fieldContainer}>
                    <div className={createStyles.fieldLabel}>
                        <span className={createStyles.required}>*</span>
                        <span>API KEY</span>
                    </div>
                    <Input 
                        value={formData.secret}
                        onChange={(e) => setFormData({...formData, secret: e.target.value})}
                        placeholder="请输入API KEY"
                        className={createStyles.fieldInput}
                    />
                </div>
                <div className={createStyles.fieldContainer} style={{marginBottom: 0}}>
                    <div className={createStyles.fieldLabel}>
                        <span className={createStyles.required}>*</span>
                        <span>知识库ID</span>
                    </div>
                    <Input 
                        value={formData.knowledge_id}
                        onChange={(e) => setFormData({...formData, knowledge_id: e.target.value})}
                        placeholder="请输入知识库ID"
                        className={createStyles.fieldInput}
                    />
                </div>
            </div>
        );
    }

    return (
        <>
            <Modal
                open={openModal}
                style={{ width: 480 }}
                className={"commonModal"}
                onCancel={() => { setOpenModal(false); modeInfo.resetFields(); handleClearForm() }}
                title={modalTitle}
                centered
                footer={
                    <>
                        <Button onClick={() => { setOpenModal(false); modeInfo.resetFields(); handleClearForm() }}>取消</Button>
                        <Button
                            type="primary"
                            onClick={
                                () => {
                                    handleOk();
                                }
                            }
                            loading={loadingSubmit}
                        >
                            确认
                        </Button>
                    </>
                }
            >
                <div className={createStyles.header}>
                    {/* {(pageName == 'flow' && isCreate) ? <div className={createStyles.typeBtnWrapper + ' normalFont'}>
                        <div className={createStyles.typeBtn + ' ' + (flowType == 1 ? createStyles.activeBtn : '')} onClick={() => { setFlowType(1) }}> <img src={taskIcon.src} />任务型技能</div>
                        <div className={createStyles.typeBtn + ' ' + (flowType == 2 ? createStyles.activeBtn : '')} onClick={() => { setFlowType(2) }}> <img src={interactionIcon.src} />交互型技能</div>
                    </div> : ''} */}
                    {/* 创建智能体 */}
                    {
                        (pageName == "agents" && isCreate) ? <div className={createStyles.typeBtnWrapper + ' normalFont'}>
                            <div className={createStyles.typeBtn + ' ' + (createAgentType == 1 ? createStyles.activeBtn : '')} onClick={() => { !aiAgentLoading && setCreateAgentType(1) }}> <img src={taskIcon.src} />标准创建</div>
                            <div className={createStyles.typeBtn + ' ' + (createAgentType == 2 ? createStyles.activeBtn : '')} onClick={() => { !aiAgentLoading && setCreateAgentType(2) }}> <img src={interactionIcon.src} />AI 创建</div>
                        </div> : ''
                    }
                    <Show
                        when={pageName != "agents" || (pageName == "agents" && isCreate && createAgentType == 1) || (pageName == "agents" && !isCreate)}
                        children={
                            <>
                                <input
                                    value={formData.name}
                                    maxLength={50}
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            name: event.target.value
                                        })
                                    }}
                                    className={createStyles.nameInput}
                                    placeholder={(title === '多专家智能体' || moreAgentTeams || isMoreAgentTeams) ? (moreAgentTeams || isMoreAgentTeams ? '输入多智能体组队名称(必填)': `输入${title}名称(必填)`) : "请输入" + title + "名称，不得超过50个字"}
                                />
                                <div className={createStyles.aiBtn}>
                                    {(!formData.name || (loading.icon || loading.desc)) && <div className={createStyles.leftBar}></div>}
                                    <div
                                        className={(formData.name && (!loading.icon && !loading.desc)) ? createStyles.aiButtonActive : createStyles.aiButton}
                                        onClick={() => aiGenerate(formData.name)}
                                    >
                                        <img className={createStyles.aiIcon} src={(formData.name && !loading.icon && !loading.desc) ? magicBarActiveImg.src : magicBarImg.src} alt="" />
                                        <div className={createStyles.aiText}>一键生成描述及图标</div>
                                    </div>
                                    {(!formData.name || loading.icon || !loading.desc) && <div className={createStyles.rightBar}></div>}
                                </div>
                            </>
                        }
                    />

                </div>
                <Show
                    when={pageName != "agents" || (pageName == "agents" && isCreate && createAgentType == 1) || (pageName == "agents" && !isCreate)}
                    children={
                        <div className={createStyles.content}>
                            <div className={createStyles.baseCon}>
                                <div className={createStyles.conLeft}>
                                    {(!loading.icon && !first) && <div onClick={() => localImgUpload()} className={createStyles.localRepeat}>
                                        <img src={repeatUploadImg.src} alt="" />
                                    </div>}
                                    {loading.icon && <div className={createStyles.glass}>
                                        <img src={aiLoadingWhite.src} alt="" />
                                    </div>}
                                    <img onLoad={handleImageLoad} className={createStyles.aiImage} src={imageUrl ? imageUrl : defaultImage.src} alt="" style={{ backgroundImage: imageBgColor }} />
                                    {(!loading.icon && first) && <div onClick={() => localImgUpload()} className={createStyles.localUpload}>
                                        <img className={createStyles.uploadIcon} src={localUpload.src} alt="" />
                                        <div className={createStyles.uploadText}>本地上传</div>
                                    </div>}
                                    {loading.icon && <div className={createStyles.aiLoading}>
                                        <img src={aiLoading.src} alt="" />
                                        <div className={createStyles.loadText}>生成中</div>
                                    </div>}
                                    {(formData.name && !first && !loading.icon) && <div className={createStyles.repeatAi} onClick={() => aiImage(formData.name)}>
                                        <img src={repeatAiImg.src} alt="" />
                                        <div className={createStyles.repeatBtn}>重新生成</div>
                                    </div>}
                                </div>
                                <div className={createStyles.conRight}>
                                    <TextArea
                                        showCount
                                        maxLength={500}
                                        disabled={loading.desc}
                                        value={formData.description}
                                        className={createStyles.textArea}
                                        placeholder={(title === '多专家智能体' || moreAgentTeams || isMoreAgentTeams) ? (moreAgentTeams || isMoreAgentTeams ? '输入组队的描述，介绍多智能体团队的功能。' : '输入多专家智能体描述，介绍多专家智能体的功能，将会展示给多专家智能体的用户') : '请输入' + title + '描述，描述越详细，建议30个字以上。'}
                                        onChange={(event: any) => setFormData({
                                            ...formData,
                                            description: event.target.value
                                        })}
                                    />
                                    {(formData.name && !first && !loading.desc) && <div className={createStyles.repeatAi} onClick={() => aiText(formData.name)}>
                                        <img src={repeatAiImg.src} alt="" />
                                        <div className={createStyles.repeatBtn}>重新生成</div>
                                    </div>}
                                </div>
                            </div>
                        </div>
                    }
                />
                {renderExternalApiFields()}
                <Show
                    when={pageName == "agents" && isCreate && createAgentType == 2}
                    children={
                        <TextArea
                            rows={6}
                            disabled={aiAgentLoading}
                            value={aiAgentDesc}
                            className={createStyles.textArea}
                            placeholder={'请描述你希望创建一个什么样的智能体'}
                            onChange={(event: any) => setAiAgentDesc(event.target.value)}
                            style={{ marginTop: '-24px' }}
                        />
                    }
                />

                <input className={createStyles.filepond} id="filepond" type="file" multiple />
                {/* {pageName == 'flow' && isCreate && <div className={createStyles.createMode}>
                    <Form layout="vertical" form={modeInfo} initialValues={{ mode: importMode }}>
                        <Form.Item label="创建方式" name='mode' labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} rules={[{ required: true, message: '请选择一种创建方式' }]}>
                            <div>
                                <Radio.Group onChange={onRaidoChange} value={importMode}>
                                    <Radio value={1}>创建空白技能</Radio>
                                    <Radio value={2}>从文件导入</Radio>
                                </Radio.Group>
                                {
                                    importMode === 2 && <FlowImportFile setUploadedFile={setUploadedFile} />
                                }
                            </div>
                        </Form.Item>
                    </Form>
                </div>} */}
            </Modal>
        </>
    );
}
