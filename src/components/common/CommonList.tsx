import { Input, Select, Popover, message, Modal } from 'antd';
import { useRecoilValue } from 'recoil';

import { reqFlowList, reqOfficalFlowList, reqCopyFlow, reqDeleteFlow } from '@/service/flow3.0'
import { reqOfficalApiList, reqCopyApi, reqDeleteApiV2 } from '@/service/api'

import commonListStyles from '@/styles/CommonList.module.scss'
import search from '@/images/commonList/search.png'
import add from '@/images/commonList/add.png'
import more from '@/images/commonList/more.svg'

import editBtn from '@/images/commonList/edit.svg'
import deleteBtn from '@/images/commonList/delete.svg'
import copyBtn from '@/images/commonList/copy.svg'

import flow from '@/images/commonList/flow.png'
import api from '@/images/commonList/api.png'
import prompt from '@/images/commonList/prompt.png'

import AddItemModal from './AddItemModal'
import { teamIdAtom } from '@/atoms/commonAtoms';

import { useState, useEffect } from 'react';
import Router from 'next/router'

export default function CommonList(prop:any) {
  const promptText = '文生文';
  const promptImg = '文生图';
  const [activeTab, setActiveTab] = useState(promptText)
  const [openAddItem, setOpenAddItem] = useState(false)

  const [openDelete, setOpenDelete] = useState(false)
  const [deleteInfo, setDeleteInfo] = useState({
      id: '',
      name: ''
  })
  const [searchValue, setSearchValue] = useState('')

  const teamId = useRecoilValue(teamIdAtom)

  const pageConfig:any = {
    'agents': {
      pageTitle: 'Agents',
      addTitle: '创建Agent',
      addNewTitle: '创建全新Agent',
      iconUrl: flow.src
    }, 
    'flow': {
      pageTitle: '技能管理(Flow)',
      addTitle: '创建Flow',
      addNewTitle: '创建全新Flow',
      iconUrl: flow.src
    },
    'tool': {
      pageTitle: '工具管理',
      addTitle: '创建API工具',
      addNewTitle: '创建全新API工具',
      iconUrl: api.src
    },
    'prompt': {
      pageTitle: <div className={'titleTabs'}>
        <div className={'tabItem ' + (activeTab === promptText ? 'activeTitleTab' : '')} onClick={() => {setActiveTab(promptText)}}>{promptText}</div>
        <div className={'tabItem ' + (activeTab === promptImg ? 'activeTitleTab' : '')} onClick={() => {setActiveTab(promptImg)}}>{promptImg}</div>
      </div>,
      addTitle: '创建文生文',
      addNewTitle: '创建全新文生文',
      iconUrl: prompt.src
    }
  }
  const { pageName } = prop;

  const [activeRenameIndex, setActiveRenameIndex] = useState(-1)
  const [activeFileName, setActiveFileName] = useState('')

  const [listData, setListData] = useState(new Array())
  const [filterListData, setFilterListData] = useState(new Array())
  const [officalListData, setOfficalListData] = useState(new Array())

  const getPopoverDom = (item:any, index:any) => {
      return <Popover
              content={(
                <div className={commonListStyles.toolWrapper +" normalFont"}>
                    <div 
                      className={commonListStyles.toolItem} 
                      onClick={(e) => {
                        e.stopPropagation();
                        setActiveRenameIndex(index);
                        setActiveFileName(item.itemTitle);
                        }
                      }>
                        <img src={editBtn.src} width={16} />
                        <span>重命名</span>   
                    </div> 
                    <div 
                      className={commonListStyles.toolItem} 
                      onClick={async(e)=>{
                        e.stopPropagation();
                        copyItem(item.itemId);
                    }}>
                        <img src={copyBtn.src} width={16} />
                        <span>复制</span>   
                    </div> 
                    <div 
                        className={commonListStyles.toolItem} 
                        onClick={(e) => {
                            e.stopPropagation();
                            setDeleteInfo({
                              id: item.itemId,
                              name: item.itemTitle
                            })
                            setOpenDelete(true)
                        }}
                    >
                        <img src={deleteBtn.src} width={16} />
                        <span>删除</span>   
                    </div>                          
                </div>
            )}
            title=""
            trigger="hover"
            arrow={false}
            placement={'bottom'}
          >
            <span className={commonListStyles.moreBtn}>
              <img src={more.src} width='14' />
            </span>
        </Popover>
  }
  const changeName = async (newName:any, chainId:any) => {
    if(newName.length > 15) {
      message.error('名称最大不能超过15个字符!');
      return false;
    }
    if(newName.length === 0) {
      message.error('名称不能为空!');
      return false;
    }
    filterListData[activeRenameIndex].itemTitle = newName;
    setFilterListData([...filterListData])

    setActiveRenameIndex(-1)
  }
  const isPromptImg = activeTab === promptImg;
  const copyItem = async(id:any) => {
    // 复制类型 1:团队内部复制 2:复制官方模版 3:复制模版社区
    if(pageName === 'flow') {
      const res = await reqCopyFlow({
          chain_no: id,
          team_id: teamId,
          type: 1
      })
      Router.push('./flowDetailV3?flowDetail='+res.chain_no+'&teamId='+teamId)
    }else if(pageName === 'api') {
        // 复制类型，1:从本项目复制，2:从官方项目复制，默认为1
        const res = await reqCopyApi({
            api_id: id,
            copy_type: 1
        })
        Router.push('./apiDetail?apiId='+res.id+'&teamId='+teamId)
    }
  }
  const deleteItem = async() => {
    const id = deleteInfo.id;
    if(pageName === 'flow') {
      const res = await reqDeleteFlow({
        chain_no: id
      })
      if(res && res.message === 'ok') {
        message.success('删除成功');
        initList()
      }
    }else if(pageName === 'api') {
        const res = await reqDeleteApiV2({
          api_id: id
        })
        if(res && res.message === 'ok') {
          message.success('删除成功');
          initList()
        }
    }
  }
  const addTitle = isPromptImg ? '创建文生图' : pageConfig[pageName].addTitle;
  const addNewTitle = isPromptImg ? '创建全新文生图' : pageConfig[pageName].addNewTitle;

  const initList = async() =>{
    let list = new Array();
    let officalList = new Array();

    const getTransferList = (res:any) => {
      return res.map((item:any) => {
        return {
          itemId: item.id || item.chain_no,
          itemTitle: item.title || item.api_name || item.name,
          imgUrl: item.result || '',
          itemDesc: item.desc || item.api_desc,
          deployStatus: item.is_deploy === 1 ? '已部署' : '',
          publicStatus: item.template_check_status === 1 ? '审核中' : (item.template_check_status === 2 ? '已公开' : '')
        }
      })
    }

    if(pageName === 'flow') {
      // const res = await reqFlowList({
      //   keyword: '',
      //   page: 1,
      //   page_size: 1000
      // })
      // const officalRes = await reqOfficalFlowList({
      //   page: 1,
      //   page_size: 1000
      // })
      // 公开状态 -1:未审核 1:待审核 2:通过 3:拒绝 4.取消审核
      // 部署状态 0：未部署，1：已部署 2:部署取消
      const res = [{
        "id": 5381,
        "chain_no": "ackEO1rS2312151ItMds",
        "title": "测试02",
        "desc": "flow2.0测试",
        "template_check_status": -1, 
        "team_id": 239,
        "is_deploy": 0
      }]
      const officalRes = [{
        "id": 5381,
        "chain_no": "ackEO1rS2312151ItMds",
        "title": "官方测试",
        "desc": "flow2.0官方测试",
        "template_check_status": -1,
        "team_id": 239,
        "is_deploy": 0
      }]
      list = getTransferList(res)
      officalList = getTransferList(officalRes)
    } else if(pageName === 'prompt') {
      // 0:文生文和文生图 1:文生文 2:文生图
      // const res = await reqPromptList({prompt_type: isPromptImg ? 2: 1})
      // const officalRes = await reqOfficalPromptList({prompt_type: isPromptImg ? 2: 1})
      // const res = [{
      //   "chain_no": "ack3os7C231222K8pqz7",
      //   "template_id": 5391,
      //   "template_type": 2,
      //   "name": "新修改的(1)",
      //   "desc": 'xxxxxxxxxxxx',
      //   "type": 2,
      //   "is_deploy": 0,
      //   "deployment_id": 0,
      //   "template_check_status": -1,
      //   "icon": null,
      //   "height": null,
      //   "width": null,
      //   "result": "",
      //   "prompt":"{\"prompt\": \"\美\好\的\幸\福\生\活\", \"negative_prompt\": \"\"}"
      // }]
      const res = [{
        "chain_no": "acMupWAL231221B6S0re",
        "template_id": 5383,
        "template_type": 2,
        "name": "新修改的",
        "desc": "",
        "type": 2,
        "is_deploy": 0,
        "deployment_id": 0,
        "template_check_status": 2,
        "icon": null,
        "height": 512,
        "width": 512,
        "result": "http://beijing.xstore.qihu.com/qiyuan-hongtu/78c7a84b-015c-4878-98ac-81a9c15c4804.png",
        "prompt":"{\"prompt\": \"\美\好\的\幸\福\生\活\", \"negative_prompt\": \"\"}"
      }]
      const officalRes = [{
        "chain_no": "acMupWAL231221B6S0re",
        "template_id": 5383,
        "template_type": 2,
        "name": "新修改的官方",
        "desc": "",
        "type": 2,
        "is_deploy": 0,
        "deployment_id": 0,
        "template_check_status": 2,
        "icon": null,
        "height": 512,
        "width": 512,
        "result": "http://beijing.xstore.qihu.com/qiyuan-hongtu/78c7a84b-015c-4878-98ac-81a9c15c4804.png",
        "prompt":"{\"prompt\": \"\美\好\的\幸\福\生\活\", \"negative_prompt\": \"\"}"
      }]

      list = getTransferList(res)
      officalList = getTransferList(officalRes)
    }else if(pageName === 'tool') {
      const res = [{
        "id": 200,
        "team_id": 239,
        "template_check_status": -1,
        "api_name": "测试api",
        "api_desc": "api 描述描述描述",
      }]
      const officalRes =  [{
        "id": 200,
        "team_id": 239,
        "template_check_status": -1,
        "api_name": "官方测试api",
        "api_desc": "api 描述描述描述",
      }]
      list = getTransferList(res)
      officalList = getTransferList(officalRes)
    }
    setListData(list)
    setFilterListData(list)
    setOfficalListData(officalList)
  }
  useEffect(() => {
    initList()
  },[pageName, activeTab])

  return (
    <>
      <div className={commonListStyles.commonList}>
        <div className={commonListStyles.titleWrapper}>
          <span className={commonListStyles.title}>{pageConfig[pageName].pageTitle}</span>  
          <Input 
            addonBefore={<Select
              value='全部'
              style={{width: 80,borderRadius: '8px'}}
              options={[
                { value: '全部', label: '全部' },
                { value: '已部署', label: '已部署' },
                { value: '已公开', label: '已公开' },
                { value: '审核中', label: '审核中' }
              ]} 
              onChange={(e) => {
                setFilterListData([...listData.filter((item:any) => {
                  return item.deployStatus === e || item.publicStatus === e 
                })])
              }}
            />
          } 
            placeholder="请输入关键词搜索" 
            addonAfter={<img src={search.src} width='16' onClick={() => {
              setFilterListData([...listData.filter((item:any) => {
                return item.itemTitle.indexOf(searchValue) > -1
              })])
            }} />}
            className={'noBorderInput commonSearchInput'}
            onBlur={(e:any)=>{
              const event:any = e;
              const newName = event.target.value
              setFilterListData([...listData.filter((item:any) => {
                return item.itemTitle.indexOf(newName) > -1
              })])
            }}
            onChange={(e) => {
              setSearchValue(e.target.value)
            }}
            value={searchValue}
          />
        </div>
        <div className={commonListStyles.list + ' normalFont'}>
            <div 
              onClick={() => {setOpenAddItem(true)}}
              className={commonListStyles.item + ' ' +(activeTab === promptText ? '' : commonListStyles.imgItem) + ' ' + commonListStyles.addItem}>
              <div><img src={add.src} width={20} /></div>
              <span>{addTitle}</span>  
            </div>
            {filterListData.map((item:any, index:any)=>{
              if(activeTab === promptText) {
                return  <div 
                    onClick={() => {
                    }}
                    className={commonListStyles.item + ' ' +((index - 2) % 4 === 0 ? commonListStyles.lastItem : '')}>
                    <img src={pageConfig[pageName].iconUrl} width={24} />
                    <div className={commonListStyles.itemContent}>
                      <div className={commonListStyles.itemTitle}>
                        
                        {activeRenameIndex === index ?  <Input
                          className={commonListStyles.title + ' noBorderInput'}
                          value={activeFileName}
                          onBlur={(e:any)=>{
                              const event:any = e;
                              const newName = event.target.value.trim();
                              changeName(newName, item.itemId);
                          }}
                          onKeyUp={async(e) => {
                              const event:any = e;
                              const newName = event.target.value.trim()
                              if(event.keyCode === 13){
                                  changeName(newName, item.itemId);
                              }
                          }}
                          onChange={(e) => {
                              setActiveFileName(e.target.value.trim())
                          }}
                          onClick={(e)=> {
                              e.stopPropagation();
                          }}
                          />  : <span className={commonListStyles.title + ' singleLineEllipsis'}>{item.itemTitle}</span>}
                        {item.deployStatus && <span className={commonListStyles.successDeploy + ' ' + commonListStyles.status + ' normalFont'}>{item.deployStatus}</span>}
                        {item.publicStatus && <span className={(item.publicStatus === '审核中' ? commonListStyles.publicing : commonListStyles.successPublic) + ' ' + commonListStyles.status + ' normalFont'}>{item.publicStatus}</span> }
                      </div>
                      <div className={commonListStyles.itemDesc + ' multiLineEllipsis'} title={item.itemDesc}>{item.itemDesc}</div>
                    </div>
                    {getPopoverDom(item, index)}
                </div>
              }else if(isPromptImg) {
                return <div 
                    onClick={() => {
                      
                    }}
                    className={commonListStyles.item + ' ' + commonListStyles.imgItem + ' ' +((index - 3) % 5 === 0 ? commonListStyles.lastItem : '')}>
                    <div className={commonListStyles.promptImgWrapper}>
                      <img src={item.imgUrl} className={commonListStyles.promptImg} />
                    </div>
                    <div className={commonListStyles.itemContent}>
                      <div className={commonListStyles.itemTitle + ' ' + commonListStyles.imgItemTitle}>
                        <span className={commonListStyles.title + ' singleLineEllipsis'}>{item.itemTitle}</span>
                        {item.deployStatus && <span className={commonListStyles.successDeploy + ' ' + commonListStyles.status + ' normalFont'}>{item.deployStatus}</span>}
                        {item.publicStatus && <span className={(item.publicStatus === '审核中' ? commonListStyles.publicing : commonListStyles.successPublic) + ' ' + commonListStyles.status + ' normalFont'}>{item.publicStatus}</span> }
                      </div>
                    </div>
                    {getPopoverDom(item, index)}
                </div>
              }
              
            })}
        </div>
      </div>
      <Modal 
          open={openDelete}
          style={{width: 480}}
          className={'commonModal'}
          onCancel={() => setOpenDelete(false)}
          title={'删除确认'}
          onOk={() => {
            deleteItem()
            setOpenDelete(false)
          }}
          okText="确认"
          cancelText="取消"
      >
          <div>
              确认删除{deleteInfo.name}吗？
          </div>
      </Modal>
      <AddItemModal 
        openAddItem={openAddItem}
        setOpenAddItem={setOpenAddItem}
        addNewTitle={addNewTitle}
        addTitle={addTitle}
        iconUrl={pageConfig[pageName].iconUrl}
        officalListData={officalListData}
        pageName={pageName}
        length={filterListData.length + 1}
      />
    </>
  )
}
