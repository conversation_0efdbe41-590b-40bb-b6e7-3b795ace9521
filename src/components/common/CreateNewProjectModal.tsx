import { useState } from "react";
import { Modal, Checkbox, Button, message, Form,Input } from "antd";
const { TextArea } = Input;

import publicStyles from "@/styles/PublicModal.module.scss";
import { reqApplyProject } from "@/service/background/project";

export default function CreateNewProjectModal(prop: any) {
  const { title, cancelHandle, okHandel, openModal, setOpenModal } = prop;
  const [isTermChecked, setIsTermChecked] = useState(true);
  const [form] = Form.useForm();

  const onFinish = () => {  
    form.validateFields().then(async (values) => {
        console.log(values);
        try {
          const res = await reqApplyProject({team_name:values.title,application_information:values.scene});
          if(res.context.code !== 0){
            message.error(res.context.message);
            setOpenModal(false);
            return;
          }
          message.success("提交成功");
          setOpenModal(false);
          form.resetFields();
        } catch (error) {
          message.error("提交失败");
        }
    });

  }
  return (
    <>
      <Modal
        open={openModal}
        style={{ width: 480 }}
        className={"commonModal"}
        onCancel={() => {setOpenModal(false);form.resetFields();}}
        title={title ? title : "添加项目"}
        footer={
          <>
            <Button onClick={() => {setOpenModal(false);form.resetFields();}}>取消</Button>
            <Button
              type="primary"
              onClick={
                () => {
                      onFinish();
                 }
              }
            >
              提交审核
            </Button>
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
          onFinish={onFinish}
          style={{ width: "100%", padding: "0 8px"}}
        >
          <Form.Item required label="项目名称" name="title" rules={[{ required: true, message: '请输入项目名称' }]} >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          <Form.Item required label="使用场景" name="scene" rules={[{ required: true, message: '请输入使用场景' }]}>
          <TextArea rows={4} placeholder="请输入使用场景" maxLength={500} />
          </Form.Item>
          <div style={{marginTop:'5px',fontSize:'14px'}}>新增项目经过平台审核后自动开通，有问题联系g<EMAIL></div>
        </Form>
      </Modal>
    </>
  );
}
