import React, { useState, useEffect } from 'react';
import { Tooltip } from 'antd';
import ImageViewerModal from './ImageViewerModal';
import { TUTORIAL_IMAGES, preloadTutorialImages } from '../../utils/imagePreloader';
import styles from './TutorialGuide.module.scss';

interface TutorialGuideProps {
  className?: string;
}

interface ImageBubbleConfig {
  imageKey: number;
  bubbles: BubbleData[];
}
interface BubbleData {
  content: string;
  style: React.CSSProperties;
  direction: 'left' | 'right';
  showDot?: boolean;
}

// 使用预加载工具中的图片数组
const tutorialImages = TUTORIAL_IMAGES;

const bubbleConfig: ImageBubbleConfig[] = [
  {
    imageKey: 0,
    bubbles: [
      {
        content: "自由编排一个专家智能体",
        style: {
          left: '62.8%',
          bottom: '27%'
        },
        direction: "right",
        showDot: true
      },
      {
        content: "选择一个已有专家来解决问题",
        style: {
          left: '62.8%',
          bottom: '10%'
        },
        direction: "right",
        showDot: true
      }
    ]
  },
  {
    imageKey: 1,
    bubbles: [
      {
        content: "告诉专家怎么做",
        style: {
          right: '28.8%',
          top: '19.2%'
        },
        direction: "left",
        showDot: true
      },
      {
        content: "可以添加各种工具提升专家能力",
        style: {
          right: '28.8%',
          top: '56.6%'
        },
        direction: "left",
        showDot: true
      },
      {
        content: "可以用纳米知识库中的知识来提升专家准确性",
        style: {
          right: '28.8%',
          top: '64.6%'
        },
        direction: "left",
        showDot: true
      },
      {
        content: "可以切换不同模型评估效果",
        style: {
          right: '28.8%',
          top: '76.6%'
        },
        direction: "left",
        showDot: true
      },
    ]
  },
  {
    imageKey: 2,
    bubbles: [
      {
        content: "输入内容，自动批量执行",
        style: {
          right: '28.8%',
          top: '19.2%'
        },
        direction: "left",
        showDot: true
      }
    ]
  },
  {
    imageKey: 3,
    bubbles: [
      {
        content: "设置分支，自动选择路径执行 ",
        style: {
          right: '28.8%',
          top: '19.2%'
        },
        direction: "left",
        showDot: true
      }
    ]
  }
]

const TutorialGuide: React.FC<TutorialGuideProps> = ({ className }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  // 组件挂载时预加载教程图片
  useEffect(() => {
    const preloadImages = async () => {
      try {
        await preloadTutorialImages();
        setImagesPreloaded(true);
      } catch (error) {
        console.error('教程图片预加载失败:', error);
        // 即使预加载失败，也允许用户查看图片
        setImagesPreloaded(true);
      }
    };

    preloadImages();
  }, []);

  const handleOpenModal = () => {
    // 如果图片还没有预加载完成，先触发预加载
    if (!imagesPreloaded) {
      preloadTutorialImages().then(() => {
        setImagesPreloaded(true);
        setIsModalVisible(true);
      }).catch(() => {
        // 即使预加载失败，也允许用户查看图片
        setImagesPreloaded(true);
        setIsModalVisible(true);
      });
    } else {
      setIsModalVisible(true);
    }
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <Tooltip title="查看使用指引" placement="topRight">
        <div
          className={`${styles.tutorialGuide} ${className || ''}`}
          onClick={handleOpenModal}
        >
          <div className={styles.iconContainer}>
            <img
              src="https://s4.ssl.qhres2.com/static/d90cf6933a13081b.svg"
              alt="使用引导"
              className={styles.icon}
            />
          </div>
        </div>
      </Tooltip>

      <ImageViewerModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        images={tutorialImages}
        initialIndex={0}
        bubbleConfig={bubbleConfig}
      />
    </>
  );
};

export default TutorialGuide;
