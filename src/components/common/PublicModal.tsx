import { useEffect, useState } from 'react';
import { Tooltip, Modal, message, Switch } from 'antd';

import publicStyles from '@/styles/PublicModal.module.scss'

import { reqPublicPrompt, reqCancelSubmitPublicPrompt, reqCancelPublicPrompt } from '@/service/common'

import checkCircle from '@/images/checkCircle.png'

export default function PublicModal(prop:any) {
    const {openPublic, setOpenPublic, chainId, publicInfo, setPublicInfo} = prop;
    const [tagList, setTagList] = useState(new Array());
    const [checkedTagList, setCheckedTagList] = useState(new Array());
    const [isPublic, setIsPublic] = useState(true)
    const getTag = async () => {
        // const res = await reqTag({
        //     tag_type: 1
        // })
        // const options = res?.map((item:any) => {
        //     return  {
        //         value: item.id,
        //         label: <Tooltip title={item.desc}>
        //                     <span>{item.name}</span>
        //                 </Tooltip>
        //     }
        // })
        // setTagList(options)
    } 
    const reqPublic = async () => {
        const res = await reqPublicPrompt({
            chain_no: chainId,
            is_public: isPublic ? 1 : 0,
            tags: checkedTagList
        })
        if(res?.message === 'success') {
            message.success('保存成功')
            setOpenPublic(false)
            setPublicInfo({
                isPublic,
                tags: checkedTagList
            })
        }
    }
    useEffect(() => {
        getTag()
    },[])

    useEffect(()=>{
        setCheckedTagList(publicInfo.tags || [])
        setIsPublic(publicInfo.isPublic)
    },[publicInfo])

    useEffect(() => {
        if(!isPublic) {
            setCheckedTagList([])
        }
    },[isPublic])
    return (
        <>
            <Modal 
                open={openPublic}
                style={{width: 480}}
                className={'commonModal'}
                onCancel={() => setOpenPublic(false)}
                title={'公开'}
                onOk={() => {
                    reqPublic()
                }}
                okText="保存"
                cancelText="取消"
            >
                <div className={publicStyles.publicList}>
                    <div className={publicStyles.publicItem}>
                        <div className={publicStyles.publicLabel}>公开：</div>
                        <div>
                            <Switch checked={isPublic} onChange={() => {setIsPublic(!isPublic)}} />
                        </div>
                    </div>
                    <div className={publicStyles.publicItem + ' ' + publicStyles.lastItem}>
                        <div className={publicStyles.publicLabel}>标签：</div>
                        <div className={publicStyles.tagList}>
                            {tagList?.map(tag => {
                                return <span className={publicStyles.tagItem +' '+ (checkedTagList.includes(tag.value) ? publicStyles.tagCheckedItem : '')} onClick={() => {
                                    if(checkedTagList.includes(tag.value)){
                                        const index = checkedTagList.indexOf(tag.value)
                                        checkedTagList.splice(index, 1)
                                        setCheckedTagList([...checkedTagList])
                                    }else {
                                        checkedTagList.push(tag.value)
                                        setCheckedTagList([...checkedTagList])
                                    }
                                }}> 
                                    {checkedTagList.includes(tag.value) ? <img src={checkCircle.src} /> : ''}
                                    {tag.label}
                                </span>
                            })}
                        </div>
                    </div>
                </div>
            </Modal>
        </>
    )
}
