/**
 * @description: 分割线
 * @params {boolean} isDashed 是否虚线
 * @params {string} length 长度
 * @params {string} dashGap 虚线间距的2倍，例如：如果需要间距为10px，则dashGap为20px
 * @params {string} dashHeight 虚线高度或实线高度
 * @params {string} color 颜色
 * @params {string} borderRadius 圆角
 * @params {string} type 类型，row：水平分割线，col：垂直分割线
 * @params {object} style 样式,覆盖computedStyle，支持自定义
 * @return {*}
 */
import React from 'react';

interface DividerProps {
  isDashed?: boolean;
  length?: string;
  dashGap?: string;
  dashHeight?: string;
  color?: string;
  borderRadius?: string;
  type?: 'row' | 'col';
  style?: React.CSSProperties;
}

const Divider: React.FC<DividerProps> = ({
  isDashed = false,
  length = '100%',
  dashGap = '10px',
  dashHeight = '1px',
  color = '#EFEFEF',
  borderRadius = '0px',
  type = 'row',
  style = {},
}) => {
  const computedClass = isDashed ? 'dashedDivider' : 'solidDivider';
  const computedStyle =
    type === 'row'
      ? isDashed
        ? {
            background: `linear-gradient(to left, transparent 0%, transparent 50%, ${color} 50%, ${color} 100%)`,
            backgroundSize: `${dashGap} ${dashHeight}`,
            backgroundRepeat: 'repeat-x',
            width: length,
            height: dashHeight,
            borderRadius: borderRadius,
          }
        : {
            width: length,
            height: dashHeight,
            backgroundColor: color,
            borderRadius: borderRadius,
          }
      : isDashed
      ? {
          background: `linear-gradient(to top, transparent 0%, transparent 50%, ${color} 50%, ${color} 100%)`,
          backgroundSize: `${dashHeight} ${dashGap}`,
          backgroundRepeat: 'repeat-y',
          width: dashHeight,
          height: length,
          borderRadius: borderRadius,
        }
      : {
          width: dashHeight,
          height: length,
          backgroundColor: color,
          borderRadius: borderRadius,
        };

  return <div className={computedClass} style={{ ...computedStyle, ...style }} />;
};

export default Divider;
