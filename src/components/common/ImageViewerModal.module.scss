// 自定义动画关键帧
@keyframes fadeInMask {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInModal {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeOutModal {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
}

.imageViewerModal {
  /* Modal容器样式 */
  position: relative;
}

.modalContent {
  position: relative;
  width: auto; // 宽度随图片比例自适应
  height: auto; // 高度自适应内容
  max-width: 90vw; // 最大宽度限制
  max-height: 95vh; // 最大高度限制
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  // animation: slideInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin: 0 auto; // 水平居中
  display: flex;
  align-items: center;
  justify-content: center;
}

// 内容滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.imageContainer {
  position: relative;
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  overflow: hidden;
}

.image {
  width: auto;
  height: auto;
  max-width: 90vw;
  max-height: 95vh; // 图片最大高度为95vh
  object-fit: contain;
  border-radius: 12px;
  display: block;
}

// 图片切换动画
@keyframes imageSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.closeButton {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  color: #fff;
  font-size: 16px;
  // transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
  }
}

.pageIndicator {
  position: fixed;
  top: 20px;
  right: 60px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  z-index: 1001;
  backdrop-filter: blur(4px);
}

.arrowLeft,
.arrowRight {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  // transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}

.arrowLeft {
  left: 80px;
}

.arrowRight {
  right: 80px;
}

.arrowIcon {
  width: 20px;
  height: 20px;
  opacity: 0.8;
  // transition: opacity 0.3s ease;

  .arrowLeft:hover &,
  .arrowRight:hover & {
    opacity: 1;
  }
}

// // 响应式设计
// @media (max-width: 768px) {
//   .modalContent {
//     width: auto; // 宽度随图片比例自适应
//     height: auto; // 高度自适应内容
//     max-width: 95vw;
//     max-height: 90vh; // 在小屏幕上稍微减少最大高度
//   }

//   .image {
//     max-height: 90vh; // 图片在小屏幕上的最大高度
//   }

//   .arrowLeft,
//   .arrowRight {
//     width: 40px;
//     height: 40px;
//   }

//   .arrowLeft {
//     left: 40px;
//   }

//   .arrowRight {
//     right: 40px;
//   }

//   .arrowIcon {
//     width: 16px;
//     height: 16px;
//   }

//   .closeButton {
//     width: 28px;
//     height: 28px;
//     font-size: 14px;
//     top: 16px;
//     right: 16px;
//   }

//   .pageIndicator {
//     top: 16px;
//     right: 52px;
//     padding: 4px 8px;
//     font-size: 12px;
//   }
// }

// @media (max-width: 480px) {
//   .modalContent {
//     width: auto; // 宽度随图片比例自适应
//     height: auto; // 高度自适应内容
//     max-width: 98vw;
//     max-height: 85vh; // 在更小屏幕上进一步减少最大高度
//   }

//   .image {
//     max-height: 85vh; // 图片在更小屏幕上的最大高度
//   }

//   .arrowLeft,
//   .arrowRight {
//     width: 36px;
//     height: 36px;
//   }

//   .arrowLeft {
//     left: 20px;
//   }

//   .arrowRight {
//     right: 20px;
//   }
// }
