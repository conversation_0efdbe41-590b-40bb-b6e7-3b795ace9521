import React from "react"
import styles from "./BubbleTooltip.module.scss"

interface BubbleTooltipProps {
  content: string
  direction?: "left" | "right"
  showDot?: boolean
  style?: React.CSSProperties
}

const BubbleTooltip: React.FC<BubbleTooltipProps> = ({
  content,
  direction = "left",
  showDot = true,
  style = {}
}) => {
  const DotIcon = () => (
    <img src="https://s5.ssl.qhres2.com/static/8981edabeb39e098.svg" alt="" />
  )

  return (
    <div
      className={`${styles.bubbleTooltip} ${styles[direction]}`}
      style={style}
    >
      {/* 气泡内容 */}
      <div className={styles.bubbleContent}>{content}</div>

      {/* 圆点 */}
      {showDot && (
        <div className={styles.bubbleDot}>
          <DotIcon />
        </div>
      )}
    </div>
  )
}

export default BubbleTooltip
