declare const window: any;

import React, {
  useEffect,
  useState,
  useRef,
} from "react";
import { Mo<PERSON>, Button, Popover } from "antd";
import Router from "next/router";
import { useRouter } from "next/router";
import * as constants from "@/constants/appConstants";
import user from "@/images/menu/user.png";
import logoutIcon from "@/images/menu/logout.png";

import {
  reqLoginToken,
  reqLoginUrl,
  reqLogout,
  reqLoginWaiWang,
  reqLogoutWaiWang,
} from "@/service/login";

import { reqUserInfo, reqProjectSelectList } from "@/service/common";
import { waiWangHosts } from '@/config/commonConfig';

import homeStyles from "@/styles/Home.module.scss";
import ailogo from "@/images/ailogo.png";
import logo from "@/images/menu/agent-logo.png";
import logocontent from "@/images/menu/logocontent.svg";
import goBtn from "@/images/menu/goBtn.svg";
import { isNullOrEmpty } from "@/utils/common";
import dynamic from "next/dynamic";
import { goToLoginWithClientUserInfo, goToLoginWithYunPanToken, goToLoginWithCompanyBrainToken, goClientUserInfoCommon } from "@/utils/tuituiLogin.js";

const MobileHomePageNoSSR = dynamic(() => import('@/components/home/<USER>'), {
  ssr: false
})


let isInitPage = false;
export default function HomePage() {
  const router = useRouter();
  const { pathname } = router
  const ticket = router.query.ticket;
  const [userName, setUserName] = useState("");
  const querySrc = router.query.src;
  const [isZhiNao, setIsZhiNao] = useState(false);
  const [isOuterNet, setIsOuterNet] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [isMobile, setIsMobile] = useState(false); // 是否是移动端
  const [isFromTuituiBoard, setIsFromTuituiBoard] = useState(false); // 是否来自推推看板
  const [isFromZhiYuBoard, setIsFromZhiYuBoard] = useState(false); // 是否来自织语看板
  const [isFromYunPanBoard, setIsFromYunPanBoard] = useState(false); // 是否来自云盘看板
  const [isFromCompanyBrainBoard, setIsFromCompanyBrainBoard] = useState(false); // 是否来自企业大脑看板
  const [isFromQpaasBoard, setIsFromQpaasBoard] = useState(false); // 是否来自QPaas平台
  const [isFromLlmopsBoard, setIsFromLlmopsBoard] = useState(false); // 是否来自llmops平台
  const [isVisibleLoginOut, setIsVisibleLoginOut] = useState(true);
  const [userInfo, setUserInfo] = useState({
    user_id: 0,
    username: "",
    email: "",
    phone_number: "",
    // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
    system_role: 0
  });
  const [isReloadLoginBySearch, setIsReloadLoginBySearch] = useState(true);
  const [isScroll, setIsScroll] = useState(false)
  const [showVideo, setShowVideo] = useState(false)
  const videoRef = useRef(null)

  // 判断location search是否改变
  const isChangeLocationSearch = () => {
    let searchVal = localStorage.getItem(constants.prompt_isReloadLoginBySearch) || '';
    console.log('home', location.search, searchVal, location.search !== searchVal)
    return isFromZhiYuBoard && searchVal !== location.search && isReloadLoginBySearch;
  }
  // 来自推推看板或是织语看板 做单点登录
  const goToSignalLoginFromBoard = async () => {
    if (!isFromTuituiBoard && !isFromZhiYuBoard && !isFromYunPanBoard && !isFromCompanyBrainBoard && !isFromQpaasBoard && !isFromLlmopsBoard) {
      return;
    }
    setIsReloadLoginBySearch(false);
    localStorage.setItem(constants.prompt_isReloadLoginBySearch, location.search);

    let res = null;
    if (isFromYunPanBoard) {
      res = await goToLoginWithYunPanToken();
    } else if (isFromCompanyBrainBoard) {
      res = await goToLoginWithCompanyBrainToken();
    } else if (isFromTuituiBoard || isFromZhiYuBoard) {
      res = await goToLoginWithClientUserInfo(isFromTuituiBoard ? 1 : 2);
    } else {
      let source_type = '';
      if (isFromQpaasBoard) {
        source_type = 'qpaas';
      }
      else if (isFromLlmopsBoard) {
        source_type = 'llmopsBoard';
      }
      source_type && (res = await goClientUserInfoCommon(source_type));
    }
    // const res = isFromYunPanBoard ? await goToLoginWithYunPanToken() :
    //   (isFromCompanyBrainBoard ? await goToLoginWithCompanyBrainToken() :
    //     await goToLoginWithClientUserInfo(isFromTuituiBoard ? 1 : 2));

    if (res && res.token) {
      localStorage.setItem(constants.prompt_authorization, "Bearer " + res.token);
      getUserInfo();
    } else {// 单点登录报错后，走正常登录逻辑
      console.log('单点登录报错----->')
      localStorage.setItem(constants.prompt_isTuituiBoard, '');
      localStorage.setItem(constants.prompt_isZhiYuBoard, '');
      localStorage.setItem(constants.prompt_isYunPanBoard, '');
      localStorage.setItem(constants.prompt_isCompanyBrainBoard, '');
      localStorage.setItem(constants.prompt_userName, '');
      localStorage.setItem(constants.prompt_authorization, '');
      localStorage.setItem(constants.prompt_teamId, '');
      localStorage.setItem(constants.prompt_targetUrl, '')
      localStorage.setItem(constants.prompt_isReloadLoginBySearch, '');
      localStorage.setItem(constants.prompt_isQpaasBoard, '')
      localStorage.setItem(constants.prompt_isLlmopsBoard, '')
      setIsFromTuituiBoard(false);
      setIsFromZhiYuBoard(false);
      setIsFromYunPanBoard(false);
      setIsFromCompanyBrainBoard(false);
      setIsFromQpaasBoard(false);
      setIsFromLlmopsBoard(false);
      Router.replace('/');
    }
  }

  const getToken = async () => {
    const res = await reqLoginToken({
      ticket,
    });
    if (res && res.token) {
    }

    // localStorage.setItem(constants.prompt_authorization, "Bearer " + res.token);
    getUserInfo();
  };
  /** 内外网登录用户获取用户信息 */
  const getUserInfo = async () => {
    console.log('HomePage.ts----------------------getUserInfo')
    const res = await reqUserInfo({});
    if (isNullOrEmpty(res)) {
      return;
    }
    // 处理亿方云盘登录的默认链接跳转问题
    if (isFromYunPanBoard || isFromCompanyBrainBoard) {
      console.log('window.location.href----------------------:', window.location.href)
      localStorage.setItem(constants.prompt_targetUrl, window.location.href);
    }
    localStorage.setItem(constants.prompt_userId, res.user_id);
    localStorage.setItem(constants.prompt_userName, res.username);
    const resUserName = res.username;
    setUserName(resUserName);
    setUserInfo({
      ...userInfo,
      user_id: res.user_id,
      username: resUserName,
      email: res.email,
      phone_number: res.phone_number,
      system_role: res.system_role
    });

    // 触发自定义事件通知其他组件
    const event = new CustomEvent(constants.prompt_systemRoleUpdated, {
      detail: { systemRole: res.system_role }
    });
    (typeof window !== 'undefined') && window.dispatchEvent(event);
    // const phone_number = res.phone_number;
    const isFirstLogin = localStorage.getItem(constants.prompt_isFirstLogin) === 'true' ? true : false; // 是否是可以自动登录逻辑
    if (!isFirstLogin) {
      localStorage.setItem(constants.prompt_isFirstLogin, 'false');
      goToAgents()
    }
  }

  const goToAgents = async() => {
    // 判断是否未登录之前的跳转
    const prompt_targetUrl = localStorage.getItem(constants.prompt_targetUrl);
    console.log("🚀 ~ goToAgents ~ prompt_targetUrl----------------------:", prompt_targetUrl)
    if (prompt_targetUrl && prompt_targetUrl.length > 0) {
      Router.replace(prompt_targetUrl);
      localStorage.setItem(constants.prompt_targetUrl, '');
      return;
    }
    const localTeamId = localStorage.getItem(constants.prompt_teamId);


    const res = await reqProjectSelectList({})
    let myProjectId = ''
    res?.data?.forEach((p) => {
      if(p.team_type == 4) {
        myProjectId = p.id
      }
    })
    
    if (localTeamId) {
      Router.replace(`/flowList?teamId=${localTeamId}`);// 内网用户登录成功，去发现页面
    } else {
      Router.replace(`/flowList?teamId=${myProjectId}`);// 内网用户登录成功，去发现页面
    }
  }

  useEffect(() => {
    if (ticket) {
      getToken();
      return;
    } else {
      if (isInitPage) {
        return;
      }
    }
    // 智能体sdk使用页面，不需要登录逻辑
    if (pathname.indexOf('agentSDK') > -1) {
      return
    }

    if (localStorage.getItem(constants.prompt_userName)) { // 已经登录的逻辑
      setUserName(localStorage.getItem(constants.prompt_userName) || "");
      // console.log('已经登录的逻辑')
    }
    getUserInfo();
    isInitPage = true;
  }, [ticket]);

  useEffect(() => {
    const prompt_userName = localStorage.getItem(constants.prompt_userName);
    if ((!(prompt_userName && prompt_userName.length > 0) && (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard)) || (isChangeLocationSearch())) { // 未登录的情况下，来自推推看板或是织语看板 做单点登录
      goToSignalLoginFromBoard();
    }
  }, [isFromTuituiBoard, isFromZhiYuBoard, isFromYunPanBoard, isFromCompanyBrainBoard, isFromQpaasBoard, isFromLlmopsBoard]);

  useEffect(() => {
    if (localStorage.getItem(constants.isZhiNao) === "yes") {
      setIsZhiNao(true);
    }

    if (localStorage.getItem(constants.prompt_isTuituiBoard) === "true" || window.location.href.indexOf("tuituiBoard") !== -1) {
      setIsFromTuituiBoard(true);
      localStorage.setItem(constants.prompt_isTuituiBoard, "true");
    }
    if (localStorage.getItem(constants.prompt_isZhiYuBoard) === "true" || window.location.href.indexOf("zhiyuBoard") !== -1) {
      setIsFromZhiYuBoard(true);
      localStorage.setItem(constants.prompt_isZhiYuBoard, "true");
    }
    if (localStorage.getItem(constants.prompt_isYunPanBoard) === "true" || window.location.href.indexOf("yunpanBoard") !== -1) {
      setIsFromYunPanBoard(true);
      localStorage.setItem(constants.prompt_isYunPanBoard, "true");

    }

    if (localStorage.getItem(constants.prompt_isCompanyBrainBoard) === "true" || (window.location.href.indexOf("companyBrain") !== -1)) {
      setIsFromCompanyBrainBoard(true);
      localStorage.setItem(constants.prompt_isCompanyBrainBoard, "true");
    }

    if (localStorage.getItem(constants.prompt_isQpaasBoard) === "true" || (window.location.href.indexOf("qpaasBoard") !== -1)) {
      setIsFromQpaasBoard(true);
      localStorage.setItem(constants.prompt_isQpaasBoard, "true");
    }

    if (localStorage.getItem(constants.prompt_isLlmopsBoard) === "true" || (window.location.href.indexOf("llmopsBoard") !== -1)) {
      setIsFromLlmopsBoard(true);
      localStorage.setItem(constants.prompt_isLlmopsBoard, "true");
    }

    if (getHrefIncludes([constants.prompt_isZhiKeBoard])) {
      setIsVisibleLoginOut(false);
    }


    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      setIsOuterNet(waiWangHosts.includes(hostname));
    }
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

    setIsMobile(isMobileDevice)

    window.addEventListener('scroll', listenerScroll);
    return () => {
      window.removeEventListener('scroll', listenerScroll);
    }
  }, []);

  useEffect(() => {
    if (querySrc === "zhinao") {
      localStorage.setItem(constants.isZhiNao, "yes");
      setIsZhiNao(true);
    }
  }, [querySrc]);

  const listenerScroll = () => {
    if (window.scrollY > 0) {
        setIsScroll(true)
    } else {
        setIsScroll(false)
    }
  }


  const getHrefIncludes = (contentList: string[]) => {
    let flag = false;
    if (contentList.includes(localStorage.getItem(constants.prompt_platform) || '')) {
      return true;
    }
    contentList.forEach((item: any) => {
      if (window.location.href.includes('source=' + item)) {
        flag = true;
      }
    });
    return flag;
  }

  const login = async () => {
    console.log("🚀 ~ login ~ login----------------------:");
    const res = await reqLoginUrl({});
    Router.push(res.login_url);
  };
  const logout = async () => {
    const res = await reqLogout({});
    // Router.push(res.logout_url)
    // location.reload();
    clearUserInfoAndStatus();
  };

  const loginWaiWang = async (name: any) => {
    const res = await reqLoginWaiWang({});
    if (!res?.is_permitted) {
      // true 在白名单里 false 不在白名单

      modal.info({
        title: '提示',
        // content: <label>您好，请您发送账户名称和使用场景到 <br /> ******************申请试用，感谢关注。</label>,
        content: <label>您好，<a href='https://daily.fe.qihoo.net/superai-web-1024/index.html'>点击链接</a>，进入Agent智能体开发大赛页面申请试用，感谢您的关注！</label>
      });
      return false;
    }


    if (res?.token) {
      localStorage.setItem(constants.prompt_authorization, "Bearer " + res?.token);
      localStorage.setItem(constants.prompt_userName, name);
      // setIsLogin(true);
      /**外网登录用户 缺少 用户Id本地设置*/
      // localStorage.setItem(constants.prompt_userId, res?.user_id); ??
    }
    setUserName(name);
    getUserInfo();
  };

  const logoutWaiWang = async () => {
    const res = await reqLogoutWaiWang({});

    if (typeof window !== "undefined") {
      const jquery: any = window.$;
      jquery.getScript("//s.ssl.qhimg.com/quc/quc7.js").then(function () {
        const q: any = window.QHPass;
        q.signOut();
      });
    }

    clearUserInfoAndStatus();
  };

  const clearUserInfoAndStatus = () => {
    setUserName("");
    // setIsLogin(false);
    setUserInfo({
      user_id: 0,
      username: "",
      email: "",
      phone_number: "",
      // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
      system_role: 0
    })
    localStorage.setItem(constants.prompt_userId, "");
    localStorage.setItem(constants.prompt_userName, "");
    localStorage.setItem(constants.prompt_authorization, "");
    localStorage.removeItem(constants.prompt_enable_canvas)
  }

  const goToTryHandel = () => {
    if (localStorage.getItem(constants.prompt_userName)) { // 已经登录的逻辑
      setUserName(localStorage.getItem(constants.prompt_userName) || "");
      getUserInfo();
    } else {
      if (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard) {
        goToSignalLoginFromBoard()
        return;
      }
      if (isOuterNet) {
        if (typeof window !== "undefined") {
          const jquery: any = window.$;
          jquery
            .getScript("//s.ssl.qhimg.com/quc/quc7.js")
            .then(function () {
              const q: any = window.QHPass;
              q.init({ src: "pcw_agent", primaryColor: '#006BFF' });
              q.signIn(function () {
                q.getUserInfo(
                  function (u: any) {
                    console.log(u, 111);
                    setUserName(u.username);
                    loginWaiWang(u.username);
                  },
                );
              });
            });
        }
      } else {
        login()
      }
    }
    localStorage.setItem(constants.prompt_isFirstLogin, 'false');
  }

  const goToVideoHandel = () =>{
    setShowVideo(true);
  }

  const closeVideoHandel = () =>{
    if(videoRef.current){
      videoRef.current.pause();
    }
    setShowVideo(false);
  }

  console.log(isFromTuituiBoard, isFromZhiYuBoard, isFromYunPanBoard, isFromCompanyBrainBoard, isFromQpaasBoard, isFromLlmopsBoard, isOuterNet, 11)

  return (
    isMobile ? <MobileHomePageNoSSR goToTryHandel={goToTryHandel} />
      : (<div className={homeStyles.homeBg}>
        <div className={`${homeStyles.videoModal} ${showVideo ? homeStyles.vedioActive : ''}`}>
          <div className={homeStyles.modelContent}>
            <div className={homeStyles.videoClose} onClick={closeVideoHandel}></div>
            <video ref={videoRef} className={homeStyles.video} src="https://p1.zhycdn.com/zyun-public-use/Seaf_promotional_video_HD_2025_07_30.mp4" controls></video>
          </div>

        </div>
        <div className={`${homeStyles.header} ${isScroll ? homeStyles.headerActive : ''}`}>
          <div  className={homeStyles.headerInner}>
            <a className={homeStyles.title} href="/">
              {isZhiNao ? (
                <img src={ailogo.src} height="35" />
              ) : (
                <img src={logo.src} height="31" style={{marginTop: 4}} />
              )}
            </a>
            <div className={homeStyles.userBox}>
              <a href="https://bot.n.cn/tools/aiagent" target="_blank">案例研究</a>
              <a href="https://easydoc.soft.360.cn/doc?project=e0dafabd6582cf6faa2290b953f105e0&doc=c90cb1593ea4a909dbb18f7fdd618a3d&config=title_menu_toc" target="_blank">文档中心</a>
              <div className={homeStyles.login}>
                {userName ? (
                  <div className={homeStyles.myAgent} >
                    <Popover
                      title=""
                      trigger="hover"
                      arrow={false}
                      placement={"bottom"}
                      content={
                        <div className={homeStyles.userInfoWrapper}>
                          <div className={homeStyles.userNameBg}>
                            <img
                              src={user.src}
                              width={24}
                              className={homeStyles.headSculpture}
                            />
                            <span className={"boldFont"}>{userInfo.username}</span>
                          </div>
                          {isVisibleLoginOut ? <div className={homeStyles.userInfoInner}>
                            <img src={logoutIcon.src} width="14" />
                            <span onClick={isOuterNet ? logoutWaiWang : logout}>
                              退出登录
                            </span>
                          </div> : null}
                        </div>
                      }>
                      <div className={homeStyles.userContent} onClick={goToTryHandel}>
                        <img src={user.src} alt="" />
                        <span>{userInfo.username}</span>
                      </div>
                    </Popover>
                  </div>
                ) : (
                  <span
                    id="signIn"
                    className={homeStyles.loginBtn}
                    onClick={
                      (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard) ? goToSignalLoginFromBoard : (
                        isOuterNet
                          ? () => {
                            if (typeof window !== "undefined") {
                              const jquery: any = window.$;
                              jquery
                                .getScript("//s.ssl.qhimg.com/quc/quc7.js")
                                .then(function () {
                                  const q: any = window.QHPass;
                                  q.init({ src: "pcw_agent", primaryColor: '#006BFF' });
                                  q.signIn(function () {
                                    q.getUserInfo(
                                      function (u: any) {
                                        console.log(u, 222)
                                        setUserName(u.username);
                                        loginWaiWang(u.username);
                                      },
                                    );
                                  });
                                });
                            }
                          }
                          : login
                      )
                    }
                  >
                    <span>开始探索</span>  
                  </span>
                )}
              </div>
            </div>
          </div>
          
        </div>
        <div className={homeStyles.homeBody}>
          <div className={homeStyles.homeBodyContent}>
            <div className={homeStyles.bodyTitle}>
              <img src={logocontent.src} width={500} />
            </div>
            <div className={homeStyles.bodySubTitle}>
              全球首个多智能体协同平台。全程自然语言编程极简搭建，全程解放人力自动执行。个人及组织皆可自主创建专属智能体。不同领域智能体可自由组队协作，助力个人成为超级个体，企业变身超级组织。已累计创建数以万计领域专家型智能体，支撑中国最大智能体社区运行。
            </div>
            <div className={homeStyles.bodyBtnWrapper}>
              <img src={goBtn.src} className={homeStyles.bodyBtn} onClick={goToTryHandel} />
              <span className={homeStyles.videoBtn} onClick={goToVideoHandel}>观看视频</span>
            </div>
            <div className={homeStyles.contentWrapper}>
              <img src="https://p2.ssl.qhimg.com/t110b9a93014bac4e3905ad04d9.png" className={homeStyles.contentWrapper} alt="" />
            </div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <h2 className={homeStyles.contentTitle}>
              赋能下一代多智能体协作
              <img className={homeStyles.titleStar} src="https://s0.ssl.qhres2.com/static/b022460b258d0a0c.svg" alt="" />
            </h2>
            <div className={homeStyles.homeContentSecond}>
              <div className={homeStyles.contentFirst}>
                <div className={homeStyles.contentLeft}>
                  <div className={homeStyles.contentLeftTitle}>自然语言设计</div>
                  <p className={homeStyles.contentLeftDesc}>无需编写任何代码，只需自然语言即可低门槛搭建专家智能体，专家智能体会智能识别用户描述中的条件判断语句、逻辑连接词（如"如果"、"否则"、"当"、"则"等）以及相应的执行动作，然后将这些语义信息转换为可执行的分支逻辑。不同专家智能体可以便捷组队来完成复杂任务。</p>
                  <div className={homeStyles.contentLeftBtns}>
                    <span className={homeStyles.contentLeftBtn}>调用MCP</span>
                    <span className={homeStyles.contentLeftBtn}>多模态处理</span>
                    <span className={homeStyles.contentLeftBtn}>任务规划、反思</span>
                    <span className={homeStyles.contentLeftBtn}>实现分支、迭代循环</span>
                  </div>
                </div>
              </div>
              <div className={homeStyles.contentSecond}>
                <div className={homeStyles.contentSecondItem}>
                  <div className={homeStyles.contentLeftTitle}>高阶迭代纠错</div>
                  <div className={homeStyles.contentLeftDesc}>具备类人脑的反思和改进能力，能够在动态变化的任务环境中保持高质量的执行效果</div>
                </div>
                <div className={homeStyles.contentSecondItem}>
                  <div className={homeStyles.contentLeftTitle}>执行超长任务</div>
                  <p className={homeStyles.contentLeftDesc}>对Agent的关键节点进行拆分，增强长文本稳定性以及降低token调用成本</p>
                </div>
                <div className={homeStyles.contentSecondItem}>
                  <div className={homeStyles.contentLeftTitle}>虚拟工作空间</div>
                  <p className={homeStyles.contentLeftDesc}>通过可视化连线方式将各个工作流节点串联后，系统会建立统一的数据共享环境</p>
                </div>
              </div>
              <div className={homeStyles.contentThird}>
                <div className={homeStyles.contentThirdItem}>
                  <div className={homeStyles.contentLeftTitle}>Agent 蜂群智慧</div>
                  <div className={homeStyles.contentLeftDesc}>通过任务拆分，将复杂工作分解为多个专业化的Agent来执行，每个Agent各司其职且相互协同，实现高效的分工合作模式。</div>
                </div>
                <div className={homeStyles.contentThirdItem}>
                  <div className={homeStyles.contentLeftTitle}>并行任务提效</div>
                  <div className={homeStyles.contentLeftDesc}>通过先进的迭代并行处理技术，能够智能识别输入数据组中的多个独立项目，并对每个项目同时执行相同的处理流程，显著提升Agent在处理大规模数据时的工作效率。</div>
                </div>
              </div>
              <div className={homeStyles.contentFourth}>
                <div className={homeStyles.contentLeft}>
                  <div className={homeStyles.contentLeftTitle}>直观的UI交互</div>
                  <p className={homeStyles.contentLeftDesc}>工作流执行过程中，用户可对工作流实现选择、修改、打断、过程可审计的相关操作。</p>
                  <div className={homeStyles.contentLeftBtns}>
                    <span className={homeStyles.contentLeftBtn}>选择</span>
                    <span className={homeStyles.contentLeftBtn}>修改</span>
                    <span className={homeStyles.contentLeftBtn}>打断</span>
                    <span className={homeStyles.contentLeftBtn}>过程可审计</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <h2 className={homeStyles.contentTitle}>
              丰富的MCP能力
              <img className={homeStyles.titleStar} src="https://s0.ssl.qhres2.com/static/b022460b258d0a0c.svg" alt="" />
            </h2>
            <div className={homeStyles.contentImg}></div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <h2 className={homeStyles.contentTitle}>
              强大的知识库支撑
              <img className={homeStyles.titleStar} src="https://s0.ssl.qhres2.com/static/b022460b258d0a0c.svg" alt="" />
            </h2>
            <div className={homeStyles.homeContentThird}>
              <div className={homeStyles.homeContentLeft}>
                <div className={homeStyles.contentLeftItem}>
                  <div className={homeStyles.contentLeftTitle}>
                    <span>数据解析</span>
                  </div>
                  <div className={homeStyles.contentLeftDesc}>多模态输入智能化处理。支持PDF、 Word、 Excel、 PPT、 图片（含OCR)。高精度解析复杂文档、公式、表格、图片内容、语义标注。</div>
                </div>
                <div className={homeStyles.contentLeftItem}>
                  <div className={homeStyles.contentLeftTitle}>
                    <span>数据切分与嵌入</span>
                  </div>
                  <div className={homeStyles.contentLeftDesc}>平衡语义与效率的智能分块。固定分块、语义分块、重叠策略；生成语义向量、构建层级结构，保留其前后上下文信息。</div>
                </div>
                <div className={homeStyles.contentLeftItem}>
                  <div className={homeStyles.contentLeftTitle}>
                    <span>多方向召回</span>
                  </div>
                  <div className={homeStyles.contentLeftDesc}>精准匹配用户需求的检索。向量检索、关键词检索、混合检索：对召回结果二次排序，提升Top-K准确性。</div>
                </div>
              </div>
              <div className={homeStyles.homeContentRight}></div>
            </div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <h2 className={homeStyles.contentTitle}>
              多专家Agent团队
              <img className={homeStyles.titleStar} src="https://s0.ssl.qhres2.com/static/b022460b258d0a0c.svg" alt="" />
            </h2>
            <div className={homeStyles.homeContentFourth}>
              <div className={homeStyles.homeContentLeft}></div>
              <div className={homeStyles.homeContentRight}>
                <div className={homeStyles.contentRightTitle}>
                  <span>模拟人类专家团队的</span>
                  <span>协作模式</span>
                </div>
                <div className={homeStyles.contentRightList}>
                  <div className={homeStyles.contentRightItem}>
                    <img src="https://s2.ssl.qhres2.com/static/9a280d8b3c509548.svg" alt="" />
                    <span>分解复杂任务流程</span>
                  </div>
                  <div className={homeStyles.contentRightItem}>
                    <img src="https://s1.ssl.qhres2.com/static/cf3d3135708710c5.svg" alt="" />
                    <span>专业智能体分工处理</span>
                  </div>
                  <div className={homeStyles.contentRightItem}>
                    <img src="https://s3.ssl.qhres2.com/static/58a72685846a9c72.svg" alt="" />
                    <span>构成高效解决网络</span>
                  </div>
                  <div className={homeStyles.contentRightItem}>
                    <img src="https://s5.ssl.qhres2.com/static/1e7db4b009992ab9.svg" alt="" />
                    <span>推进处理复杂任务</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <h2 className={homeStyles.contentTitle}>
              配套智能体客户端
              <img className={homeStyles.titleStar} src="https://s0.ssl.qhres2.com/static/b022460b258d0a0c.svg" alt="" />
            </h2>
            <div className={homeStyles.homeContentFiveth}>
              <div className={homeStyles.homeContentTop}>
                <div className={homeStyles.contentTopList}>
                  <div className={homeStyles.contentTopItem}>
                    <div>
                      <img src="https://s0.ssl.qhres2.com/static/896477c0fda58d95.svg" alt="" />
                      <span>运行可视可交互</span>
                    </div>
                    <div>
                      <img src="https://s4.ssl.qhres2.com/static/99e2b15705645e08.svg" alt="" />
                      <span>运行过程可自由配置界面交互</span>
                    </div>
                    <div>
                      <img src="https://s4.ssl.qhres2.com/static/99e2b15705645e08.svg" alt="" />
                      <span>规划过程、运行过程可人员参与交互</span>
                    </div>
                  </div>
                  <div className={homeStyles.contentTopItem}>
                    <div>
                      <img src="https://s3.ssl.qhres2.com/static/f6753d739e064ed9.svg" alt="" />
                      <span>统一智能体入口</span>
                    </div>
                    <div>
                      <img src="https://s4.ssl.qhres2.com/static/99e2b15705645e08.svg" alt="" />
                      <span>智能体使用和交互集中管理</span>
                    </div>
                    <div>
                      <img src="https://s4.ssl.qhres2.com/static/99e2b15705645e08.svg" alt="" />
                      <span>企业SSO、IAM系统打通，实现按权限定制入口内容</span>
                    </div>
                  </div>
                  <div className={homeStyles.contentTopItem}>
                    <div>
                      <img src="https://s1.ssl.qhres2.com/static/a58b51458ef007e8.svg" alt="" />
                      <span>执行力更强</span>
                    </div>
                    <div>
                      <img src="https://s4.ssl.qhres2.com/static/99e2b15705645e08.svg" alt="" />
                      <span>可调用本地桌面程序、访问本地文件系统、网络、USB设备、打印服务等资源</span>
                    </div>
                    <div>
                      <img src="https://s4.ssl.qhres2.com/static/99e2b15705645e08.svg" alt="" />
                      <span>支持任务调度、本地缓存、定时触发、跨任务依赖执行</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className={homeStyles.homeContentBottom}>
              </div>
            </div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <h2 className={homeStyles.contentTitle}>
              场景案例
              <img className={homeStyles.titleStar} src="https://s0.ssl.qhres2.com/static/b022460b258d0a0c.svg" alt="" />
            </h2>
            <div className={homeStyles.homeContentSixth}>
              <div className={homeStyles.homeContentLeft}>
                <div className={homeStyles.homeContentLeftTitle}>
                  <img src="https://s2.ssl.qhres2.com/static/e3d2efa3f259c521.svg" alt="" />
                  <span>纳米AI超级搜索</span>
                </div>
                <div className={homeStyles.homeContentLeftContent}>
                  <p>纳米AI超级搜索是基于SEABOT框架构建的企业级智能搜索中枢，集成语义检索、多源数据聚合、知识图谱理解与智能问答生成等能力，通过对企业内部外部知识的统一建模与智能调取，重望信息获取与决策支持流程，帮助组织快速定位关键内容、精准获取答案、提升知识利用效率。</p>
                </div>
              </div>
              <div className={homeStyles.homeContentRight}>
                <div className={homeStyles.homeContentLeftTitle}>
                  <img src="https://s0.ssl.qhres2.com/static/80e87aa5456bea6b.svg" alt="" />
                  <span>小安智面</span>
                </div>
                <div className={homeStyles.homeContentLeftContent}>
                  <p>小安智面是基于SEABOT框架构建的AI招聘智能体，集成多模态简历解析、岗位匹配、智能面试问答与候选人意图识别等能力，通过对招聘全流程的智能化重构，帮助企业大幅提升筛人效率、决策精准度与候选人体验。</p>
                </div>
              </div>
            </div>
          </div>
          <div className={homeStyles.homeBodyContent}>
            <div className={homeStyles.homeContentLast}>
              <div className={homeStyles.homeContentLastTitle}>立即探索，成为超级个体</div>
              <div className={homeStyles.homeContentLastBtns}>
                {/* <span className={homeStyles.homeContentLastBtn}>方案咨询</span> */}
                <span className={homeStyles.homeContentLastBtn}
                  onClick={
                    (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromQpaasBoard || isFromLlmopsBoard) ? goToSignalLoginFromBoard : (
                      isOuterNet
                        ? () => {
                          if (typeof window !== "undefined") {
                            const jquery: any = window.$;
                            jquery
                              .getScript("//s.ssl.qhimg.com/quc/quc7.js")
                              .then(function () {
                                const q: any = window.QHPass;
                                q.init({ src: "pcw_agent", primaryColor: '#006BFF' });
                                q.signIn(function () {
                                  q.getUserInfo(
                                    function (u: any) {
                                      console.log(u, 222)
                                      setUserName(u.username);
                                      loginWaiWang(u.username);
                                    },
                                  );
                                });
                              });
                          }
                        }
                        : login
                    )
                  }
                >开始探索</span>
              </div>
            </div>
          </div>
        </div>
        <div className={homeStyles.homeFooter}>
          <div className={homeStyles.footer}>
            <div className={homeStyles.footerLogo}>
              <div></div>
            </div>
            <div className={homeStyles.footerLine}></div>
            <div className={homeStyles.footerContent}>
              <div className={homeStyles.footerContentLeft}>
                <div className={homeStyles.footerContentLeftItem}>
                  <div>360产品</div>
                  <div className={homeStyles.footerContentLeftItemLink}>
                    <a href="https://www.n.cn/" target="_blank">
                      <img src="https://p5.ssl.qhimg.com/t110b9a930153723fa7d40182a9.png" alt="" />
                      <span>纳米AI</span>
                    </a>
                    <a href="https://www.ccwork.com.cn" target="_blank">
                      <img src="https://p5.ssl.qhimg.com/t110b9a9301f02bd7e3fa44509e.png" alt="" />
                      <span>360智语</span>
                    </a>
                    <a href="https://browser.360.net" target="_blank">
                      <img src="https://p1.ssl.qhimg.com/t110b9a930149200dc0a19f7dc7.png" alt="" />
                      <span>360AI企业浏览器</span>
                    </a>
                    <a href="https://www.fangcloud.com" target="_blank">
                      <img src="https://p1.ssl.qhimg.com/t110b9a930102a0c815f7f2b907.png" alt="" />
                      <span>360 AI企业知识库</span>
                    </a>
                    <a href="https://ai.360.com/lab" target="_blank">
                      <img src="https://s3.ssl.qhres2.com/static/12bd03cbe45de9fa.svg" alt="" />
                      <span>大模型卫士</span>
                    </a>
                    <a href="https://zyun.360.cn/product/tlm" target="_blank">
                      <img src="https://p2.ssl.qhimg.com/t110b9a930146e0766e93b7467d.png" alt="" />
                      <span>天纪大模型开发TLM</span>
                    </a>
                  </div>
                </div>
                <div className={homeStyles.footerContentRightItem}>
                  <div>联系我们</div>
                  <div className={homeStyles.footerContentLeftItemDesc}>
                    <span>私有化客服热线：400 021 0360</span>
                     <span>私有化咨询邮箱：<EMAIL></span>
                  </div>
                </div>
              </div>
              {/* <div className={homeStyles.footerContentRight}>
                <div className={homeStyles.footerContentRightTitle}>关注我们</div>
                <div className={homeStyles.footerContentRightDesc}>
                  <div className={homeStyles.footerContentRightImg}>
                    <span>微信群</span>
                  </div>
                  <div className={homeStyles.footerContentRightImg}>
                    <span>微信公众号</span>
                  </div>
                </div>
              </div> */}
            </div>
          </div>

          <div className={homeStyles.footerCopyright}>
            <p  style={{ color: "#999" }}>
              Copyright © 2005-<span>2024</span>版权所有
              天津三六零快看科技有限公司
              <a href="https://jubao.tjcac.gov.cn/" target="_blank" style={{ marginLeft: '8px', color: "#999" }}>天津举报中心</a>
              <a href="https://beian.miit.gov.cn/" target="_blank" style={{ color: '#999' }}>津ICP备20006251号-2</a>
              <span style={{ marginLeft: '8px' }}>违法和不良信息举报电话：022-88355238 022-88355239</span>
              <a href="//www.beian.gov.cn/portal/registerSystemInfo?recordcode=12011602001446" target="_blank" style={{ color: "#999" }}>
                <img src="//p3.ssl.qhimg.com/t01fc7b7bb8e1952d11.png" alt="备案" style={{ verticalAlign: 'middle', margin: '-1px 4px 0' }} />
                津公网安备 12011602001446号
              </a>
            </p>
          </div>
          {contextHolder}
        </div>
      </div>)
  );
}
