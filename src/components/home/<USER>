
import React from 'react';
import HomeStyle from '@/styles/Home.module.scss';
import logo from "@/images/logo.svg";
import rightIcon from "@/images/common/mobile_right_icon.svg";
import { Button,Modal } from 'antd';

type Props = {
    goToTryHandel: () => void;
}
export default function MobileHomePage(props:Props) {

    const { goToTryHandel } = props;
    const [modal, contextHolder] = Modal.useModal();

    // const toTryHandel = () => {
    //     modal.info({
    //         title: '提示',
    //         content: `页面尚未适配当前屏幕，建议在PC上使用。申请账号试用，请您发送账户名称和使用场景到g<EMAIL>，感谢关注。`,
    //       });
    // }
    return (
        <div className={HomeStyle.mobileHomeBg}>
            <img src='https://p5.ssl.qhimg.com/t110b9a9301d7f9da4996729608.png' width={'100%'} />
        </div>
    )
}