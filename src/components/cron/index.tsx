import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import CustomCron from './CustomCron';
import moment from 'moment';

import indexStyles from './index.module.scss'
 
const { Option } = Select;
const defaultCron = '0 * * * * ?';
const timeTypes = [
  { key: 'everyDay', label: '每天' },
  { key: 'everyWeek', label: '每周' },
  { key: 'everyMonth', label: '每月' }
];
// 秒 分 小时 日期 月份 星期
// * 表示任意值
// ? 表示日期或者星期任选一个
const defaultCronValue:any = {
  'everyDay': '0 * * * * ?',
  'everyWeek': '0 * * ? * *',
  'everyMonth': "0 * * * * ?"
}

interface Props {
  /**
   * @description 默认显示的cron
   * */
  value?: string;
  /**
   * @description 默认周期类型
   * @default everyDay
   * */
  defaultType?: 'everyDay' | 'everyWeek' | 'everyMonth';
  /**
   * @description 改变后回调
   * */
  onChange?: (cron?: string | null) => void;
  setCronValue: (cron:any) => void;
  setTriggerList: (list:any) => void
}
const Cron: React.FC<Props> = ({ defaultType, value, onChange, setCronValue, setTriggerList }) => {
  const [defaultTimeType, setDefaultTimeType] = useState(timeTypes[0].key);
  const [isInit, setIsInit] = useState(false)

  // useEffect(() => {
  //   if (defaultType) {
  //     setDefaultTimeType(defaultType);
  //   }
  // }, [defaultType]);

  /* eslint-disable */
  useEffect(() => {
    if (!value) return;
    const currentCron = value.split(' ');
    // 秒 分 小时 日期 月份 星期
    const [seconds, minutes, hours, dayOfDate, dayOfMonth, dayOfWeek]: any = currentCron;
    if (defaultTimeType !== 'customize') {
      let selectTimeType = 'everyDay';

      if ((dayOfWeek != '?' && dayOfWeek != '*') || value == '0 * * ? * *') {
        selectTimeType = 'everyWeek';
      }
      if (dayOfDate != '?' && dayOfDate != '*') {
        selectTimeType = 'everyMonth';
      }
      console.log(value, currentCron,dayOfWeek, dayOfDate,selectTimeType)
      !isInit && selectTimeType && setDefaultTimeType(selectTimeType);
      setIsInit(true)
    }
    // if (
    //   minutes.indexOf(',') !== -1 ||
    //   hours.indexOf(',') !== -1 ||
    //   (hours === '*' && minutes !== '*') ||
    //   (hours !== '*' && minutes === '*')
    // ) {
    //   setDefaultTimeType('customize');
    // }
  }, [value, isInit]);


  const handleTimeTypeChange = (selectValue: string, onChange:any) => {
    setDefaultTimeType(selectValue);
    setCronValue(defaultCronValue[selectValue] || '')
    setTriggerList(new Array())
    onChange(defaultCronValue[selectValue] || '')
  };

  
  return (
    <div className={defaultTimeType !== 'customize' ? indexStyles.cron : ''}>
      周期：<Select
        style={{ marginRight: '16px', width: '80px' }}
        className={indexStyles.customSelect}
        placeholder="请选择类型"
        onChange={(val) => handleTimeTypeChange(val, onChange)}
        value={defaultTimeType}
      >
        {timeTypes.map((item) => (
          <Option key={item.key} value={item.key}>
            {item.label}
          </Option>
        ))}
      </Select>
      <CustomCron onChange={onChange} value={value} type={defaultTimeType} />
    </div>
  );
};

export default Cron;
