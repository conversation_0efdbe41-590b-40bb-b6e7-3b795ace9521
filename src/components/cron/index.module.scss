.cron {
  margin-top: 10px;
  .selectRight {
    margin-right: 10px;
  }

  .selectAutoWidth {
    width: auto;
  }

  .selectFixedWidth {
    width: 200px;
  }
}

.custom {
  margin-top: 10px;
}


.cronBox {
  display: flex;
  align-items: center;
  margin: 10px 0;

  .cronLabel {
    min-width: 40px;
    text-align: left;
  }

  .cronSelect {
    min-width: 30%;
    max-width: 90%;
  }
}
.customSelect {
 & :global(.ant-select-selector) {
    border-radius: 8px;
}
}
