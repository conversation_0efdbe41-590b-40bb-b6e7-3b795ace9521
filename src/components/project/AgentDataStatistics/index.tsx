import React, { useEffect, useState } from 'react';
import { Button, Empty, Table, Spin, Select, DatePicker } from 'antd';
import { useRouter } from 'next/router';
import { reqAgentTableList, reqAgentEcharts } from '@/service/common';
import commonListStyles from '@/styles/CommonList.module.scss';
import operationListStyles from '@/styles/OperationList.module.scss';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

const EditableCell: React.FC<React.PropsWithChildren<any>> = ({
  children,
  ...restProps
}) => {
  return (
    <td {...restProps} style={{ 'padding': '4px 16px' }} align="center" >
      {children}
    </td>
  );
};

export default function AgentDataStatistics(props: any) {
  const { callStatus, searchFileName } = props;

  const router = useRouter();
  const teamId = router.query.teamId;
  
  const [loading, setLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false)
  const [chartData, setChartData] = useState(new Array());
  const [agentTableList, setAgentTableList] = useState(Array<any>);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  })

  const maxDate = dayjs().endOf('day');
  const minDate = dayjs().endOf('day').subtract(1, 'year');
  const defaultEndDate = dayjs().minute() > 0 ? dayjs().add(1, 'hour').startOf('hour') : dayjs();
  const defaultStartDate = dayjs().startOf('day');

  const columns: any = [
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>文件名</span>,
      dataIndex: "agent_name",
      key: "agent_name",
      ellipsis: true,
      align: 'center',
      render: (_: any, record: any) => {
        return <Button type="link"  style={{padding:'0px'}}>
          <a href={'agentDetail?from=2&agentId=' + record.agent_id + '&teamId=' + teamId } target='_blank'>{record.agent_name}</a>
        </Button>
      },
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>类型</span>,
      dataIndex: "type",
      key: "type",
      ellipsis: false,
      align: 'center',
      render: () => ('agent')
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>最后一次调用时间</span>,
      dataIndex: "last_called_time",
      key: "last_called_time",
      ellipsis: false,
      align: 'center',
      render: (text: any) => (!text ? '-' : text)
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>调用量</span>,
      dataIndex: "call_count",
      key: "call_count",
      ellipsis: false,
      align: 'center',
      render: (text: any) => (!text ? '-' : text)
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>被调用情况</span>,
      dataIndex: "call_status",
      key: "call_status",
      ellipsis: false,
      align: 'center',
      render: (text: any) => {
        const statusText: any = {
          1: '成功',
          2: '失败',
          3: '中止',
        }
        if(!text) return ''
        return statusText[text]
      } 
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>发布人</span>,
      dataIndex: "public_user",
      key: "public_user",
      ellipsis: false,
      align: 'center'
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>发布时间</span>,
      dataIndex: "public_time",
      key: "public_time",
      ellipsis: false,
      align: 'center',
      render: (text: any) => (!text ? '-' : text)
    },
    {
      title: <span style={{ color: '#626F84', fontWeight: 'normal' }}>操作</span>,
      dataIndex: "action",
      key: "action",
      ellipsis: false,
      align: 'center',
      render: (_: any, record: any) => {
        return <Button type="link">
          <a href={'agentServiceMonitoring?agentId=' + record.agent_id + '&teamId=' + teamId} target='_blank'>服务监控</a>
        </Button>
      },
    },
  ];

  const initData = async () => {
    setLoading(true)

    const res = await reqAgentTableList({ keyword: searchFileName, page: pagination.current, page_size: pagination.pageSize, access_status: callStatus })
    setAgentTableList([...res.data]);
    setPagination({ ...pagination, total: res.pagination.total });
    
    // 首次触发请求图表数据
    handleDateChange({}, [defaultStartDate.format('YYYY-MM-DD'), defaultEndDate.format('YYYY-MM-DD')])

    setLoading(false);
  }

  const handleChange = (page: any) => {
    setPagination({ total: pagination.total, pageSize: page.pageSize, current: page.current });
  }

  const getChart = async () => {
    const G2Plot = await import('../../../utils/g2plot')
    const { Column } = G2Plot;
    const stackedColumnPlot = new Column('container', {
        data: chartData,
        isGroup: true,
        height: 330,
        xField: 'date',
        yField: 'count',
        seriesField: 'name',
        /** 设置颜色 */
        color: ['#47A1FF', '#74E0B7','#FFCD58','#BFC9D5','#FF5E68'],
        /** 设置间距 */
        marginRatio: 0.1,
        label: {
            // 可手动配置 label 数据标签位置
            position: 'middle', // 'top', 'middle', 'bottom'
            // 可配置附加的布局方法
            layout: [
                // 柱形图数据标签位置自动调整
                { type: 'interval-adjust-position' },
                // 数据标签防遮挡
                { type: 'interval-hide-overlap' },
                // 数据标签文颜色自动调整
                { type: 'adjust-color' },
            ]
        },
        legend: { position: 'top' } 
    });
    stackedColumnPlot.render();
  }

  const handleDateChange = async(date: any, dateString: any) => {
    setChartLoading(true)

    const res = await reqAgentEcharts({
      static_type: 2,
      start_time: dateString[0] + ' 00:00:00',
      end_time: dateString[1] + ' 23:59:59',
    })

    let newData = [];
    let isZero = true;

    if (res && res.x_axis.length) {
      const { series, x_axis } = res; 

      for (let i = 0; i < x_axis.length; i++) {
        if (series.error[i] || series.success[i] || series.suspend[i] || series.total[i]) {
          isZero = false;
        }

        newData.push({ name: '全部', date: x_axis[i], count: series.total[i] });
        newData.push({ name: '成功', date: x_axis[i], count: series.success[i] });
        newData.push({ name: '失败', date: x_axis[i], count: series.error[i] });
        newData.push({ name: '中止', date: x_axis[i], count: series.suspend[i] });
      }
    }

    setChartData(isZero ? [] : newData)
    setChartLoading(false)
  }

  useEffect(() => {
    initData()
  }, [callStatus, searchFileName, pagination.current, pagination.pageSize])

  useEffect(()=>{
    document.getElementById('container')!.innerHTML= ''
    if(!chartData?.length) {
        return ;
    }
    getChart()
  },[chartData?.length])

  return (
    <>
      <div className={operationListStyles.monitorChart}>
        <div className={operationListStyles.monitorChartTitle}>
          <div className={operationListStyles.monitorChartTitleContent}>监控数据</div>
          <div>
            <RangePicker 
              defaultValue={[defaultStartDate, defaultEndDate]}
              disabledDate={(current) => current && (current > maxDate || current < minDate)}
              onChange={handleDateChange}
              format="YYYY-MM-DD"
            />
          </div>
        </div>
        <Spin spinning={chartLoading}><div id="container" className={operationListStyles.container}></div></Spin>
        {chartData?.length ? '' : <div className={operationListStyles.barEmptyWrapper}><Empty description="暂无记录" /></div>}
      </div>
      <div className={commonListStyles.tableList + ' normalFont ' + operationListStyles.monitorTable}>
        <Table
          loading={loading}
          components={{
            body: { cell: EditableCell },
          }}
          dataSource={agentTableList}
          columns={columns}
          scroll={{ y: 720 }}
          className='commonTable normalFont logTable'
          rowKey="agent_id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: any) => `共 ${total} 条记录`,
          }}
          onChange={(page) => handleChange(page)}
        />
      </div>
    </>
  );
};

