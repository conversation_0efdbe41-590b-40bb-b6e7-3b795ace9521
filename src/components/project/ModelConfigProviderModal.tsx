import React, { useEffect, useState } from 'react'
import { Modal, Button, Input, message, Row, Col, Form, Select, Space, Radio, Switch } from 'antd'
import ModelConfigStyle from '@/styles/ModelConfig.module.scss'
import { ModelProviderConfigType } from './ModelConfig'
import { reqAddModelProvider, reqDeleteModelProvider } from '@/service/background/project'
import { clone } from 'ramda'

export interface IKnowledgeDataSelectModalProps {
    open: boolean
    modelProvider: any
    setIsOpen: (open: boolean) => void
    // onDeleteHandle: (providerName: string) => void
    onRefreshHandle: () => void
    showDelete: boolean,
    modelProviderConfig: ModelProviderConfigType[],
    isEdit: boolean,
    editParams: any // 编辑时的参数
}

const ModelConfigProviderModal: React.FC<IKnowledgeDataSelectModalProps> = ({
    open,
    setIsOpen,
    // onDeleteHandle,
    onRefreshHandle,
    modelProvider,
    showDelete,
    modelProviderConfig,
    isEdit,
    editParams
}) => {
    const [form] = Form.useForm();
    const [isLoading, setIsLoading] = useState(false)
    const [isDeleteLoading, setIsDeleteLoading] = useState(false)
    const [isEditApiKey, setIsEditApiKey] = useState(false) // 是否编辑了API Key

    const handleOk = async () => {
        form.validateFields().then(values => {
            const cloneValues = clone(values)
            Object.keys(cloneValues).forEach((key: any) => { // 判断是否是key的类型是否是 secret-input，如果是则不传值真实值
                if (modelProviderConfig.find((item: any) => item.variable === key)?.type === 'secret-input' && cloneValues[key].indexOf('***') > -1) {
                    values[key] = (cloneValues[key] === editParams[key] ? '[__HIDDEN__]' : cloneValues[key])
                }
            })
            reqAddModelProviderHandle(values)
        }).catch(errorInfo => {
            console.log(errorInfo)
        })
    }

    const reqAddModelProviderHandle = async (params: any) => {
        setIsLoading(true)
        const credentials = { credentials: params }
        const res = await reqAddModelProvider(modelProvider.provider, credentials)
        setIsLoading(false)
        if (!res) {
            return
        }
        message.success('添加成功')
        onRefreshHandle()
        setIsOpen(false)
    }

    const reqDeleteModelProviderHandle = async () => {
        setIsDeleteLoading(true)
        const res = await reqDeleteModelProvider(modelProvider.provider)
        setIsDeleteLoading(false)
        if (!res) {
            return
        }
        message.success('移除成功')
        onRefreshHandle()
        setIsOpen(false)
    }

    const handleCancel = () => {
        form.resetFields()
        setIsOpen(false)
        setIsEditApiKey(false)
    }

    const onDelete = () => {
        reqDeleteModelProviderHandle()
        // onDeleteHandle(modelProvider)
    }

    useEffect(() => {
        if (open && !isEdit) {
            // 初始化表单
            form.resetFields()
        }
        if (editParams && Object.keys(editParams).length == 0) {
            form.resetFields()
        }
        if (isEdit && editParams) {
            // 编辑模式
            Object.keys(editParams).forEach((key: any) => {
                form.setFieldsValue({ [key]: editParams[key] })
            })
        }
    }, [open, isEdit, editParams])

    const onClickLineHandle = (line: string) => {
        window.open(modelProvider.help.url.zh_Hans, '_blank')
    }

    return (
        <>
            <Modal
                open={open}
                width={520}
                title={`设置${modelProvider?.label.zh_Hans}`}
                cancelText='取消'
                okText='导入'
                className='knowledge-modal knowledge'
                onCancel={handleCancel}
                onOk={handleOk}
                // closeIcon={false}
                centered
                destroyOnClose
                footer={<div className={ModelConfigStyle.modelFooter}>
                    <Button type='link' className={ModelConfigStyle.linkBtn} onClick={() => onClickLineHandle('')}>
                        {`从${modelProvider?.label?.zh_Hans}获取API Key`}
                    </Button>
                    <div>
                        {showDelete && <Button className={ModelConfigStyle.btn} loading={isDeleteLoading} onClick={onDelete} >删除</Button>}
                        <Button onClick={handleCancel} className={ModelConfigStyle.btn}>取消</Button>
                        <Button className={ModelConfigStyle.btn} loading={isLoading} onClick={handleOk} disabled={isEdit && !isEditApiKey} type='primary'>确定</Button>
                    </div>
                </div>}
            >
                <div className={ModelConfigStyle.providerModalForm}>
                    <Form
                        form={form}
                        layout="vertical"
                        onChange={(val: any) => {
                            editParams[val.target.id] === val.target.value ? setIsEditApiKey(false) : setIsEditApiKey(true)
                        }}
                        initialValues={{ model_type: 'llm' }}>
                        <Row gutter={[24, 0]}>
                            {
                                modelProviderConfig.map((item: any, index: number) => {
                                    return (
                                        <Col span={24} key={index}>
                                            <Form.Item
                                                label={item.label.zh_Hans}
                                                name={item.variable}
                                                initialValue={item.type.indexOf('radio') > -1 ? item.options[0].value : (item.type.indexOf('select') > -1 ? item.options[0].value : '')}
                                                rules={[{ required: item.required, message: item.placeholder?.zh_Hans }]}
                                            >
                                                {item.type.indexOf('input') > -1 ? <Input autoComplete='off' placeholder={item.placeholder?.zh_Hans} /> :
                                                    (
                                                        item.type.indexOf('select') > -1 ? <Select
                                                            placeholder={item.placeholder?.zh_Hans}
                                                            allowClear
                                                            options={item.options && item.options.map((option: any) => ({ label: option.label.zh_Hans, value: option.value }))} /> :
                                                            (
                                                                item.type.indexOf('radio') > -1 ? <Radio.Group>
                                                                    <Space direction="vertical">
                                                                        {
                                                                            item.options && item.options.map((option: any, index: number) => {
                                                                                return (
                                                                                    <Radio key={index} value={option.value}>{option.label.zh_Hans}</Radio>
                                                                                )
                                                                            })
                                                                        }
                                                                    </Space>
                                                                </Radio.Group> : (
                                                                    item.type.indexOf('switch') > -1 ? <Switch /> : null
                                                                )
                                                            )
                                                    )
                                                }
                                            </Form.Item>
                                        </Col>
                                    )
                                })
                            }
                        </Row>
                    </Form>
                </div>
            </Modal >
        </>
    )
}

export default ModelConfigProviderModal
