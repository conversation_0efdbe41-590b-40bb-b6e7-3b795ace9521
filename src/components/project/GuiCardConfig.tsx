import { useState, useEffect } from "react";
import { Input, Button, Modal, Pagination, Spin, Upload, message } from "antd";
import flowStyles from "@/styles/flow/Flow.module.scss";
import styles from "@/components/agent/detail/AgentDetailAddPluginModal.module.scss";
import AgentListEmpty from "@/components/agent/detail/AgentListEmpty";
import { initJsonEditor } from "@/components/flow3.0/flowFunc";
import { InboxOutlined, SyncOutlined, DeleteOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import uploadBtn from '@/images/gui/upload.svg'
import {
    reqImageUpload,
    reqGuiCardList,
    reqGuiCardCreate,
    reqGuiCardDetail,
    reqGuiCardDelete,
    reqGuiCardUpdate,
} from "@/service/api";
import { useRouter } from "next/router";
const { Dragger } = Upload;
const originCardInfo = {
    id: 0,
    name: "",
    url: "",
    image: "",
    data_desc: {},
};
export default function GuiCardConfig(props: any) {
    const {
        keyword,
        guiCardRunSearch,
        guiCardUserId,
        guiCardAdd,
        setGuiCardAdd,
    } = props;
    // 列表
    const [list, setList] = useState([]);
    const [listLoading, setListLoading] = useState(false);
    const [pageInfo, setPageInfo] = useState({
        page: 1,
        pageSize: 10,
        total: 100,
    });
    const [modalTitle, setModalTitle] = useState("添加GUI 卡片");
    const [guiCardOpen, setGuiCardOpen] = useState(false);
    const [cardInfo, setCardInfo] = useState(originCardInfo);
    const jsonEditorId = "jsoneditor-guicard-model";
    const [submitLoading, setSubmitLoading] = useState(false);
    const [imgLoading, setImgLoading] = useState(false);
    const [cardJson, setCardJson] = useState({});
    const router = useRouter();

    useEffect(() => {
        if (router?.query?.isAddGuiModalOpen === 'true') {
            setGuiCardOpen(true);
        }
    }, [router?.query?.isAddGuiModalOpen]);

    // 获取列表
    const handleGetList = async (params: any) => {
        setListLoading(true);
        try {
            const res = await reqGuiCardList(params);
            if (res) {
                setList(res.list);
                setPageInfo({
                    ...pageInfo,
                    total: res.total,
                });
            } else {
                setList([]);
                setPageInfo({
                    ...pageInfo,
                    total: 0,
                });
            }
            setListLoading(false);
        } catch (error) {
            setList([]);
            setPageInfo({
                ...pageInfo,
                total: 0,
            });
            setListLoading(false);
            message.error("查询卡片数据失败，请重试!");
        }
    };
    useEffect(() => {
        handleGetList({
            page: pageInfo.page,
            page_size: pageInfo.pageSize,
            keyword,
            user_id: guiCardUserId,
        });
    }, [pageInfo.page, pageInfo.pageSize, guiCardUserId, guiCardRunSearch]);
    useEffect(() => {
        setModalTitle("添加GUI 卡片");
        guiCardAdd && setGuiCardOpen(true);
    }, [guiCardAdd]);
    const isJSON = (obj: any) => {
        try {
            JSON.parse(obj);
            return true;
        } catch (e) {
            return false;
        }
    }
    useEffect(() => {
        guiCardOpen &&
            initJsonEditor(
                cardInfo.data_desc,
                "guicard-model",
                false,
                (value: any) => {
                    if (isJSON(value)) {
                        setCardJson(JSON.parse(value));
                    }
                }
            );
    }, [guiCardOpen, cardInfo.data_desc]);
    const imgUploadProps: UploadProps = {
        name: "file",
        multiple: false,
        showUploadList: false,
        accept: ".jpg,.jpeg,.png",
        async beforeUpload(file) {
            setImgLoading(true);
            const isJpgOrPng =
                file.type === "image/jpeg" ||
                file.type === "image/png" ||
                file.type === "image/jpg" ||
                file.type === "image/webp";
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJpgOrPng) {
                message.error("请上传png、jpeg、jpg、webp格式图片");
            } else if (!isLt10M) {
                message.error("上传图片大小不超过2M");
            }

            if (!isJpgOrPng || !isLt10M) {
                setImgLoading(false);
            }

            if (isJpgOrPng && isLt10M) {
                const files = new FormData();
                files.append("file", file);
                reqImageUpload(files)
                    .then((res) => {
                        setCardInfo({
                            ...cardInfo,
                            image: res.image_url,
                        });
                        setImgLoading(false);
                    })
                    .catch((error) => {
                        setImgLoading(false);
                        setCardInfo({
                            ...cardInfo,
                            image: "",
                        });
                        message.error("图片上传失败，请重新上传");
                    });
            }
            return false;
        },
    };
    const handleClear = () => {
        // 清空默认数据
        setGuiCardOpen(false);
        guiCardAdd && setGuiCardAdd(false);
        setCardInfo(originCardInfo);
        setCardJson({});
    };
    const handleSubmit = async () => {
        setSubmitLoading(true);
        try {
            let { name, url, image, data_desc } = cardInfo;
            if (!name || !url || !image || !cardJson) {
                message.error("请填写完整卡片信息");
                setSubmitLoading(false);
                return;
            }
            let params = {
                name,
                url,
                image,
                data_desc: cardJson
            };
            let res = null;
            if (cardInfo.id) {
                res = await reqGuiCardUpdate({ ...params, card_id: cardInfo.id });
            } else {
                res = await reqGuiCardCreate(params);
            }
            setSubmitLoading(false);
            if (res && ((cardInfo.id && res.message) || res.card_id)) {
                message.success(cardInfo.id ? "更新成功" : "添加成功");
                handleClear();
                setSubmitLoading(false);
                handleGetList({
                    page: pageInfo.page,
                    pageSize: pageInfo.pageSize,
                    keyword,
                    user_id: guiCardUserId,
                });
            } else {
                message.error("添加失败，请重试");
            }
        } catch (error) {
            setSubmitLoading(false);
            message.error("添加失败，请重试");
        }
    };
    const handleCancel = () => {
        handleClear();
    };
    return (
        <>
            <div className="flex h-full flex-col bg-[#F7F8FA] relative">
                <div className="absolute w-full h-full flex items-center justify-center z-[99] bg-slate-50 opacity-50"
                    style={{
                        display: listLoading ? "flex" : "none",
                    }}
                >
                    <Spin spinning={listLoading}></Spin>
                </div>
                {list && list.length === 0 ? (
                    <div className="w-full h-full flex items-center justify-center">
                        <AgentListEmpty desc={"抱歉，没有找到相关卡片～"} />
                    </div>
                ) : (
                    <div
                        className={
                            flowStyles.guiCardListBox +
                            " grid grid-cols-4 gap-4 mt-5 flex-1 overflow-y-auto"
                        }
                        style={{
                            gridAutoRows: '264px' /* 固定每行高度为264px */
                        }}
                    >
                        {list?.map((item: any, index: any) => (
                            <div
                                key={item.id}
                                style={{ height: "264px" }}
                                className={
                                    "bg-white rounded-[8px] shadow-md hover:shadow-lg " +
                                    flowStyles.guiCardBox
                                }
                                onClick={async () => {
                                    setModalTitle("编辑GUI 卡片");
                                    setListLoading(true);
                                    // 获取详情
                                    try {
                                        if (!item.id) {
                                            message.error("卡片id不存在，请重试!");
                                            setListLoading(false);
                                            return;
                                        }
                                        let res = await reqGuiCardDetail(item.id);
                                        if (res && res.id) {
                                            setCardJson(res.data_desc || {});
                                            setCardInfo({
                                                id: res.id,
                                                name: res.name,
                                                url: res.url,
                                                image: res.image,
                                                data_desc: res.data_desc || {},
                                            });
                                            setGuiCardOpen(true);
                                        } else {
                                            message.error("卡片详情获取失败，请重试!");
                                        }
                                        setListLoading(false);
                                    } catch (error) {
                                        setListLoading(false);
                                        message.error("卡片详情获取失败，请重试!");
                                    }
                                }}
                            >
                                <div className={flowStyles.guiCardBoxDelete}>
                                    <Button
                                        size="small"
                                        icon={<DeleteOutlined />}
                                        onClick={async (e: any) => {
                                            e.stopPropagation();
                                            setListLoading(true);
                                            try {
                                                if (!item.id) {
                                                    message.error("卡片id不存在，请重试!");
                                                    setListLoading(false);
                                                    return;
                                                }
                                                let res = await reqGuiCardDelete(item.id);
                                                if (res && res.message) {
                                                    message.success("卡片删除成功!");
                                                    handleGetList({
                                                        page: pageInfo.page,
                                                        page_size: pageInfo.pageSize,
                                                        keyword,
                                                        user_id: guiCardUserId,
                                                    });
                                                } else {
                                                    message.error("卡片删除失败，请重试!");
                                                }
                                                setListLoading(false);
                                            } catch (error) {
                                                setListLoading(false);
                                                message.error("卡片删除失败，请重试!");
                                            }
                                        }}
                                    >
                                        删除
                                    </Button>
                                </div>
                                <div
                                    style={{
                                        width: "100%",
                                        height: "214px",
                                        borderRadius: "8px",
                                    }}
                                >
                                    <img
                                        src={item.image}
                                        style={{
                                            width: "100%",
                                            height: "100%",
                                            objectFit: "cover",
                                            borderRadius: "8px 8px 0px 0px",
                                        }}
                                    />
                                </div>
                                <div
                                    className="flex items-center justify-start pl-4 pr-4 mt-1"
                                    style={{
                                        height: "30px",
                                        borderRadius: "8px",
                                        fontWeight: 700,
                                    }}
                                >
                                    {item.name}
                                </div>
                            </div>
                        ))}
                    </div>
                )}
                <div className="h-[60px] flex justify-end items-center pr-5">
                    <Pagination
                        total={pageInfo.total}
                        showSizeChanger
                        showTotal={(total) => `共 ${total} 条`}
                        onChange={(page, pageSize) => {
                            setPageInfo({
                                ...pageInfo,
                                page: page,
                                pageSize: pageSize,
                            });
                        }}
                    />
                </div>
            </div>
            <Modal
                open={guiCardOpen}
                // style={{padding:'0px 0px 12px 24px'}}
                onCancel={() => handleCancel()}
                width={"70%"}
                title={modalTitle}
                className={"commonModal customModal"}
                footer = {
                    <>
                            <Button onClick={() => handleCancel()}>取消</Button>
                            <Button
                                type="primary"
                                onClick={() => handleSubmit()}
                                loading={submitLoading}
                            >
                                确认
                            </Button>
                    </>
                }
            >
                <div
                    className={styles.agentDetailAddPluginModalContent + " flex-col"}
                    // style={{ height: "calc(100vh - 200px)" }}
                >
                    {/* <div className="w-full h-[64px] flex items-center justify-between p-4">
                        <div className="text-[18px] font-bold">{modalTitle}</div>
                        
                    </div> */}
                    <div className="w-full h-full flex">
                        <div
                            className="w-[40%] h-full p-4 pt-0 flex flex-col gap-3"
                            style={{ overflowY: "auto", maxHeight: "calc(100vh - 275px)" }}
                        >    
                            <div className="text-[14px] font-semibold mt-[12px]">添加卡片</div>
                            <div className={flowStyles.guiCardAddParams}>
                                <div>卡片名称</div>
                                <Input
                                    style={{ width: "100%" }}
                                    placeholder="请输入卡片名称"
                                    value={cardInfo.name}
                                    onChange={(e) => {
                                        setCardInfo({
                                            ...cardInfo,
                                            name: e.target.value,
                                        });
                                    }}
                                />
                            </div>
                            <div className={flowStyles.guiCardAddParams}>
                                <div>卡片URL</div>
                                <Input
                                    style={{ width: "100%" }}
                                    placeholder="请输入卡片URL"
                                    value={cardInfo.url}
                                    onChange={(e) => {
                                        setCardInfo({
                                            ...cardInfo,
                                            url: e.target.value,
                                        });
                                    }}
                                />
                            </div>
                            <div
                                className={flowStyles.guiCardAddParams}
                                style={{ height: "425px" }}
                            >
                                <div>卡片数据</div>
                                <div
                                    style={{ width: "100%", height: "calc(100% - 40px)" }}
                                    className={
                                        flowStyles.textAreaWrapper +
                                        " " +
                                        flowStyles.opTextAreaWrapper +
                                        " " +
                                        flowStyles.resultTextAreaWrapper
                                    }
                                >
                                    <div
                                        id={jsonEditorId}
                                        className={flowStyles.jsoneditor}
                                    ></div>
                                </div>
                            </div>
                        </div>
                        <div className="w-[60%] bg-[#EDF1F5] p-4 flex flex-col">
                            <div className="flex justify-between h-[30px] items-center font-semibold">
                                图片预览{" "}
                                {cardInfo.image && (
                                    <div>
                                        <Button
                                            type="text"
                                            size="small"
                                            icon={<DeleteOutlined />}
                                            onClick={() => {
                                                setCardInfo({
                                                    ...cardInfo,
                                                    image: "",
                                                });
                                            }}
                                        >
                                            删除
                                        </Button>{" "}
                                        <Upload {...imgUploadProps}>
                                            <Button type="text" size="small" icon={<SyncOutlined />}>
                                                重新上传
                                            </Button>
                                        </Upload>
                                    </div>
                                )}
                            </div>
                            <div className="w-full h-full relative flex items-center justify-center">
                                <div className="absolute w-full h-full flex items-center justify-center z-[99] bg-slate-50 opacity-50" style={{ display: imgLoading ? 'flex' : 'none'}}><Spin spinning={imgLoading}></Spin></div>
                                {cardInfo.image ? (
                                    <div className="w-full h-full flex items-center justify-center">
                                        <img
                                            src={cardInfo.image}
                                            alt=""
                                            style={{
                                                width: "90%",
                                                height: "auto",
                                                objectFit: "contain",
                                            }}
                                        />
                                    </div>
                                ) : (
                                    <Dragger {...imgUploadProps} className="w-full block bg-white rounded-[8px]">
                                        <p className="ant-upload-drag-icon">
                                            <img src={uploadBtn.src} width={48} height={48}></img>
                                        </p>
                                        <p className="ant-upload-text">
                                            点击或拖拽图片到此区域上传
                                        </p>
                                        <p className="ant-upload-hint">
                                            支持上传JPG、JPEG、PNG、webp类型图片，大小不超过10M
                                        </p>
                                    </Dragger>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>
        </>
    );
}
