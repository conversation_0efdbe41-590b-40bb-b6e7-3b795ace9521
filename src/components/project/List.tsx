import { Button, Input, Select, message, Dropdown, Space, MenuProps, Segmented } from 'antd';
import commonListStyles from '@/styles/CommonList.module.scss'
import projectStyle from '@/styles/Project.module.scss'

import teamMemberStyles from '@/styles/TeamMembers.module.css'
import operationListStyles from '@/styles/OperationList.module.scss'
import search from '@/images/commonList/search.png'
// 成员管理
import MembersList from '@/components/project/MembersList'
// 服务运维-flow
import OperationList from '@/components/project/OperationList'
// 运维服务-agent
import AgentDataStatistics from '@/components/project/AgentDataStatistics';
// 模型配置
import ModalConfig from '@/components/project/ModelConfig';
// 授权管理
import AuthManage from '@/components/project/AuthManage/index';
// 卡片
import GuiCardConfigPage from '@/components/project/GuiCardConfig';
import { reqProjectSelectList } from "@/service/common";
import { isNullOrEmpty } from "@/utils/common";
import { useRouter } from 'next/router'

import { useState, useEffect } from 'react';
import { DownOutlined, SmileOutlined } from '@ant-design/icons';
import commonSelectIcon from '@/images/commonList/common-select.svg'
import arrowDown from '@/images/commonList/arrow-down.svg'
import commonListCardStyle from "@/styles/CommonListCard.module.scss";
import * as constants from '@/constants/appConstants';


enum PageTableType {
  'Agent' = 1,
  'Flow' = 2,
}

export default function CommonList(prop: any) {
  const router = useRouter();
  const teamId = router.query.teamId;
  const tab = router.query.tab;
  const memberManage = '成员管理';
  const operation = '服务运维';
  const modelConfig = '模型配置';
  const authManage = '插件授权管理';
  const guiCardConfig = 'GUI卡片';

  const [tableType, setTableType] = useState(PageTableType.Flow)

  const [activeTab, setActiveTab] = useState(operation)

  const [hasMemberManger, setHasMemberManger] = useState(false)
  const [searchName, setSearchName] = useState('')
  const [isPersonalProject, setIsPersonalProject] = useState(false) // 当前选择项目是否是个人项目

  const [callStatus, setCallStatus] = useState(-1) // 调用状态
  const [fileStatus, setFileStatus] = useState(0); // 文件状态 0-全部 1-部署成功 2-已停用

  const [searchFileName, setSearchFileName] = useState('')

  const [selectPropel, setSelectPropel] = useState(0)

  const [openAddMember, setOpenAddMember] = useState(false);
  // 云盘二期需求，移除Agent审核功能
  const [isYunPanBoard, setIsYunPanBoard] = useState(false);// 是否是云盘看板

  // gui卡片
  const guiCardStatusList = [
    {
      label: '全部',
      value: 0
    },
  ]
  const [guiCardUserId, setGuiCardUserId] = useState(0)
  const [guiCardKeyword, setGuiCardKeyword] = useState('')
  const [guiCardRunSearch, setGuiCardRunSearch] = useState(false)
  const [guiCardAdd, setGuiCardAdd] = useState(false)
  const getItems = (key: number) => {
    const items: MenuProps['items'] = guiCardStatusList.map((item, index) => {
      return {
        key: item.value,
        label: (
          <div className={projectStyle.selectProItem} onClick={() => { setGuiCardUserId(item.value) }}>
            <span className={projectStyle.selectProItemTitle}>{item.label}</span>
            {guiCardUserId === item.value ? <img className={projectStyle.selectProItemTrue} src={commonSelectIcon.src} alt="" /> : null}
          </div>
        ),
      }
    })
    return { items }
  }

  useEffect(() => {
    if (teamId != undefined) {
      getProjectList()
    }
  }, [teamId])

  useEffect(() => {
    const isFromYunPan = localStorage.getItem(constants.prompt_isYunPanBoard) === 'true' || router.query.source === 'yunpanBoard';
    setIsYunPanBoard(isFromYunPan);
  }, [])

  const getProjectList = async () => {
    try {
      const res = await reqProjectSelectList({})
      if (res.context.code == 0) {
        let localTeamId = router.query.teamId;
        if (!isNullOrEmpty(localTeamId)) {
          if (res.data != undefined) {
            let has = true
            if (Array.isArray(res.data)) {
              res.data.forEach((item: any) => {
                if (localTeamId == item.id) {
                  if (item.team_type == 4) {//个人项目没有成员管理
                    has = false
                    setIsPersonalProject(true)
                  }
                  if (item.role == 2) {//项目成员没有成员管理
                    has = false
                    setIsPersonalProject(false)
                  }
                }
              });
            }
            setHasMemberManger(has)

            if (has) {
              if (tab === undefined || tab === '') {
                setRouterQuery('1')
              } else {
                setRouterQuery(tab as string)
              }
            } else {
              if (tab === undefined || tab === '') {
                setRouterQuery('1')
              } else {
                setRouterQuery(tab as string)
              }
            }
          }
        } else {
          setHasMemberManger(false)
          setActiveTab(operation)
        }
        return;
      }
    } catch (error) {
      // console.log('error=====', error);
    }
  };

  const setRouterQuery = async (tab: string) => {
    if (tab == '0') {
      setActiveTab(memberManage)
    } else if (tab == '1') {
      setActiveTab(operation)
    } else if (tab == '2') {
      setActiveTab(authManage)
    } else if (tab == '3') {
      setActiveTab(modelConfig)
    } else if (tab == '4') {
      setActiveTab(guiCardConfig)
    }
    let queryParams: any = { teamId: teamId, tab: tab };
    if (router?.query?.isAddGuiModalOpen === 'true' && tab == '4') {
      queryParams.isAddGuiModalOpen = 'true'
    }
    await router.push({
      pathname: '/projectList',
      query: queryParams
    })
  }

  const createOption = (label: string, value: string) => ({
    label: (
      <div style={{ fontWeight: tab === value ? '600' : '400', color: tab === value ? '#006BFF' : '#626F84', borderRadius: isYunPanBoard ? '4px' : '8px' }}>
        {label}
      </div>
    ),
    value
  });

  const handleChangePageType = (value: PageTableType) => {
    if (value == PageTableType.Agent) setCallStatus(0)
    if (value == PageTableType.Flow) setCallStatus(-1)
    setTableType(value)
  }

  return (
    <>
      <div className={commonListStyles.commonList} style={{ background: '#fff', padding: '0', height: '100vh', minWidth: '1200px', overflowX: 'auto' }}>
        <div className={commonListStyles.titleWrapper} style={{ padding: '16px', background: isYunPanBoard ? '#fff' : 'rgba(245, 246, 249, 1)', marginBottom: '0' }}>
          {hasMemberManger && <div className={commonListStyles.titleWrapperLeft}>
            <span className={isYunPanBoard ? commonListStyles.titleForYunPan : commonListStyles.title}>设置</span>
            <Segmented
              style={{ background: '#EEEFF2', fontSize: '14px', color: '#626F84', borderRadius: isYunPanBoard ? '4px' : '8px' }}
              options={[
                createOption(operation, '1'),
                // createOption(authManage, '2'),
                createOption(modelConfig, '3'),
                createOption(memberManage, '0'),
                // createOption(guiCardConfig, '4'),
              ]}
              value={tab}
              onChange={(value) => {
                setRouterQuery(value as string)
              }} />
            {/* {
              isYunPanBoard && isAdmin && <Button style={{ marginLeft: '16px' }} onClick={e => {
                router.push({
                  pathname: '/background/examineList',
                  query: {
                    teamId: teamId,
                  }
                })
              }}>Agent 审核</Button>
            } */}
          </div >}
          {
            !hasMemberManger && <div className={commonListStyles.titleWrapperLeft}>
              <span className={isYunPanBoard ? commonListStyles.titleForYunPan : commonListStyles.title}>设置</span>
              <Segmented
                style={{ background: '#EEEFF2', fontSize: '14px', color: '#626F84', borderRadius: '4px' }}
                options={isPersonalProject ? [
                  createOption(operation, '1'),
                  // createOption(authManage, '2'),
                  // createOption(modelConfig, '3'),
                  // createOption(guiCardConfig, '4'),
                ] : [
                  createOption(operation, '1'),
                  // createOption(authManage, '2'),
                  // createOption(guiCardConfig, '4'),
                ]}
                value={tab}
                onChange={(value) => {
                  setRouterQuery(value as string)
                }} />
            </div >
          }
          {
            activeTab === memberManage && <div>
              <Input
                placeholder="请输入关键词搜索"
                prefix={<img src={search.src} width='16' />}
                className={'commonOuterStyle.search'}
                allowClear
                style={{ width: '256px', borderRadius: '8px' }}
                onChange={e => {
                  setSearchName(e.target.value)
                }}
              />
              <Button type='primary' className={teamMemberStyles.addMember + ' primaryButton'} onClick={() => { setOpenAddMember(true) }}>添加成员</Button>
            </div>
          }
          {
            activeTab === operation &&
            <div className={projectStyle.headerRight}>
              <div className={isYunPanBoard ? '' : projectStyle.status} >
                {!isYunPanBoard && <span className={projectStyle.selectTitle}>类型:</span>}
                <Space>
                  <Select
                    defaultValue={tableType}
                    onChange={handleChangePageType}
                    variant="borderless"
                    style={{ width: isYunPanBoard ? 120 : 80 }}
                    className={isYunPanBoard ? projectStyle.selectForYunPan : ''}
                    options={[
                      { value: PageTableType.Agent, label: 'Agent' },
                      { value: PageTableType.Flow, label: 'Flow' },
                    ]}
                  />
                </Space>
              </div>
              <div className={isYunPanBoard ? '' : projectStyle.status} >
                {!isYunPanBoard && <span className={projectStyle.selectTitle}>调用状态:</span>}
                <Space>
                  <Select
                    value={callStatus}
                    variant="borderless"
                    style={{ width: isYunPanBoard ? 120 : 80 }}
                    className={isYunPanBoard ? projectStyle.selectForYunPan : ''}
                    onChange={(value: any) => { setCallStatus(value) }}
                    options={
                      tableType == PageTableType.Agent ? [
                        { value: 0, label: '全部' },
                        { value: 1, label: '成功' },
                        { value: 2, label: '失败' },
                        { value: 3, label: '中止' },
                      ] : [
                        { value: -1, label: '全部' },
                        { value: 1, label: '正常' },
                        { value: 2, label: '异常' },
                        { value: 0, label: '未调用' },
                      ]}
                  />
                </Space>
              </div>

              <div className={projectStyle.search}>
                <Input
                  placeholder="请输入关键词搜索"
                  prefix={<img src={search.src} width='16' />}
                  allowClear style={{ width: isYunPanBoard ? '276px' : '256px', borderRadius: isYunPanBoard ? '4px' : '8px', border: '1px solid #E1E7ED' }}
                  onChange={e => {
                    setSearchFileName(e.target.value)
                  }}
                />
              </div>
            </div>
          }
          {
            activeTab === guiCardConfig &&
            <div className={projectStyle.headerRight}>
              <div className={isYunPanBoard ? projectStyle.statusForYunPan : projectStyle.status} >
                <Dropdown menu={getItems(3)} overlayClassName={commonListCardStyle.dropMenuBox}>
                  <Space className={isYunPanBoard ? projectStyle.spaceForYunPan : ''}>
                    {!isYunPanBoard && <span className={projectStyle.selectTitle}>创建人:</span>}
                    <span className={projectStyle.selectSubtitle}>{guiCardStatusList.find(item => item.value === guiCardUserId)?.label}</span>
                    <img src={arrowDown.src} height={18} width={18} style={{ marginTop: '6px' }} />
                  </Space>
                </Dropdown>
              </div>
              {/* <div className={projectStyle.status}>
                <Dropdown menu={getItems(2)}>
                  <Space>
                    <span className={projectStyle.selectTitle}>文件状态:</span>
                    <span className={projectStyle.selectSubtitle}>{fileNameStatusList.find(item => item.value === fileStatus)?.label}</span>
                    <DownOutlined />
                  </Space>
                </Dropdown>
              </div> */}

              <div className={projectStyle.search}>
                {/* <Input
                  placeholder="请输入关键词搜索"
                  prefix={<img src={search.src} width='16' />}
                  allowClear style={{ width: '256px', borderRadius: '8px' }}
                  onChange={e => {
                    setSearchFileName(e.target.value)
                  }}
                /> */}
                <Input placeholder="请输入关键词搜索" prefix={<img src={search.src} width='16' />} onPressEnter={() => { setGuiCardRunSearch(!guiCardRunSearch) }} style={{ width: isYunPanBoard ? 276 : 256, borderRadius: isYunPanBoard ? '4px' : '8px' }} value={guiCardKeyword} onChange={e => {
                  setGuiCardKeyword(e.target.value)
                }} />
                <Button type='primary' className={teamMemberStyles.addMember + ' primaryButton'} style={{ borderRadius: isYunPanBoard ? '4px' : '8px' }} onClick={() => { setGuiCardAdd(!guiCardAdd) }}>创建GUI 卡片</Button>
              </div>
            </div>
          }
        </div >
        <div className={commonListStyles.tableList + ' normalFont'} style={{ padding: '0px', border: '0', borderRadius: '0', ...(activeTab !== operation ? { height: 'calc(100vh - 68px)' } : { height: 'auto' }) }}>
          {activeTab === memberManage && <MembersList
            openAddMember={openAddMember}
            searchName={searchName}
            hasMemberManger={hasMemberManger}
            setOpenAddMember={setOpenAddMember}
          />
          }
          {
            activeTab === operation && tableType == PageTableType.Agent &&
            <AgentDataStatistics
              callStatus={callStatus}
              searchFileName={searchFileName}
            />
          }
          {
            activeTab === operation && tableType == PageTableType.Flow && <OperationList
              callStatus={callStatus}
              fileStatus={fileStatus}
              searchFileName={searchFileName}
            />
          }
          {
            activeTab === modelConfig && <ModalConfig />
          }
          {
            activeTab === authManage && <AuthManage />
          }
          {
            activeTab === guiCardConfig && <GuiCardConfigPage keyword={guiCardKeyword} guiCardUserId={guiCardUserId}
              guiCardRunSearch={guiCardRunSearch} guiCardAdd={guiCardAdd} setGuiCardAdd={setGuiCardAdd} />
          }
        </div>
      </div>
    </>
  )
}
