import React, { useEffect } from "react";
import ModelConfigStyle from '@/styles/ModelConfig.module.scss'
import { Button, Tag, Popover, Tooltip } from "antd";
import { useState } from "react";
import { PlusOutlined, SettingOutlined } from '@ant-design/icons';
import { fetchData } from "@/utils/fetch";
import { useRecoilState } from "recoil";
import { modelProviderIconUrlList } from "@/atoms/projectModelAtoms";
import ModelConfigCheck from "@/images/model/model_config_check.png";
import ModelConfigCheckActive from "@/images/model/model_config_check_active.png";

type ModelConfigItemCardProps = {
    modelProvider: any;
    modelList: any;
    index: number;
    addModel: (modelProvider: any) => void;
    setProviderHandle: (modelProvider: any) => void;
    // getModelList: (provider: string) => void;
    onClickEditModelConfigHandle: (modelProvider: any, subItem: any) => void;
}

const ModelConfigItemAddedCard: React.FC<ModelConfigItemCardProps> = ({
    modelProvider,
    modelList,
    setProviderHandle,
    index,
    addModel,
    // getModelList, 
    onClickEditModelConfigHandle
}) => {

    const [showAddModal, setShowAddModal] = useState(false)
    const [showCheck, setShowCheck] = useState(false)
    const [modelProviderIconUrl, setModelProviderIconUrl] = useRecoilState(modelProviderIconUrlList)

    const onAddModalHandle = () => {
        // console.log('添加模型')
        addModel(modelProvider)
    }
    const setProvider = () => {
        // console.log('设置')
        setProviderHandle(modelProvider)
    }

    const onCheckMouseEnter = () => {
        setShowCheck(true)
        // getModelList(modelProvider.provider)
    }

    const onCheckMouseLeave = () => {
        setShowCheck(false)
    }

    const getCacheIcon = () => {
        if (modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider)) {
            return modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider).url
        } else {
            return ''
        }
    }
    const [imageIcon, setImageIcon] = useState<any>(getCacheIcon())


    useEffect(() => {
        if (!modelProvider.icon_large) {
            setImageIcon('')
        } else {
            try {
                if (modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider)) {
                    setImageIcon(modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider).url)
                    return
                }
                fetchData(modelProvider.icon_large?.zh_Hans, {}, 'get').then((res: any) => {
                    const response = new Response(res.body);
                    const type = res.headers.get('content-type')
                    response.blob().then(blob => {
                        let url = ''
                        if (type === 'image/png') {
                            url = URL.createObjectURL(blob);
                        } else {
                            const svgBlob = new Blob([blob], { type: 'image/svg+xml;charset=utf-8' });
                            url = URL.createObjectURL(svgBlob);
                        }
                        // 现在你可以使用 url 了
                        setImageIcon(url)
                        setModelProviderIconUrl((pre: any[]) => {
                            return [...pre, { modelProvider: modelProvider.provider, url: url }]
                        })
                    });
                })
            } catch (error) {
                setImageIcon('')
            }
        }
    }, [modelProvider])

    const popoverContent = (
        <div className={ModelConfigStyle.popoverContent}>
            <div className={ModelConfigStyle.popoverContentTitle}>
                {modelProvider.label?.zh_Hans}
            </div>
            <div className={ModelConfigStyle.popoverContentDetail}>
                {
                    modelList?.map((subItem: any, index: number) => {
                        return <div className={ModelConfigStyle.modelSubItem} key={index}>
                            <div className={ModelConfigStyle.modelSubItemLeft}>
                                <span style={{ marginRight: '16px' }}>{subItem.label?.zh_Hans || ''}</span>
                                {
                                    [subItem.model_type].map((tag, subIndex) => {
                                        return (
                                            <Tag style={{ color: '#657083', border: '1px solid var(---, #EDF1F5)', padding: '1px 12px' }} key={subIndex}>{tag.toUpperCase()}</Tag>
                                        )
                                    })
                                }
                            </div>
                            <div className={ModelConfigStyle.modelSubItemRight}>
                                {subItem.fetch_from === 'customizable-model' &&
                                    <Button size="small" type="text" style={{ color: '#006BFF', fontSize: '14px' }} onClick={() => onClickEditModelConfigHandle(modelProvider, subItem)}>编辑</Button>
                                }
                                <div className={ModelConfigStyle.dot}></div>
                                {/* <Tag bordered={false} style={{ marginLeft: '5px', background: '#E8FFEA', color: '#00B42A' }}>已授权</Tag> */}
                            </div>
                        </div>
                    })
                }
            </div>
        </div>
    );
    return <div className={ModelConfigStyle.itemAddedCardContent}
        style={{
            background: index % 3 === 0 ? 'linear-gradient(99.37deg, #E9F4FF 5.55%, #E2ECFF 78.26%)' :
                (index % 3 === 1 ? 'linear-gradient(99.37deg, #EDF2FE 5.55%, #E4F9FF 97.03%)' :
                    'linear-gradient(99.37deg, #F1F5FF 5.55%, #E6E7FF 97.03%)')
        }}
        onMouseEnter={() => {
            setShowAddModal(true)
        }} onMouseLeave={() => {
            setShowAddModal(false)
        }}>
        <div className={ModelConfigStyle.addedItemTop}>
            {imageIcon && imageIcon.length > 0 ? <img src={imageIcon} alt="" /> : <span style={{ fontWeight: '600' }}>{modelProvider.label.zh_Hans}</span>}
            {
                ((modelProvider.configurate_methods?.length == 1 && modelProvider.configurate_methods?.includes('predefined-model')) || modelProvider.configurate_methods?.length === 2) ?
                    <Tooltip placement="topLeft" title='设置API-KEY（已授权）' >
                        <Button icon={<SettingOutlined />} onClick={(e) => {
                            e.stopPropagation()
                            setProvider()
                            // onAuthHandle(item, true)
                            // getEditProviderParams(item.provider)
                        }} className={ModelConfigStyle.addedItemTopRightBtn}>API-KEY
                            <span className={ModelConfigStyle.dot}></span>
                        </Button>
                    </Tooltip>
                    : null
            }
        </div>
        <div className={ModelConfigStyle.itemCardContentTag} >
            {
                modelProvider.supported_model_types.map((tag: any, index: number) => {
                    return <Tag style={{ color: '#657083', backgroundColor: 'rgba(255, 255, 255, 0.65)', border: '1px solid var(---, #EDF1F5)', marginRight: '0px', fontSize: '12px', padding: '1px 12px' }} key={index}>{tag.toUpperCase()}</Tag>
                })
            }
        </div>
        <div className={ModelConfigStyle.itemCardBottom}>
            {
                !showAddModal ? <span className={ModelConfigStyle.itemCardBottomTis}>{modelList?.length || 0}个模型</span> :
                    <div className={ModelConfigStyle.itemCardBottomShow}>
                        <Popover placement="rightTop" title={null} content={popoverContent}>
                            <div className={ModelConfigStyle.itemCardBottomShowLeft} onMouseEnter={onCheckMouseEnter} onMouseLeave={onCheckMouseLeave}>
                                <img src={showCheck ? ModelConfigCheckActive.src : ModelConfigCheck.src} alt="" />
                                <span style={{ color: showCheck ? '#006BFF' : '#1D2531' }}>查看{modelList?.length || 0}个模型</span>
                            </div>
                        </Popover>
                        {
                            ((modelProvider.configurate_methods?.length == 1 && modelProvider.configurate_methods?.includes('customizable-model')) || modelProvider.configurate_methods?.length === 2) ?
                                <Button className={ModelConfigStyle.addModelBtn} onClick={onAddModalHandle} type="text" icon={<PlusOutlined />}>添加模型</Button> : null
                        }
                    </div>
            }
        </div>
    </div >;
};

export default ModelConfigItemAddedCard;