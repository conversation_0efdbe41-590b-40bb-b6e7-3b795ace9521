import React, { useEffect, useState } from 'react'
import { Modal, Button, Input, Row, Col, Form, Select, Radio, Space, Switch, message } from 'antd'
import ModelConfigStyle from '@/styles/ModelConfig.module.scss'
import { ModelProviderConfigType } from './ModelConfig'
import { reqAddModelProviderModels, reqDeleteModelProviderModel } from '@/service/background/project'
import { clone } from 'ramda'

export interface IKnowledgeDataSelectModalProps {
    open: boolean,
    // showDelete: boolean,
    setIsOpen: (open: boolean) => void,
    modelProviderConfig: ModelProviderConfigType[],
    onRefreshHandle: () => void,
    isEdit: boolean,
    modelLLMList: string[],
    modelProvider: any,
    editParams: any // 编辑时的参数配置对象
}

const ModelConfigAddModal: React.FC<IKnowledgeDataSelectModalProps> = ({
    open,
    isEdit,
    setIsOpen,
    onRef<PERSON><PERSON><PERSON><PERSON>,
    modelLLMList,
    modelProviderConfig,
    modelProvider,
    editParams

}) => {
    const [form] = Form.useForm();
    const [isLoading, setIsLoading] = useState(false)
    const [isDeleteLoading, setIsDeleteLoading] = useState(false)

    const handleOk = async () => {
        form.validateFields().then(values => {
            // console.log(values)
            reqAddModelProviderModelsHandle(values)
        }).catch(errorInfo => {
            console.log(errorInfo)
        })
    }
    const handleCancel = () => {
        setIsOpen(false)
    }

    const reqAddModelProviderModelsHandle = async (params: any) => {
        setIsLoading(true)
        const newParams = clone(params)
        delete newParams.model_type
        delete newParams.model //variable
        // 非必填的 api_key 默认为 [__HIDDEN__]
        if (modelProvider?.model_credential_schema.credential_form_schemas.filter((item: any) => (item.variable === 'api_key' && item.required === false)).length > 0) {
            if (newParams.api_key.length === 0 || newParams.api_key === undefined || newParams.api_key === '[__HID****_]') {
                newParams.api_key = '[__HIDDEN__]'
            }
        }

        const customParams = {
            model: params.model,
            model_type: modelLLMList[params.model_type],
            credentials: newParams
        }
        const res = await reqAddModelProviderModels(modelProvider.provider, customParams)
        setIsLoading(false)
        // console.log('res===>', res)
        if (!res) {
            return
        }
        message.success('添加成功')
        onRefreshHandle()
        setIsOpen(false)
    }

    const reqDeleteModelProviderModelHandle = async () => {
        setIsDeleteLoading(true)
        const params = {
            provider: modelProvider.provider,
            model: form.getFieldValue('model'),
            model_type: modelLLMList[form.getFieldValue('model_type')]
        }
        const res = await reqDeleteModelProviderModel(modelProvider.provider, params)
        setIsDeleteLoading(false)
        // console.log('res===>', res)
        if (!res) {
            return
        }
        message.success('删除成功')
        onRefreshHandle()
        setIsOpen(false)
    }

    const onDelete = () => {
        // setIsOpen(false)
        reqDeleteModelProviderModelHandle()
    }

    useEffect(() => {
        if (open && !isEdit) {
            // 初始化表单
            form.resetFields()
        }
        if (editParams && Object.keys(editParams).length == 0) {
            form.resetFields()
        }
        if (isEdit && editParams) {
            // 编辑模式,遍历对象editParams，将值赋给表单
            Object.keys(editParams).forEach((key: any) => {
                if (key === 'model_type') {
                    form.setFieldsValue({ model_type: modelLLMList.indexOf(editParams.model_type) })
                } else {
                    form.setFieldsValue({ [key]: editParams[key] })
                }
            })
        }
    }, [open, isEdit, editParams])

    return (
        <>
            <Modal
                open={open}
                width={520}
                title={`添加${modelProvider?.label.zh_Hans}`}
                cancelText='取消'
                okText='导入'
                centered
                destroyOnClose
                className='knowledge-modal knowledge'
                onCancel={handleCancel}
                onOk={handleOk}
                // closeIcon={false}
                footer={<>
                    {isEdit && <Button loading={isDeleteLoading} onClick={onDelete} >删除</Button>}
                    <Button onClick={handleCancel} >取消</Button>
                    <Button loading={isLoading} type='primary' onClick={handleOk} >确定</Button>
                </>}
            >
                <div className={ModelConfigStyle.providerModalForm}>
                    <Form
                        form={form}
                        layout="vertical"
                        initialValues={{ model_type: 0 }}
                    >
                        <Row gutter={[24, 0]}>
                            {
                                <>
                                    <Col span={24}>
                                        <Form.Item
                                            label={'模型类型'}
                                            name={'model_type'}
                                            rules={[{ required: true }]}
                                        >
                                            <Radio.Group>
                                                <Space direction="vertical">
                                                    {
                                                        modelLLMList.map((item: any, index: number) => {
                                                            return (
                                                                <Radio key={index} value={index}>{item.toUpperCase()}</Radio>
                                                            )
                                                        })
                                                    }
                                                </Space>
                                            </Radio.Group>
                                        </Form.Item>
                                    </Col>
                                    <Col span={24}>
                                        <Form.Item
                                            label={modelProvider?.model_credential_schema?.model?.label.zh_Hans || '模型名称'}
                                            name={'model'}
                                            rules={[{ required: true }]}
                                        >
                                            <Input autoComplete='off' placeholder={modelProvider?.model_credential_schema?.model?.placeholder?.zh_Hans || ''} />
                                        </Form.Item>
                                    </Col>
                                    {
                                        modelProviderConfig.map((item: any, index: number) => {
                                            return (
                                                <Col span={24} key={index}>
                                                    <Form.Item
                                                        label={item.label.zh_Hans}
                                                        name={item.variable}
                                                        initialValue={item.type.indexOf('radio') > -1 ? item.options[0].value : (item.type.indexOf('select') > -1 ? item.options[0].value : '')}
                                                        rules={[{ required: item.required, message: item.placeholder?.zh_Hans || '' }]}
                                                    >
                                                        {item.type.indexOf('input') > -1 ? <Input autoComplete='off' placeholder={item.placeholder?.zh_Hans || ''} /> :
                                                            (
                                                                item.type.indexOf('select') > -1 ? <Select
                                                                    placeholder={item.placeholder?.zh_Hans || ''}
                                                                    allowClear
                                                                    options={item.options && item.options.map((option: any) => ({ label: option.label.zh_Hans, value: option.value }))} /> :
                                                                    (
                                                                        item.type.indexOf('radio') > -1 ? <Radio.Group>
                                                                            <Space direction="vertical">
                                                                                {
                                                                                    item.options && item.options.map((option: any, index: number) => {
                                                                                        return (
                                                                                            <Radio key={index} value={option.value}>{option.label.zh_Hans}</Radio>
                                                                                        )
                                                                                    })
                                                                                }
                                                                            </Space>
                                                                        </Radio.Group> : (
                                                                            item.type.indexOf('switch') > -1 ? <Switch /> : null
                                                                        )
                                                                    )
                                                            )
                                                        }
                                                    </Form.Item>
                                                </Col>
                                            )
                                        })
                                    }
                                </>
                            }
                        </Row>
                    </Form>
                </div>
            </Modal >
        </>
    )
}

export default ModelConfigAddModal
