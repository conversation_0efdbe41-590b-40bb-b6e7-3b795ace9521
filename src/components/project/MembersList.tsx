import { useEffect, useRef, useState } from "react";
import { Input, Button, Select, message, Popconfirm, Modal, Table } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import {
    reqMemberList,
    reqAddMember,
    reqChangeRole,
    reqDeleteMember,
    reqDomainList,
} from "@/service/member";
import Router, { useRouter } from "next/router";

import deleteIcon from "@/images/delete.svg";
import addMember from "@/images/addMember.svg";

import teamMemberStyles from "@/styles/TeamMembers.module.css";
import operationListStyles from "@/styles/OperationList.module.scss";
import search from '@/images/commonList/search.png'


export default function TeamMembersList(props: any) {
    const router = useRouter();
    const teamId = router.query.teamId;
    const tab = router.query.tab;
    const [editingKey, setEditingKey] = useState("");

    const { openAddMember, setOpenAddMember } = props;
    const [searchList, setSearchList] = useState(new Array());
    const [memberData, setMemberData] = useState(new Array());
    const [inputKey, setInputKey] = useState(new Date().getTime());
    const { searchName, hasMemberManger } = props;
    const [pageSize, setPageSize] = useState(20);
    const [total, setTotal] = useState(20);
    const [curPage, setCurPage] = useState(1);
    const [userName, setUserName] = useState("");
    const [runing, setRuning] = useState(false);
    function isEditing(record: any) {
        if (record == undefined) {
            return false;
        }
        return record.member_id === editingKey;
    }
    const edit = (record: any) => {
        setEditingKey(record?.member_id);
    };

    const getMemberList = async () => {
        if (
            teamId === undefined ||
            teamId === "" ||
            tab === undefined ||
            tab === ""
        ) {
            return;
        }
        const res = await reqMemberList({
            team_id: router.query.teamId,
            page: curPage,
            page_size: pageSize,
            name: searchName,
        });

        if (res && res.pagination) {
            setPageSize(res.pagination.page_size);
            setCurPage(res.pagination.page);
            setTotal(res.pagination.total);
        }
        if (res != undefined && res.data != undefined && Array.isArray(res.data)) {
            setMemberData(res.data);
        }
    };
    useEffect(() => {
        if (hasMemberManger) {
            getMemberList();
        }
    }, [searchName, curPage, pageSize, teamId, tab, hasMemberManger]);

    const deleteMember = async (member_id: any, name: any) => {
        const res = await reqDeleteMember({
            member_id,
        });
        if (res != undefined && res.message == "success") {
            message.success("删除成功");
            getMemberList();
        } else {
            message.success("删除失败");
        }
    };
    const cancel = () => {
        setEditingKey("");
    };
    const updateRole = async (record: any) => {
        let res = await reqChangeRole({
            member_id: record.member_id,
            role: record.role,
            team_id: teamId,
        });
        if (res != undefined) {
            if (res.message === "success") {
                setEditingKey("");
                message.success({
                    type: "success",
                    content: "更新成功",
                });
                getMemberList();
            } else {
                message.error({
                    type: "error",
                    content: res.context.message,
                });
            }
        } else {
            message.error({
                type: "error",
                content: "更新失败",
            });
        }
    };

    const columns = [
        {
            title: "姓名",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "角色",
            dataIndex: "role",
            key: "role",
            render: (text: any, record: any, index: any) => {
                const editable = isEditing(record);
                const show = text === 1;
                let roleName = "管理员";
                if (text === 1) {
                    roleName = "管理员";
                } else {
                    roleName = "成员";
                }
                return editable ? (
                    <Select
                        className={teamMemberStyles.selectRole}
                        options={[
                            {
                                label: "管理员",
                                value: 1,
                            },
                            {
                                label: "成员",
                                value: 2,
                            },
                        ]}
                        onChange={(value) => {
                            record.role = value;
                        }}
                        defaultValue={roleName}
                        style={{ width: "90px" }}
                    ></Select>
                ) : (
                    <span>{roleName}</span>
                );
            },
        },
        {
            title: "邮箱",
            dataIndex: "email",
            key: "email",
        },
        {
            title: "操作",
            dataIndex: "action",
            key: "action",
            render: (_: any, record: any) => {
                const editable = isEditing(record);
                return editable ? (
                    <div>
                        <Popconfirm
                            title={"当前人员角色已经被修改，是否保存？"}
                            description=""
                            onConfirm={() => {
                                updateRole(record);
                            }}
                            onCancel={() => { }}
                            okText="确认"
                            cancelText="取消"
                        >
                            <span className={operationListStyles.detailBtn}>保存</span>
                        </Popconfirm>

                        <span
                            onClick={() => cancel()}
                            className={operationListStyles.detailBtn}
                        >
                            取消
                        </span>
                    </div>
                ) : (
                    <>
                        <a
                            onClick={() => edit(record)}
                            className={operationListStyles.detailBtn}
                        >
                            编辑
                        </a>
                        <Popconfirm
                            title={"是否要删除 " + record.name + " ?"}
                            description=""
                            onConfirm={() => {
                                deleteMember(record.member_id, record.name);
                            }}
                            onCancel={() => { }}
                            okText="确认"
                            cancelText="取消"
                        >
                            <span className={operationListStyles.cancelDeployBtn}>删除</span>
                        </Popconfirm>
                    </>
                );
            },
        },
    ];
    const [messageKey, setMessageKey] = useState(0);
    const timer = useRef<any>(null);
    const getDomainList = async (name: any) => {
        let res;
        res = await reqDomainList({
            name: name.replaceAll('\'', ''),
        });

        if (res != undefined) {
            setSearchList(
                res.map((item: any) => {
                    return {
                        ...item,
                        isAdd: false,
                    };
                })
            );
        }

        if (runing) return;
        setRuning(true);
        res && res.length == 0 && message.info({content: "未搜索到该账号"});
        // res && res.length == 0 && message.info({content: "未搜索到该账号", key: messageKey});
        setTimeout(() => {
            setRuning(false);
        }, 3000);
        // timer.current = setInterval(() => {
        //     setMessageKey(messageKey+1)
        // }, 3000)
    };

    // useEffect(() => {
    //     return () => {timer.current = null}
    // }, [])

    const getAddMember = async (index: any) => {
        const activeItem = searchList[index];
        let res;

        res = await reqAddMember({
            team_id: router.query.teamId,
            id: activeItem.user_id,
            role: 2,
        });

        if (res?.message === "success") {
            message.success("添加成功");
            setOpenAddMember(false);
            getMemberList();
            searchList[index] = {
                ...searchList[index],
                isAdd: true,
            };
            setSearchList([...searchList]);
        }
    };
    useEffect(() => {
        if (openAddMember) {
            setInputKey(new Date().getTime());
        }
    }, [openAddMember]);
    useEffect(() => {
        if (localStorage.getItem("prompt_userName")) {
            setUserName(localStorage.getItem("prompt_userName") || "");
        }
        if (typeof window !== "undefined") {
            const hostname = window.location.hostname;
        }
    }, []);
    return (
        <>
            <Table
                columns={columns}
                rowKey={(record) => record.member_id}
                scroll={{ y: 720 }}
                dataSource={memberData}
                pagination={{
                    current: curPage,
                    pageSize: pageSize,
                    total: total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: any) => `共 ${total} 条记录`,
                }}
                onChange={(pagination) => {
                    setCurPage(pagination.current || 1);
                    setPageSize(pagination.pageSize || 20);
                }}
                className="commonTable normalFont"
            />
            <Modal
                open={openAddMember}
                style={{ width: 480 }}
                className={"commonModal noFooterModal"}
                onCancel={() => setOpenAddMember(false)}
                title={"添加成员"}
                footer={<></>}
            >
                <Input
                    prefix={<img src={search.src} width='16' />}
                    placeholder={"请输入成员姓名"}
                    onKeyUp={(e) => {
                        const event: any = e.target;

                        getDomainList(event.value);
                    }}
                    key={inputKey}
                />
                <div className={teamMemberStyles.searchList}>
                    {searchList.map((item, index) => {
                        return (
                            <div className={teamMemberStyles.searchItem}>
                                <div className={teamMemberStyles.nameWrapper}>
                                    <span className={teamMemberStyles.firstName}>
                                        {item.name.substr(0, 1)}
                                    </span>
                                    <span>{item.name}</span>
                                </div>
                                <span className={teamMemberStyles.email}>{item.email}</span>
                                {item.isAdd ? (
                                    <span>已添加</span>
                                ) : (
                                    <Button
                                        className={teamMemberStyles.addBtn}
                                        onClick={() => {
                                            getAddMember(index);
                                        }}
                                    >
                                        <img src={addMember.src} />
                                        <span>添加</span>
                                    </Button>
                                )}
                            </div>
                        );
                    })}
                </div>
            </Modal>
        </>
    );
}
