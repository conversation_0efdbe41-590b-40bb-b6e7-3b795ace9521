import React, { useEffect } from "react";
import ModelConfigStyle from '@/styles/ModelConfig.module.scss'
import { Button, Tag } from "antd";
import { useState } from "react";
import { PlusOutlined, SettingOutlined } from '@ant-design/icons';
import { fetchData } from "@/utils/fetch";
import { useRecoilState } from "recoil";
import { modelProviderIconUrlList } from "@/atoms/projectModelAtoms";



type ModelConfigItemCardProps = {
    modelProvider: any;
    addModel: (modelProvider: any) => void;
    setProviderHandle: (modelProvider: any) => void;
}

const ModelConfigItemCard: React.FC<ModelConfigItemCardProps> = ({ modelProvider, addModel, setProviderHandle }) => {

    const [showAddModal, setShowAddModal] = useState(false)
    const [modelProviderIconUrl, setModelProviderIconUrl] = useRecoilState(modelProviderIconUrlList)

    const onAddModalHandle = () => {
        // console.log('添加模型')
        addModel(modelProvider)
    }
    const setProvider = () => {
        // console.log('设置')
        setProviderHandle(modelProvider)
    }

    const getCacheIcon = () => {
        if (modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider)) {
            return modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider).url
        } else {
            return ''
        }
    }
    const [imageIcon, setImageIcon] = useState<any>(getCacheIcon())


    useEffect(() => {
        if (!modelProvider.icon_large) {
            setImageIcon('')
        } else {
            try {
                if (modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider)) {
                    setImageIcon(modelProviderIconUrl.find((item: any) => item.modelProvider === modelProvider.provider).url)
                    return
                }
                fetchData(modelProvider.icon_large?.zh_Hans, {}, 'get').then((res: any) => {
                    const response = new Response(res.body);
                    const type = res.headers.get('content-type')
                    response.blob().then(blob => {
                        let url = ''
                        if (type === 'image/png') {
                            url = URL.createObjectURL(blob);
                        } else {
                            const svgBlob = new Blob([blob], { type: 'image/svg+xml;charset=utf-8' });
                            url = URL.createObjectURL(svgBlob);
                        }
                        // 现在你可以使用 url 了
                        setImageIcon(url)
                        setModelProviderIconUrl((pre: any[]) => {
                            return [...pre, { modelProvider: modelProvider.provider, url: url }]
                        })
                    });
                })
            } catch (error) {
                setImageIcon('')
            }
        }
    }, [modelProvider])


    const getBottomBtn = () => {
        if (modelProvider.configurate_methods.length == 1 && modelProvider.configurate_methods.includes('predefined-model')) {
            return <Button style={{ width: '100%', marginRight: '8px' }} icon={<SettingOutlined />} onClick={setProvider}>设置</Button>
        } else if (modelProvider.configurate_methods.length == 1 && modelProvider.configurate_methods.includes('customizable-model')) {
            return <Button style={{ width: '100%' }} icon={<PlusOutlined />} onClick={onAddModalHandle}>添加模型</Button>
        } else {
            return <div className={ModelConfigStyle.itemCardSet}>
                <Button style={{ width: '40%', marginRight: '8px' }} icon={<SettingOutlined />} onClick={setProvider}>设置</Button>
                <Button style={{ width: '55%' }} icon={<PlusOutlined />} onClick={onAddModalHandle}>添加模型</Button>
            </div>
        }
    }

    const [clientWidth, setClientWidth] = useState<number>(document.body.clientWidth)
    return <div className={ModelConfigStyle.itemCardContent}
        style={{ background: '#FFF' }}
        onMouseEnter={() => {
            setShowAddModal(true)
        }} onMouseLeave={() => {
            setShowAddModal(false)
        }}>
        {imageIcon && imageIcon.length > 0 ? <img src={imageIcon} alt="" /> : <span style={{ fontWeight: '600' }}>{modelProvider.label.zh_Hans}</span>}
        <div className={ModelConfigStyle.itemCardContentTitle}>
            {modelProvider.description?.zh_Hans}
        </div>
        <div className={ModelConfigStyle.itemCardContentTag} style={{ minHeight: clientWidth > 1355 ? '53px' : '80px' }} >
            {showAddModal ?
                getBottomBtn() :
                modelProvider.supported_model_types.map((tag: any, index: number) => {
                    return <Tag style={{ marginRight: '0px', fontSize: '12px', padding: '1px 12px' }} key={index}>{tag.toUpperCase()}</Tag>
                })
            }
        </div>
    </div >;
};

export default ModelConfigItemCard;