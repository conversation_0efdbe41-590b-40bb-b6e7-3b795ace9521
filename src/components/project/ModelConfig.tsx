
import { useEffect, useState } from "react";
import ModelConfigStyle from '@/styles/ModelConfig.module.scss'
import { Col, Row, Spin } from "antd";
import ModelConfigItemCard from './ModelConfigItemCard'
import ModelConfigItemAddedCard from './ModelConfigItemAddedCard'
import ModelConfigAddModal from "./ModelConfigAddModal";
import ModelConfigProviderModal from "./ModelConfigProviderModal";
import { reqGetModelProvider, reqGetModelProviderModels, reqGetModelProviderParams, reqGetAddModelParams } from '@/service/background/project'
import { fetchData } from '@/utils/fetch'
import { clone, set } from "ramda";

import { waiWangHosts } from '@/config/commonConfig';

const apiProxyConfig = require('../../../api.proxy.config');
const apiENV = process.env.NEXT_PUBLIC_API_ENV as string || 'development'
export const getUrl = ((apiProxyConfig[apiENV] as { source: string, destination: string }[]).find(item => item.source.startsWith('/api'))?.destination as string).split('/:path*')[0]

export type ModelProviderConfigType = {
    [key: string]: any;
}

const ModelConfig = () => {

    const [addedList, setAddedList] = useState<any[]>([]) // 已经添加激活的
    const [providerModelList, setProviderModelList] = useState([]) // 供应商的模型列表
    const [providerModelCache, setProviderModelCache] = useState<any[]>([]) // 供应商的模型列表进行缓存 : key: providerName, value: modelList
    const [isUnfoldList, setIsUnfoldList] = useState<string[]>([]) // 是否展开
    const [isShowProviderModal, setIsShowProviderModal] = useState(false) // 是否展示模型供应商弹窗
    const [isShowAddModal, setIsShowAddModal] = useState(false) // 是否展示添加模型弹窗
    const [currentProvider, setCurrentProvider] = useState<any>(null) // 当前模型供应商
    const [isEditProviderModal, setIsEditProviderModal] = useState(false) // 是否正在编辑模型供应商弹窗
    const [isEditAddModal, setIsEditAddModal] = useState(false) // 是否正在编辑添加模型弹窗
    const [modelProviderList, setModelProviderList] = useState<any[]>([]) // 模型供应商列表
    const [addedModelIconList, setAddedModelIconList] = useState<any[]>([]) // 已添加模型的icon URL列表,进行缓存
    const [editModelProviderParams, setEditModelProviderParams] = useState<any>({}) // 填写的编辑的模型供应商的参数
    const [editAddModelParams, setEditAddModelParams] = useState<any>({}) // 填写的编辑的模型供应商的参数
    const [isLoading, setIsLoading] = useState(false) // 是否正在加载

    useEffect(() => {
        getProviderList()
    }, [])

    const getProviderList = async () => {
        setIsLoading(true)
        setIsUnfoldList([]);
        const res = await reqGetModelProvider({})
        setIsLoading(false)
        if (!res) return
        let cloneRes = clone(res)
        const hostname = window.location.hostname;
        if (waiWangHosts.includes(hostname)) { // 外网不显示OpenAI
            cloneRes = cloneRes.filter((item: any) => item.provider !== 'openai')
        }
        const addedList = cloneRes.filter((item: any) => (item.custom_configuration.status === 'active' || item.system_configuration.enabled === true))
        const modelProviderList = cloneRes.filter((item: any) => (item.custom_configuration.status === 'no-configure' && item.system_configuration.enabled !== true))
        Promise.all(addedList.map(async (item: any) => {
            // if (!item.icon_large) return
            try {
                const url = await getImageUrl(item.icon_large?.zh_Hans || '')
                setAddedModelIconList((prev: any) => {
                    return [...prev, { provider: item.provider, url }]
                })
                const providerModelList = await reqGetModelProviderModels(item.provider)
                if (!providerModelList) return
                setProviderModelCache((pre: any) => {
                    return [...pre, { providerName: item.provider, modelList: providerModelList }]
                })
            } catch (error) {
            }
        }
        ))
        setAddedList(addedList)
        setModelProviderList(modelProviderList)
    }

    const getImageUrl = async (paramUrl: string) => {
        if (!paramUrl) return ''
        return new Promise((resolve, reject) => {
            try {
                fetchData(paramUrl, {}, 'get').then((res: any) => {
                    const response = new Response(res.body);
                    const type = res.headers.get('content-type')
                    response.blob().then(blob => {
                        let url = ''
                        if (type === 'image/png') {
                            url = URL.createObjectURL(blob);
                        } else {
                            const svgBlob = new Blob([blob], { type: 'image/svg+xml;charset=utf-8' });
                            url = URL.createObjectURL(svgBlob);
                        }
                        resolve(url)
                    });
                })
            } catch (error) {
                reject('')
            }
        })
    }

    const onAddModelHandle = (item: any) => {
        setIsEditAddModal(false)
        setIsShowAddModal(true)
        setCurrentProvider(item)
    }

    const onAuthHandle = (item: any, isEdit: boolean = false) => {
        // console.log('设置API-KEY', itemName)
        setCurrentProvider(item)
        setIsShowProviderModal(true)
        isEdit ? setIsEditProviderModal(true) : setIsEditProviderModal(false)
    }

    const onClickEditModelConfigHandle = (item: any, modelItem: any) => {
        setIsEditAddModal(true)
        setIsShowAddModal(true)
        setCurrentProvider(item)
        getEditAddModelParams(item.provider, modelItem.model_type, modelItem.model)
    }

    // 获取供应商编辑的参数
    const getEditProviderParams = async (providerName: string) => {
        setEditModelProviderParams({})
        // 获取填写的编辑参数
        const res = await reqGetModelProviderParams(providerName)
        if (!res) return
        setEditModelProviderParams(res.credentials)
    }
    // 获取添加的模型的编辑参数
    const getEditAddModelParams = async (providerName: string, model_type: string, model: string) => {
        setEditAddModelParams({})
        // 获取填写的编辑参数
        const res = await reqGetAddModelParams(providerName, {
            model_type,
            model
        })
        if (!res) return
        setEditAddModelParams({ ...res.credentials, model_type: model_type, model: model })
    }

    //  // 获取当前视口的宽度
    const [clientWidth, setClientWidth] = useState<number>(document.body.clientWidth)
    useEffect(() => {
        window.onresize = () => {
            setClientWidth(document.body.clientWidth)
        }
        return () => {
            window.onresize = null
        }
    }, [])

    return (
        <div className={ModelConfigStyle.container}>
            <Spin spinning={isLoading} tip="加载中...">
                {
                    addedList.length > 0 && <div className={ModelConfigStyle.addedContainer}>
                        <div className={ModelConfigStyle.addedContainerTitle}>已添加模型 {'(' + addedList.length + ')'}</div>
                        <div className={ModelConfigStyle.addedContainerContent}>
                            <Row gutter={[12, 12]}>
                                {
                                    addedList.map((item, index) => (
                                        <Col span={8} key={index}>
                                            <ModelConfigItemAddedCard addModel={onAddModelHandle} index={index} modelProvider={item}
                                                setProviderHandle={(item) => {
                                                    onAuthHandle(item, true)
                                                    getEditProviderParams(item.provider)
                                                }} onClickEditModelConfigHandle={onClickEditModelConfigHandle}
                                                modelList={providerModelCache.find((ele: any) => ele.providerName === item.provider)?.modelList} />
                                        </Col>
                                    ))
                                }
                            </Row>
                        </div>
                    </div>
                }

                <div className={ModelConfigStyle.addingContainer} style={{
                    marginTop: addedList.length > 0 ? '24px' : '0'
                }}>
                    <div className={ModelConfigStyle.addingContainerTitle}>模型供应商</div>
                    <div className={ModelConfigStyle.addingContainerContent}>
                        <Row gutter={[12, 12]}>
                            {
                                modelProviderList.map((item, index) => (
                                    <Col span={6} key={clientWidth > 1320 ? index : index + 1320}>
                                        <ModelConfigItemCard modelProvider={item} addModel={onAddModelHandle} setProviderHandle={onAuthHandle} />
                                    </Col>
                                ))
                            }
                        </Row>
                    </div>
                </div>
                <ModelConfigAddModal onRefreshHandle={getProviderList} modelProvider={currentProvider} isEdit={isEditAddModal}
                    editParams={editAddModelParams}
                    modelLLMList={currentProvider?.supported_model_types || []}
                    modelProviderConfig={currentProvider?.model_credential_schema?.credential_form_schemas || []}
                    open={isShowAddModal} setIsOpen={setIsShowAddModal} />
                <ModelConfigProviderModal onRefreshHandle={getProviderList} isEdit={isEditProviderModal} editParams={editModelProviderParams}
                    modelProviderConfig={currentProvider?.provider_credential_schema?.credential_form_schemas || []}
                    showDelete={addedList.includes(currentProvider)} modelProvider={currentProvider} open={isShowProviderModal}
                    setIsOpen={setIsShowProviderModal} />
            </Spin >
        </div >
    )
}

export default ModelConfig;