import { useEffect, useState } from 'react';
import { Button, Select, message, Table, Popconfirm, Spin, Empty } from 'antd';
import type { TableColumnsType } from 'antd';
import { useRouter } from 'next/router'

import { reqCancelDeploy, reqDeployFileList, reqDeployList, reqReDeploy } from '@/service/common'
import {reqMonitorTeamBar} from '@/service/monitor';
import commonListStyles from '@/styles/CommonList.module.scss'



import DeployModal from '@/components/common/DeployModal'

import operationListStyles from '@/styles/OperationList.module.scss'
import styles from '@/styles/Common.module.scss'

export default function OperationList(props: any) {

    const router = useRouter();
    const teamId = router.query.teamId;

    const [promptTitle, setPromptTitle] = useState('');
    const [deployList, setDeployList] = useState(new Array())
    const [openDeploy, setOpenDeploy] = useState(false)
    const [isDeploySuccess, setIsDeploySuccess] = useState(-1)
    const [deployIndex,setDeployIndex] = useState(0)
    const [status, setStatus] = useState('全部')
    const [fileName, setFileName] = useState('全部')
   
    const [curPage, setCurPage] =  useState(1)
    const [pageSize, setPageSize] = useState(20)
    const [total, setTotal] = useState(20)


    const [upDateStamp, setUpDateStamp] = useState(0)
    const [chartLoading, setChartLoading] = useState(false)
    const [chartData, setChartData] = useState(new Array());
    const [intervalType, setIntervalType] = useState(5);



  
    const { callStatus, fileStatus, searchFileName } = props
    function jumpTo(record:any){
        let type=""
        if (record.template_type == 1 ){
            type ="text"

        }
        if (record.template_type == 2) {
            type = "imge"

        }
        if (record.template_type == 3) {
            type = "flow"

        }
        if (record.template_type == 4) {//知识库
       

        }
        if (record.template_type == 5) {
            type = "api"

        }
        if (record.template_type == 6) {
            type = "agent"

        }


        router.push('/operationDetail?id=' + record.template_id +'&type='+type+'&teamId='+teamId)

    }

    const getDeployList = async () => {
        const res = await reqDeployList({
            team_id: teamId,
            page: curPage,
            page_size: pageSize,
            status:  fileStatus,
            call_status: callStatus === '全部' ? -1 : callStatus,
            chain_no: fileName === '全部' ? "" : fileName

        })
        if(res!=undefined){
             setDeployList(res.deployment_list);
           
            if (res && res.pagination){
                setPageSize(res.pagination.page_size)
                setTotal(res.pagination.total),
                setCurPage(res.pagination.page)
            }

        }
        
    }

    // const getDeployFileList = async () => {
    //     // const res = await reqDeployFileList({
    //     //     team_id: teamId
    //     // })
    //     const res = [{
    //         "chain_no": "acDHEk5e231102zm3uma",
    //         "chain_name": "Flow4"
    //     }]
    //     const dataArray:any = [];
    //     res.forEach((item:any) => {
    //         dataArray.push({
    //             label: item.chain_name,
    //             value: item.chain_no
    //         })
    //     })
    //     setFileNameList([...fileNameList.concat(dataArray)])
    // } 
    
    useEffect(() => {
        if(isDeploySuccess != -1) {
            message.success('部署成功')
            deployList[deployIndex].status = 1
            setDeployList([...deployList])
            setOpenDeploy(false)
        }
    },[isDeploySuccess])
    getTemplateValue

    useEffect(()=>{
        if(teamId) {
            getDeployList();
         
        }
    }, [teamId,callStatus, fileStatus, searchFileName,pageSize,curPage])

    useEffect(() => {
        if(upDateStamp) {
            getDeployList();
        }
    },[upDateStamp])
    function getTemplateValue(value:any){
        if(value ==undefined){
            return '-'
        }
        if (value == 1) {
            return '文生文'
        }
        if (value == 2) {
            return '文生图'
        }
        if (value == 3) {
            return 'flow'
        }

        if (value == 4) {
            return '知识库'
        }
        if (value == 5) {
            return 'api'
        }
        if (value == 6) {
            return 'agent'
        }
      
            return '-'
       
    }

    const statusObj:any = {
        "1": '成功',
        "2": "失败",
        "3": "取消",
        "4": '异常',
        "0": '未调用'
    }
    const columns: TableColumnsType<any> = [
        {
            title: '文件名',
            dataIndex: 'chain_name',
            key: 'chain_name',
            render: (text: any, record: any) => {
               return <span  onClick={()=>jumpTo(record)} className={operationListStyles.cancelDeployBtn}>{text}</span>
            },
            // fixed: 'left',
        },
        {
            title: '类型',
            dataIndex: 'template_type',
            key: 'template_type',
            render: (text: any, record: any) => {
               
                return <span>{getTemplateValue(text)}</span>
            }
        },
        {
            title: 'ID',
            dataIndex: 'template_id',
            width:80,
            key: 'template_id'
        },
        
        {
            title: '最后一次被调用时间',
            dataIndex: 'last_called_time',
            width: 240,
            key: 'last_called_time',
            render: (text:any) => {
                const obj = new Date(text*1000);
                const month = obj.getMonth()+1;
                const day = obj.getDate();
                const hour = obj.getHours();
                const min = obj.getMinutes();
                const second = obj.getSeconds();
                return <div>{text == 0 ? '-' : obj.getFullYear()+"-"+(month > 9 ? month : '0'+month)+"-"+(day > 9 ? day : '0'+day)+' '+(hour > 9 ? hour : '0'+hour)+':'+(min >9 ? min : '0'+ min)+':'+(second >9 ? second : '0'+ second)}</div>
            }
        },
        {
            title: '调用量',
            dataIndex: 'call_count',
            width:80,
            key: 'call_count'
        },
        {
            title: '被调用情况',
            dataIndex: 'call_status',
            width: 160,
            key: 'call_status',
            render: (text:any) => {
                return <><span>{statusObj[text] || ''}</span></>
            }
        },
        {
            title: '文件状态',
            dataIndex: 'status',
            width: 160,
            key: 'status',
            render: (text:any) => {
                return <div><span className={text == '1' ? operationListStyles.fileSuccessStatus : operationListStyles.fileErrorStatus}></span>{text == '1' ? '已部署' : '已停用'}</div>
            }
        },
        {
            title: '部署人',
            dataIndex: 'user_name',
            key: 'user_name'
        },
        {
            title: '部署时间',
            dataIndex: 'deploy_time',
            key: 'deploy_time',
            render: (text:any) => {
                return <div>{text.replace('T' ,' ')}</div>
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            fixed:'right',
            render: (_:any, record:any,index:number) => {
                return <>
                {record.status == '2' ? <span className={operationListStyles.cancelDeployBtn} onClick={async() => {
                    setOpenDeploy(true);
                    setDeployIndex(index);

                }}>重新部署</span> : <Popconfirm
                    title={"是否要取消部署 "+record.chain_name+' ?'}
                    description=""
                    onConfirm={async() => {
                        const res = await reqCancelDeploy({
                            id: record.id
                        })
                        message.success('取消成功')
                        deployList[index].status = 2
                        setDeployList([...deployList])
                    }}
                    onCancel={() => {

                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <span className={operationListStyles.cancelDeployBtn}>取消部署</span>
                </Popconfirm>
                }
                <a 
                    href={"./monitor?deployId=" + record.id + '&templateId=' + record.template_id +'&teamId='+teamId+'&template_type='+record.template_type} 
                    className={operationListStyles.detailBtn}
                >服务监控</a></>
            }
        }
    ];

    const getMonitor = async() => {
        setChartLoading(true)
        const res = await reqMonitorTeamBar({
            interval: intervalType,
        })
        setChartData(res?.list)
        setChartLoading(false)
    }
    
    useEffect(()=>{
        if(teamId) {
            getMonitor()
        }
    },[teamId,intervalType])

    useEffect(()=>{
        document.getElementById('container')!.innerHTML= ''
        if(!chartData?.length) {
            return ;
        }
        getChart()
    },[chartData?.length])

    const getChart = async () => {
        const G2Plot = await import('../../utils/g2plot')
        const { Column } = G2Plot;
        const stackedColumnPlot = new Column('container', {
            data: chartData,
            isGroup: true,
            height: 330,
            xField: 'date',
            yField: 'count',
            seriesField: 'name',
            /** 设置颜色 */
            color: ['#47A1FF', '#74E0B7','#FFCD58','#BFC9D5','#FF5E68'],
            /** 设置间距 */
            marginRatio: 0.1,
            label: {
                // 可手动配置 label 数据标签位置
                position: 'middle', // 'top', 'middle', 'bottom'
                // 可配置附加的布局方法
                layout: [
                    // 柱形图数据标签位置自动调整
                    { type: 'interval-adjust-position' },
                    // 数据标签防遮挡
                    { type: 'interval-hide-overlap' },
                    // 数据标签文颜色自动调整
                    { type: 'adjust-color' },
                ]
            },
            legend: { position: 'top' } 
        });
        stackedColumnPlot.render();
    }

    
    return (
        <>
        <div className={operationListStyles.monitorChart}>
            <div className={operationListStyles.monitorChartTitle}>
                <div className={operationListStyles.monitorChartTitleContent}>监控数据</div>
                <div>
                    <div className={operationListStyles.monitorChartLabel}>时间周期</div>
                    <Select
                        className='noBorderSelector'
                        style={{width:150}}
                        options={[
                            {
                                label: '今天',
                                value: 5
                            },
                            {
                                label: '昨天',
                                value: 4
                            },
                            {
                                label: '最近7天',
                                value: 1
                            },
                            {
                                label: '最近30天',
                                value: 2
                            },
                            {
                                label: '最近12个月',
                                value: 3
                            },
                        ]}
                        value={intervalType}
                        onChange={v => {
                            setIntervalType(v)
                        }}
                    ></Select>
                </div>

            </div>
            
            <Spin spinning={chartLoading}><div id="container" className={operationListStyles.container}></div></Spin>
            {chartData?.length ? '' : <div className={operationListStyles.barEmptyWrapper}><Empty description="暂无记录" /></div>}
        </div>
        <div   className={commonListStyles.tableList + ' normalFont ' + operationListStyles.monitorTable}>
            <Table 
                columns={columns} 
                dataSource={deployList} 
                    scroll={{ x: 'max-content',  y: 720 }}
                    rowKey={(record) => record.id}

                pagination={{
                        current: curPage,
                        pageSize: pageSize,
                        total: total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                    showTotal: (total:any) => `共 ${total} 条记录`}}
                onChange={(pagination) => {
                    setCurPage(pagination.current || 1)
                    setPageSize(pagination.pageSize||20)
                    
                    setUpDateStamp(new Date().getTime())
                
                }}
                className='commonTable normalFont logTable'
                // className={operationListStyles.tableWrapper + ' commonTable normalFont'}
            />
        </div>

        <DeployModal 
            openDeploy={openDeploy}
            setOpenDeploy={setOpenDeploy}
            chainId={deployList.length && deployList[deployIndex].chain_no}
            teamId={teamId}
            deployId={deployList.length && deployList[deployIndex].id}
            deployType={'reDeploy'}
            setIsDeploySuccess={setIsDeploySuccess}
            isNotJump={true}
        />
        </>
    )
}
