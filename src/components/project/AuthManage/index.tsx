import { useEffect, useState, useMemo } from 'react';
import { Input, Tabs, Modal, Form, Button, Select, message } from 'antd';
import authStyle from './index.module.scss';
import ItemAuth from './Auth';
import ItemFlow from './FlowAgent';

import { reqPluginBriefList } from '@/service/api';
import {
    reqAuthPluginList,
    reqCreateAuth,
    reqEditAuth
} from '@/service/auth';

import search from '@/images/commonList/search.png';
import warnBlue from '@/images/warnBlue.svg';

export default function TeanAuthManage(props: any) {
    const [open, setOpen] = useState(false);
    const [apiList, setApiList] = useState(new Array<any>());
    const [originList, setOriginList] = useState(new Array<any>());
    const [options, setOptions] = useState(new Array<any>());
    const [title, setTitle] = useState('新建');
    const [activeId, setActiveId] = useState(-1);
    const [apiId, setApiId] = useState(0);
    const [searchValue, setSearchValue] = useState('')
    const [editId, setEditId] = useState(0);
    const [activeKey, setActiveKey] = useState('1');
    const [authType, setAuthType] = useState(2);

    const initForm = {
        apiName: 0,
        authName: '',
        appId: '',
        appSecret: '',
        dbUsername: '',
        dbPassword: '',
        dbIp: '',
        dbPort: '',
        dbDatabase: '',
        keyName: '',
        keyValue: ''
    }
    const [formData, setFormData] = useState(initForm);

    const [form] = Form.useForm();

    const handleEdit = (item: any) => {
        setAuthType(item.auth_type);
        setEditId(item.id);
        const info = JSON.parse(item.auth_info);
        if (item.auth_type === 2) {
            setFormData({
                ...initForm,
                apiName: item.api_id,
                authName: item.auth_name,
                appId: info.app_id,
                appSecret: info.secret,
            })
        } else if (item.auth_type === 4) {
            setFormData({
                ...initForm,
                apiName: item.api_id,
                authName: item.auth_name,
                dbDatabase: info.db_database,
                dbUsername: info.db_username,
                dbPassword: info.db_password,
                dbIp: info.db_ip,
                dbPort: info.db_port
            })
        } else if (item.auth_type === 3) {
            setFormData({
                ...initForm,
                apiName: item.api_id,
                authName: item.auth_name,
                keyName: info.key_name,
                keyValue: info.key_value,
            })
        }

        setTitle('编辑')
        setOpen(true)
    }

    const handleAuthCancel = () => {
        reqAuthPluginList({}).then((res) => {
            setApiList(res);
            setOriginList(res);
        })
    }

    const optionIds = useMemo(() => {
        return options.map((item) => item.value);
    }, [options])

    const tabItems = [
        {
            key: '1',
            label: '关联授权',
            children: <ItemAuth apiId={apiId} open={open} activeKey={activeKey} optionIds={optionIds} clickEdit={handleEdit} clickCancel={handleAuthCancel} />
        },
        {
            key: '2',
            label: '关联技能',
            children: <ItemFlow apiId={apiId} isFlow={true} activeKey={activeKey} />
        },
        {
            key: '3',
            label: '关联智能体',
            children: <ItemFlow apiId={apiId} isFlow={false} activeKey={activeKey} />
        }
    ]

    const initList = () => {
        reqAuthPluginList({}).then((res) => {
            setApiList(res);
            setOriginList(res);

            if (res && res.length) {
                setActiveId(0)
                setApiId(res[0].api_id);
            }
        })

        reqPluginBriefList({}).then((res) => {
            const optionArr = res ? res.map((item: any) => ({
                value: item.id,
                label: item.name,
                authType: item.auth_type
            })) : []

            if (optionArr.length) {
                setOptions(optionArr);
                setFormData({
                    ...initForm,
                    apiName: optionArr[0].value
                });
                setAuthType(optionArr[0].authType)
            }
        })
    }

    const onFinish = () => {
        let curAuthType = 2;
        let authInfo;

        options.forEach((item: any) => {
            if (item.value === formData.apiName) {
                curAuthType = item.authType;
            }
        })

        if (curAuthType === 2) {
            authInfo = JSON.stringify({
                app_id: formData.appId,
                secret: formData.appSecret
            })
        } else if (curAuthType === 4) {
            authInfo = JSON.stringify({
                db_database: formData.dbDatabase,
                db_username: formData.dbUsername,
                db_password: formData.dbPassword,
                db_ip: formData.dbIp,
                db_port: formData.dbPort
            })
        } else if (curAuthType === 3) {
            authInfo = JSON.stringify({
                key_name: formData.keyName,
                key_value: formData.keyValue
            })
        }

        if (title === '新建') {
            reqCreateAuth({
                api_id: formData.apiName,
                auth_name: formData.authName,
                auth_type: curAuthType,
                auth_info: authInfo
            }).then((res) => {
                if (res) {
                    reqAuthPluginList({}).then((res) => {
                        setApiList(res);
                        setOriginList(res);
                    })

                    setOpen(false)
                    message.success('新建授权成功');

                    setFormData({
                        ...initForm,
                        apiName: options[0].value
                    });
                }
            })
        } else {
            reqEditAuth({
                auth_id: editId,
                api_id: formData.apiName,
                auth_name: formData.authName,
                auth_type: curAuthType,
                auth_info: authInfo
            }).then((res) => {
                if (res) {
                    setOpen(false)
                    message.success('编辑授权成功');

                    setFormData({
                        ...initForm,
                        apiName: options[0].value
                    });
                }
            })
        }
    }

    const handleCancel = () => {
        setOpen(false);
        setFormData({
            ...initForm,
            apiName: options[0].value
        });
    }

    const handleItemDetail = (item: any, index: number) => {
        setActiveId(index)
        setApiId(item.api_id);
    }

    const handleSearch = (event: any) => {
        const search = event.target.value;
        setSearchValue(search);

        const list = new Array<any>();
        originList.forEach((item: any) => {
            if (item.name.indexOf(search) !== -1) {
                list.push(item)
            }
        })
        setApiList(list);
    }

    const handleCreate = () => {
        setOpen(true);
        setTitle('新建');
        initList();
    }

    useEffect(() => {
        initList();
    }, [])

    return (
        <div className={authStyle.content}>
            <div className={authStyle.left}>
                <div className={authStyle.authNumber}>
                    <div className={authStyle.authNumberText}>已授权配置</div>
                    <span>{apiList?.length}</span>
                </div>
                <div className={authStyle.search}>
                    <Input
                        value={searchValue}
                        placeholder="请输入插件名称"
                        prefix={<img src={search.src} width='16' />}
                        allowClear
                        style={{ width: '210px', borderRadius: '8px', marginRight: '16px' }}
                        onChange={handleSearch}
                    />
                    <div className={authStyle.createBtn} onClick={handleCreate}>新建</div>
                </div>
                <div className={authStyle.apiList}>
                    {apiList?.length && apiList.map((item, index) => (
                        <div
                            onClick={() => { handleItemDetail(item, index) }}
                            className={activeId === index ? authStyle.apiActiveItem : authStyle.apiItem}
                        >
                            <div className={authStyle.apiItemLeft}>
                                <img src={item.images} alt="图像" />
                            </div>
                            <div className={authStyle.apiItemRight}>
                                <div className={authStyle.apiItemTitle}>{item.name}</div>
                                <div className={authStyle.apiItemCorrelation}>
                                    <div className={authStyle.apiCorrelationAuth}>关联授权：{item.total}</div>
                                    <div className={authStyle.apiCorrelationFlow}>关联服务：{item.total_service}</div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className={authStyle.right}>
                <Tabs
                    defaultActiveKey="1"
                    items={tabItems}
                    activeKey={activeKey}
                    onChange={(key: string) => { setActiveKey(key) }}
                />
            </div>
            <Modal
                open={open}
                style={{ width: 520, padding: 0 }}
                className={"commonModal"}
                wrapClassName={authStyle.authModal}
                onCancel={handleCancel}
                footer={
                    <>
                        <Button onClick={handleCancel}>取消</Button>
                        <Button
                            type="primary"
                            onClick={
                                () => {
                                    onFinish()
                                }
                            }
                        >
                            确认
                        </Button>
                    </>
                }
            >
                <div className={authStyle.authHeader}>{title}授权配置</div>
                <div className={authStyle.authWarn}>
                    <img src={warnBlue.src} alt="" />
                    授权信息项目组内成员均可使用，请谨慎添加授权
                </div>
                <div className={authStyle.authForm}>
                    <Form
                        form={form}
                        layout="vertical"
                    >
                        <Form.Item
                            label="插件"
                            required
                        >
                            <Select
                                value={formData.apiName}
                                placeholder='请输入插件名称'
                                options={options}
                                onChange={(value: number) => {
                                    setAuthType(options.find(item => item.value === value)?.authType)
                                    setFormData({
                                        ...formData,
                                        apiName: value
                                    })
                                }}
                            />
                        </Form.Item>
                        <Form.Item
                            label="授权配置名称"
                            required
                        >
                            <Input
                                value={formData.authName}
                                placeholder='请输入链接器配置名称'
                                onChange={(event: any) => {
                                    setFormData({
                                        ...formData,
                                        authName: event.target.value
                                    })
                                }}
                            />
                        </Form.Item>
                        {authType === 2 && <>
                            <Form.Item
                                label="AppID"
                                required
                            >
                                <Input.TextArea
                                    value={formData.appId}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入 AppID'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            appId: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="AppSecret"
                                required
                            >
                                <Input.TextArea
                                    value={formData.appSecret}
                                    showCount
                                    maxLength={400}
                                    placeholder='请输入 AppSecret'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            appSecret: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                        </>}
                        {authType === 4 && <>
                            <Form.Item
                                label="数据库名称"
                                required
                            >
                                <Input
                                    value={formData.dbDatabase}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入数据库名称'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbDatabase: event.target.value
                                        })
                                    }}

                                />
                            </Form.Item>
                            <Form.Item
                                label="账号"
                                required
                            >
                                <Input
                                    value={formData.dbUsername}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入账号'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbUsername: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="密码"
                                required
                            >
                                <Input
                                    value={formData.dbPassword}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入密码'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbPassword: event.target.value
                                        })

                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="IP地址"
                                required
                            >
                                <Input
                                    value={formData.dbIp}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入IP地址'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbIp: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label="端口号"
                                required
                            >
                                <Input
                                    value={formData.dbPort}
                                    showCount
                                    maxLength={100}
                                    placeholder='请输入端口号'
                                    onChange={(event: any) => {
                                        setFormData({
                                            ...formData,
                                            dbPort: event.target.value
                                        })
                                    }}
                                />
                            </Form.Item>
                        </>}
                        {authType === 3 && <>
                            <Form.Item
                                label="name"
                                required
                            >
                                <Input.TextArea
                                    value={formData.keyName}
                                    showCount
                                    maxLength={50}
                                    placeholder="请输入name"
                                    onChange={(event: any) => setFormData({
                                        ...formData,
                                        keyName: event.target.value
                                    })}
                                />
                            </Form.Item>
                            <Form.Item
                                label="value"
                                required
                            >
                                <Input.TextArea
                                    value={formData.keyValue}
                                    showCount
                                    maxLength={200}
                                    placeholder="请输入value"
                                    onChange={(event: any) => setFormData({
                                        ...formData,
                                        keyValue: event.target.value
                                    })}
                                />
                            </Form.Item>
                        </>}
                    </Form>
                </div>
            </Modal>
        </div>
    )
}
