.content {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;
    background-color: #FFFFFF;

    .left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        border-right: 1px solid #EDF1F5;

        .authNumber {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            padding: 12px 16px;

            .authNumberText, span {
                font-size: 14px;
                line-height: 24px;
            }

            .authNumberText {
                color: #1B2532;
                font-weight: 600;
                margin-right: 8px;
            }

            span {
               color: #9EA7B8;
            }
        }

        .search {
            display: flex;
            flex-direction: row;
            padding: 0 16px 16px 16px;

            .createBtn {
                height: 32px;
                line-height: 18px;
                padding: 5px 16px;
                justify-content: center;
                align-items: center;
                border-radius: 8px;
                background: #1D7CFF;
                color: #fff;
                border: 1px solid transparent;
                box-shadow: none;
                cursor: pointer;
            }

            .createBtn:hover {
                opacity: 0.8;
            }
        }

        .apiList::-webkit-scrollbar {
            display: none;
        } 

        .apiList {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            height: calc(100vh - 200px);
            overflow-y: auto;

            .apiItem:hover {
                background-color: #F7F8FA;
            }

            .apiItem, .apiActiveItem {
                display: flex;
                flex-direction: row;
                padding: 12px 16px;
                width: 320px;

                .apiItemLeft {
                    margin-right: 16px;

                    img {
                        width: 48px;
                        height: 48px;
                        border-radius: 8px;
                    }
                }

                .apiItemRight {
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;

                    .apiItemTitle {
                        color: #1B2532;
                        font-size: 14px;
                        line-height: 22px;
                        margin-bottom: 4px;
                    }

                    .apiItemCorrelation {
                        width: 207px;
                        display: flex;
                        flex-direction: row;
                        color: #9EA7B8;
                        font-size: 12px;
                        line-height: 20px;

                        .apiCorrelationAuth {
                            width: 120px;
                        }
                    }
                }
            }

            .apiActiveItem {
                background-color: #F7F8FA;

                .apiItemRight {
                    .apiItemTitle {
                        color: #1D7CFF;
                    }       
                }
            }
        }
    }

    .right {
        width: calc(100% - 321px);
        
        :global {
            .ant-tabs .ant-tabs-tab {
                margin-left: 16px;
                padding: 9px 8px;
                margin-top: 4px;
            }
        }
    }
}

.authModal {
    display: flex;
    flex-direction: column;

    :global {
        .ant-modal-body {
            padding: 0 !important;
        }
    }

    .authHeader {
        background-color: #F7F8FA;
        border-radius: 8px 8px 0 0;
        padding: 16px 24px;
        color: #1B2532;
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
    }

    .authWarn {
        background-color: #E8F5FF;
        padding: 13px 24px;
        color: #1D2531;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        flex-direction: row;
        align-items: center;

        img {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
    }

    .authForm {
        padding: 24px;
    }
}