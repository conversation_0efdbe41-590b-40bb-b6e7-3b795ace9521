import { useEffect, useState } from "react";
import { Table, Pagination } from "antd";
import { reqPluginFlowInell } from "@/service/auth";
import styles from "./FlowAgent.module.scss";
import operationListStyles from '@/styles/OperationList.module.scss'

import { useRouter } from "next/router";
import { getCurrentUrlParamValue } from "@/utils/url";

export default function ItemFlow(props: any) {
    const { apiId, isFlow, activeKey } = props

    const router = useRouter();
    const initPage = {
        page: 1,
        total: 0,
        pageSize: 10,
        pageTotal: 0
    }
    const [pageInfo, setPageInfo] = useState(initPage);
    const [tableData, setTableData] = useState(new Array<any>());

    const queryData = () => {
        if (apiId) [
            reqPluginFlowInell({
                api_id: apiId,
                page: pageInfo.page,
                page_size: pageInfo.pageSize,
                relation_type: isFlow ? 3 : 6
            }).then((res) => {
                res && setTableData(res.list)
                res && setPageInfo({
                    ...pageInfo,
                    total: res.total,
                    pageTotal: Math.ceil(res.total / pageInfo.pageSize)
                })
            })
        ]
    }

    const handlePageChange = async(current: number, size: number) => {
        setPageInfo({
            ...pageInfo,
            page: current,
            pageSize: size,
            pageTotal: Math.ceil(pageInfo.total / size)
        })

        queryData();
    }

    const handleDetail = (id: number) => {
        const teamId = getCurrentUrlParamValue("teamId");

        if (isFlow) {
            router.push(`/flowDetailV3?id=${id}&teamId=${teamId}`);
        } else {
            router.push(`/agentDetail?agentId=${id}&teamId=${teamId}&from=2`);
        }
    }

    useEffect(() => {
        queryData()
    }, [apiId, activeKey])

    const columns = [
        {
            title: '授权名称',
            dataIndex: 'auth_name',
            key: 'auth_name'
        },
        {
            title: `关联${isFlow ? '技能' : '智能体'}名称`,
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: '操作',
            dataIndex: 'edit',
            key: 'edit',
            render: (_: any, row: any) => (
                <span className={operationListStyles.detailBtn} onClick={() => handleDetail(row.relation_id)}>查看</span>
            )
        }
    ]

    return (
        <div className={styles.flow}>
            <Table
                columns={columns}
                dataSource={tableData}
                pagination={false}
            />
            <div className={styles.footer}>
                <div className={styles.footerTotal}>
                    共<span> {pageInfo.total} </span>条
                    第<span> {pageInfo.page} </span>/ {pageInfo.pageTotal}页
                </div>
                <Pagination
                    showSizeChanger
                    showQuickJumper 
                    defaultCurrent={1}
                    total={pageInfo.total}
                    onChange={handlePageChange}
                />
            </div>
        </div>
    )
}
