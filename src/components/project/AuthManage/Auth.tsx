import { useEffect, useState, useMemo } from "react";
import { Table, But<PERSON>, Popconfirm, Pagination, message } from "antd";
import styles from "./Auth.module.scss";
import operationListStyles from '@/styles/OperationList.module.scss';
import { formatGMTDate } from '@/utils/common'

import {
    reqPluginAuthList,
    reqDeleteAuth
} from "@/service/auth";

export default function ItemAuth(props: any) {
    const {
        open,
        apiId,
        activeKey,
        optionIds,
        clickEdit,
        clickCancel
    } = props;

    const initPage = {
        page: 1,
        total: 0,
        pageSize: 10,
        pageTotal: 0
    }
    const [pageInfo, setPageInfo] = useState(initPage);
    const [tableData, setTableData] = useState(new Array<any>());

    const queryData = () => {
        if (apiId) {
            reqPluginAuthList({
                api_id: apiId,
                page: pageInfo.page,
                page_size: pageInfo.pageSize,
                sort_time: 0
            }).then((res) => {
                res && setTableData(res)
                res && setPageInfo({
                    ...pageInfo,
                    total: res.length,
                    pageTotal: Math.ceil(res.length / pageInfo.pageSize)
                })
            })
        }
    }

    const handleTableAction = (pag: any, filters: any, sorter: any) => {
        if (apiId) {
            reqPluginAuthList({
                api_id: apiId,
                page: pageInfo.page,
                page_size: pageInfo.pageSize,
                sort_time: sorter.order === 'ascend' ? 0 : 1
            }).then((res) => {
                res && setTableData(res)
                res && setPageInfo({
                    ...pageInfo,
                    // wlr-todo 暂用 
                    total: res.length,
                    pageTotal: Math.ceil(res.length / pageInfo.pageSize)
                })
            })
        }
    }

    const handlePageChange = async(current: number, size: number) => {
        setPageInfo({
            ...pageInfo,
            page: current,
            pageSize: size,
            pageTotal: Math.ceil(pageInfo.total / size)
        })

        queryData();
    }

    const handleEdit = (item: any) => {
        clickEdit(item);
    }

    const isOperate = useMemo(() => {
        return optionIds.includes(apiId);
    }, [optionIds, apiId])

    useEffect(() => {
        queryData()
    }, [apiId, open, activeKey])

    const columns = [
        {
            title: '授权配置名称',
            dataIndex: 'auth_name',
            key: 'auth_name'
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            sorter: true,
            render: (time: string) => (
                <span>{formatGMTDate(time)}</span>
            )
        },
        {
            title: '创建人',
            dataIndex: 'user_name',
            key: 'user_name',
            width: 108
        },
        {
            title: '操作',
            dataIndex: 'edit',
            key: 'edit',
            width: 180,
            render: (_: any, item: any) => (
                <div>
                    <Button
                        type="link"
                        disabled={!isOperate}
                        className={operationListStyles.detailBtn}
                        onClick={() => { handleEdit(item) }}
                    >编辑</Button>
                    <Popconfirm
                        title={"确认删除这条授权吗？"}
                        onConfirm={() => {
                            reqDeleteAuth({
                                auth_id: item.id
                            }).then((res) => {
                                if (res) {
                                    queryData();
                                    clickCancel();
                                    message.warning('授权删除成功');
                                }
                            })
                        }}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="link"
                            className={operationListStyles.detailBtn}
                        >删除</Button>
                    </Popconfirm>
                </div>
            )
        }
    ]

    return (
        <div className={styles.auth}>
            <Table
                columns={columns}
                onChange={handleTableAction}
                dataSource={tableData}
                pagination={false}
                className='commonTable normalFont'
            />
            <div className={styles.footer}>
                <div className={styles.footerTotal}>
                    共<span> {pageInfo.total} </span>条
                    第<span> {pageInfo.page} </span>/ {pageInfo.pageTotal}页
                </div>
                <Pagination
                    showSizeChanger
                    showQuickJumper 
                    defaultCurrent={1}
                    total={pageInfo.total}
                    onChange={handlePageChange}
                />
            </div>
        </div>
    )
}
