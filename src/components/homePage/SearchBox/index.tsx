import React, { useState } from 'react';
import { Input, Button } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import styles from './index.module.scss';

interface SearchBoxProps {
  placeholder?: string;
  onSearch?: (value: string) => void;
  buttonText?: string;
}

export default function SearchBox({ 
  placeholder = "请输入关键词搜索", 
  onSearch,
  buttonText = "创建多专家智能体"
}: SearchBoxProps) {
  const [searchValue, setSearchValue] = useState('');

  const handleSearch = () => {
    onSearch?.(searchValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={styles.searchBoxContainer}>
      <div className={styles.searchInput}>
        <Input
          size="large"
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyPress={handleKeyPress}
          prefix={<SearchOutlined className={styles.searchIcon} />}
        />
      </div>
      <Button 
        type="primary" 
        size="large" 
        className={styles.createButton}
        onClick={() => {/* 处理创建按钮点击 */}}
      >
        {buttonText}
      </Button>
    </div>
  );
}