.searchBoxContainer {
  display: flex;
  align-items: center;
  gap: 16px;
}

.searchInput {
  flex: 1;
  
  :global(.ant-input-affix-wrapper) {
    border-radius: 24px;
    border: 1px solid #e0e0e0;
    
    &:hover {
      border-color: #1c53e0;
    }
    
    &:focus-within {
      border-color: #1c53e0;
      box-shadow: 0 0 0 2px rgba(28, 83, 224, 0.1);
    }
  }
  
  :global(.ant-input) {
    border: none;
    box-shadow: none;
    font-size: 14px;
    
    &::placeholder {
      color: rgba(0, 0, 0, 0.4);
    }
  }
}

.searchIcon {
  color: rgba(0, 0, 0, 0.4);
}

.createButton {
  background: linear-gradient(97.36deg, #1c53e0 6.51%, #c258f7 100%);
  border: none;
  border-radius: 24px;
  font-weight: 500;
  padding: 0 24px;
  height: 40px;
  
  &:hover {
    background: linear-gradient(97.36deg, #1c53e0 6.51%, #c258f7 100%);
    opacity: 0.9;
  }
}