import React from 'react';
import styles from './index.module.scss';

interface TitleTextProps {
  tabs: string[];
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function TitleText({ tabs }: TitleTextProps) {
  return (
    <div className={styles.titleTextContainer}>
      {tabs.map((tab) => (
        <span
          key={tab}
          className={`${styles.tab} ${styles.active}`}
        >
          {tab}
        </span>
      ))}
    </div>
  );
}