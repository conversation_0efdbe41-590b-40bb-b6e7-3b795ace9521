import React from "react"
import styles from "./index.module.scss"

interface CourseCardProps {
  leftTopIcon?: React.ReactNode
  bigText?: string
  smallText?: string
  backgroundPic?: string
  backgroundColor?: string
}

export default function CourseCard({
  leftTopIcon,
  bigText,
  smallText,
  backgroundPic,
  backgroundColor = "#f0f0f0"
}: CourseCardProps) {
  const cardStyle = backgroundPic
    ? { backgroundImage: `url(${backgroundPic})` }
    : { backgroundColor }

  return (
    <div className={styles.courseCard} style={cardStyle}>
      <div className={styles.content}>
        <div className={styles.leftTopIcon}>{leftTopIcon || null}</div>
        <div className={styles.textContent}>
          <h3 className={styles.bigText}>{bigText || ""}</h3>
          <p className={styles.smallText}>{smallText || ""}</p>
        </div>
      </div>
    </div>
  )
}
