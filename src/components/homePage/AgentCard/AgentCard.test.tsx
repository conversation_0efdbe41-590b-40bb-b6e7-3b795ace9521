import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import AgentCard from './index';

// Mock data for testing
const mockProps = {
  icon: 'https://example.com/icon.png',
  title: '沉浸式Vlog剪辑师',
  desc: '一句话生成你的Vlog自拍故事视频！',
  tag: '限免',
  creator: '素蝈蝈',
  onClick: jest.fn()
};

describe('AgentCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all props correctly', () => {
    render(<AgentCard {...mockProps} />);
    
    // Check if title is rendered
    expect(screen.getByText('沉浸式Vlog剪辑师')).toBeInTheDocument();
    
    // Check if description is rendered
    expect(screen.getByText('一句话生成你的Vlog自拍故事视频！')).toBeInTheDocument();
    
    // Check if tag is rendered
    expect(screen.getByText('限免')).toBeInTheDocument();
    
    // Check if creator is rendered
    expect(screen.getByText('创建者：素蝈蝈')).toBeInTheDocument();
  });

  it('renders without tag when tag is not provided', () => {
    const propsWithoutTag = { ...mockProps };
    delete propsWithoutTag.tag;
    
    render(<AgentCard {...propsWithoutTag} />);
    
    // Tag should not be rendered
    expect(screen.queryByText('限免')).not.toBeInTheDocument();
    
    // Other elements should still be rendered
    expect(screen.getByText('沉浸式Vlog剪辑师')).toBeInTheDocument();
  });

  it('calls onClick when card is clicked', () => {
    render(<AgentCard {...mockProps} />);
    
    const card = screen.getByText('沉浸式Vlog剪辑师').closest('div');
    fireEvent.click(card!);
    
    expect(mockProps.onClick).toHaveBeenCalledTimes(1);
  });

  it('applies correct styling classes', () => {
    render(<AgentCard {...mockProps} />);
    
    const card = screen.getByText('沉浸式Vlog剪辑师').closest('div');
    
    // Check if main container has correct classes
    expect(card).toHaveClass('bg-white', 'rounded-xl', 'cursor-pointer');
  });

  it('sets background image for icon correctly', () => {
    render(<AgentCard {...mockProps} />);
    
    const iconElement = screen.getByText('沉浸式Vlog剪辑师')
      .closest('div')
      ?.querySelector('[style*="backgroundImage"]');
    
    expect(iconElement).toHaveStyle({
      backgroundImage: `url('${mockProps.icon}')`
    });
  });
});
