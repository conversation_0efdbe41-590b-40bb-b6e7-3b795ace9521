.agentCard {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 160px;
  display: flex;
  flex-direction: column;
  
  &:hover {
    border-color: #1c53e0;
    box-shadow: 0 4px 12px rgba(28, 83, 224, 0.1);
    transform: translateY(-2px);
  }
}

.header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.iconWrapper {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tag {
  background: linear-gradient(97.36deg, #1c53e0 6.51%, #c258f7 100%);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.content {
  flex: 1;
  margin-bottom: 12px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.desc {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.footer {
  margin-top: auto;
}

.creator {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}