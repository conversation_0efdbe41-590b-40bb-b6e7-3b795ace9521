import React from 'react';
import styles from './index.module.scss';

interface AgentCardProps {
  icon: string;
  title: string;
  desc: string;
  tag?: string;
  creator: string;
  onClick?: () => void;
}

export default function AgentCard({ 
  icon, 
  title, 
  desc, 
  tag, 
  creator,
  onClick 
}: AgentCardProps) {
  return (
    <div className={styles.agentCard} onClick={onClick}>
      <div className={styles.header}>
        <div className={styles.iconWrapper}>
          <img src={icon} alt={title} className={styles.icon} />
        </div>
        {tag && (
          <span className={styles.tag}>{tag}</span>
        )}
      </div>
      
      <div className={styles.content}>
        <h3 className={styles.title}>{title}</h3>
        <p className={styles.desc}>{desc}</p>
      </div>
      
      <div className={styles.footer}>
        <span className={styles.creator}>创建者：{creator}</span>
      </div>
    </div>
  );
}