.backgroundPic {
  position: fixed;
  top: 0;
  right: 0;
  width: 1000px;
  height: auto;
  z-index: 0;
  pointer-events: none;
  -webkit-mask-image: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0,0,0,0.0) 10%,rgba(0,0,0,0.0) 20%, rgba(0, 0, 0, 1) 90%,rgba(0,0,0,1) 100%);
  mask-image: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0,0,0,0.0) 10%,rgba(0,0,0,0.0) 20%, rgba(0, 0, 0, 1) 90%,rgba(0,0,0,1) 100%);
}

.bgImage {
  width: 100%;
  height: auto;
  display: block;
}