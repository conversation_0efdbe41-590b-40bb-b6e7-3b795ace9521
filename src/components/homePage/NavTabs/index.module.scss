.NavTab {
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  padding: 5px 16px;
  height: 32px;
  box-sizing: border-box;
  white-space: nowrap;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  background: rgba(0, 0, 0, 0.06);
  border: none;
  cursor: pointer;
  user-select: none;
}

.Icon {
  width: 16px;
  height: 16px;
}

.navTabsContainer {
  position: relative;
  height: 32px;
  overflow: hidden;
  
  .navTabs {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    transition: width 0.3s linear;
    
    .navTab {
      float: left;
      margin-right: 12px;
      @extend .NavTab;
      
      &.navTabItem.show {
        visibility: visible;
      }
      
      &.navTabItem.hide {
        visibility: hidden;
      }
      
      &:hover {
        background: rgba(0, 0, 0, 0.15);
      }
      
      &.active {
        color: rgba(255, 255, 255, 0.9);
        background: linear-gradient(97.36deg, #1c53e0 6.51%, #c258f7 100%);
      }
      
      .navTabIcon {
        margin-right: 4px;
        @extend .Icon;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

.navTabMoreDropdownArea {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 10;
}

.navTabMoreWrapper {
  position: absolute;
  top: 0;
  z-index: 10;
  flex-grow: 0;
  flex-shrink: 0;
  z-index: 10;
  background: #fff;
  padding-right: 100px; // 设置是为了遮盖flex布局里的tab标签
}

.navTabMore {
  @extend .NavTab;
  
  &::before {
    content: '';
    display: inline-block;
    margin-right: 3px;
    @extend .Icon;
    background: url(https://p2.ssl.img.360kuai.com/t110b9a9301617c35fd5cc87a0a.png) no-repeat 0 0;
    background-size: cover;
  }
}