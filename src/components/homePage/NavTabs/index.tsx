import { useState, useEffect, useRef } from 'react';
import { Dropdown } from 'antd';
import { swap, debounce } from '../../../utils';
import { getAgentTags } from '../../../service/homePage';
import styles from './index.module.scss';

const MORE_BTN_WIDTH = 80;

interface NavTabsProps {
  activeTab: string;
  updateActiveTab: (tabId: string) => void;
}

export default function NavTabs({ activeTab, updateActiveTab }: NavTabsProps) {
  const [tabs, setTabs] = useState([]);
  const [count, setCount] = useState(); // 展示的tab个数，不包含更多
  const [morePosition, setMorePosition] = useState(0);
  const [showMore, setShowMore] = useState(false);
  const containerRef = useRef(null);

  const onMoreItemClick = (e) => {
    const index = tabs.findIndex((item) => String(item.id) === e.key);
    if (index > -1 && count > 0) {
      const newTabs = swap(tabs, index, count - 1); // 更多，所以是倒数第二个交换
      setTabs(newTabs.filter((t) => !!t));
    }
    updateActiveTab(e.key);
  };

  // 计算每行可以容纳多少个元素
  const calculateColumns = () => {
    if (!containerRef.current) return;
    
    // 容器宽度
    const {
      width: allWidth,
      height: allHeight,
      left: allLeft,
      top: allTop,
    } = containerRef.current.getBoundingClientRect() || {};

    // 高度换行，则证明超出了，需要展示更多按钮
    if (allHeight > 32) {
      let lastElementRect = 0, // 最右侧的元素
        count = 0; // 第一行个数
      const childNodes = containerRef.current.querySelectorAll('.navTabItem');
      
      for (let i = 0; i < childNodes.length; i++) {
        const elementRect = childNodes[i].getBoundingClientRect();
        if (elementRect.top > allTop) {
          break;
        }
        lastElementRect = elementRect;
        count++;
      }

      // 剩余空间(看是否能放的下更多按钮)
      let restDistance =
        allWidth -
        (lastElementRect.left - allLeft) -
        lastElementRect.width -
        12;
      
      setShowMore(true);
      while (restDistance < MORE_BTN_WIDTH) {
        restDistance += lastElementRect.width + 12;
        count--;
      }
      
      const rect = childNodes[count - 1]?.getBoundingClientRect() || {};
      setCount(count);
      setMorePosition(rect.left - allLeft + rect.width + 12);
    } else {
      setShowMore(false);
    }
  };

  useEffect(() => {
    // 获取tabs
    async function fetchTabs() {
      try {
        const data = (await getAgentTags()) || [];
        if (data.length) {
          setTabs(data.filter((t) => !!t));
          if (!activeTab && data.length > 0) {
            updateActiveTab(data[0].id);
          }
        }
      } catch (error) {
        console.error(error);
      }
    }
    fetchTabs();
  }, []);

  useEffect(() => {
    calculateColumns();
    window.addEventListener('resize', calculateColumns);
    return () => window.removeEventListener('resize', calculateColumns);
  }, [containerRef.current]);

  useEffect(() => {
    calculateColumns();
  }, [tabs, activeTab]);

  return (
    <div className={styles.navTabsContainer}>
      <div className={styles.navTabs} ref={containerRef}>
        {tabs?.map((tab) => (
          <span
            className={[
              'navTabItem',
              styles.navTab,
              styles.navTabItem,
              String(tab.id) === String(activeTab) ? styles.active : '',
            ].join(' ')}
            key={tab.id}
            onClick={() => {
              updateActiveTab(tab.id);
            }}
          >
            {!!tab.icon && <img className={styles.navTabIcon} src={tab.icon} />}
            {tab.tag_name}
          </span>
        ))}
      </div>
      {!!showMore && (
        <div
          className={styles.navTabMoreWrapper}
          style={{ left: morePosition }}
        >
          <Dropdown
            trigger={['click']}
            placement="bottomRight"
            autoAdjustOverflow
            menu={{
              items: tabs.slice(count).map((i) => ({
                label: <span>{i.tag_name}</span>,
                key: i.id,
              })),
              selectedKeys: activeTab,
              onClick: onMoreItemClick,
            }}
            overlayClassName={styles.navTabMoreDropdown}
            overlayStyle={{ width: '120px!important' }}
          >
            <span
              className={`${styles.navTab} ${styles.navTabMore}`}
              key="more"
            >
              更多
            </span>
          </Dropdown>
        </div>
      )}
    </div>
  );
}