import React from 'react';

interface TopTextProps {
  title: string;
  subtitle: string;
}

export default function TopText({ title, subtitle }: TopTextProps) {
  // 分割标题，将"智筑未来"和"一键搭建超级智能体"分开
  const splitTitle = (fullTitle: string) => {
    if (fullTitle.includes('智筑未来')) {
      return {
        gradientPart: '智筑未来',
        normalPart: fullTitle.replace('智筑未来', '').trim()
      };
    }
    // 如果没有找到"智筑未来"，则将整个标题作为普通文本
    return {
      gradientPart: '',
      normalPart: fullTitle
    };
  };

  const { gradientPart, normalPart } = splitTitle(title);

  return (
    <div className="text-center mb-10">
      {/* 主标题 */}
      <div className="box-border content-stretch flex flex-row font-bold gap-3 items-center justify-center leading-[0] not-italic p-0 relative text-[44px] text-left text-nowrap mb-4" style={{ fontFamily: 'PingFang SC, -apple-system, BlinkMacSystemFont, sans-serif' }}>
        {gradientPart && (
          <div
            className="bg-clip-text bg-gradient-to-r from-[#8d68ff] relative shrink-0 to-[#3c75f2] to-[97.396%]"
            style={{ WebkitTextFillColor: "transparent" }}
          >
            <p className="block leading-[normal] text-nowrap whitespace-pre">
              {gradientPart}
            </p>
          </div>
        )}
        {normalPart && (
          <div className="relative shrink-0 text-[#1d2531]">
            <p className="block leading-[normal] text-nowrap whitespace-pre">
              {normalPart}
            </p>
          </div>
        )}
      </div>

      {/* 副标题 */}
      <div className="text-lg text-gray-600 leading-6">
        {subtitle}
      </div>
    </div>
  );
}