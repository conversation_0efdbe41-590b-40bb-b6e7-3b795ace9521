import { useState, useEffect } from 'react';
import { Button, Input, InputNumber, Tooltip, Popconfirm, Select, Switch, message } from 'antd';
import { reqGetApplyList, reqApplyListAdd, reqApplyListUpdate } from '@/service/flow';
import { reqGetApplyListV3, reqApplyListAddV3, reqApplyListUpdateV3 } from '@/service/flow3.0';

import operationListStyles from '@/styles/OperationList.module.scss';
import flowAppStyles from '@/styles/flow/FlowApp.module.scss'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { copyParamValue } from '@/utils/common';
import questionCircle from '@/images/questionCircle.svg';

// app应用列表默认项
const applyEditOrigin = {
  id: '',
  domain_name: '',
  params: '',
  access_restrictions: 0,
  domain_schema: 'https://',
  request_address: '',
  domain_route: '/flowBaoApp'
};

export default function OperationApp(props: any) {
    const { chainId, activeTab, deployDetail, changeDeploy, isV2, status } = props;

    // app应用
    const [applyList, setApplyList] = useState([]);
    const [applyEdit, setApplyEdit] = useState(applyEditOrigin);

    // 不同版本接口
    const apiVersionMap = isV2 ? {
        list: reqGetApplyList,
        add: reqApplyListAdd,
        update: reqApplyListUpdate,
    } : {
        list: reqGetApplyListV3,
        add: reqApplyListAddV3,
        update: reqApplyListUpdateV3,
    }

    const onAddDomain = () => {
        if (applyList.findIndex((liItem: any) => liItem.id === '-1') > -1) {
            message.warning('不能连续新建, 请先保存!')
            return;
        }
        setApplyList([{
            ...applyEditOrigin,
            id: '-1'
        }, ...applyList] as any);
        setApplyEdit({
            ...applyEditOrigin,
            id: '-1'
        });
    }

    const onCancelRelease = () => {
        setApplyEdit(applyEditOrigin);
        setApplyList(applyList.filter((liItem: any) => liItem.id !== '-1'));
    }

    const onSaveDomain = () => {
        if (applyEdit.id === '-1') {
            createApplyList(applyEdit);
        } else {
            editApplyList(applyEdit);
        }
    }

    // app应用列表编辑保存
    const editApplyList = async (val: any) => {
        if (val.id > 0 && (!val.domain_name)) {
            message.warning('域名或参数, 不能为空!')
            return;
        }
        try {
            const params: any = {
                id: val.id,
                is_deleted: 1,
                domain_schema: val.domain_schema,
                domain_name: val.domain_name,
                domain_route: val.domain_route,
                params: val.params,
                access_restrictions: val.access_restrictions || 0,
            }
            if (!isV2) {
                params.publish_type = 'app';
            }
            let res = await apiVersionMap.update(params);
            if (res && res.context && res.context.code === 0) {
                message.success('保存成功!');
                setApplyEdit(applyEditOrigin);
                getApplyList();
            } else {
                message.error('保存失败!');
            }
        } catch (error) {
            message.error('保存失败!');
        }
    }

    // app应用列表删除
    const delApplyList = async (val: any) => {
        try {
            const params: any = {
                id: val.id,
                is_deleted: 2,
            }
            if (!isV2) {
                params.publish_type = 'app';
            }
            let res = await apiVersionMap.update(params);
            if (res && res.context && res.context.code === 0) {
                message.success('删除成功!');
                setApplyEdit(applyEditOrigin);
                getApplyList();
            } else {
                message.error('删除失败!');
            }
        } catch (error) {
            message.error('删除失败!');
        }
    }

    // app应用列表添加
    const createApplyList = async (val: any) => {
        if (!val.domain_name) {
            message.warning('域名或参数, 不能为空!')
            return;
        }
        try {
            const params: any = {
                template_id: chainId,
                domain_schema: val.domain_schema,
                domain_name: val.domain_name,
                domain_route: val.domain_route,
                params: val.params,
                access_restrictions: val.access_restrictions,
                publish_type: 'app'
            }
            if (!isV2) {
                params.publish_type = 'app';
            }
            let res = await apiVersionMap.add(params);
            if (res && res.context && res.context.code === 0) {
                message.success('添加成功!');
                setApplyEdit(applyEditOrigin);
                getApplyList();
            } else {
                message.error('添加失败!');
            }
        } catch (error) {
            message.error('添加失败!');
        }
    }

    // 获取app应用列表
    const getApplyList = async () => {
        try {
            const params: any = { template_id: chainId }
            if (!isV2) {
                params.publish_type = 'app';
            }
            let res = await apiVersionMap.list(params);
            if (res && Array.isArray(res)) {
                setApplyList(res as any);
            }
        } catch (error) {
            console.log('获取app列表失败!')
        }
    };

    useEffect(() => {
        if (activeTab === 'app') {
          getApplyList();
        }
    }, [activeTab]);

    return <>
        <div className={operationListStyles.operationDetailStatus}>
            <div className={operationListStyles.publicWrapper}>
                <span className={operationListStyles.publicTitle}>发布成APP应用</span>
                {isV2 
                    ? <Switch onChange={changeDeploy} checked={deployDetail.status === 1} /> 
                    : <Switch onChange={(e) => { changeDeploy(e, false) }} checked={status} />}
            </div>
        </div>
        {deployDetail.status === 1 && <>
            <div className={flowAppStyles.operationWrapper}>
                <div><Button type="default" icon={<PlusOutlined />} onClick={() => onAddDomain()}>添加域名</Button></div>
                {applyList.length > 0 && applyList.map((item: any, index: any) => {
                    return (item.id === applyEdit.id ?
                        <div className={flowAppStyles.operationContentTwo} key={'edit' + index}>
                            <div className={flowAppStyles.contentHeader}>
                                <div className={flowAppStyles.contentHeaderTitle}>发布域名{index + 1}</div>
                                <div className={flowAppStyles.contentHeaderBtns}>
                                    <Button type="default" onClick={() => onCancelRelease()}>取消</Button>
                                    <Button type="primary" onClick={() => onSaveDomain()}>保存</Button>
                                </div>
                            </div>
                            <div className={flowAppStyles.contentParams}>
                                <div className={flowAppStyles.contentParamsItemFirst}>
                                    <div className={flowAppStyles.contentParamsItem}>
                                        <div className={flowAppStyles.contentParamsItemLabel}>
                                            <span>*</span>网站域名
                                        </div>
                                        <div className={flowAppStyles.contentParamsItemDetail}>
                                            <Input addonBefore={<Select
                                                style={{
                                                    width: '100px'
                                                }}
                                                defaultValue="https://"
                                                value={applyEdit.domain_schema}
                                                onChange={(val: any) => {
                                                    setApplyEdit({
                                                        ...applyEdit,
                                                        domain_schema: val,
                                                    });
                                                }}
                                                options={[
                                                    { value: 'https://', label: 'https://' },
                                                    { value: 'http://', label: 'http://' },
                                                ]}
                                            ></Select>}
                                                addonAfter={applyEdit.domain_route === '' ? '' : <Select
                                                    style={{
                                                        width: '150px'
                                                    }}
                                                    defaultValue="/flowBaoApp"
                                                    value={applyEdit.domain_route}
                                                    onChange={(val: any) => {
                                                        setApplyEdit({
                                                            ...applyEdit,
                                                            domain_route: val
                                                        });
                                                    }}
                                                    options={[
                                                        { value: '/flowBaoApp', label: '/flowBaoApp' },
                                                    ]}
                                                ></Select>}
                                                style={{ minWidth: '400px' }}
                                                value={applyEdit.domain_name}
                                                onChange={(e: any) => {
                                                    setApplyEdit({
                                                        ...applyEdit,
                                                        domain_name: e.target.value,
                                                    });
                                                }} />
                                        </div>
                                    </div>
                                    <div className={flowAppStyles.contentParamsItem}>
                                        <div className={flowAppStyles.contentParamsItemLabel}>
                                            <span>*</span>参数信息
                                        </div>
                                        <div className={flowAppStyles.contentParamsItemDetail}>
                                            <Input value={applyEdit.params} onChange={(e: any) => {
                                                setApplyEdit({
                                                    ...applyEdit,
                                                    params: e.target.value,
                                                });
                                            }} />
                                        </div>
                                    </div>
                                </div>
                                <div className={flowAppStyles.contentParamsItem}>
                                    <div className={flowAppStyles.contentParamsItemLabel}>
                                        访问限制
                                    </div>
                                    <div className={flowAppStyles.contentParamsItemDetail}>
                                        <InputNumber addonAfter="次/分钟" defaultValue={100} value={applyEdit.access_restrictions} onChange={(val: any) => {
                                            setApplyEdit({
                                                ...applyEdit,
                                                access_restrictions: val,
                                            });
                                        }} />
                                        <Tooltip
                                            title="0 表示不限制，其他按照输入值限制"
                                        >
                                            <img src={questionCircle.src} alt="注意事项" />
                                        </Tooltip>
                                    </div>
                                </div>
                                <div className={flowAppStyles.contentParamsItem}>
                                    <div className={flowAppStyles.contentParamsItemLabel}>
                                        访问地址
                                    </div>
                                    <div className={flowAppStyles.contentParamsItemDetail + " " + flowAppStyles.contentParamsItemDetailAddress}>
                                        <span onClick={() => { copyParamValue(applyEdit.request_address || '') }}>{applyEdit.request_address || ''}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        : <div className={flowAppStyles.operationContentTwo} key={'static' + index}>
                            <div className={flowAppStyles.contentHeader}>
                                <div className={flowAppStyles.contentHeaderTitle}>发布域名{index + 1}</div>
                                <div className={flowAppStyles.contentHeaderBtns}>
                                    <Button icon={<EditOutlined />} onClick={() => {
                                        setApplyEdit(item);
                                    }} />
                                    <Popconfirm
                                        placement="topRight"
                                        title="确认是否删除?"
                                        description=""
                                        onConfirm={() => {
                                            delApplyList(item);
                                        }}
                                        onCancel={() => { }}
                                    >
                                        <Button icon={<DeleteOutlined />} />
                                    </Popconfirm>
                                </div>
                            </div>
                            <div className={flowAppStyles.contentParams}>
                                <div className={flowAppStyles.contentParamsItemFirst + ' ' + flowAppStyles.contentParamsItemFirstStatic}>
                                    <div className={flowAppStyles.contentParamsItem}>
                                        <div className={flowAppStyles.contentParamsItemLabel}>
                                            网站域名
                                        </div>
                                        <div className={flowAppStyles.contentParamsItemDetail}>
                                            {item.domain_schema + item.domain_name + item.domain_route}
                                        </div>
                                    </div>
                                    <div className={flowAppStyles.contentParamsItem}>
                                        <div className={flowAppStyles.contentParamsItemLabel}>
                                            参数信息
                                        </div>
                                        <div className={flowAppStyles.contentParamsItemDetail}>
                                            {item.params}
                                        </div>
                                    </div>
                                </div>
                                <div className={flowAppStyles.contentParamsItem}>
                                    <div className={flowAppStyles.contentParamsItemLabel}>
                                        访问限制
                                    </div>
                                    <div className={flowAppStyles.contentParamsItemDetail}>
                                        {item.access_restrictions === 0 ? '无限制' : + item.access_restrictions + ' 次/分钟'}
                                    </div>
                                </div>
                                <div className={flowAppStyles.contentParamsItem}>
                                    <div className={flowAppStyles.contentParamsItemLabel}>
                                        访问地址
                                    </div>
                                    <div className={flowAppStyles.contentParamsItemDetail + " " + flowAppStyles.contentParamsItemDetailAddress}>
                                        <span onClick={() => { copyParamValue(item.request_address || '') }}>{item.request_address || ''}</span>
                                    </div>
                                </div>
                            </div>
                        </div>)
                })}
            </div>
        </>}
    </>
}