import { useState, useEffect } from 'react';
import { Button, Input, message, Popconfirm, Select, Switch } from 'antd';
import { reqGetApplyListV3, reqApplyListAddV3, reqApplyListUpdateV3 } from '@/service/flow3.0';

import operationListStyles from '@/styles/OperationList.module.scss';
import flowAppStyles from '@/styles/flow/FlowApp.module.scss'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';

// web页面列表默认项
const webEditOrigin = {
  id: '',
  domain_name: 'testapp.agent.360.com/agentChat',
  params: '',
  access_restrictions: 0,
  domain_schema: 'https://',
  request_address: '',
  domain_route: ''
};

export default function OperationWebApp(props: any) {
    const { chainId, activeTab, deployDetail, changeDeploy, publishId, sysUid, isV2, status } = props;

    // web页面
    const [webList, setWebList] = useState([]);
    const [webEdit, setWebEdit] = useState(webEditOrigin)

    const formatUrl = (url: string) => {
        return `${url.split('?')[0]}?app_id=${deployDetail.app_id}&app_secret=${deployDetail.app_secret}&template_id=${publishId}&sys_uid=${sysUid}`;
    }

    // 获取web应用列表
    const getApplyList = async () => {
        try {
            let res = await reqGetApplyListV3({ template_id: chainId, publish_type: 'web' });
            if (res && Array.isArray(res)) {
                setWebList(res as any);
            }
        } catch (error) {
            console.log('获取web列表失败!')
        }
    };

    // web应用列表编辑保存
    const editApplyList = async (val: any) => {
        if (val.id > 0 && (!val.domain_name)) {
            message.warning('域名或参数, 不能为空!')
            return;
        }
        try {
            let res = await reqApplyListUpdateV3({
                id: val.id,
                is_deleted: 1,
                domain_schema: val.domain_schema,
                domain_name: val.domain_name,
                domain_route: val.domain_route,
                params: val.params,
                access_restrictions: val.access_restrictions || 0,
                publish_type: 'web'
            });
            if (res && res.context && res.context.code === 0) {
                message.success('保存成功!');
                setWebEdit(webEditOrigin);
                getApplyList();
            } else {
                message.error('保存失败!');
            }
        } catch (error) {
            message.error('保存失败!');
        }
    }

    // web应用列表删除
    const delApplyList = async (val: any) => {
        try {
            let res = await reqApplyListUpdateV3({
                id: val.id,
                is_deleted: 2,
                publish_type: 'web'
            });
            if (res && res.context && res.context.code === 0) {
                message.success('删除成功!');
                setWebEdit(webEditOrigin);
                getApplyList();
            } else {
                message.error('删除失败!');
            }
        } catch (error) {
            message.error('删除失败!');
        }
    }

    // web应用列表添加
    const createApplyList = async (val: any) => {
        if (!val.domain_name) {
            message.warning('域名或参数, 不能为空!')
            return;
        }
        try {
            let res = await reqApplyListAddV3({
                template_id: chainId,
                domain_schema: val.domain_schema,
                domain_name: val.domain_name,
                domain_route: val.domain_route,
                params: val.params,
                access_restrictions: val.access_restrictions,
                publish_type: 'web'
            });
            if (res && res.context && res.context.code === 0) {
                message.success('添加成功!');
                setWebEdit(webEditOrigin);
                getApplyList();
            } else {
                message.error('添加失败!');
            }
        } catch (error) {
            message.error('添加失败!');
        }
    }

    const onAddDomain = () => {
        if (webList.findIndex((liItem: any) => liItem.id === '-1') > -1) {
            message.warning('不能连续新建, 请先保存!')
            return;
        }
        setWebList([{
            ...webEditOrigin,
            id: '-1'
        }, ...webList] as any);
        setWebEdit({
            ...webEditOrigin,
            id: '-1'
        });
    }

    const onCancelRelease = () => {
        setWebEdit(webEditOrigin);
        setWebList(webList.filter((liItem: any) => liItem.id !== '-1'));
    }

    const onSaveDomain = () => {
        if (webEdit.id === '-1') {
            createApplyList(webEdit);
        } else {
            editApplyList(webEdit);
        }
    }

    useEffect(() => {
        if (activeTab === 'web') {
            getApplyList();
        }
    }, [activeTab]);

    return <>
        <div className={operationListStyles.operationDetailStatus}>
            <div className={operationListStyles.publicWrapper}>
                <span className={operationListStyles.publicTitle}>发布成WEB页面</span>
                <Switch onChange={(e) => { changeDeploy(e, true) }} checked={status} />
            </div>
        </div>
        {deployDetail.status === 1 && <>
            <div className={flowAppStyles.operationWrapper}>
                <div><Button type="default" icon={<PlusOutlined />} onClick={() => onAddDomain()}>添加域名</Button></div>
                {webList.length > 0 && webList.map((item: any, index: any) => {
                    return (item.id === webEdit.id ?
                        <div className={flowAppStyles.operationContentTwo} key={'edit' + index}>
                            <div className={flowAppStyles.contentHeader}>
                                <div className={flowAppStyles.contentHeaderTitle}>发布域名{index + 1}</div>
                                <div className={flowAppStyles.contentHeaderBtns}>
                                    <Button type="default" onClick={() => onCancelRelease()}>取消</Button>
                                    <Button type="primary" onClick={() => onSaveDomain()}>保存</Button>
                                </div>
                            </div>
                            <div className={flowAppStyles.contentParams}>
                                <div className={flowAppStyles.contentParamsItemFirst}>
                                    <div className={flowAppStyles.contentParamsItem}>
                                        <div className={flowAppStyles.contentParamsItemLabel}>
                                            <span>*</span>网站域名
                                        </div>
                                        <div className={flowAppStyles.contentParamsItemDetail}>
                                            <Input addonBefore={<Select
                                                style={{
                                                    width: '100px'
                                                }}
                                                defaultValue="https://"
                                                value={webEdit.domain_schema}
                                                onChange={(val: any) => {
                                                    setWebEdit({
                                                        ...webEdit,
                                                        domain_schema: val,
                                                    });
                                                }}
                                                options={[
                                                    { value: 'https://', label: 'https://' },
                                                    { value: 'http://', label: 'http://' },
                                                ]}
                                            ></Select>}
                                                addonAfter={webEdit.domain_route === '' ? '' : <Select
                                                    style={{
                                                        width: '150px'
                                                    }}
                                                    defaultValue="/flowBaoApp"
                                                    value={webEdit.domain_route}
                                                    onChange={(val: any) => {
                                                        setWebEdit({
                                                            ...webEdit,
                                                            domain_route: val
                                                        });
                                                    }}
                                                    options={[
                                                        { value: '/flowBaoApp', label: '/flowBaoApp' },
                                                    ]}
                                                ></Select>}
                                                style={{ minWidth: '400px' }}
                                                value={webEdit.domain_name}
                                                onChange={(e: any) => {
                                                    setWebEdit({
                                                        ...webEdit,
                                                        domain_name: e.target.value,
                                                    });
                                                }} />
                                        </div>
                                    </div>
                                </div>
                                <div className={flowAppStyles.contentParamsItem}>
                                    <div className={flowAppStyles.contentParamsItemLabel}>
                                        访问地址
                                    </div>
                                    {item.request_address && <div className={flowAppStyles.contentParamsItemDetail + " " + flowAppStyles.contentParamsItemDetailAddress}>
                                        <span onClick={() => { window.open(formatUrl(item.request_address) || '', '_blank') }}>{formatUrl(item.request_address) || ''}</span>
                                    </div>}
                                </div>
                            </div>
                        </div>
                        : <div className={flowAppStyles.operationContentTwo} key={'static' + index}>
                            <div className={flowAppStyles.contentHeader}>
                                <div className={flowAppStyles.contentHeaderTitle}>发布域名{index + 1}</div>
                                <div className={flowAppStyles.contentHeaderBtns}>
                                    <Button icon={<EditOutlined />} onClick={() => {
                                        setWebEdit(item);
                                    }} />
                                    <Popconfirm
                                        placement="topRight"
                                        title="确认是否删除?"
                                        description=""
                                        onConfirm={() => {
                                            delApplyList(item);
                                        }}
                                        onCancel={() => { }}
                                    >
                                        <Button icon={<DeleteOutlined />} />
                                    </Popconfirm>
                                </div>
                            </div>
                            <div className={flowAppStyles.contentParams}>
                                <div className={flowAppStyles.contentParamsItemFirst + ' ' + flowAppStyles.contentParamsItemFirstStatic}>
                                    <div className={flowAppStyles.contentParamsItem}>
                                        <div className={flowAppStyles.contentParamsItemLabel}>
                                            网站域名
                                        </div>
                                        <div className={flowAppStyles.contentParamsItemDetail}>
                                            {item.domain_schema + item.domain_name + item.domain_route}
                                        </div>
                                    </div>
                                </div>
                                <div className={flowAppStyles.contentParamsItem}>
                                    <div className={flowAppStyles.contentParamsItemLabel}>
                                        访问地址
                                    </div>
                                    {item.request_address && <div className={flowAppStyles.contentParamsItemDetail + " " + flowAppStyles.contentParamsItemDetailAddress}>
                                        <span onClick={() => { window.open(formatUrl(item.request_address) || '', '_blank') }}>{formatUrl(item.request_address) || ''}</span>
                                    </div>}
                                </div>
                            </div>
                        </div>)
                })}
            </div>
        </>}
    </>
}