import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import moment from "moment";
import styles from "./OperationDetailFlowTimer.module.scss";

export default function OperationDetailFlowTimer(props: any) {
    const { params, deployId } = props;

    const router = useRouter();
    const teamId = router.query.teamId;
    const teamplateId = router.query.id;
    
    const [time, setTime] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
    
    useEffect(() => {
        if (params) {
            setTime(moment(params.next_exec_time).format('YYYY-MM-DD HH:mm:ss'));
        }
    }, [params])

    const formatWeek = (week: string) => {
        const weekArr = ['一', '二', '三', '四', '五', '六', '日'];
        return `周${weekArr[+week - 1]}`;
    }

    const disTime = useMemo(() => {
        return time.slice(10, 19);
    }, [time])

    const disDate = useMemo(() => {
        const dateArr = time.slice(0, 10).split('-');
        return `${dateArr[0]}年${dateArr[1]}月${dateArr[2]}日`;
    }, [time])

    const disCycleTime = useMemo(() => {
        if (!params.cron) return '';

        const cronArr = params.cron.split(' ');
        const hhArr = cronArr[2].split(',').map((item: string) => item.length === 1 && item !== '0' ? `0${item}` : item).join('/');
        const mmArr = cronArr[1].split(',').map((item: string) => item.length === 1 && item !== '0' ? `0${item}` : item).join('/');

        const hhDis = cronArr[2] === '*' ? '每时' : `${hhArr}时`;
        const mmDis = cronArr[1] === '*' ? '每分' : `${mmArr}分`;

        if (cronArr[5] !== '?' && cronArr[5] !== '*') {
            const weekArr = cronArr[5].split(',').map((item: string) => { 
                return formatWeek(item) }).join('/');

            const weekDIs = cronArr[5] === '*' ? '每周' : `每${weekArr}`;
            return `${weekDIs}，${hhDis}，${mmDis}`;
        } else {
            if (cronArr[3] === '?') {
                return `每日，${hhDis}，${mmDis}`
            } else if (cronArr[3] !== '*') {
                const dayArr = cronArr[3].split(',').map((item: string) => { return `${item}号` }).join('/');
                return `每月${dayArr}，${hhDis}，${mmDis}`
            } else {
                return `每日，${hhDis}，${mmDis}`;
            }
        }
    }, [params])

    return (
        <div className={styles.outer}>
            <div className={styles.title}>说明：</div>
            <div className={styles.moreInfo}>当前启用版本为定时启动版本，详细执行记录请前往 <a href={"./monitor?deployId=" +deployId+ '&templateId=' +teamplateId+'&teamId='+teamId+'&template_type=3'} >分析统计</a> 页面查看</div>
            <div className={styles.fixedTime}>
                <span className={styles.fixedLabel}>启动时间(固定周期)</span>
                <span className={styles.fixedDate}>
                    {disCycleTime}
                </span>
            </div>
            <div className={styles.startTime}>
                <span className={styles.startLabel}>最近一次启动时间</span>
                {params.next_exec_time && <span className={styles.startDate}>
                    <span>{disDate}</span> 
                    <span>{moment(params.next_exec_time).format('dddd')}</span> 
                    <span>{disTime}</span>
                </span>}
            </div>
        </div>
    )
}