.outer {
    margin-top: 16px;
    overflow-y: auto;
    height: calc(100% - 32px);

    .header {
        color: #1B2532;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 16px;
    }

    .inner {
        background: #F7F9FA;
        padding: 16px;
        width: calc(100% - 32px);

        .label {
            color: #1B2532;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin-right: 16px;
        }

        .inline {
            color: #1B2532;
            font-size: 14px;
            line-height: 22px;
            margin-bottom: 24px;
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 8px;
        }

        .params {
            .table {
                width: 100%;
                font-size: 14px;
                line-height: 22px;
                display: flex;
                flex-direction: column;
                margin: 8px 0 24px;

                span {
                    display: inline-block;
                    padding: 9px 16px;
                    margin-bottom: 1px;
                    background: #FFFFFF;
                }

                .tbHeader {
                    color: #626F84;
                    font-weight: 400;
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    background: #FFFFFF;
                }

                .content {
                    color: #1B2532;
                    margin-top: 1px;
                }
            }
        }

        .result {
            margin-bottom: 24px;

            .label {
                margin-bottom: 8px;
                display: inline-block;
            }

            .tokenDisplay {
                padding: 12px 16px;
                background: #FFFFFF;
                border: 1px solid #D5D7DE;
                border-radius: 4px;
            }
        }
    }
}