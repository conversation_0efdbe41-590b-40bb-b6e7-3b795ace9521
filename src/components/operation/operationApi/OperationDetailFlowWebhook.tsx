import { useState, useEffect } from "react";
import Editor from "@monaco-editor/react";
import { useRouter } from "next/router";
import styles from "./OperationDetailFlowWebhook.module.scss";

export default function OperationDetailFlowWebhook(props: any) {
    const { params } = props;

    const router = useRouter();
    const flowId = router.query.id;

    const [reqUrl, setReqUrl] = useState('');

    const paramToCode = (event: any) => {
        if(event.length === 0) {
            return ''
        }

        const holderStr = ',\n      '
        const input_keysStr = event.map((item:any)=>{
            return  `"${item.name}": ${JSON.stringify(item.value).replaceAll("\\", "\\\\")}`
        }).join(holderStr)
        
        return input_keysStr;
    }

    const generateCode = () => {
        const splitCode = `import json
import requests

def run_flow(inputs: dict, url: str, id: str, Token: str, eventName: str):
  headers = {
    'Authorization': f'{Token}',
  }
  response = requests.post(
    url=f"{url}/webhook/flow/{id}/{eventName}",
    headers=headers,
    json=inputs
  )
  return response

if __name__ == '__main__':
  request_inputs = '''{
    "event_params": {
      ${paramToCode(params.event_params)}
    },
    "base_url": "${window.location.origin}/api",
    "template_id": "${flowId}",
    "token":"${params.access_restrictions.type === 3 ? params.access_restrictions.token : ''}",
    "event_name":"${params.event_name}"
  }'''

  request_inputs = json.loads(request_inputs)
  base_url = request_inputs.get("base_url")
  token = request_inputs.get("token")
  event_name = request_inputs.get("event_name")
  template_id = request_inputs.get("template_id")

  event_params = request_inputs.get("event_params")
  result = run_flow(event_params, base_url, template_id, token, event_name)
  print(result.json())`;

        return splitCode;
    }

    useEffect(() => {
        const url = `${window.location.origin}/api/webhook/flow/${flowId}/${params.event_name}`;
        setReqUrl(url);
    }, [params])

    const getTableDom = (params: any) => {
        return (
            <div className={styles.table}>
                <div className={styles.tbHeader}>
                    <span style={{ width: 'calc(25% - 1px)' }}>名称</span>
                    <span style={{ width: '1px', height: '16px', padding: 0, background: '#F7F9FA' }}></span>
                    <span style={{ width: 'calc(25% - 1px)' }}>类型</span>
                    <span style={{ width: '1px', height: '16px', padding: 0, background: '#F7F9FA' }}></span>
                    <span style={{ width: '50%' }}>样例值</span>
                </div>
                {params.map((item: any) => {
                    return (
                        <div className={styles.content}>
                            <span style={{ width: '25%' }}>{item.name}</span>
                            <span style={{ width: '25%' }}>{item.type}</span>
                            <span style={{ width: '50%' }}>{item.value}</span>
                        </div>
                    )
                })}
            </div>
        )
    }

    return (
        <div className={styles.outer}>
            <div className={styles.header}>调用说明</div>
            <div className={styles.inner}>
                <div className={styles.inline}>
                    <span className={styles.label}>Post 请求地址：</span>
                    <span>{reqUrl}</span>
                </div>
                <div className={styles.params}>
                    <span className={styles.label}>请求 body 参数：</span>
                    {getTableDom(params.event_params)}
                </div>
                <div className={styles.inline}>
                    <span className={styles.label}>请求示例：</span>
                    <span>{reqUrl}</span>
                </div>
                <div className={styles.result}>
                    <span className={styles.label}>结果获取：</span>
                    <div className={styles.tokenDisplay}>
                        <Editor
                            className="deploy-detail-right-content-editor"
                            theme="dark"
                            language="python"
                            height={"660px"}
                            value={generateCode()}
                            options={{
                                fontSize: 14,
                                lineNumbers: "off",
                                roundedSelection: false,
                                scrollBeyondLastLine: false,
                                readOnly: true,
                                minimap: {
                                    enabled: false,
                                },
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
