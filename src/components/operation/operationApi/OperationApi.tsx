import { useEffect, useState } from 'react';
import { Dropdown, Switch, Tooltip } from 'antd';
import OperationDetailFlow from '@/components/operation/OPerationDetailFlow';
import OperationDetailText from '@/components/operation/OPerationDetailText';
import operationListStyles from '@/styles/OperationList.module.scss';
import deployDetailSrc from '@/images/deployDetailSrc.svg';
import { copyParamValue } from '@/utils/common';

export default function OperationApi(props: any) {
  const { deployDetail, publishDetail, flowApiType, changeDeploy, menuProps, modeArr, curMode, deployId, flowType, flowSysVar } = props;
  const [apiHost, setApiHost] = useState('');

  useEffect(() => {
    if (typeof window !== "undefined") {
      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      const port = window.location.port; // 这里单独拿 port，嵌到iframe里面不单独写有可能取不到

      let origin = protocol + '//' + hostname;
      if (port) {
        origin += ':' + port;
      }
      setApiHost(origin);
    }
  }, [])


  return <>
    <div className={operationListStyles.operationDetailStatus}>
      <div className={operationListStyles.publicWrapper}>
        <span className={operationListStyles.publicTitle}>{flowApiType === 1
          ? 'API服务' : flowApiType === 2 ? '定时任务开启' : 'webhook 调用开启'}</span>
        <Switch onChange={changeDeploy} checked={deployDetail.status === 1} />
      </div>
      {deployDetail.status === 1 && flowApiType === 1 && <div className={operationListStyles.rightFooter}>
        {publishDetail.chain_type != 'text' && <Dropdown
          menu={menuProps}
          overlayClassName={operationListStyles.dropMenuList}
        >
          <div className={operationListStyles.selectMode}>
            {modeArr[curMode - 1]}
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.4773 6.35225C13.6969 6.13258 14.0531 6.13258 14.2727 6.35225C14.4741 6.55362 14.4909 6.86967 14.3231 7.09014L14.2727 7.14775L9.77275 11.6477C9.57138 11.8491 9.25533 11.8659 9.03486 11.6981L8.97725 11.6477L4.47725 7.14775C4.25758 6.92808 4.25758 6.57192 4.47725 6.35225C4.67862 6.15089 4.99467 6.13411 5.21514 6.30191L5.27275 6.35225L9.37479 10.4541L13.4773 6.35225Z" fill="#1D2531" />
            </svg>
          </div>
        </Dropdown>}
        <Tooltip placement="topLeft" title={apiHost + '/api'}>
          <div className={operationListStyles.rightFooterApi} onClick={() => { copyParamValue(apiHost + '/api') }}>
            <span className={operationListStyles.rightFooterApiTis}>API服务</span>
            <span className={operationListStyles.rightFooterApiUrl}>{apiHost + '/api'}</span>
          </div>
        </Tooltip>
        <Tooltip placement="bottomLeft" title={<span>app_id：{deployDetail.app_id} <br /> 密钥：{deployDetail.app_secret}</span>}>
          <div className={operationListStyles.rightFooterScr} onClick={() => { copyParamValue(`app_id:${deployDetail.app_id}` + ' ' + `密钥:${deployDetail.app_secret}`) }}>
            <img src={deployDetailSrc.src} />
            <span className={operationListStyles.rightFooterApiSrc}>API秘钥</span>
          </div>
        </Tooltip>
      </div>}
    </div>
    {
      deployDetail.status === 1 && <div className={operationListStyles.teamMembersDetail}>
        {publishDetail.chain_type == 'text' ?
          <OperationDetailText base_url={apiHost + '/api'} app_id={deployDetail.app_id} app_secret={deployDetail.app_secret} description='' template_id={Number(publishDetail.id)} input_keys={deployDetail.input_keys} /> :
          <OperationDetailFlow mode={curMode} deployId={deployId} flowApiType={flowApiType} flowType={+(flowType || '1')} sysVar={flowSysVar} base_url={apiHost + '/api'} app_id={deployDetail.app_id} app_secret={deployDetail.app_secret} description='' template_id={Number(publishDetail.id)} input_keys={deployDetail.input_keys} />}
      </div>
    }
  </>
}