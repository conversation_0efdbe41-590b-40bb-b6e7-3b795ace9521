export const initJsonEditor = async (text: string, id: any) => {
    const { JSONEditor } = await import('@/utils/vanilla-jsoneditor')
    let content = {
        text,
        json: undefined
    }

    document.getElementById(id)!.innerHTML = '';

    const editor = new (JSONEditor as any)({
        target: document.getElementById(id),
        props: {
            content,
            mode: 'text',
            readOnly: true,
        }
    })
}