import { useEffect, useRef, useState, useMemo } from "react";
import { Select, Tag } from "antd";
import OperationDetailStyles from "@/styles/OperationDetail.module.scss";
import Editor from "@monaco-editor/react";
import loader from '@monaco-editor/loader';

loader.config({ paths: { vs: './vs' } });
import { copyParamValue } from "@/utils/common";

import copyCode from '@/images/copyCode.svg'

import { Tree } from "antd";

interface Props {
  pyCode: string;
  detailData: Record<string, any>[];
  isAgentDetailTitle:boolean;
  isAgentTitle:boolean;
  curlCode?: string;
  jsCode?: string;
  mode?: number
}

const OperationDetailCommon = (props: Props) => {
  const [code, setCode] = useState(props.pyCode);
  const [language, setLanguage] = useState("python");

  const handleChange = (value: string) => {
    if (value === "Python") {
      setCode(props.pyCode);
      setLanguage("python");
    } else if (value === "CURL" && props.curlCode) {
      setCode(props.curlCode);
      setLanguage("shell");
    } else if (value === "JavaScript" && props.jsCode) {
      setCode(props.jsCode);
      setLanguage("javascript");
    }
  };

  useEffect(() => {
    setCode(props.pyCode);
    setLanguage("python");
  }, [props]);

  const selectOptions = useMemo(() => {
    const { pyCode, curlCode, jsCode } = props;

    const options = [];
    if (pyCode) {
      options.push({
        value: "Python",
        label: "Python",
      }); 
    } 
    if (curlCode) {
      options.push({
        value: "CURL",
        label: "CURL",
      }); 
    } 
    if (jsCode) {
      options.push({
        value: "JavaScript",
        label: "JavaScript",
      });
    }

    return options;
  }, [props])

  const editorRef: any = useRef(null);

  const copyCodeHandle = () => {
      copyParamValue(editorRef.current.getValue());
  };


  function handleEditorDidMount(editor: any) {
    editorRef.current = editor;
  }

  return (
    <div className={OperationDetailStyles.container}>
      <div className={OperationDetailStyles.left}>
        {props.detailData.map((item, index) => {
          return (
            <div className={OperationDetailStyles.leftContent} key={index}>
              <div className={OperationDetailStyles.leftTitle}>
                {item.title}
              </div>
              {item.func ? <div className={OperationDetailStyles.leftUrl}>
                <Tag className={OperationDetailStyles.leftFunc} color="#39A114">
                  {item.func}
                </Tag>
                <span className={OperationDetailStyles.leftUrlTis}>
                  {item.url}
                </span>
              </div> : null}
              <div className={OperationDetailStyles.leftSubTitle}>
                <p style={{ whiteSpace: 'pre-wrap' }}>{item.subTitles}</p>
              </div>
              {item.url ? <div className={OperationDetailStyles.leftContentReq}>
                <div className={OperationDetailStyles.leftContentReqTitle}>
                  Request Body
                </div>
                <div className={OperationDetailStyles.reqContainer}>
                  {reqItemFunc(item)}
                </div>
              </div> : null}
              {item.reqCode ? (
                <div className={OperationDetailStyles.leftContentCode}>
                  <div className={OperationDetailStyles.leftContentReqTitle}>
                    请求示例
                  </div>
                  <Editor
                    className="deploy-detail-left-content-editor"
                    theme="dark"
                    language="json"
                    width={"85%"}
                    height={item.reqHeight}
                    value={item.reqCode}
                    options={{
                      fontSize: 14,
                      lineNumbers: "off",
                      roundedSelection: false,
                      scrollBeyondLastLine: false,
                      readOnly: true,
                      minimap: {
                        enabled: false,
                      },
                    }}
                  />
                </div>
              ) : null}
              <div className={OperationDetailStyles.leftContentRes}>
                {item.url ? <div className={OperationDetailStyles.leftContentReqTitle}>
                  Response Body
                </div> : null}
                {
                  <div className={OperationDetailStyles.childrenContent}>
                    <Tree
                      showLine={true}
                      showIcon={false}
                      selectable={false}
                      defaultExpandedKeys={["0-0", "1-0", "0-0-1"]}
                      treeData={item.res}
                    />
                  </div>
                }
              </div>
              {item.resCode ? <div className={OperationDetailStyles.leftContentCode}>
                <div className={OperationDetailStyles.leftContentReqTitle}>
                  返回示例
                </div>
                <Editor
                  className="deploy-detail-left-content-editor"
                  theme="dark"
                  language="json"
                  width={"85%"}
                  height={item.resHeight}
                  value={item.resCode}
                  options={{
                    fontSize: 14,
                    lineNumbers: "off",
                    roundedSelection: false,
                    scrollBeyondLastLine: false,
                    readOnly: true,
                    minimap: {
                      enabled: false,
                    },
                  }}
                />
              </div> : null}
            </div>
          );
        })}
      </div>
      <div className={OperationDetailStyles.right}>
        <div className={OperationDetailStyles.rightTop}>
          <div className={OperationDetailStyles.rightTopTitle}>{props.isAgentDetailTitle?'获取Agent开场信息代码示例':props.isAgentTitle?'请求服务代码示例':'代码'}</div>
          <Select
            className="deploy-detail-right-top-select"
            defaultValue="Python"
            style={{ width: 120 }}
            variant="borderless"
            value={language === 'shell' ? 'CURL' : language.charAt(0).toUpperCase() + language.slice(1)}
            onChange={handleChange}
            options={selectOptions}
          />
          <div
            className={OperationDetailStyles.copyTis}
            onClick={copyCodeHandle}
          >
            <img src={copyCode.src} />
          </div>
        </div>
        <div className={OperationDetailStyles.rightContent}>
          <Editor
            onMount={handleEditorDidMount}
            className="deploy-detail-right-content-editor"
            theme="dark"
            language={language}
            value={code}
            options={{
              fontSize: 14,
              lineNumbers: "off",
              roundedSelection: false,
              scrollBeyondLastLine: false,
              readOnly: true,
              minimap: {
                enabled: false,
              },
            }}
          />
        </div>
      </div>
    </div>
  );
};

const reqItemFunc = (item: any) => {
  let locationList = item.req.map((itemS: any, index: number) => {
    return itemS.location;
  });
  locationList = Array.from(new Set(locationList));

  return locationList.map((locationItem: string, index: number) => {
    return (
      <div className={OperationDetailStyles.locationItem} key={index}>
        <div className={OperationDetailStyles.locationItemName}>
          {locationItem.charAt(0).toUpperCase() + locationItem.slice(1)}参数
        </div>
        {item.req.map((itemS: any, indexS: number) => {
          return (
            <div
              className={OperationDetailStyles.locationItemContent}
              key={indexS}
            >
              {itemS.location === locationItem ? (
                <div className={OperationDetailStyles.reqItem}>
                  {itemS.children ? (
                    <>
                      <span className={OperationDetailStyles.reqItemName}>
                        {itemS.name}
                      </span>
                      <span className={OperationDetailStyles.reqItemType}>
                        {itemS.type}
                      </span>
                      <span className={OperationDetailStyles.reqItemReq}>
                        {itemS.required ? "必需" : "可选"}
                      </span>
                      <div className={OperationDetailStyles.childrenContent}>
                        <Tree
                          showLine={true}
                          showIcon={false}
                          selectable={false}
                          defaultExpandedKeys={["0-0", "0-0-0", "0-0-1"]}
                          treeData={[itemS]}
                        />
                      </div>
                    </>
                  ) : (
                    <>
                      <div>
                        <span className={OperationDetailStyles.reqItemName}>
                          {itemS.name}
                        </span>
                        <span className={OperationDetailStyles.reqItemType}>
                          {itemS.type}
                        </span>
                        <span className={OperationDetailStyles.reqItemReq}>
                          {itemS.required ? "必需" : "可选"}
                        </span>
                      </div>
                      <div className={OperationDetailStyles.reqItemDesc}>
                        {itemS.desc}
                      </div>
                    </>
                  )}
                </div>
              ) : null}
            </div>
          );
        })}
      </div>
    );
  });
};

export default OperationDetailCommon;
