.container {
    width: 100%;
    height: 100%;
}

.left {
    width: 50%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    border-right: 1px solid #D5D7DE;
    padding-top: 16px;
    float: left;
}

.leftContent {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: 12px;
}

.leftContent:last-child {
    margin-bottom: 0;
}

.leftTitle {
    color: #1B2532;
    /* 加粗/20px */
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    margin-left: -10px;
}

.leftUrl {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin: 8px 0;
}

.leftFunc {
    margin-right: 12px;
}

.leftUrlTis {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.leftSubTitle {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.leftContentReq {
    margin-top: 24px;
    width: 85%;
    /* background: red; */
}

.leftContentRes {
    margin-top: 12px;
    width: 85%;
}

.leftContentCode {
    width: 100%;
    margin-top: 24px;
    margin-bottom: 12px;
}

.leftContentReqTitle {
    color: #1B2532;
    /* 加粗/20px */
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    /* 150% */
}

.right {
    width: 50%;
    height: 100%;
    box-sizing: border-box;
    float: left;
}

.rightTop {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 44px;
    background: #F7F8FA;
    padding: 0 16px;
    position: relative;
}

.rightTopTitle {
    color: #1B2532;
    /* 加粗/20px */
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    /* 150% */
}

.copyTis {
    color: #006BFF;
    /* 正常/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    /* 150% */
    cursor: pointer;
    position: absolute;
    right: 16px;
    top: 14px;
}

.rightContent {
    width: 100%;
    height: calc(100% - 44px);
    padding: 0px 0 0 16px;
}

.locationItem {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.locationItemName {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 16px;
    margin-top: 16px;
}

.reqItemName {
    border: 1px solid #9BA7BA;
    background: rgba(240, 245, 255, 0.60);
    text-align: center;
    border-radius: 4px;
    box-sizing: border-box;
    height: 30px;
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
    padding: 4px 8px;
    margin-right: 12px;
}

.reqItemType {
    color: var(--auxiliary-text, #9BA7BA);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
}

.reqItemReq {
    margin-left: 12px;
}

.reqItemDesc {
    color: var(--main-text, #1B2532);
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    margin-top: 12px;
    margin-bottom: 12px;
    /* margin-top: 12px;
    margin-bottom: 12px; */
}

.childrenContent {
    margin-top: 12px;
}

.contentTitle {
    color: #1B2532;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    padding: 8px 16px;
    height: 64px;
    line-height: 48px;
}

.agentContent {
    display: flex;
    flex-direction: column;
    padding-top: 24px;
    padding-left: 32px;
}

.contentWrapper {
    background: #fff;
    height: calc(100% - 96px);
    min-height: calc(100vh - 96px);
    padding-top: 16px;
    position: relative;

    .messageWrapper {
        padding: 16px 24px 16px;
        background: #F1FEE1;
        margin: 0 16px;
        border-radius: 4px;

        img {
            vertical-align: middle;
            margin-top: -3px;
        }

        .successTips {
            font-size: 16px;
            font-weight: 600;
            margin: 0 16px 0 8px;
        }
    }

    .publicContent {
        padding: 24px 32px;
    }

    .publicItem {
        width: 100%;
        display: flex;
        margin-bottom: 46px;
    }

    .publishStatusItem {
        justify-content: space-between;
    }

    .publicLabel {
        display: flex;

        span.publicLabelTitle {
            margin-right: 40px;
            color: #1B2532;
            font-size: 14px;
            font-weight: 600;
            min-width: 100px;
        }

        .publishStatus {
            margin-right: 28px;

            .circleIcon {
                margin-right: 8px;
            }
        }

        .publishStatusTip {
            color: #626F84;
            font-size: 12px;
            margin-top: 3px;
        }
    }

    .tagList {
        width: calc(100vw - 450px);
    }

    .tagItem {
        padding: 8px 24px;
        margin-right: 16px;
        border-radius: 4px;
        margin-bottom: 16px;
        cursor: pointer;
        border: 1px solid #D5D7DE;
        background: #FFF;
        display: inline-block;
    }

    .agentTagItem {
        padding: 8px 24px;
        margin-right: 16px;
        border-radius: 4px;
        cursor: pointer;
        border: 1px solid #D5D7DE;
        background: #FFF;
        display: inline-block;
    }

    .tagItem img,
    .agentTagItem img {
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
        margin-top: -3px;
    }

    .tagCheckedItem {
        border-color: #477FFF;
        color: #1D7CFF;
    }

    .markDownModalBg {
        background: #fff;
        border-radius: 10px;
        padding-bottom: 0;
    }

    .deployTerms {
        padding-top: 50px;
    }

    .deployTermsTitle {
        padding-top: 16px;
    }

    .fuWuA {
        color: #477FFF;
        margin-left: -10px;
    }

    .footer {
        position: absolute;
        right: 0;
        bottom: 0;
        width: calc(100% - 64px);
        margin: 16px 32px;
        border-radius: 4px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
    }
}

.agentContentWrapper {
    height: calc(100% - 96px);
    min-height: calc(100vh - 96px);
    padding-top: 16px;
    // width: 1080px;
    margin: 0 auto;

    :global {
        ant-tabs-tab-btn:hover {
            content: '即将上线，敬请期待';
            border: 1px solid red;

        }
    }

    :global .ant-tabs-nav-wrap{
        margin-left: -12px;
    }

}

.circleIcon {
    width: 8px;
    height: 8px;
    display: inline-block;
    background: #BCCAD6;
    border-radius: 4px;
    margin-right: 4px;
}

.reviewCircleIcon {
    background: #1D7CFF;
}

.publishedCircleIcon {
    background: #4EC01E;
}

.publishedBtn {
    border-radius: 4px;
    background: #DFFAC7;
    color: #4EC01E;
    padding: 5px 8px;
    font-size: 12px;
    display: inline-block;
    vertical-align: top;
    margin-right: 8px;
}

.publishedVersion {
    display: inline-block;
    font-size: 12px;
    color: #657083;
    padding-left: 8px;

    &.published {
        border-left: 1px solid #E1E7ED;
    }
}

.unPublishedBtn {
    color: #626F84;
    font-size: 12px;
    display: inline-flex;
    height: 22px;
    padding: 5px 8px;
    gap: 10px;
    border-radius: 4px;
    background: #EBF0F5;
    text-align: center;
    align-items: center;
    justify-items: center;
}

.agentPublishedBtn {
    font-size: 12px;
    display: inline-flex;
    height: 22px;
    padding: 5px 8px;
    gap: 10px;
    border-radius: 4px;
    background: #F1FEE1;
    color: #4EC01E;
    text-align: center;
    align-items: center;
    justify-items: center;
}

.unpubishItemContent {
    margin-top: 18px;
}

.publishItems {
    width: 100%;
    display: flex;
    // width: 1080px;
    height: 60px;
    padding: 8px 16px;
    // justify-content: center;
    align-items: center;
    border-bottom: 1px solid #EDF1F5;
    background: #FFF;
    border-right: 1px solid #EDF1F5;
    border-left: 1px solid #EDF1F5;

    &:hover {
        // border: 1px solid #1890ff !important;
        background: #F7F9FA !important
    }

    .checkedContent {
        // flex: 0 0 auto;
        // /* 不伸缩，固定宽度 */
        // min-width: 0;
        width: 320px;
        display: inline-flex;
        align-items: center;

    }

    .publishedConetnt {
        flex: 1;
        /* 伸缩占据剩余空间 */
        min-width: 0;
        // margin-left: 16px;
        display: inline-flex;
    }

    .itemInfo {
        display: inline-flex;
        align-items: center;
        margin-left: 12px;
    }

    .channelName {
        font-size: 14px;
        font-weight: 600;
        margin: 0 8px;
        cursor: pointer;
        color: #1B2532;

        &:hover {
            color: #006BFF;
        }
    }


    .btnList {
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
        width: 288px;

        .btnItem {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 72px;
            height: 32px;
            padding: 4px 8px;
            justify-content: center;
            flex-shrink: 0;
            margin-left: 8px;

            &:hover {
                cursor: pointer;
                border-radius: 4px;
                background: #EDF1F5;
            }
        }
    }

    .btnList .btnItem:nth-last-child(2),
    .btnList .btnItem:last-child {
        padding-left: 8px;
    }


    .authStatus {
        font-size: 12px;
        color: #9EA7B8;
        position: relative;
        padding-left: 10px;

        &::before {
            display: block;
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background-color: #9EA7B8;
        }

        &.authed {
            color: #00B42A;

            &::before {
                background: #00B42A;
            }
        }
    }

    .line {
        height: 13px;
        width: 1px;
        background: #E1E7ED;
        display: inline-flex;
        margin: 0px 8px;
        justify-content: center;
        align-items: center;
    }

    .publishInfo {
        color: #626F84;
        font-size: 12px;
    }

    .publishInfoBack {
        color: var(---, #1D2531);
        font-size: 14px;
        margin: 0 8px;
    }

    .channelTag {
        font-size: 14px;
        color: #626F84;

    }

    .chooseTagList {
        display: inline-block;
        font-size: 14px;
        color: var(---, #1B2532);
        text-align: center;
        cursor: pointer;

        img,
        span {
            display: inline-block;
            vertical-align: middle;
        }
    }

}

.publishItems:first-child {
    border-top: 1px solid #EDF1F5;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.publishItems:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}


.cancelModal {
    padding: 24px;

    :global {

        .ant-modal-body {
            display: flex;
            align-items: center;

            span {
                margin-left: 8px;
            }
        }

    }

}

.menuBox {
    display: flex;
    border-radius: 4px;

    // .ant-dropdown-menu-submenu-expand-icon {
    //     // margin-top: -20px;
    // }

    overflow-y: hidden;

    .ant-space-item {
        display: inline-flex;
        justify-content: center;
        align-items: center;
    }

    :global {
        .ant-space-item {
            display: inline-flex;
            justify-content: center;
            align-items: center;
        }
    }

}


.examinedBtn {
    color: #626F84;
    font-size: 12px;
    display: inline-flex;
    height: 22px;
    padding: 5px 8px;
    gap: 10px;
    border-radius: 4px;
    background: #EBF0F5;
    text-align: center;
    align-items: center;
    justify-items: center;
    border-radius: 4px;
    background: var(---Blue-Blue-10, #EEF6FF);
    color: var(---Blue-Blue-60, #477FFF);

}


.tooltipBox {
    display: inline-flex;
    justify-content: center;
    align-items: center;

    .descIcon,
    .aaaaaIcon,
    .linkIcon {
        cursor: pointer;
        fill: #9EA7B8;
        // margin: 0 8px;

        &:hover {
            fill: #006BFF;
        }

        &.disabled {
            fill: #9EA7B8;
            cursor: no-drop;
        }
    }
}

.dropMenuList {
    box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
    border-radius:  8px;
    background: #FFF;

    :global {
        .ant-dropdown-menu-submenu-title {
            position: relative;
        }

        .ant-dropdown-menu-submenu-expand-icon {
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.authDialog {

    .desc {
        font-size: 14px;
        color: #626F84;
        padding: 24px 0;

        a {
            text-decoration: underline;
        }
    }

    :global {
        .ant-modal-content {
            padding-top: 0 !important;
        }

        .ant-modal-header {
            padding: 16px 24px !important;
            position: relative;
            top: 0 !important;
            left: -24px !important;
            width: calc(100% + 48px) !important;
            background: #F7F8FA !important;
            margin-bottom: 0 !important;
        }
    }
}
.operationDetailStatus{
    padding: 16px;
    color: #1b2532;
    position: relative;
    // display: flex;
    justify-content: space-between;
    background: #FFF;
}

.publicWrapper {
    background: #FFF;
    border-Bottom: 1px solid #D5D7DE;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 16px;
    line-height: 32px;

    .publicTitle {
        font-weight: 600;
        margin-right: 40px;
        font-size: 16px;
    }
}

.publicApiWrapper {
    background: #FFF;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 16px;
    line-height: 32px;

    .publicTitle {
        font-weight: 600;
        margin-right: 40px;
        font-size: 16px;
    }

}

.publicContent {
    background: #FFF;
    padding: 16px;
    border-bottom: 1px solid #D5D7DE;
}

.publicContentTitle {
    margin-bottom: 12px;
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.teamMembersDetail {
    border-top: 1px solid #D5D7DE;
    padding: 0 0 0 32px;
    width: 100%;
    height: calc(100vh - 354px);
    color: #1B2532;
    font-size: 15px;
    background: #FFF;
}

.apiLine {
    width: 148.5px;
    height: 1px;
    background: #EBF0F5;
    display: inline-block;
    margin: 0 9px;
}

.addApiDialog {

    // background: #006BFF;
    :global {
        .ant-btn-default {
            border-radius: 4px;
            border: 1px solid #006BFF;
            color: #006BFF;
        }

        .ant-modal-content {
            padding-top: 0;
        }

        .ant-modal-header {
            padding: 16px 24px;
            position: relative;
            top: 0;
            left: -24px;
            width: calc(100% + 48px);
            background: #F7F8FA;
            margin-bottom: 24px;
        }

    }
}

.apiKeyContent {
    display: flex;
    width: 480px;
    flex-direction: column;
    border-radius: 4px;
    background: #F7F9FA;
    padding: 24px;
}


.authDialog {

    .desc {
        font-size: 14px;
        color: #626F84;
        padding: 24px 0;

        a {
            text-decoration: underline;
        }
    }

    :global {
        .ant-modal-content {
            padding-top: 0 !important;
        }

        .ant-modal-header {
            padding: 16px 24px !important;
            position: relative;
            top: 0 !important;
            left: -24px !important;
            width: calc(100% + 48px) !important;
            background: #F7F8FA !important;
            margin-bottom: 0 !important;
        }
    }
}

.publishContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 4px;
    background: #FFF;
    // width: 1080px;
}

.menuContent {
    display: inline-flex;
    width: 280px;
    align-items: center;

    :global {
        .ant-space-item {
            cursor: pointer;
        }
    }
}

.statusContent {
    display: inline-flex;
    width: 160px;
    height: 32px;
    align-items: center;
}
.agentVersion{
    
    color: #1B2532;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
}
.versionContent{
    width: 100%;
    position: relative;

    :global {
        .ant-input-outlined:focus-within {
            border: 1px solid #1677EF !important;
            box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
        }
    }

    .textArea {
        height: 100% !important;
        width: 100% !important;
       
    }

    :global .ant-input-affix-wrapper-focused {
        border: none !important;
    }

    :global .ant-input-affix-wrapper{
        border-radius: 8px !important;
        border-color: #E1E7ED;

    }


    :global .ant-input-data-count {
        right: 12px;
        color: #9EA7B8;
        font-size: 12px;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; 
        bottom: 0 !important;
        margin-right: 7px !important;
    }

    :global .ant-input-textarea-show-count > .ant-input {
        height: 88px;
    }

}

.tagPopover {

    :global(.ant-popover-arrow) {
        display: none;
    }
    
    :global(.ant-popover-inner-content) {
        padding: 5px 12px;
    }
    
    :global(.ant-popover) {
        margin-top: -8px;
    }
    :global(.ant-popover-content){
        top:-10px !important;
        left: 0 !important;
        box-shadow: 0px 7px 17px -2px rgba(75, 85, 105, 0.12), 0px 11px 25px 4px rgba(27, 37, 50, 0.07);
        border-radius: 8px;
        background: #FFF;
    }
}

.popoverContent {
    min-width: 150px;
    
    .tagItem {
        margin-bottom: 8px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}
