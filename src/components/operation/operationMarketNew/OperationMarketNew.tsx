import { Button, Checkbox, CheckboxChangeEvent, Form, Input, MenuProps, message, Modal, Popover, Radio, Space, Tooltip } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import operationDetailStyles from './operationMarketNew.module.scss';
import arrowDownIcon from "@/images/arrowDown.png";
import arrowUpIcon from "@/images/arrowUp.png";
import { useRouter } from 'next/router';
import { reqFlowCancelPublic, reqFlowChannelList, reqFlowChannelTags, reqFlowInReviewCancel } from '@/service/flow3.0';
import { notify } from '@/utils/messageManager';

const OperationMarketNew = forwardRef((props: any, ref) => {
  const { chainId, setInitLoading, setIsVisiblePublish } = props;
  const router = useRouter();
  const [channelList, setChannelList] = useState(new Array());
  const [openPopoverId, setOpenPopoverId] = useState<number | string | null>(null); // 当前打开的 item id 不能用boolean值，为了保证页面只有一个pover组件显示


  useEffect(() => {
    let resultIndex = channelList.findIndex(item => item.publish_status == 0);
    setIsVisiblePublish(resultIndex > -1);
  }, [channelList])

  useImperativeHandle(ref, () => ({
    channelList: channelList,
    getChannel: () => getChannel(),
  }));

  const initChannelList = (list?: any) => {
    const initList = list ? list : channelList;
    initList?.forEach(channel => {
      channel['publish_status'] = channel.is_publish;
      channel['selected_tags'] = channel?.channel_tags?.map((tag: any) => { return { id: tag.tag_id, tag_name: tag.tag_name } }) || []
    })
    setChannelList(initList || []);
  }

  const handleTagRadioChange = (e: any, item: any) => {
    const tag = {
      tag_id: e.target.value,
      tag_name: item?.channel_tag_list?.find(obj => obj.id == e.target.value).tag_name || ''
    }
    handleTagSelectionChange(e, item, tag, false)
  }

  const handleTagSelectionChange = (e: any, item: any, tag: any, is_checkbox = true) => {
    // 阻止事件冒泡和默认行为，防止dropdown关闭
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    if (is_checkbox) {
      const isTagSelected = item['selected_tags'].some(t => t.id === tag.tag_id);
      if (isTagSelected) {
        // 如果标签已选中，检查是否是最后一个标签
        if (item['selected_tags'].length === 1) {
          // 如果是最后一个标签，不允许取消选中
          message.warning('至少需要选择一个标签'); // 保持不变
        } else {
          // 如果不是最后一个标签，则可以移除
          item['selected_tags'] = item['selected_tags'].filter(t => t.id !== tag.tag_id);
        }
      } else {
        // 如果标签未选中，则添加
        item['selected_tags'].push({ id: tag.tag_id, tag_name: tag.tag_name });
      }
    }

    if (!is_checkbox) {
      item['selected_tags'] = [{ id: tag.tag_id, tag_name: tag.tag_name }]
    }

    setChannelList([...channelList])
  };

  const handleDropdownClick = async (item: any) => {
    if (openPopoverId === item.id) {
      // 如果已经打开，就关闭
      setOpenPopoverId(null);
      return;
    }

    // 加载标签列表
    let tagList = await getTagList(item.id);
    const agentTagList = tagList?.map(item => {
      return { ...item, id: item.tag_id }
    })
    item['channel_tag_list'] = agentTagList
    // 标签列表加载完成后，再设置openPopoverId
    setOpenPopoverId(item.id);
  };

  //取消发布
  const newCanelPublish = async (id: any) => {
    console.log('flow取消指定渠道发布')
    setInitLoading(true);
    try {
      const res = await reqFlowCancelPublic({
        flow_id: chainId,
        channel_id: id
      })
      getChannel();
      message.success('取消成功')
    }
    catch (error: any) {
      getChannel();
      message.error(error.message);
    }

  }

  // 审核状态撤销审核 reqFlowInReviewCancel
  const inReviewCancelPublish = async (id: any) => {
    try {
      setInitLoading(true);
      await reqFlowInReviewCancel({
        flow_id: chainId,
        template_check_id: id
      })
      notify.info('操作成功');
      getChannel();
    } catch (err) {
      setInitLoading(false);
    }
  }

  //获取渠道标签
  const getTagList: any = async (id: any) => {
    const res = await reqFlowChannelTags({
      channel_id: id,
      compiled: 1,
    })
    return res
  }

  const getChannel = async () => {
    console.log("flow-公开发布渠道获取");
    setInitLoading(true);
    const res = await reqFlowChannelList({ flow_id: Number(chainId) });
    setInitLoading(false);
    const channelList = res?.channels_info || [];
    initChannelList(channelList);

  }

  const handleCheckboxChange = (e: CheckboxChangeEvent, item: any) => {
    item.publish_status = e.target.checked ? 1 : 0;
    setChannelList([...channelList])
  };

  //跳转到渠道商店对应的flow列表 本地不做
  const jumpChannel = (id: any) => {
    console.log("🚀 ~ jumpChannel ~ id:", id)
  }

  useEffect(() => {
    if (chainId) {
      getChannel()
    }

  }, [chainId])



  return <div className={operationDetailStyles.publishContent}>
    {
      channelList?.map((item) => (
        <div className={operationDetailStyles.publishItems} key={item.id}>
          <div className={operationDetailStyles.checkedContent}      >
            <Checkbox onChange={(e) => handleCheckboxChange(e, item)} checked={item.publish_status} disabled={item.is_publish == 1}
              defaultChecked={item.publish_status}></Checkbox>
            <div className={operationDetailStyles.itemInfo}  >
              <img src={item.image_url} style={{ height: '19px', width: '19px' }} onClick={() => { jumpChannel(item) }} />
              <Tooltip title={item.description}>
                <span className={operationDetailStyles.channelName} onClick={() => { jumpChannel(item) }}>{item.channel_name}</span>
              </Tooltip>
              {item.template_check_status == 2 && <div className={operationDetailStyles.tooltipBox}>
                <Tooltip
                  title={
                    <>
                      <span className={operationDetailStyles.publishInfo}>发布版本: </span>
                      <span className={operationDetailStyles.publishInfoBack}>{item.version ? item.version : '-'}</span>
                      <div className={operationDetailStyles.line}></div>
                      <span className={operationDetailStyles.publishInfo}>发布时间: </span>
                      <span className={operationDetailStyles.publishInfoBack}>{item.created_at ? item.created_at : '-'}</span>
                    </>
                  }
                >
                  <svg className={operationDetailStyles.linkIcon} xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fillRule="evenodd" clipRule="evenodd" d="M8.00065 0.833008C11.9587 0.833008 15.1673 4.04163 15.1673 7.99967C15.1673 11.9577 11.9587 15.1663 8.00065 15.1663C4.04261 15.1663 0.833984 11.9577 0.833984 7.99967C0.833984 7.72353 1.05784 7.49967 1.33398 7.49967C1.61013 7.49967 1.83398 7.72353 1.83398 7.99967C1.83398 11.4054 4.59489 14.1663 8.00065 14.1663C11.4064 14.1663 14.1673 11.4054 14.1673 7.99967C14.1673 4.59392 11.4064 1.83301 8.00065 1.83301C6.08457 1.83301 4.32235 2.71393 3.17 4.16549L4.36538 4.16573C4.62028 4.16573 4.83063 4.35647 4.86149 4.60301L4.86538 4.66573C4.86538 4.92063 4.67464 5.13098 4.4281 5.16183L4.36538 5.16573H2.2338H2.21775H1.94113C1.68623 5.16573 1.47588 4.97498 1.44502 4.72845L1.44113 4.66573V2.24149C1.44113 1.96535 1.66498 1.74149 1.94113 1.74149C2.19603 1.74149 2.40638 1.93223 2.43723 2.17877L2.44113 2.24149L2.44077 3.47657C3.7804 1.83002 5.80387 0.833008 8.00065 0.833008ZM8.49818 3.93775C8.46736 3.69121 8.25702 3.50044 8.00212 3.50042C7.72598 3.50039 7.5021 3.72423 7.50207 4.00037L7.50167 8.0033L7.506 8.06903C7.52036 8.1774 7.56997 8.27875 7.64812 8.3569L10.4746 11.1834L10.5258 11.2281C10.7218 11.3773 11.0027 11.3624 11.1817 11.1834L11.2264 11.1322C11.3756 10.9362 11.3607 10.6553 11.1817 10.4763L8.50155 7.79642L8.50207 4.00047L8.49818 3.93775Z" />
                  </svg>
                </Tooltip>
              </div>}
            </div>
          </div>
          <div className={operationDetailStyles.menuContent}>
            {
              item?.channel_tags?.length > 0 ?
                <>
                  <span className={operationDetailStyles.channelTag}>渠道标签：</span>
                  <Popover
                    trigger="click"
                    destroyOnHidden={true}
                    open={openPopoverId === item.id}
                    onOpenChange={(visible) => {
                      if (!visible) {
                        setOpenPopoverId(null); // 关闭
                      }
                    }}
                    overlayClassName={operationDetailStyles.tagPopover}
                    placement="bottom"
                    content={
                      openPopoverId == item.id ?
                        <div className={operationDetailStyles.popoverContent}>
                          <Radio.Group
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 8,
                            }}
                            onChange={(e) => { handleTagRadioChange(e, item) }}
                            value={item?.['selected_tags'][0].id}
                            options={
                              item?.channel_tag_list?.map(tag => {
                                return {
                                  value: tag.tag_id,
                                  label: tag.tag_name
                                }
                              })
                            }
                          />
                        </div>
                        : ''
                    }
                  >
                    <div onClick={() => handleDropdownClick(item)} style={{ display: 'inline-block' }} className={operationDetailStyles.menuBox}>
                      <Space>
                        <Tooltip title={
                          (item?.['selected_tags'] || []).map(tag => tag.tag_name).join(' | ')
                        }>
                          {(item?.['selected_tags'] || []).map((tag, index) => (
                            <span key={tag.id}>
                              {tag.tag_name}
                              {/* 只有在有多个标签时，且当前标签不是最后一个时才显示分隔符 */}
                              {(index != item?.['selected_tags']?.length - 1) ? ' | ' : ''}
                            </span>
                          ))}
                          {/* 如果标签数量大于 2，则显示省略号 */}
                          {item?.['selected_tags']?.length > 2 && (
                            <span style={{ marginLeft: '4px' }}>...</span>
                          )}
                        </Tooltip>
                        <img src={(openPopoverId === item.id) ? arrowUpIcon.src : arrowDownIcon.src} width={16} />
                      </Space>
                    </div>
                  </Popover>
                </>
                : ''
            }

          </div>
          <div className={operationDetailStyles.statusContent}>
            {item.is_publish == 2 && <span className={operationDetailStyles.agentPublishedBtn}>已发布</span>}
            {item.is_publish == 1 && <span className={operationDetailStyles.examinedBtn}>审核中</span>}
            {item.is_publish == 0 && <span className={operationDetailStyles.unPublishedBtn}>未发布</span>}
          </div>
          <div className={operationDetailStyles.btnList}>
            {item.is_publish == 2 && <span className={operationDetailStyles.btnItem} onClick={() => { newCanelPublish(item.id) }} style={{ color: '#006BFF' }}>取消发布</span>}
            {item.is_publish == 1 && <span className={operationDetailStyles.btnItem} onClick={() => { inReviewCancelPublish(item.template_check_id) }} style={{ color: '#006BFF' }}>撤销审核</span>}
          </div>
        </div>
      ))
    }
  </div>
})

export default OperationMarketNew; 