import moment from "moment";
import { flowOrder, curlCodeFlowOrder, pyCodeFlowOrderPre, pyCodeFlowOrderPro } from "@/utils/FlowConstants/flowOrder";
import { flowNoOrder, curlCodeFlowNoOrder, pyCodeFlowNoOrderPre, pyCodeFlowNoOrderPro } from "@/utils/FlowConstants/flowNoOrder";
import { flowSame, curlCodeFlowSame, pyCodeFlowSamePre, pyCodeFlowSamePro } from "@/utils/FlowConstants/flowSame";
import { flowPoll, curlCodeFlowPoll, pyCodeFlowPollPre, pyCodeFlowPollPro } from "@/utils/FlowConstants/flowPoll";
import { InterOrder, pyCodeOrderPre, pyCodeOrderPro } from "@/utils/FlowConstants/Interactive/order";
import { InterPoll, pyCodePollPre, pyCodePollPro } from "@/utils/FlowConstants/Interactive/poll";

import OperationDetailCommon from "./OperationDetailCommon";
import OperationDetailFlowTimer from "./operationApi/OperationDetailFlowTimer";
import OperationDetailFlowWebhook from "./operationApi/OperationDetailFlowWebhook";

interface Props {
  flowType: number,
  sysVar: any,
  mode: number,
  base_url: string;
  app_id: string;
  app_secret: string;
  template_id: number;
  description: string;
  input_keys: any;
  flowApiType: number;
  deployId: number;
}

const generateCode = (input_keys: any[],type: number) => {
  const holderStr = type === 1 ? ',\n        ' : type === 2 ? ',\n    ' : ',\n    '
  const input_keysStr = input_keys.map((item:any)=>{
      return `"${item.name}": ${JSON.stringify(item.value).replaceAll("\\", "\\\\")}` 
  }).join(holderStr)
  return input_keysStr
};

const generatePyCode = (input_keys: any[],type: number) => {
  if(input_keys.length === 0) {
    return ''
}
  const holderStr = ',\n       '
  const input_keysStr = input_keys.map((item:any)=>{
      return  `"${item.name}": ${JSON.stringify(item.value).replaceAll("\\", "\\\\")}`
  }).join(holderStr)
  return input_keysStr;
};

const selectMode = (props: any) => {
  const { mode, flowType } = props;

  if (flowType === 2) {
    switch(mode) {
      case 1:
        return {
          data: InterOrder,
          pre: pyCodeOrderPre,
          pro: pyCodeOrderPro,
          curl: curlCodeFlowOrder
        }
      default: 
        return {
          data: InterPoll,
          pre: pyCodePollPre,
          pro: pyCodePollPro,
          curl: curlCodeFlowOrder
        }
    }
  }
  switch(mode) {
    case 1:
      return {
        data: flowOrder,
        pre: pyCodeFlowOrderPre,
        pro: pyCodeFlowOrderPro,
        curl: curlCodeFlowOrder
      }
    case 2:
      return {
        data: flowNoOrder,
        pre: pyCodeFlowNoOrderPre,
        pro: pyCodeFlowNoOrderPro,
        curl: curlCodeFlowNoOrder
      }
    case 3:
      return {
        data: flowSame,
        pre: pyCodeFlowSamePre,
        pro: pyCodeFlowSamePro,
        curl: curlCodeFlowSame
      }
    default:
      return {
        data: flowPoll,
        pre: pyCodeFlowPollPre,
        pro: pyCodeFlowPollPro,
        curl: curlCodeFlowPoll
      }
  }
}

const OperationDetailFlow = (props: Props) => {
  const curData = selectMode(props);

  let curlCodeForFlowInput = ``;
  let pyCodeForFlowInput = ``;

  if (props.flowApiType === 1) {
    pyCodeForFlowInput = curData.pre + 
`
if __name__ == '__main__':
    request_inputs = '''{
        "chain_inputs": {
            "inputs": {
              ${generatePyCode(props.input_keys,1)}
            },
            "sys_vars": {
              "sys_uid": "${props.sysVar.sys_uid}",
              "sys_source": "${props.sysVar.sys_source}"
            }
        },
        "base_url": "${props.base_url}",
        "app_id": "${props.app_id}",
        "app_secret": "${props.app_secret}",
        "template_id": "${props.template_id}"
    }'''

    request_inputs = json.loads(request_inputs)
    base_url = request_inputs.get("base_url")
    template_id = request_inputs.get("template_id")
    ` + curData.pro;
      
  
    if (props.flowType === 1) {
      curlCodeForFlowInput = `
export base_url="${props.base_url}"
export app_id="${props.app_id}"  # AK
export app_secret="${props.app_secret}"  # SK
export template_id="${props.template_id}"  # 实例编号
export chain_inputs='{
    "chain_inputs": {
      "inputs": {
        ${generateCode(props.input_keys,2)}
      },
      "sys_vars": {
        "sys_uid": "${props.sysVar.sys_uid}",
        "sys_source": "${props.sysVar.sys_source}"
      }
    }
}' # 输入参数` + curData.curl;
    }
  }
 

  return (
    <>
      {props.flowApiType === 1 ? <OperationDetailCommon
        pyCode={pyCodeForFlowInput}
        curlCode={props.flowType === 1 ? curlCodeForFlowInput: ''}
        detailData={curData.data}
        isAgentDetailTitle={false}
        isAgentTitle={false}
        mode={props.mode}
      /> : props.flowApiType === 2 ? <OperationDetailFlowTimer 
        params={props.input_keys}
        deployId={props.deployId}
      /> 
      : <OperationDetailFlowWebhook
        params={props.input_keys}
      />}
    </>
  );
};

export default OperationDetailFlow;
