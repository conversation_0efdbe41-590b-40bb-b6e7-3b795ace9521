import React, { useRef, useEffect, useState, useContext, useCallback } from 'react';
import { Input, Tooltip, Spin, Button, Form, Table, message, Popconfirm } from 'antd';
import operationDetailStyles from '@/styles/OperationDetail.module.scss'
import operationDetail3Styles from '@/styles/OperationDetail3.module.scss'
import { reqAgentVersionManageList, reqAgentVersionManageAdd, reqAgentVersionManageDel, reqAgentVersionManageOnlineVersion,
    reqAgentVersionManagePublish, reqAgentVersionManageUpdateDesc, reqAgentVersionManageUseSetting } from '@/service/agent';
import { reqAgentVersionV3ManageAdd, reqAgentVersionV3ManageDel, reqAgentVersionV3ManageOnlineVersion, reqAgentVersionV3ManagePublish, 
    reqAgentVersionV3ManageUpdateDesc, reqAgentVersionV3ManageUseSetting } from '@/service/flow3.0';
import Router,{ useRouter } from 'next/router'
import versionManageEditImg from '@/images/edit.png'
import versionManageQuestionCircle from '@/images/questionCircle.svg'
import versionManageStarImg from '@/images/star.svg'
import flowExportJsonFile from '@/utils/FlowConstants/flowExportJsonFileV3';
const { TextArea } = Input;
// 定义接口 列表项数据
interface listItem {
    id: number,
    version: string
    generate_time: string
    input_type: number
    version_remark: string
    user_name: string
    publish_time: string
    publish_status: number
}
// 版本信息，表格中可编辑输入框处理
const context:any = null
const EditableContext = React.createContext(context);
const EditableRow = ({ index, ...props }: any) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};
const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}: any) => {
    const [editing, setEditing] = useState(false);
    const inputRef:any = useRef(null);
    const form = useContext(EditableContext);
    useEffect(() => {
        if (editing) {
            inputRef.current?.focus();
        }
    }, [editing]);
    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({
            [dataIndex]: record[dataIndex],
        });
    };
    const save = async () => {
        try {
        const values = await form.validateFields();
        toggleEdit();
        handleSave({
            ...record,
            ...values,
        });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };
    let childNode = children;
    if (editable) {
        childNode = editing ? (
        <Form.Item
            style={{
                margin: 0,
            }}
            name={dataIndex}
            rules={[
            {
                required: true,
                message: `${title} 不能为空!`,
            },
            ]}
        >
            <Input ref={inputRef} showCount maxLength={200} size="small" onPressEnter={save} onBlur={save} />
        </Form.Item>
        ) : (
        <div
            // style={{
            //     paddingRight: 24,
            // }}
            onClick={toggleEdit}
        >
            {children}
        </div>
        );
    }
    return <td {...restProps}>{childNode}</td>;
};
const noPublishTextTip = '不同触发方式的版本不能替换部署，请先取消部署当前版本';
const versionTitleTip = '可以从下方列表中选择任意版本【部署】后开始对外提供服务，包括agent引用，公开到市场，api调用';
const noDelTextTip = '已部署版本不能删除，请先取消部署当前版本';
const replacePublishText = '部署当前版本会替换api调用的版本，请注意输入参数保持一致，是否继续？';
const triggerTypeEnum: any = {
    0: '默认',
    1: 'api',
    2: '定时',
    3: 'webhook'
};
const publishStatusEnum: any = {
    1: '未部署',
    2: '已部署'
};
const VersionManage = (props: any) => {
    const { getCount, getHasPublishVersion, flowType, chainName, isV2, setIsPublish, deployDetail, changeDeploy, publishDetail } = props;
    const router = useRouter();
    const template_id = router.query.id; // chain_id 或者 template_id
    // const deployType =  router.query.type || '';
    const teamId = router.query.teamId;
    const version = isV2 ? '' : 'V3';
    // const from = router.query.from;
    // from 1:上架审核 detail: 各详情页 
    const originObj: any = {
        id: -1,
        version: '',
        generate_time: '',
        input_type: 1,
        version_remark: '',
        user_name: '',
        publish_time: '',
        publish_status: -1,
    };
    const [currentVersion, setCurrentVersion] = useState(originObj);
    const [dataSource, setDataSource] = useState<listItem[]>([]);
    const [count, setCount] = useState(0);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    // 生成版本描述信息
    const [versionRemark, setVersionRemark] = useState('');
    // 列表loading
    const [tableLoading, setTableLoading] = useState(false);
    // 生成版本loading
    const [createLoading, setCreateLoading] = useState(false);
    // table 滚动
    const tblRef:any = useRef(null);
    // 不同版本接口
    const apiVersionMap = isV2 ? {
        add: reqAgentVersionManageAdd,
        del: reqAgentVersionManageDel,
        onlineVersion: reqAgentVersionManageOnlineVersion,
        publish: reqAgentVersionManagePublish,
        updateDesc: reqAgentVersionManageUpdateDesc,
        useSet: reqAgentVersionManageUseSetting
    } : {
        add: reqAgentVersionV3ManageAdd,
        del: reqAgentVersionV3ManageDel,
        onlineVersion: reqAgentVersionV3ManageOnlineVersion,
        publish: reqAgentVersionV3ManagePublish,
        updateDesc: reqAgentVersionV3ManageUpdateDesc,
        useSet: reqAgentVersionV3ManageUseSetting
    }
    // 列表项数据
    const defaultColumns = [
        {
            title: '序号',
            width: 60,
            align: 'center',
            className: operationDetailStyles.versionMangeColumnCom,
            render: (text: any, record: any, index: number) => {
                return (page - 1) * pageSize + index + 1;
            },
        },
        {
            title: '版本号',
            dataIndex: 'version',
            width: 200,
            className: operationDetailStyles.versionMangeColumnCom,
            render: (text: any, record: any) => <a onClick={() => handleVersionSee(record)}>{text}</a>,
        },
        {
            title: '版本信息',
            dataIndex: 'version_remark',
            width: 220,
            editable: true,
            className: operationDetailStyles.versionMangeColumnCom,
            render: (text: any, record: any) => 
                record.publish_status === -1 ? '' : (<Tooltip title={text}>
                    <div className={operationDetailStyles.versionManageDescBox}>
                        <div className={operationDetailStyles.versionManageDesc}>{text}</div> 
                        <span className={operationDetailStyles.versionManageDescImg}><img src={versionManageEditImg.src} alt=""/></span>
                    </div></Tooltip>)
        },
        {
            title: '部署状态',
            dataIndex: 'publish_status',
            width: 80,
            className: operationDetailStyles.versionMangeColumnCom,
            render: (text: any, record: any) => {
                return <div className={operationDetailStyles[deployDetail.status == 1 && deployDetail.version === record.version ? 'publishedBtn' : 'unPublishedBtn']}>{publishStatusEnum[deployDetail.status == 1 && deployDetail.version === record.version ? 2 : 1]}</div>;
            }
        },
        // {
        //     title: '执行方式',
        //     dataIndex: 'input_type',
        //     width: 80,
        //     className: operationDetailStyles.versionMangeColumnCom,
        //     render: (_: any, record: any) => {
        //         return triggerTypeEnum[record.input_type] || (record.input_type === -1 ? '' : record.input_type);
        //     }
        // },
        // {
        //     title: '部署人',
        //     dataIndex: 'user_name',
        //     width: 80,
        //     className: operationDetailStyles.versionMangeColumnCom,
        // },
        {
            title: '生成时间',
            dataIndex: 'generate_time',
            width: 200,
            className: operationDetailStyles.versionMangeColumnCom,
            render: (text: any, record: any) => {
                return text && text.replace('T', ' ') || '';
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            fixed: 'right',
            width: 170,
            className: operationDetailStyles.versionMangeColumnCom,
            render: (_: any, record: any) => {
                return dataSource.length >= 1 ? (
                    <div style={{'width': '100%', 'display': 'flex', 'justifyContent': 'space-between', 'alignItems': 'center'}}>
                    {/* <a onClick={() => handleVersionSee(record)}>查看</a>
                    {record.input_type === currentVersion.input_type ? (record.publish_status === 2 ? <a onClick={() => handleVersionPublish(record)}>取消</a> : 
                    <Popconfirm title={replacePublishText} onConfirm={() => handleVersionPublish(record)}><a>部署</a></Popconfirm>
                    <div style={{cursor: 'pointer'}} onClick={() => handleVersionPublish(record)}><a>部署</a></div>
                    ) :
                        (currentVersion.input_type === -1 ? <a onClick={() => handleVersionPublish(record)}>部署</a> : <Tooltip title={noPublishTextTip}> <a className={operationDetailStyles.versionListLiRightBtnNo}>部署</a></Tooltip>)}
                     {
                     record.publish_status !== 2 ?
                        <Popconfirm title="删除此版本?" onConfirm={() => handleVersionDel(record)}>
                            <Tooltip title={record.publish_status === 2 ? noDelTextTip : ''}><a className={operationDetailStyles[record.publish_status === 2 ? 'versionListLiRightBtnNo' : 'versionListLiRightBtnEdit']}>删除</a></Tooltip> 
                        </Popconfirm>
                       : <Tooltip title={record.publish_status === 2 ? noDelTextTip : ''}><a className={operationDetailStyles[record.publish_status === 2 ? 'versionListLiRightBtnNo' : 'versionListLiRightBtnEdit']}>删除</a></Tooltip> 
                    }
                    <a  onClick={() => flowExportJsonFile(record.template_id, chainName+ '-' + record.version, record.version)}>导出</a>
                    <a  onClick={() => handleVersionUseSetting(record)}>使用此配置</a> */}

                    {
                        deployDetail.status == 1 && deployDetail.version === record.version ? (publishDetail.template_check_status == 1 || publishDetail.template_check_status == 2 ?
                            <Tooltip title={'已公开版本不能取消!'}><a className={operationDetailStyles.versionListLiRightBtnNo}>取消</a></Tooltip> :
                        <a onClick={() => handleVersionPublish(record)}>取消</a>) : <a style={{cursor: 'pointer'}} onClick={() => handleVersionPublish(record)}>部署</a>
                    }
                    {
                     record.publish_status !== 2 ?
                        <Popconfirm title="删除此版本?" onConfirm={() => handleVersionDel(record)}>
                            <Tooltip title={record.publish_status === 2 ? noDelTextTip : ''}><a className={operationDetailStyles[record.publish_status === 2 ? 'versionListLiRightBtnNo' : 'versionListLiRightBtnEdit']}>删除</a></Tooltip> 
                        </Popconfirm>
                       : <Tooltip title={record.publish_status === 2 ? noDelTextTip : ''}><a className={operationDetailStyles[record.publish_status === 2 ? 'versionListLiRightBtnNo' : 'versionListLiRightBtnEdit']}>删除</a></Tooltip> 
                    }
                    <a  onClick={() => handleVersionUseSetting(record)}>使用此配置</a>
                </div>
                ) : null
            }
        },
    ];
    
    // 修改描述信息
    const handleSave = async (row: any) => {
        setTableLoading(true);
        try{
            const res = await apiVersionMap.updateDesc({
                teamid: teamId,
                template_id,
                version: row.version,
                version_remark: row.version_remark
            });
            if (res) {
                // 不显示已发布版本描述，不用请求更新已发布信息
                // if (row.publish_status === 2) {
                //     loadGetCurrentVersionData();
                // }
                // 重新请求数据
                await getVersionListData();
                message.success('版本信息修改成功!')
            }
        }
        catch (error: any) {
            message.error(error.message);
        }
        setTableLoading(false);
    };
    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    const columns:any = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: any) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave,
            }),
        };
    });
    // 更新父组件数据
    useEffect(() => {
        getCount(count);
    }, [count]);
    useEffect(() => {
        getVersionListData();
    }, [page, pageSize]);
    // 获取当前发布的版本详情
    const loadGetCurrentVersionData = async () => {{}
        try{
            const res = await apiVersionMap.onlineVersion({
                teamid: teamId,
                template_id,
            });
            if (res && res.version) {
                setCurrentVersion(res);
            } else {
                setCurrentVersion(originObj);
            }
        }
        catch (error: any) {
            message.error(error.message);
            setCurrentVersion(originObj);
        }
    };
    // 版本部署, 取消
    const handleVersionPublish = async (record: any) => {
        let flag = '';
        // 版本取消
        if (record && record.publish_status === 2 && (record.version === currentVersion.version || currentVersion.version === '')) {
            // flag = '取消';
            flag = 'cancel';
        }
        if (record && record.publish_status === 1 && (record.version !== currentVersion.version && (record.input_type === currentVersion.input_type || currentVersion.version === '' || currentVersion.input_type === -1))) {
            // flag = '部署';
            flag = 'publish';
        }
        if (flag) {
            if (flag === 'cancel' && (publishDetail.template_check_status == 1 || publishDetail.template_check_status == 2)) {
                return;
            }
            setTableLoading(true);

            if (flag === 'cancel') {
                // 先取消部署
                await changeDeploy(0);
            }
            // 已发布但未部署
            console.error(deployDetail)
            if (flag === 'cancel' && deployDetail.status != 1) {
                await changeDeploy(1);
                setTableLoading(false);
                return;
            }
            try{
                const res = await apiVersionMap.publish({
                    teamid: teamId,
                    template_id,
                    version: record.version,
                });
                if (res) {
                    if (flag === 'publish' && deployDetail.status != 1) {
                        await changeDeploy(1);
                    }
                    await loadGetCurrentVersionData();
                    await getVersionListData();
                    // message.success(flag === 'cancel' ? '取消' : '部署' + '成功');
                }
            }
            catch (error: any) {
                // message.error(error.message);
            }
            setTableLoading(false);
        }
    }
    // 使用此配置
    const handleVersionUseSetting = async (val: any) => {
        if (val && val.template_id && val.team_id && val.version) {
            setTableLoading(true);
            try{
                let res = await apiVersionMap.useSet({
                    teamid: val.team_id,
                    template_id: val.template_id,
                    version: val.version,
                });
                if (res) {
                    router.push(`/flowDetailV3?id=${val.template_id}&teamId=${val.team_id}`);
                }
            }
            catch (error: any) {
                message.error(error.message);
            }
            setTableLoading(false);
        } else {
            message.error('参数错误，请刷新重试!');
        }
    }
    // 版本查看
    const handleVersionSee = (val: any) => {
        if (val && val.template_id && val.team_id && val.version) {
            router.push(`/flowDetailV3?id=${val.template_id}&teamId=${val.team_id}&version=${val.version}&flow_type=${flowType}`);
        }
    }
    // 版本删除
    const handleVersionDel = async (record: any) => {
        if (record.publish_status === 2 && record.version === currentVersion.version) {
            message.error('已部署版本不能删除, 请先取消!');
            return;
        }
        setTableLoading(true);
        try{
            const res = await apiVersionMap.del({
                teamid: teamId,
                template_id,
                version: record.version
            });
            if (res) {
                if (dataSource.length === 1) {
                    setPage(page - 1);
                } else {
                    await getVersionListData();
                }
                message.success('版本删除成功!');
            }
        }
        catch (error: any) {
            message.error(error.message);
        }
        setTableLoading(false);
    }

    // 初次进入请求版本列表
    useEffect(()=> {
        if(template_id){
            loadGetCurrentVersionData();
            getVersionListData();
        }
    },[template_id]);
    // 监听currentVersion
    useEffect(()=> {
        // 更新已发布版本标识
        getHasPublishVersion(currentVersion);
    },[currentVersion]);
    // 获取版本列表
    const getVersionListData = async () =>{
        setTableLoading(true);
        try{
            const res = await reqAgentVersionManageList({
                teamid: teamId,
                template_id,
                page: page,
                page_size: pageSize,
            });
            if (res) {
                setCount(res.total);
                setDataSource([...res.list]);

                let isPublish = false;
                res.list.forEach((item: any) => {
                    if (item.publish_status === 2) {
                        isPublish = true;
                    } 
                })
                setIsPublish(isPublish)
            }
        }
        catch (error: any) {
            message.error(error.message);
        }
        setTableLoading(false);
    }
    const onVersionDescChange = (e: any) => {
        setVersionRemark(e.target.value);
    }
    // 创建新版本
    const onCreateNewVersion = async () => {
        if (!versionRemark) {
            message.warning('请输入版本信息!');
            return;
        }
        setCreateLoading(true);
        try{
            let res = await apiVersionMap.add({
                teamid: teamId,
                template_id,
                version_remark: versionRemark
            });
            setCreateLoading(false);
            if (res) {
                message.success('生成版本成功！');
                setVersionRemark('');
                if (page === 1) {
                    await getVersionListData();
                } else {
                    setPage(1);
                }
                if (dataSource.length > 0) {
                    tblRef.current?.scrollTo({ index: 0 });
                }
            }
        }
        catch (error: any) {
            message.error(error.message);
            setVersionRemark('');
            setCreateLoading(false);
        }
    }

  return (
    <div className={`${operationDetailStyles.versionManageBox} ${operationDetail3Styles.versionManageBox}`}>
        <Spin spinning={createLoading} tip="版本生成中...">
            <div className={operationDetailStyles.versionManageCreate}>
                <div className={operationDetailStyles.versionManageCreateTop}>
                    <div className={operationDetailStyles.versionManageCreateTopTitle}>版本信息</div>
                    <div><Button onClick={onCreateNewVersion} className={operationDetailStyles.versionMangeCreateBtn}>生成版本</Button></div>
                </div>
                <div className={operationDetailStyles.versionManageCreateBottom}>
                    <TextArea placeholder='请输入版本信息' showCount maxLength={200} rows={3} onChange={onVersionDescChange} value={versionRemark}/>
                </div>
            </div>
        </Spin>
        <div className={operationDetailStyles.versionManageSort}>
            <div className={operationDetailStyles.versionManageSortLeft}>
                <div className={operationDetailStyles.versionManageCreateTopTitle}>版本管理
                    {/* <Tooltip title={versionTitleTip}><img src={versionManageQuestionCircle.src} alt="" /></Tooltip> */}
                </div>
                <div className={operationDetailStyles.versionManagePublishDetail}>
                    {
                        currentVersion.version ? <>
                            <div>
                                <span className={operationDetailStyles.versionManagePublishDetailKey}>当前已部署版本 : </span>
                                <span className={operationDetailStyles.versionManagePublishDetailValue}>{currentVersion.version}</span>
                            </div>
                            <div><span className={operationDetailStyles.versionManagePublishDetailKey}>部署时间 : </span><span>{currentVersion.publish_time && currentVersion.publish_time.replace('T', ' ') || ''}</span></div>
                        </>
                        :
                        <>
                            <div><span className={operationDetailStyles.versionManagePublishDetailNoValue}>没有已部署版本</span></div>
                        </>
                    }
                
                </div>
            </div>
            <div className={operationDetailStyles.versionManageSortRight}>

            </div>
        </div>
        <div className={operationDetailStyles.versionManageTable}>
            <Table
                ref={tblRef}
                loading={tableLoading}
                rowKey={(record) => record.id}
                components={components}
                bordered
                dataSource={dataSource}
                columns={columns}
                scroll={{
                    y: 430
                }}
                pagination={{
                    current: page,
                    pageSize: pageSize,
                    total: count,
                    showTotal: (total: any) => `共 ${total} 条记录`
                }}
                onChange={(pagination) => {
                    setPage(pagination.current || 1)
                    setPageSize(pagination.pageSize || 20)
                }}
            />
        </div>
    </div>
  );
};
export default VersionManage;