import { Input, Switch, Tooltip, message } from "antd";
import Editor from "@monaco-editor/react";
import operationMCPStyles from './operationMCP.module.scss';
import { useEffect, useState, useMemo } from "react";
import { reqCancelPublishMCP, reqGetMCPDetail, reqPublishMCP } from "@/service/api";
import { CopyOutlined } from "@ant-design/icons";

const { TextArea } = Input;

/**
 * @param type 
 * @returns flow
 */

export default function OperationMCP(props: any) {
  const { type, id, name, description, chainNo, enName, setIsMcpStatus, openMCP, publishService, cancelService, detailService } = props;
  const [operationMPCDetail, setOperationMPCDetail] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [mpcStatus, setMpcStatus] = useState(false);
  const [isSSE, setIsSSE] = useState(false);

  // 发布mpc
  const publishInterface = publishService || reqPublishMCP;
  // 取消mcp发布
  const cancelInterface = cancelService || reqCancelPublishMCP;
  // 获取mcp详情
  const detailInterface = detailService || reqGetMCPDetail;
  // host
  const baseUrl = (window.location.host.includes('test') || window.location.host.includes('dev')) ? 'https://testmcp.agent.qihoo.net/mcp/' : 'https://mcp.agent.qihoo.net/mcp/'

  const copyToClipboard = (text: any) => {
    // 创建一个临时的 textarea 元素
    const textarea = document.createElement("textarea");
    // 设置 textarea 的值为要复制的文本
    textarea.value = text;
    // 将 textarea 元素添加到文档中
    document.body.appendChild(textarea);
    // 选中 textarea 中的文本
    textarea.select();
    // 执行复制命令
    document.execCommand('copy');
    // 移除临时的 textarea 元素
    document.body.removeChild(textarea);
    message.success('复制成功');
  }

  const changeDeploy = async (e: any) => {
    if (e) {
      try {
        await openMCP();
        publishInterface({ server_type: 1, template_type: type, template_id: id, name, description }).then((res: any) => {
          getMcpDetail();
          setOperationMPCDetail({ ...operationMPCDetail, unique_id: res.context.message })
          setMpcStatus(e);
          setIsMcpStatus(e);
        })
      } catch (err: any) {
      }
    } else {
      try {
        const res = await cancelInterface({ unique_id: operationMPCDetail.unique_id })
        setMpcStatus(e);
        setIsMcpStatus(e);
      } catch (err: any) {
      }
    }
  }

  const getMcpDetail = async () => {
    try {
      const res = await detailInterface({ template_type: type, template_id: id })
      if (res?.data) {
        setMpcStatus(true);
        setOperationMPCDetail(res?.data)
      }
    } catch (err: any) {
      setLoading(false)
    }
  }

  useEffect(() => {
    getMcpDetail()
  }, [])

  const script = useMemo(() => {
    return `{
    "mcpServers": {
        "${chainNo}": {
            "name": "${name}",
            "type": "streamableHttp",
            "description": "${description}",
            "baseUrl": "${baseUrl}",
            "headers": {
                "Authorization": "${operationMPCDetail.unique_id}"
            }
        }
    }
}`
  }, [operationMPCDetail])

  return <div className={operationMCPStyles.operationMcpOuter}>
    <div className={operationMCPStyles.mcpStatus}>
      <span className={operationMCPStyles.mcpTitle}>发布为MCP服务</span>
      <Switch onChange={changeDeploy} checked={mpcStatus} />
    </div>

    {mpcStatus && <>
      <div className={operationMCPStyles.operationWrapper}>
        <div className={operationMCPStyles.mcpServer}>
          <div className={operationMCPStyles.configLabel}>服务信息</div>
          <div className={operationMCPStyles.tool}>
            <div className={operationMCPStyles.toolItem}>
              <div className={operationMCPStyles.toolLabel}>工具名称</div>
              <Input value={`flow_${enName}`} onChange={() => { }} />
            </div>
            <div className={operationMCPStyles.toolItem}>
              <div className={operationMCPStyles.toolLabel}>工具描述</div>
              <Input value={description} onChange={() => { }} />
            </div>
          </div>
        </div>

        <div style={{ width: '100%' }}>
          <div className={operationMCPStyles.configLabel}>接口信息</div>

          <div className={operationMCPStyles.interfaceTabs}>
            <div
              className={(isSSE ? operationMCPStyles.tab : operationMCPStyles.tabActive) + ' ' + operationMCPStyles.httpTab}
              onClick={() => { setIsSSE(false) }}
            >Streamable HTTP</div>
            {/* <div 
              className={(isSSE ? operationMCPStyles.tabActive : operationMCPStyles.tab) + ' ' + operationMCPStyles.sseTab}
              onClick={() => {setIsSSE(true)}}
            >SSE</div> */}
          </div>

          <div className={operationMCPStyles.content}>
            <div className={operationMCPStyles.address}>
              <div className={operationMCPStyles.conLabel}>接入地址</div>
              <Input
                width={'100%'}
                suffix={
                  <Tooltip title="copy">
                    <CopyOutlined style={{ color: 'rgba(59, 59, 59, 0.45)' }} onClick={() => copyToClipboard(baseUrl)} />
                  </Tooltip>
                }
                value={baseUrl}
                onChange={() => { }}
              />
            </div>
            <div className={operationMCPStyles.script}>
              <div className={operationMCPStyles.conLabel}>接入脚本</div>
              <div className={operationMCPStyles.copyIcon}>
                <Tooltip title="copy">
                  <CopyOutlined style={{ color: 'rgba(59, 59, 59, 0.45)' }} onClick={() => copyToClipboard(script)} />
                </Tooltip>
              </div>
              <TextArea
                rows={13}
                value={script}
                onChange={() => { }}
              />
            </div>
          </div>
        </div>
      </div>
    </>}
  </div>
}