.operationMcpOuter {
    background: #FFFFFF;

    .mcpStatus {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #E1E7ED;

        .mcpTitle {
            font-weight: 600;
            margin-right: 40px;
            font-size: 14px;
        }
    }

    .operationWrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;

        .configLabel {
            font-weight: 600;
            margin-right: 40px;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .mcpServer {
            width: 100%;

            .tool {
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-bottom: 32px;

                .toolItem {
                    width: calc(50% - 16px);
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    .toolLabel {
                        min-width: 64px;
                        color: #657083;
                        font-size: 14px;
                        line-height: 22px;
                        text-align: start;
                    }
                }

                .toolItem:first-child {
                    margin-right: 32px; 
                }
            }
        }
        
        .interfaceTabs {
            display: flex;
            flex-direction: row;
            align-items: center;

            .tabActive {
                color: #006BFF;
                background: #F7F9FA;
                border-radius: 8px 8px 0 0;
                font-weight: 600;
                cursor: pointer;
            }

            .tab {
                color: #657083;
                font-size: 14px;
                line-height: 22px;
                font-weight: 400;
                cursor: pointer;
            }

            .sseTab {
                width: 120px;
                text-align: center;
                font-size: 14px;
                line-height: 22px;
                padding: 2px 0;
            }

            .httpTab {
                padding: 2px 16px;
                font-size: 14px;
                line-height: 22px;
            }
        }

        .content {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            background: #F7F9FA;
            width: 100%;
            padding: 16px;
            border-radius: 0 0 8px 8px;

            .conLabel {
                color: #1D2531;
                font-size: 14px;
                line-height: 22px;
                font-weight: 600;
                padding: 5px 8px 5px 0;
            }

            .address, .script {
                width: 100%;
            }

            .script {
                margin-top: 28px;
                position: relative;

                .copyIcon {
                    position: absolute;
                    right: 10px;
                    top: 36px;
                    z-index: 9;
                }
            }
        } 
    }
}