
  
  import OperationDetail<PERSON>ommon from "./OperationDetailCommon";
  import Router,{ useRouter } from 'next/router'
  import { useEffect } from "react";
  
  interface Props {
      base_url: string;
      app_id: string;
      app_secret: string;
      template_id: number;
      description: string;
      input_keys: any[];
  }


  
  const AgentDetail = (props: Props) => {

    const router = useRouter();
    const chainId = router.query.id; // chain_id 或者 template_id
    const version = router.query.version; //version 智能体版本号
  
    const pyCodeForTest = `
      import requests

      url = "${window.location.origin}/api/v2/openapi/agent_detail?agent_id=${chainId}&version=${version}"

      payload = {}
      headers = {
        'Authorization': 'Bearer your_token'

      }
      response = requests.request("GET", url, headers=headers, data=payload)

      print(response.text)
        `;



  
    const curlCodeForTest = `
    curl --location '${window.location.origin}/api/v2/openapi/agent_detail?agent_id=${chainId}&version=${version}'
    --header 'Authorization: Bearer your_token' 

    `;
  
    const jsCodeForTest = `
      const myHeaders = new Headers();
        myHeaders.append("Authorization", "Bearer your_token");

        const requestOptions = {
        method: "GET",
        headers: myHeaders,
        redirect: "follow"
        };

        fetch("${window.location.origin}/api/v2/openapi/agent_detail?agent_id=${chainId}&version=${version}", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
      `;
    
  
    const agentData = [
      {
        title: '\u00A0\u00A0获取Agent开场信息',
        func: 'get',
        url: '/api/v2/openapi/agent_detail',
        subTitles: '获取Agent开场信息',
        req: [
            {
                name: 'agent_id',
                title: 'agent_id',
                key:'0-0',
                location: 'query',
                type: 'integer',
                required: true,
                desc: 'agent_id，int',
            },
            {
                name: 'version',
                title: 'version',
                key:'0-1',
                location: 'query',
                type: 'string',
                required: true,
                desc: '智能体发布版本号',
            },
            {
                name: 'Authorization',
                title: 'Authorization',
                key:'1-0',
                location: 'header',
                type: 'string',
                required: true,
                desc: '服务标识，"Bearer "与上述获取token接口获取的access_token拼接而成',
            },

        ],
        reqHeight: "60px",
reqCode: `${window.location.origin}/api/v2/openapi/agent_detail?agent_id=${chainId}&version=${version}`,
    res: [
       {
        title: (
            <div>
                <span style={{marginRight: '12px'}}>data</span>
                <span style={{marginRight: '12px'}}>object</span>
                <span style={{marginRight: '16px'}}>必填</span>
                <div></div>
            </div>
        ),
        type: 'object',
        desc:'',
        key: '0-0',
        required: true,
        children: [
            {
                title: (
                    <div>
                        <span style={{marginRight: '12px'}}>message</span>
                        <span style={{marginRight: '12px'}}>object</span>
                        <span style={{marginRight: '16px'}}>必填</span>
                        <div style={{marginRight: '16px'}}></div>
                    </div>
                ),
                type: 'object',
                desc: '',
                key: '0-0-0',
                required: true,
            }
        ]
       },
    ],
    resHeight: "220px",
    resCode: `{
    "context": {
        "message": "OK",
        "code": 0,
        "request_id": "",
        "timestamp": 1725345581
    },
    "data": {
        "id": 962,
        "name": "ppt3",
        "description": "ppt",
        "icon": "https://p0.ssl.qhimg.com/d/inn/e1d63139cf30/agentIcon8.png",
        "greeting_message": "欢迎来到PPT3的世界！我是您的专属智能助手，随时准备帮助您掌握新角色的每一个细节。让我们一起探索未知的技能，揭开这个角色的神秘面纱。请随时向我提问，无论是技能解析还是背景故事，我将竭诚为您提供深入浅出的解答。准备好开始了吗？",
        "greeting_question": [
            "这个ppt有多少页？",
            "新角色的技能有哪些？",
            "新角色的描述是什么？"
        ],
        "version": ${version}
    }
}`,
    },
    ]

      let pyCodeForTextInput = pyCodeForTest ;
      
      let curlCodeForTextInput = curlCodeForTest;
      
      let jsCodeForTextInput = jsCodeForTest;
  
    return (
      <OperationDetailCommon
        pyCode={pyCodeForTextInput}
        jsCode={jsCodeForTextInput}
        curlCode={curlCodeForTextInput}
        detailData={agentData}
        isAgentDetailTitle={true}
        isAgentTitle={false}
      />
    );
  };
  
  export default AgentDetail;
  