/*
 * @Author: yh
 * @Date: 2025-06-13 17:19:27
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-14 11:06:33
 * @FilePath: \prompt-web\src\components\operation\operationMarket\OperationMarket.tsx
 */
import { Button, Popconfirm, Radio, Tooltip, message } from 'antd';
import operationDetailStyles from '@/styles/OperationDetail.module.scss';
import questionCircle from '@/images/questionCircle.svg';
import checkCircle from '@/images/checkCircle.png'

export default function OperationMarket(props: any) {
  const {publishDetail, onChangeAuth, tagList, checkedTagList, onOpen, deployType , authType, isAuth, onClickTag, onClickOpen  } = props;

  return <div className={operationDetailStyles.publicContent}>
              <div className={operationDetailStyles.publicItem}>
                  <div className={operationDetailStyles.publicLabel}><span className={operationDetailStyles.publicLabelTitle}>标签选择：</span></div>
                  <div className={operationDetailStyles.tagList}>
                      {([-1, 3, 4].includes(publishDetail.template_check_status) ? tagList : tagList.filter((tag: any) => {
                          return checkedTagList.includes(tag.value)
                      }))?.map((tag, idx) => {
                          return <span key={idx} className={operationDetailStyles.tagItem + ' ' + (checkedTagList.includes(tag.value) ? operationDetailStyles.tagCheckedItem : '')} onClick={() => onClickTag(tag)}>
                              {checkedTagList.includes(tag.value) ? <img src={checkCircle.src} width='16' /> : ''}
                              {tag.label}
                          </span>
                      })}
                  </div>
              </div>
              {deployType === 'api' && (authType === 2 || authType === 4) && <div className={operationDetailStyles.publicItem}>
                  <div className={operationDetailStyles.publicLabel}>
                      <div className={operationDetailStyles.publicLabelTitle}>
                          第三方授权
                          <Tooltip
                              placement="topLeft"
                              title="第三方使用时，是否需要申请独立的key"
                          >
                              <img src={questionCircle.src} alt="注意事项" />
                          </Tooltip>
                      </div>
                  </div>
                  <div className={operationDetailStyles.tagList}>
                      <Radio.Group
                          value={isAuth}
                          disabled={publishDetail.template_check_status === 1 || publishDetail.template_check_status === 2}
                          onChange={(e) => onChangeAuth(e)}
                      >
                          <Radio value={1}>需要</Radio>
                          <Radio value={2}>不需要</Radio>
                      </Radio.Group>
                  </div>
              </div>}
              <div className={operationDetailStyles.publicItem + ' ' + operationDetailStyles.publishStatusItem}>
                  <div className={operationDetailStyles.publicLabel}>
                      <span className={operationDetailStyles.publicLabelTitle}>公开状态</span>
                      <div className={operationDetailStyles.publishStatus + ' nomralFont'}>
  
                          {[-1, 3, 4].includes(publishDetail.template_check_status) && <><span className={operationDetailStyles.circleIcon}></span>未公开</>}
                          {publishDetail.template_check_status === 1 && <><span className={operationDetailStyles.circleIcon + ' ' + operationDetailStyles.reviewCircleIcon}></span>审核中</>}
                          {publishDetail.template_check_status === 2 && <><span className={operationDetailStyles.circleIcon + ' ' + operationDetailStyles.publishedCircleIcon}></span>已公开</>}
                      </div>
                      <div className={operationDetailStyles.publishStatusTip}>上架内容需要符合国家法律和平台规则，如有违反，平台有权下架并终止合作，由此产生的赔偿由上架方负责</div>
                  </div>
  
                  {deployType !== 'agent' && <>
                      {[-1, 3, 4].includes(publishDetail.template_check_status) && deployType === 'agent' ? <Popconfirm
                          placement="topRight"
                          title={<><div>当前智能体公开后，其中的包含的知识</div><div>库会被其他用户使用，是否继续公开?</div></>}
                          description=""
                          onConfirm={() => onOpen()}
                          onCancel={() => {
  
                          }}
                          okText="继续公开"
                          cancelText="取消"
                      >
                          <Button className='primaryButton' type='primary' style={{ marginTop: '-5px' }}>公开到市场</Button>
                      </Popconfirm> : <Button className='primaryButton' type='primary' style={{ marginTop: '-5px' }} onClick={() => onClickOpen()}>{[-1, 3, 4].includes(publishDetail.template_check_status) ? '公开到市场' : (publishDetail.template_check_status === 1 ? "撤销审核" : '取消公开')}</Button>}
                  </>}
  
              </div>
          </div>
}