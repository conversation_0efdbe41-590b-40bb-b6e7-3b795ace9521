import { useEffect, useMemo, useRef, useState } from 'react';
import { message, Tooltip, Input, Button, Tabs, Spin } from 'antd';
import Router, { useRouter } from 'next/router'
import type { MenuProps } from 'antd';
import VersionManage from '@/components/operation/versionManage';

import { reqUserInfo } from '@/service/common'
import { reqDeploy, reqCancelDeploy, reqReDeploy, reqDeployDetail, reqDeployEnable, reqDeployDisable, reqFlowPublic } from '@/service/flow3.0';
import { reqFlowPublishDetail, reqPublishFlow } from '@/service/flow3.0';
import { reqCancelPublishAgent, } from '@/service/agent';

import { reqPublicPrompt, reqCancelSubmitPublicPrompt, reqCancelPublicPrompt } from '@/service/common'

import operationListStyles from '@/styles/OperationList.module.scss'
import operationDetailStyles from '@/styles/OperationDetail.module.scss'
import operationDetail3Styles from '@/styles/OperationDetail3.module.scss'
import styles from '@/styles/Common.module.scss'

import leftBtn from '@/images/common/leftBtn.svg'
import image from '@/images/common/image.svg'
import success from '@/images/success.svg'
import checkedIcon from "@/images/checked.png";

import OperationMarket from './operationMarket/OperationMarket';
import OperationApi from './operationApi/OperationApi';
import OperationApp from './operationWebApp/OperationApp';
import OperationWeb from './operationWebApp/OperationWeb';
import OperationMCP from './operationMCP/OperationMCP';
import OperationMarketNew from './operationMarketNew/OperationMarketNew';
// 包括1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent
const deployTypeObj: any = {
  text: 1,
  image: 2,
  flow: 3,
  knowledge: 4,
}

const zhongwenReg = /[\u4e00-\u9fa5]/g;

export default function OperationDetail(props: any) {
  const { backFuc } = props;

  const router = useRouter();
  const chainId = router.query.id; // chain_id 或者 template_id
  const deployType = router.query.type || ''; // api发布不在这个页面了
  const teamId = router.query.teamId;
  const from = router.query.from;
  // from 1:上架审核 detail: 各详情页 

  const [flowType, setFlowType] = useState(1);
  const [flowSysVar, setFlowSysVar] = useState({
    sys_uid: '',
    sys_source: '',
  });
  const [tagList, setTagList] = useState(new Array());;
  const [checkedTagList, setCheckedTagList] = useState(new Array());
  const [activeTab, setActiveTab] = useState('market');

  // 正常模式 1-有序SSE 2-无序SSE 3-同步 4-轮询
  // 交互模式 1-有序SSE 2-轮询
  const [curMode, setCurMode] = useState(1);

  const [publishId, setPublishId] = useState(0) // 发布id 也是主键id 模板id
  const [deployId, setDeployId] = useState(0) // 部署id

  const [initLoading, setInitLoading] = useState(false)
  const [isAuth, setIsAuth] = useState(2);

  const [publishLoading, setPublishLoading] = useState(false);
  const [flowApiType, setFlowApiType] = useState(1)
  const [listCount, setListCount] = useState(0);
  const [publishVersion, setPublishVersion] = useState('');
  // llmops 平台
  const [isFromLlmopsBoard, setIsFromLlmopsBoard] = useState(false);
  const [isYunPanBoard, setIsYunPanBoard] = useState(false);

  const [sysUid, setSysUid] = useState('');

  const [digitalMan, setDigitalMan] = useState({
    digit_avatar_status: null,
    digit_avatar_url: '',
    digit_avatar_background: '',
  })

  const [chainNo, setChainNo] = useState('');
  const [enName, setEnName] = useState('');
  const [isPublish, setIsPublish] = useState(false); // 是否发布
  const [isMcpStatus, setIsMcpStatus] = useState(false); // 是否开启MCP服务
  const [isWebStatus, setIsWebStatus] = useState(false); // 是否开启Web应用
  const [isAppStatus, setIsAppStatus] = useState(false); // 是否开启APP应用
  const operationMarketRef = useRef(null);

  const [publishDetail, setPublishDetail] = useState({
    // 发布详情
    id: '',
    image: '',
    chain_name: '',
    chain_desc: '',
    // 审核状态，-1:未审核 1:待审核 2:通过 3:拒绝 4:撤销
    template_check_status: -1,
    // template_check_status为1（待审核）或者2（公开）的时候的公开标签列表
    tags: new Array<any>(),
    // 默认为0，全部，1:未发布，2:已发布
    publish_status: 1,
    chain_type: 'text'
  })

  const [deployDetail, setDeployDetail] = useState({
    // 部署详情
    app_id: '',
    app_secret: '',
    // 部署状态 接口报错(前端定义为0): 未部署 1:部署成功 2:取消部署 
    // 未部署需要另外请求
    // 是否部署 0：未部署，1：已部署 2:部署取消
    status: 1,
    url: '',
    input_keys: [],
    version: '', // 部署的版本号
  })

  // 发布渠道是否显示发布
  const [isVisiblePublish, setIsVisiblePublish] = useState(false);

  useEffect(() => {
    const isFromYunPan = localStorage.getItem('prompt_isYunPanBoard') === 'true' || router.query.source === 'yunpanBoard';
    setIsYunPanBoard(isFromYunPan);
  }, []);

  const getPublishDetail = async () => {
    setInitLoading(true)
    let isDeploy = false;
    let deployId = 0;
    if (deployType === 'flow') {
      reqUserInfo({}).then((res) => {
        if (res) {
          setFlowSysVar({
            sys_source: res.src,
            sys_uid: res.src === 'agent' ? res.user_id : res.user_uid,
          })
          setSysUid(res.user_uid)
        }
      })

      setActiveTab('versionManage');
      const res = await reqFlowPublishDetail({
        template_id: chainId
      })
      if (res) {
        setFlowType(res.flow_type);
        setPublishDetail({
          id: res.id,
          image: res.images,
          chain_type: deployType,
          publish_status: res.publish_status,
          chain_name: res.title,
          chain_desc: res.desc,
          template_check_status: res.template_check_status,
          tags: res.tags_relation
        })

        setChainNo(res.chain_no);
        setEnName(res.en_title);
        setDeployDetail({
          ...deployDetail,
          // 这里只能取到未部署 
          status: res.is_deploy
        })
        setPublishId(res.id)
        deployId = res.deployment_id
        setDeployId(deployId)
        setCheckedTagList(res.tags_relation && res.tags_relation.map((t: any) => +t) || [])

      }
      // (业务更新)，api调用是否显示详情和已发布版本有关，不依赖运行版本
      // const detailRes = await reqFlowDetail({
      //     template_id: chainId,
      //     team_id: teamId
      // })
      // const startBlockId = detailRes.flow_config?.block_keys[0];
      // if(startBlockId) {
      //     const startType = detailRes.flow_config.blocks[startBlockId].input_type || 1;
      // }

      isDeploy = res.is_deploy === 1
    }

    if ((deployType === 'text' || deployType === 'flow') && isDeploy) {
      getDeployDetail(deployId);
    }
    setInitLoading(false)
  }

  const getDeployDetail = async (id = 0) => {
    const res = await reqDeployDetail({
      id: id || deployId
    })

    res && setDeployDetail({
      app_id: res.app_id,
      app_secret: res.app_secret,
      status: res.status, // 部署状态 接口报错: 未部署 1:部署成功 2:取消部署 
      url: res.url,
      input_keys: res.input_keys,
      version: res.version
    })

    if (res && res.publish_type) {
      setIsWebStatus(Boolean(res.publish_type.web))
      setIsAppStatus(Boolean(res.publish_type.app))
    }
  }
  useEffect(() => {
    if (chainId) {
      getPublishDetail();
      getTag()
    }
  }, [chainId, publishVersion])

  const getTag = async () => {
    // const res = await reqTag({
    //   tag_type: 1, // 5api类型
    // })

    // const options = res?.map((item: any) => {
    //   return {
    //     value: item.id,
    //     label: <Tooltip title={item.desc}>
    //       <span>{item.name}</span>
    //     </Tooltip>
    //   }
    // })
    // setTagList(options || [])
  }

  const changeDeploy = async (isDeploy: any, showmsgtip = true): Promise<any> => {
    console.log("🚀 ~ changeDeploy ~ showmsgtip:", showmsgtip)
    const resDeployType: any = deployType;
    try {
      if (isDeploy) {
        if (deployDetail.status === 0) {
          const res = await reqDeploy({
            template_id: publishId,
            template_type: deployTypeObj[resDeployType]
          });

          if (res) {
            setDeployId(res.id);
            getDeployDetail(res.id);
            if (showmsgtip) message.success('部署成功');
            Promise.resolve('');
            return res; // ✅ 成功时返回结果
          }
        } else {
          // 重新部署
          const res = await reqReDeploy({
            template_id: publishId,
            id: deployId,
            template_type: deployTypeObj[resDeployType]
          });

          if (res) {
            getDeployDetail();
            if (showmsgtip) message.success('重新部署成功');

            Promise.resolve('');
            return res; // ✅ 成功时返回结果
          }
        }
      } else {
        if (isMcpStatus) {
          message.warning('需先关闭MCP服务');
          return Promise.reject('需先关闭MCP服务'); // ❗️返回 reject，方便后续判断
        }
        // if (isAppStatus) {
        //   message.warning('需先关闭APP服务');
        //   return Promise.reject('需先关闭APP服务'); 
        // }
        if (isWebStatus) {
          message.warning('需先关闭WEB服务');
          return Promise.reject('需先关闭WEB服务');
        }

        try {
          const res = await reqCancelDeploy({
            id: deployId
          });
          if (res?.message) {
            setDeployDetail({
              ...deployDetail,
              status: 2
            });
            if (showmsgtip) message.success('取消成功');
            Promise.resolve('');
            return res; // ✅ 返回取消结果
          }

          return Promise.reject('reqCancelDeploy err');
        } catch (error) {
          return Promise.reject(error);
        }


      }

      return Promise.reject('接口调用失败或未命中条件'); // ❗️所有路径都未返回时补一个兜底
    } catch (err) {
      console.error('changeDeploy error:', err);
      return Promise.reject(err);
    }
  };

  const changeWebApp = async (isDeploy: any, isWeb: boolean): Promise<any> => {
    if (isDeploy) {
      if (!isPublish) {
        message.warning(`未发布版本时，不支持开启${isWeb ? 'Web' : 'App'}服务`);
        return
      }

      if (!(flowApiType === 1)) {
        message.warning('仅支持api执行方式');
        return
      }

      if (!(deployDetail.status === 1)) {
        // message.warning('未开启API时，不支持开启MCP服务');
        const res = await changeDeploy(1);
        if (!res) return
      }

      const resDeployType: any = deployType;
      if (deployDetail.status === 0) {
        const res = await reqDeploy({
          template_id: publishId,
          template_type: deployTypeObj[resDeployType]
        });

        if (res) {
          setDeployId(res.id);
          getDeployDetail(res.id);
        }
      }
      const res = await reqDeployEnable({
        type: isWeb ? 'web' : 'app',
        id: deployId
      });
      if (res && isWeb) {
        setIsWebStatus(true)
      } else if (res && !isWeb) {
        setIsAppStatus(true)
      }
    } else {
      const res = await reqDeployDisable({
        type: isWeb ? 'web' : 'app',
        id: deployId
      });
      if (res && isWeb) {
        setIsWebStatus(false)
      } else if (res && !isWeb) {
        setIsAppStatus(false)
      }
    }
  }

  const onPublishToMarket = async () => {
    if (deployDetail.status != 1) {
      // try {
      //   await changeDeploy(1, false)
      // } catch (error) {
      //   setInitLoading(false);
      //   return
      // }
      message.error('没有部署的技能不能发布到智能体市场。');
      return;
    }
    setInitLoading(true);

    const data = operationMarketRef.current.channelList?.filter(i => i.publish_status == 1 && i.is_publish == 0)?.map(item => {
      const channel_tag_id = item?.selected_tags?.map(i => { return i.id });
      return {
        channel_id: item.id,
        channel_tag_id: channel_tag_id?.length > 0 ? channel_tag_id : [-1]
      }
    })

    try {
      const res = await reqFlowPublic({
        flow_id: chainId,
        channel_infos: data.length > 0 ? data : [-1]
      })
      operationMarketRef.current.getChannel();
      message.success('操作成功');
    } catch (err) {
      setInitLoading(false);
    }
  }


  // 
  const market = {
    key: 'market',
    label: '发布到智能体广场',
    children: <>
      {
        activeTab == 'market' ?
          <OperationMarketNew ref={operationMarketRef} chainId={chainId} key={'market'} setInitLoading={setInitLoading} setIsVisiblePublish={setIsVisiblePublish}/>
          // <OperationMarket
          //   publishDetail={publishDetail}
          //   onChangeAuth={onChangeAuth}
          //   tagList={tagList}
          //   checkedTagList={checkedTagList}
          //   onOpen={onOpen}
          //   deployType={deployType}
          //   authType={2}
          //   isAuth={isAuth}
          //   onClickTag={onClickTag}
          //   onClickOpen={onClickOpen}
          // />
          : ''
      }
    </>
  };

  const modeArr = useMemo(() => {
    return +(flowType || 1) === 1 ? ['有序SSE模式', '无序SSE模式', '同步模式', '轮询模式'] : ['有序SSE模式', '轮询模式'];
  }, [flowType])

  const modeItems: MenuProps['items'] = useMemo(() => {
    const result: MenuProps['items'] = [];

    modeArr.forEach((item: any, index: number) => {
      result.push({
        label: (
          <div className={operationListStyles.modeLabel}>
            {modeArr[index]}
            {++index === curMode && <img src={checkedIcon.src} style={{ marginLeft: 70 }} />}
          </div>
        ),
        key: index
      })
    })

    return result;
  }, [modeArr, curMode])

  const menuProps = {
    items: modeItems,
    onClick: (curItem: any) => {
      setCurMode(+curItem.key);
    }
  }

  const waibu = {
    key: 'waibu',
    label: 'API调用',
    children: <>
      {
        activeTab == 'waibu' ?
          <OperationApi
            deployDetail={deployDetail}
            publishDetail={publishDetail}
            flowApiType={flowApiType}
            changeDeploy={changeDeploy}
            menuProps={menuProps}
            modeArr={modeArr}
            curMode={curMode}
            deployId={deployId}
            flowType={flowType}
            flowSysVar={flowSysVar}
          />
          : ''
      }
    </>,
  };

  // const isOnline = waiWangHosts.includes(hostName);
  // +'&appId='+deployDetail.app_id+'&appSecrect='+deployDetail.app_secret
  // const appUrl = (isOnline ? 'https://app.'+hostName+'/' : ( hostName === 'agent.qihoo.net' ? 'https://app.agent.360.com/' : 'https://app.agent.qihoo.net/')) + 'flowApp?id='+chainId;
  const appUrl = 'https:///bao.360.com/flowBaoApp?id=' + chainId + '&platform=xiaohongshu';

  const app = {
    key: 'app',
    label: 'APP应用',
    children: <>
      {
        activeTab == 'app' ?
          <OperationApp
            deployDetail={deployDetail}
            changeDeploy={changeWebApp}
            chainId={chainId}
            isV2={false}
            activeTab={activeTab}
            status={isAppStatus}
          />
          : ''
      }
    </>
  };

  const web = {
    key: 'web',
    label: 'Web应用',
    children: <>
      {
        activeTab == 'web' ?
          <OperationWeb
            deployDetail={deployDetail}
            changeDeploy={changeWebApp}
            publishId={publishId}
            sysUid={sysUid}
            chainId={chainId}
            isV2={false}
            activeTab={activeTab}
            status={isWebStatus}
          />
          : ''
      }
    </>
  };

  const handleOpenMCP = (): Promise<any> => {
    return new Promise(async (resolve, reject) => {
      if (!isPublish) {
        message.warning('未发布版本时，不支持开启MCP服务');
        throw new Error("");
      }

      if (!(flowApiType === 1)) {
        message.warning('仅支持api执行方式');
        throw new Error("仅支持api执行方式");
      }

      if (!(deployDetail.status === 1)) {
        // message.warning('未开启API时，不支持开启MCP服务');
        await changeDeploy(1);
      }

      resolve({});
    })
  }


  const mcp = {
    key: 'mcp',
    label: 'MCP服务',
    children: <OperationMCP
      type='flow'
      id={chainId}
      name={publishDetail.chain_name}
      description={publishDetail.chain_desc}
      openMCP={handleOpenMCP}
      chainNo={chainNo}
      enName={enName}
      setIsMcpStatus={setIsMcpStatus}
    // publishService
    // cancelService
    // detailService
    />
  };

  const versionManage = {
    key: 'versionManage',
    // label: '版本管理' + (listCount ? ` (${listCount})` : ''),
    label: '版本管理',
    children: <>
      {
        activeTab == 'versionManage' ?


          <VersionManage
            flowType={flowType}
            chainName={publishDetail.chain_name}
            getCount={(num: any) => setListCount(num)}
            getHasPublishVersion={(curVersionParams: any) => {
              setPublishVersion(curVersionParams.version);
              setFlowApiType(curVersionParams.input_type || 1);
            }}
            isV2={false}
            setIsPublish={setIsPublish}
            deployDetail={deployDetail}
            publishDetail={publishDetail}
            changeDeploy={async(value: any) => {
              // if (deployDetail.status != 1) {
                try {
                  await changeDeploy(value, false)
                } catch (error) {
                  setInitLoading(false);
                  return
                }
              // }
            }}
          />
          : ''
      }
    </>
  };

  const unZhongwenTitleLength = publishDetail.chain_name?.replaceAll(zhongwenReg, '').length;
  const zhongwenTitleLength = publishDetail.chain_name?.length - unZhongwenTitleLength;

  // 普通 显示：mcp ，  交互显示：web和app
  const getTabsResult = () => {
    let itemsResult: any = [];
    if (deployType === 'flow' || deployType === 'text') {
      itemsResult = [market, waibu];
      if (deployType === 'flow') {
        // APP应用测试环境先隐藏；上线后是新老交互flow需要兼容，老flow还有app应用，新交互flow是web应用
        // itemsResult = flowType === 2 ? [versionManage, market, waibu, app, web] : [versionManage, market, waibu, mcp];
        itemsResult = flowType === 2 ? [versionManage, market, waibu, web] : [versionManage, market]; //  waibu, mcp];
      }
      isFromLlmopsBoard && (itemsResult = [waibu]);
    } else {
      itemsResult = [market];
      isFromLlmopsBoard && (itemsResult = []);
    }
    return isYunPanBoard ? [versionManage] : itemsResult;
  }
  return (
    <>
      <div className={styles.commonContent}>

        <Spin spinning={initLoading} className='commonSpin'>
          <div className={styles.top} style={{ borderBottom: '1px solid #EEEFF2' }}>
            <div className={styles.title}>
              <img src={leftBtn.src} className={styles.leftBtn} onClick={() => {
                if (backFuc) {
                  backFuc()
                } else {
                  if (from !== undefined && from === "1") {
                    Router.push('/background/examineList')
                  } else if (from === 'detail') {
                    const resDeployType: any = deployType;
                    const typeObj: any = {
                      'text': 'textPrompt',
                      'image': 'imagePrompt',
                      'flow': 'flow',
                      // 'api': 'api',
                      // 'agent': 'agent',
                    }
                    if (resDeployType === 'flow') {
                      Router.push('flowDetailV3?id=' + chainId + '&teamId=' + teamId + '&flow_type=' + flowType)
                    } else if (flowType == 2) {
                      Router.push('flowDetailV3?id=' + chainId + '&teamId=' + teamId + '&flow_type=' + flowType)
                    } else {
                      Router.push(typeObj[resDeployType] + 'Detail?id=' + chainId + '&teamId=' + teamId + '&flow_type=' + flowType)
                    }
                  } else {
                    Router.push('/projectList?teamId=' + teamId + '&tab=1')
                  }
                }
              }} />

              <div className={styles.imageWrapper} style={{ display: 'flex', alignContent: 'center', justifyContent: 'center', width: '40px', height: '40px', background: digitalMan.digit_avatar_background }}>
                <img src={publishDetail.image || image.src} height='100%' />
              </div>
              <div>
                {/* <div style={{ 'display': 'flex', "alignItems": 'center' }}>
                  <Input
                    value={publishDetail.chain_name}
                    className={styles.nameInput + ' transparentInput'}
                    style={{ width: unZhongwenTitleLength * 12 + zhongwenTitleLength * 20, maxWidth: 500, display: 'inline-block' }}
                  />
                  {publishDetail.publish_status === 2 && <span className={operationDetailStyles.publishedBtn}>已发布</span>}
                </div>
                <Input value={publishDetail.chain_desc} placeholder="写下你的描述......" className={styles.descInput + ' transparentInput'} maxLength={500} /> */}
                <div style={{ 'display': 'flex', "alignItems": 'center' }}>
                  <div
                    className={styles.nameWrapper}
                    style={{ maxWidth: 800, display: 'inline-block' }}
                  >{publishDetail.chain_name}</div>
                  {publishDetail.publish_status === 2 && <span className={operationDetailStyles.publishedBtn}>已发布</span>}
                </div>
                <div className={styles.descWrapper}>{publishDetail.chain_desc || ''}</div>
              </div>
            </div>
            <div className={styles.buttonWrapper}>
              {
                activeTab == 'market' ?
                  <Button type='primary' className='primaryButton' onClick={onPublishToMarket} disabled={isVisiblePublish}>
                    发布
                  </Button>
                  : ''
              }
            </div>
          </div>

          <div className={`${operationDetailStyles.contentWrapper} ${operationDetail3Styles.contentWrapper}`}>
            {publishDetail.publish_status === 2 && deployType !== 'flow' && <div className={operationDetailStyles.messageWrapper + ' normalFont'}>
              <img src={success.src} />
              <span className={operationDetailStyles.successTips}>发布成功！</span>
              <span>发布成功后，可以公开到市场，也可以通过其他渠道使用。同时文件将不可被编辑，如需编辑，请取消发布后编辑。</span>
            </div>}

            {deployType !== 'flow' && <div className={operationDetailStyles.contentTitle}>使用渠道</div>}
            <Tabs
              className='common_card_tabs'
              defaultActiveKey="version"
              items={getTabsResult()}
              activeKey={activeTab}
              onChange={(e) => {
                setActiveTab(e)
              }}
            >
            </Tabs>
          </div>
        </Spin>
      </div>
    </>
  )
}