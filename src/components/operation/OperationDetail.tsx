import { JSXElementConstructor, ReactElement, ReactFragment, ReactPortal, use, useEffect, useMemo, useState } from 'react';
import { message, Tooltip, Input, Button, Tabs, Switch, Spin, Popconfirm, Checkbox, Modal, Dropdown, Space, Radio, InputNumber, Select } from 'antd';
import Router, { useRouter } from 'next/router'
import type { MenuProps } from 'antd';
// import DeployModal from '@/components/common/DeployModal'
import VersionManage from '@/components/operation/versionManage';

import { reqPublishPlugin, reqCancelPublishPlugin, reqGetMCPDetail, reqPublishMCP, reqCancelPublishMCP } from '@/service/api'
import { reqDeploy, reqCancelDeploy, reqReDeploy, reqDeployDetail, reqUserInfo } from '@/service/common'
import { reqFlowPublishDetail, reqPublishFlow } from '@/service/flow3.0';
import { reqCancelPublishAgent, } from '@/service/agent';

import { reqPublicPrompt, reqCancelSubmitPublicPrompt, reqCancelPublicPrompt } from '@/service/common'
import { copyParamValue } from '@/utils/common'

import operationListStyles from '@/styles/OperationList.module.scss'
import operationDetailStyles from '@/styles/OperationDetail.module.scss'
import styles from '@/styles/Common.module.scss'
import flowAppStyles from '@/styles/flow/FlowApp.module.scss'

import deployDetailSrc from '@/images/deployDetailSrc.svg'
import leftBtn from '@/images/common/leftBtn.svg'
import questionCircle from '@/images/questionCircle.svg';
import image from '@/images/common/image.svg'
import checkCircle from '@/images/checkCircle.png'
import success from '@/images/success.svg'
import checkedIcon from "@/images/checked.png";

import { waiWangHosts } from '@/config/commonConfig';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import OperationMarket from './operationMarket/OperationMarket';
import OperationApi from './operationApi/OperationApi';
import OperationApp from './operationWebApp/OperationApp';
import OperationMCP from './operationMCP/OperationMCP';
// 包括1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent
const deployTypeObj: any = {
    text: 1,
    image: 2,
    flow: 3,
    knowledge: 4,
    api: 5,
    agent: 6
}

const zhongwenReg = /[\u4e00-\u9fa5]/g;

export default function OperationDetail(props: any) {
    const { backFuc } = props;

    const router = useRouter();
    const chainId = router.query.id; // chain_id 或者 template_id
    const deployType = router.query.type || '';
    const teamId = router.query.teamId;
    const from = router.query.from;
    // from 1:上架审核 detail: 各详情页 

    const [flowType, setFlowType] = useState(1);
    const [flowSysVar, setFlowSysVar] = useState({
        sys_uid: '',
        sys_source: '',
    });
    const [tagList, setTagList] = useState(new Array());;
    const [checkedTagList, setCheckedTagList] = useState(new Array());
    const [activeTab, setActiveTab] = useState('market');
    const [chainNo, setChainNo] = useState('')
    const [enName, setEnName] = useState('')

    // 正常模式 1-有序SSE 2-无序SSE 3-同步 4-轮询
    // 交互模式 1-有序SSE 2-轮询
    const [curMode, setCurMode] = useState(1);

    const [publishId, setPublishId] = useState(0) // 发布id 也是主键id 模板id
    const [deployId, setDeployId] = useState(0) // 部署id

    const [initLoading, setInitLoading] = useState(false)
    const [apiHost, setApiHost] = useState('');
    const [isAuth, setIsAuth] = useState(2);
    const [authType, setAuthType] = useState(2);
    const [appPublished, setAppPublished] = useState(false)

    const [publishLoading, setPublishLoading] = useState(false);

    const [flowApiType, setFlowApiType] = useState(1)
    const [listCount, setListCount] = useState(0);
    const [publishVersion, setPublishVersion] = useState('');
    const [isPublish, setIsPublish] = useState(false); // 是否发布
    const [isMcpStatus, setIsMcpStatus] = useState(false); // 是否开启MCP服务

    // llmops 平台
    const [isFromLlmopsBoard, setIsFromLlmopsBoard] = useState(false);
    const [isYunPanBoard, setIsYunPanBoard] = useState(false);

    const [sysUid, setSysUid] = useState('');

    const [digitalMan, setDigitalMan] = useState({
        digit_avatar_status: null,
        digit_avatar_url: '',
        digit_avatar_background: '',
    })

    const [publishDetail, setPublishDetail] = useState({
        // 发布详情
        id: '',
        image: '',
        chain_name: '',
        chain_desc: '',
        // 审核状态，-1:未审核 1:待审核 2:通过 3:拒绝 4:撤销
        template_check_status: -1,
        // template_check_status为1（待审核）或者2（公开）的时候的公开标签列表
        tags: new Array<any>(),
        // 默认为0，全部，1:未发布，2:已发布
        publish_status: 1,
        chain_type: 'text'
    })

    const [deployDetail, setDeployDetail] = useState({
        // 部署详情
        app_id: '',
        app_secret: '',
        // 部署状态 接口报错(前端定义为0): 未部署 1:部署成功 2:取消部署 
        // 未部署需要另外请求
        // 是否部署 0：未部署，1：已部署 2:部署取消
        status: 1,
        url: '',
        input_keys: []
    })

    useEffect(() => {
        const isFromYunPan = localStorage.getItem('prompt_isYunPanBoard') === 'true' || router.query.source === 'yunpanBoard';
        setIsYunPanBoard(isFromYunPan);
    }, []);

    const getPublishDetail = async () => {
        setInitLoading(true)
        let res;
        let isDeploy = false;
        let deployId = 0;
        if (deployType === 'flow') {
            reqUserInfo({}).then((res) => {
                if (res) {
                    setFlowSysVar({
                        sys_source: res.src,
                        sys_uid: res.src === 'agent' ? res.user_id : res.user_uid,
                    })
                    setSysUid(res.user_uid)
                }
            })

            setActiveTab('versionManage');
            const res = await reqFlowPublishDetail({
                template_id: chainId
            })
            if (res) {
                setFlowType(res.flow_type);
                setPublishDetail({
                    id: res.id,
                    image: res.images,
                    chain_type: deployType,
                    publish_status: res.publish_status,
                    chain_name: res.title,
                    chain_desc: res.desc,
                    template_check_status: res.template_check_status,
                    tags: res.tags_relation
                })
                setChainNo(res.chain_no)
                setEnName(res.en_title)
                setDeployDetail({
                    ...deployDetail,
                    // 这里只能取到未部署 
                    status: res.is_deploy
                })
                setPublishId(res.id)
                deployId = res.deployment_id
                setDeployId(deployId)
                setCheckedTagList(res.tags_relation && res.tags_relation.map((t: any) => +t) || [])

            }
            // (业务更新)，api调用是否显示详情和已发布版本有关，不依赖运行版本
            // const detailRes = await reqFlowDetail({
            //     template_id: chainId,
            //     team_id: teamId
            // })
            // const startBlockId = detailRes.flow_config?.block_keys[0];
            // if(startBlockId) {
            //     const startType = detailRes.flow_config.blocks[startBlockId].input_type || 1;
            // }

            isDeploy = res.is_deploy === 1
        } 

        if ((deployType === 'text' || deployType === 'flow') && isDeploy) {
            getDeployDetail(deployId);
        }
        setInitLoading(false)
    }

    const getDeployDetail = async (id = 0) => {
        const res = await reqDeployDetail({
            id: id || deployId
        })

        res && setDeployDetail({
            app_id: res.app_id,
            app_secret: res.app_secret,
            status: res.status, // 部署状态 接口报错: 未部署 1:部署成功 2:取消部署 
            url: res.url,
            input_keys: res.input_keys
        })

    }
    useEffect(() => {
        if (chainId) {
            getPublishDetail();
            getTag()
        }
    }, [chainId, publishVersion])

    const getTag = async () => {
        // const res = await reqTag({
        //     tag_type: deployType == 'api' ? 5 : 1
        // })

        // const options = res?.map((item: any) => {
        //     return {
        //         value: item.id,
        //         label: <Tooltip title={item.desc}>
        //             <span>{item.name}</span>
        //         </Tooltip>
        //     }
        // })
        // setTagList(options || [])
    }

    // const changeDeploy = async (isDeploy: any) => {
    //     const resDeployType: any = deployType;
    //     if (isDeploy) {
    //         if (deployDetail.status === 0) {
    //             const res = await reqDeploy({
    //                 template_id: publishId,
    //                 template_type: deployTypeObj[resDeployType]
    //             })
    //             if (res) {
    //                 setDeployId(res.id)
    //                 getDeployDetail(res.id);

    //                 message.success('部署成功')
    //             }
    //         } else {
    //             // 重新部署
    //             const res = await reqReDeploy({
    //                 template_id: publishId,
    //                 id: deployId,
    //                 template_type: deployTypeObj[resDeployType]
    //             })
    //             if (res) {
    //                 getDeployDetail();
    //                 message.success('重新部署成功')
    //             }
    //         }
    //     } else {
    //         if (isMcpStatus) {
    //             message.warning('需先关闭MCP服务')
    //             return;
    //         }

    //         const res = await reqCancelDeploy({
    //             id: deployId
    //         })
    //         if (res.message) {
    //             setDeployDetail({
    //                 ...deployDetail,
    //                 status: 2
    //             })
    //             message.success('取消成功')
    //         }
    //     }
    // }
    const changeDeploy = async (isDeploy: any): Promise<any> => {
        const resDeployType: any = deployType;
        try {
            if (isDeploy) {
                if (deployDetail.status === 0) {
                    const res = await reqDeploy({
                        template_id: publishId,
                        template_type: deployTypeObj[resDeployType]
                    });

                    if (res) {
                        setDeployId(res.id);
                        getDeployDetail(res.id);
                        message.success('部署成功');
                        Promise.resolve('');
                        return res; // ✅ 成功时返回结果
                    }
                } else {
                    // 重新部署
                    const res = await reqReDeploy({
                        template_id: publishId,
                        id: deployId,
                        template_type: deployTypeObj[resDeployType]
                    });

                    if (res) {
                        getDeployDetail();
                        message.success('重新部署成功');
                        Promise.resolve('');
                        return res; // ✅ 成功时返回结果
                    }
                }
            } else {
                if (isMcpStatus) {
                    message.warning('需先关闭MCP服务');
                    return Promise.reject('需先关闭MCP服务'); // ❗️返回 reject，方便后续判断
                }

                const res = await reqCancelDeploy({
                    id: deployId
                });

                if (res.message) {
                    setDeployDetail({
                        ...deployDetail,
                        status: 2
                    });
                    message.success('取消成功');
                    Promise.resolve('');
                    return res; // ✅ 返回取消结果
                }
            }

            return Promise.reject('接口调用失败或未命中条件'); // ❗️所有路径都未返回时补一个兜底
        } catch (err) {
            console.error('changeDeploy error:', err);
            return Promise.reject(err);
        }
    };

    const publish = async () => {
        setPublishLoading(true);

        if (deployType === 'api') {
            const res = await reqPublishPlugin('' + chainId);
            if (res && (res.api_id == chainId)) {
                setPublishDetail({
                    ...publishDetail,
                    publish_status: 2
                })
                message.success('发布成功')
            }
        } else if (deployType === 'flow') {
            const res = await reqPublishFlow({
                template_id: chainId
            })
            if (res && res.message) {
                setPublishDetail({
                    ...publishDetail,
                    publish_status: 2
                })
                message.success('发布成功')
            }
        }

        setTimeout(() => {
            setPublishLoading(false);
        }, 500);
    }
    const cancelPublish = async () => {
        setPublishLoading(true);

        if (deployType === 'api') {
            const res = await reqCancelPublishPlugin('' + chainId)
            if (res && (res.api_id == chainId)) {
                setPublishDetail({
                    ...publishDetail,
                    publish_status: 1
                })
                message.success('取消发布成功')
            }
        } else if (deployType === 'flow') {
            const res = await reqPublishFlow({
                template_id: chainId
            })

            if (res && res.message === 'ok') {
                setPublishDetail({
                    ...publishDetail,
                    publish_status: 1
                })
                message.success('取消发布成功')
            }
        } else if (deployType === 'agent') {
            const res = await reqCancelPublishAgent({
                agent_id: chainId
            })

            if (res && res.result === '取消发布成功') {
                setPublishDetail({
                    ...publishDetail,
                    publish_status: 1
                })
                message.success('取消发布成功')
            }
        }

        setTimeout(() => {
            setPublishLoading(false);
        }, 500);
    }

    // 标签选择
    const onClickTag = (tag: any) => {
        if (checkedTagList.includes(tag.value) && [-1, 3, 4].includes(publishDetail.template_check_status)) {
            setCheckedTagList([]);
        } else {
            if (tag.value === 0) {
                setCheckedTagList(tagList.map((t: any) => { return t.value }))
            } else {
                setCheckedTagList([tag.value])
            }
        }
    }

    // 是否需要第三方授权状态
    const onChangeAuth = (e: any) => {
        setIsAuth(e.target.value);
    }

    // 继续公开
    const onOpen = async () => {
        if ([-1, 3, 4].includes(publishDetail.template_check_status)) {
            const resDeployType: any = deployType
            // 公开
            const res = await reqPublicPrompt({
                template_id: publishId,
                template_type: deployTypeObj[resDeployType],
                tags: checkedTagList.filter((t: any) => { return t != 0 })
            })
            if (res?.message === 'success') {
                setPublishDetail({
                    ...publishDetail,
                    template_check_status: 1
                })
                message.success('提交成功')
            }
        }
    }

    const onClickOpen = async () => {
        const resDeployType: any = deployType
        if ([-1, 3, 4].includes(publishDetail.template_check_status)) {
            if (!checkedTagList.length) {
                message.warning('请选择标签');
                return
            }
            const resDeployType: any = deployType
            // 公开
            const res = await reqPublicPrompt({
                template_id: publishId,
                template_type: deployTypeObj[resDeployType],
                tags: checkedTagList.filter((t: any) => { return t != 0 }),
                is_auth: resDeployType === 'api' ? isAuth : null
            })
            if (res?.message === 'success') {
                setPublishDetail({
                    ...publishDetail,
                    template_check_status: 1
                })
                message.success('提交成功')
            }
        } else if (publishDetail.template_check_status === 1) {
            // 撤销审核
            const res = await reqCancelSubmitPublicPrompt({
                template_id: publishId,
                template_type: deployTypeObj[resDeployType],
                is_auth: resDeployType === 'api' ? isAuth : null
            })
            if (res.message === 'success') {
                setPublishDetail({
                    ...publishDetail,
                    template_check_status: -1
                })
                message.success('撤销成功')
            }
        } else if (publishDetail.template_check_status === 2) {
            // 取消公开
            const res = await reqCancelPublicPrompt({
                template_id: publishId,
                template_type: deployTypeObj[resDeployType],
                is_auth: resDeployType === 'api' ? isAuth : null
            })
            if (res.message === 'success') {
                setPublishDetail({
                    ...publishDetail,
                    template_check_status: -1
                })
                message.success('取消成功')
            }
        }
    }

    // 
    const market = {
        key: 'market',
        label: <>{deployType == 'agent' ? '智能体商店' : '公开到市场'}</>,
        children: <>
            {
                activeTab == 'market' ?
                    <OperationMarket
                        publishDetail={publishDetail}
                        onChangeAuth={onChangeAuth}
                        tagList={tagList}
                        checkedTagList={checkedTagList}
                        onOpen={onOpen}
                        deployType={deployType}
                        authType={authType}
                        isAuth={isAuth}
                        onClickTag={onClickTag}
                        onClickOpen={onClickOpen}
                    />
                    : ''
            }
        </>
    };

    const modeArr = useMemo(() => {
        return +(flowType || 1) === 1 ? ['有序SSE模式', '无序SSE模式', '同步模式', '轮询模式'] : ['有序SSE模式', '轮询模式'];
    }, [flowType])

    const modeItems: MenuProps['items'] = useMemo(() => {
        const result: MenuProps['items'] = [];

        modeArr.forEach((item: any, index: number) => {
            result.push({
                label: (
                    <div className={operationListStyles.modeLabel}>
                        {modeArr[index]}
                        {++index === curMode && <img src={checkedIcon.src} style={{ marginLeft: 70 }} />}
                    </div>
                ),
                key: index
            })
        })

        return result;
    }, [modeArr, curMode])

    const menuProps = {
        items: modeItems,
        onClick: (curItem: any) => {
            setCurMode(+curItem.key);
        }
    }

    const waibu = {
        key: 'waibu',
        label: 'API调用',
        children: <>
            {
                activeTab == 'waibu' ?
                    <OperationApi
                        deployDetail={deployDetail}
                        publishDetail={publishDetail}
                        flowApiType={flowApiType}
                        changeDeploy={changeDeploy}
                        menuProps={menuProps}
                        modeArr={modeArr}
                        curMode={curMode}
                        deployId={deployId}
                        flowType={flowType}
                        flowSysVar={flowSysVar}
                    />
                    : ''
            }
        </>,
    };

    // const isOnline = waiWangHosts.includes(hostName);
    // +'&appId='+deployDetail.app_id+'&appSecrect='+deployDetail.app_secret
    // const appUrl = (isOnline ? 'https://app.'+hostName+'/' : ( hostName === 'agent.qihoo.net' ? 'https://app.agent.360.com/' : 'https://app.agent.qihoo.net/')) + 'flowApp?id='+chainId;
    const appUrl = 'https:///bao.360.com/flowBaoApp?id=' + chainId + '&platform=xiaohongshu';

    const app = {
        key: 'app',
        label: 'APP应用',
        children: <>
            {
                activeTab == 'app' ?
                    <OperationApp
                        deployDetail={deployDetail}
                        changeDeploy={changeDeploy}
                        chainId={chainId}
                        activeTab={activeTab}
                        isV2={true}
                    />
                    : ''
            }
        </>
    };

    const handleOpenMCP = (): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            if (!isPublish) {
                message.warning('未发布版本时，不支持开启MCP服务');
                throw new Error("未发布版本时，不支持开启MCP服务");
            }

            if (!(flowApiType === 1)) {
                message.warning('仅支持api执行方式');
                throw new Error("仅支持api执行方式");
            }

            if (!(deployDetail.status === 1)) {
                // message.warning('未开启API时，不支持开启MCP服务');
                await changeDeploy(1);
            }

            resolve({});
        })
    }


    const mcp = {
        key: 'mcp',
        label: 'MCP服务',
        children: <OperationMCP
            type='flow'
            id={chainId}
            name={publishDetail.chain_name}
            description={publishDetail.chain_desc}
            openMCP={handleOpenMCP}
            chainNo={chainNo}
            enName={enName}
            setIsMcpStatus={setIsMcpStatus}
        />
    };

    const versionManage = {
        key: 'versionManage',
        label: '版本管理' + (listCount ? ` (${listCount})` : ''),
        children: <>
            <VersionManage
                flowType={flowType}
                chainName={publishDetail.chain_name}
                getCount={(num: any) => setListCount(num)}
                getHasPublishVersion={(curVersionParams: any) => {
                    setPublishVersion(curVersionParams.version);
                    setFlowApiType(curVersionParams.input_type || 1);
                }}
                isV2={true}
                setIsPublish={setIsPublish}
            />
        </>,
    };

    const unZhongwenTitleLength = publishDetail.chain_name?.replaceAll(zhongwenReg, '').length;
    const zhongwenTitleLength = publishDetail.chain_name?.length - unZhongwenTitleLength;

    const getTabsResult = () => {
        let itemsResult: any = [];
        if (deployType === 'flow' || deployType === 'text') {
            itemsResult = [market, waibu];
            if (deployType === 'flow') {
                itemsResult = flowType === 2 ? [versionManage, market, waibu, app, mcp] : [versionManage, market, waibu];
            }
            isFromLlmopsBoard && (itemsResult = [waibu]);
        } else {
            itemsResult = [market];
            isFromLlmopsBoard && (itemsResult = []);
        }
        return isYunPanBoard ? [versionManage] : itemsResult;
    }
    return (
        <>
            <div className={styles.commonContent}>

                <Spin spinning={initLoading} className='commonSpin'>

                    <div className={styles.top}>
                        <div className={styles.title}>
                            <img src={leftBtn.src} className={styles.leftBtn} onClick={() => {
                                if (backFuc) {
                                    backFuc()
                                } else {
                                    if (from !== undefined && from === "1") {
                                        Router.push('/background/examineList')
                                    } else if (from === 'detail') {
                                        const resDeployType: any = deployType;
                                        const typeObj: any = {
                                            'text': 'textPrompt',
                                            'image': 'imagePrompt',
                                            'flow': 'flow',
                                            'api': 'api',
                                            'agent': 'agent',
                                        }
                                        if (resDeployType === 'agent') {
                                            Router.push(typeObj[resDeployType] + 'Detail?agentId=' + chainId + '&teamId=' + teamId + '&from=2')
                                        } else if (resDeployType === 'api') {
                                            Router.push('actionList?id=' + chainId + '&teamId=' + teamId)
                                        } else {
                                            if (resDeployType === 'flow') {
                                                Router.push('flowDetail?id=' + chainId + '&teamId=' + teamId + '&flow_type=' + flowType)
                                            } else if (flowType == 2) {
                                                Router.push('flowDetail?id=' + chainId + '&teamId=' + teamId + '&flow_type=' + flowType)
                                            } else {
                                                Router.push(typeObj[resDeployType] + 'Detail?id=' + chainId + '&teamId=' + teamId + '&flow_type=' + flowType)
                                            }
                                        }
                                    } else {
                                        Router.push('/projectList?teamId=' + teamId + '&tab=1')
                                    }
                                }
                            }} />
                            {/* {digitalMan.digit_avatar_status === 1 && digitalMan.digit_avatar_background ? 
                            ( digitalMan.digit_avatar_url ? 
                                <div className={styles.imageWrapper} style={{background: digitalMan.digit_avatar_background, textAlign:'center', height:'40px', width:'40px' }}>
                                    <img src={digitalMan.digit_avatar_url} height={40} />
                                </div>
                                : 
                                <div className={styles.imageWrapper} style={{background: digitalMan.digit_avatar_background, width:'40px', height:'40px'}}></div>
                            )
                            :    
                            <img src={publishDetail.image || image.src} width={40} height={40} className={styles.imageWrapper} />
                        } */}
                            <div className={styles.imageWrapper} style={{ display: 'flex', alignContent: 'center', justifyContent: 'center', width: '40px', height: '40px', background: digitalMan.digit_avatar_background }}>
                                <img src={publishDetail.image || image.src} height='100%' />
                            </div>
                            <div>
                                <div>
                                    <Input
                                        value={publishDetail.chain_name}
                                        className={styles.nameInput + ' transparentInput'}
                                        style={{ width: unZhongwenTitleLength * 12 + zhongwenTitleLength * 20, maxWidth: 500, display: 'inline-block' }}
                                    />
                                    {((deployType !== 'flow' && publishDetail.publish_status === 2) || (deployType === 'flow' && publishVersion)) && <span className={operationDetailStyles.publishedBtn}>已发布</span>}
                                </div>
                                <Input value={publishDetail.chain_desc} placeholder="写下你的描述......" className={styles.descInput + ' transparentInput'} maxLength={500} />
                            </div>
                        </div>
                        <div className={styles.buttonWrapper}>
                            {
                                deployType !== 'flow' ? (publishDetail.publish_status === 1 ? (
                                    <Button type='primary' className='primaryButton' onClick={publish}>
                                        发布
                                    </Button>
                                ) : (
                                    <Button type='default' className='defabultButton' onClick={cancelPublish}>
                                        取消发布
                                    </Button>
                                )) : <></>
                            }
                        </div>
                    </div>
                    <div className={operationDetailStyles.contentWrapper}>
                        {publishDetail.publish_status === 2 && deployType !== 'agent' && deployType !== 'flow' && <div className={operationDetailStyles.messageWrapper + ' normalFont'}>
                            <img src={success.src} />
                            <span className={operationDetailStyles.successTips}>发布成功！</span>
                            <span>发布成功后，可以公开到市场，也可以通过其他渠道使用。同时文件将不可被编辑，如需编辑，请取消发布后编辑。</span>
                        </div>}

                        {deployType !== 'agent' && deployType !== 'flow' && <div className={operationDetailStyles.contentTitle}>使用渠道</div>}
                        {deployType !== 'agent' && <Tabs
                            defaultActiveKey="version"
                            items={getTabsResult()}
                            activeKey={activeTab}
                            onChange={(e) => {
                                setActiveTab(e)
                            }}
                        >
                        </Tabs>}
                    </div>
                </Spin>
            </div>
        </>
    )
}
