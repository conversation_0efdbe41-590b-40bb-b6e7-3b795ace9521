import {
  pyCodeForTest,
  curlCodeForTest,
  jsCodeForTest,
  textData,
} from "@/utils/constantsText";

import OperationDetail<PERSON>ommon from "./OperationDetailCommon";

interface Props {
    base_url: string;
    app_id: string;
    app_secret: string;
    template_id: number;
    description: string;
    input_keys: any[];
}

const generateCode = (input_keys: any[],type: number) => {
    const holderStr = type === 1 ? ',\n        ' : type === 2 ? ',\n    ' : ',\n    '
    const input_keysStr = input_keys.map((item:any)=>{
        if(item.type === 'string'){
            return  `"${item.name}": "${item.value}"`
        }else if(item.type === 'number'){
            return `"${item.name}": ${item.value}` 
        }
    }).join(holderStr)
    return input_keysStr
};

const generatePyCode = (input_keys: any[],type: number) => {
    if(input_keys.length === 0) {
        return ''
    }
    const holderStr = ',\n       '
    const input_keysStr = input_keys.map((item:any)=>{
        return  `"${item.name}": '''${item.value}'''`
    }).join(holderStr)
    return input_keysStr
};

const OperationDetailText = (props: Props) => {

    let pyCodeForTextInput = pyCodeForTest + 
`
if __name__ == '__main__':
    request_inputs = {
        "chain_inputs": {
            ${generatePyCode(props.input_keys,1)}
        },
        "base_url": "${props.base_url}",
        "app_id": "${props.app_id}",
        "app_secret": "${props.app_secret}",
        "template_id": "${props.template_id}"
    }
    results, is_ok = prompt_run(**request_inputs)
    print(results)
`;
    
        let curlCodeForTextInput = `
export base_url="${props.base_url}"
export app_id="${props.app_id}"  # AK
export app_secret="${props.app_secret}"  # SK
export template_id="${props.template_id}"  # 实例编号
export chain_inputs='{
    ${generateCode(props.input_keys,2)}
}' # 输入参数` + curlCodeForTest;
    
         let jsCodeForTextInput = `
import axios from 'axios'
const base_url = "${props.base_url}";  
const app_id = "${props.app_id}";  // AK  
const app_secret = "${props.app_secret}";  // SK  
const template_id = ${props.template_id};  // 实例编号  
const chain_inputs = {
    ${generateCode(props.input_keys,3)}
};  // 输入参数  ` + jsCodeForTest;

  return (
    <OperationDetailCommon
      pyCode={pyCodeForTextInput}
      jsCode={jsCodeForTextInput}
      curlCode={curlCodeForTextInput}
      detailData={textData}
      isAgentDetailTitle={false}
      isAgentTitle={false}
    />
  );
};

export default OperationDetailText;
