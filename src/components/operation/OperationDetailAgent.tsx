

import OperationDetailCommon from "./OperationDetailCommon";
import Router, { useRouter } from 'next/router'
import { useEffect, useState } from "react";
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

interface Props {
    base_url: string;
    app_id: string;
    app_secret: string;
    template_id: number;
    description: string;
    input_keys: any[];
    newVersion:string,
}



const OperationDetailText = (props: Props) => {

    const router = useRouter();
    const chainId = router.query.id; // chain_id 或者 template_id
    // const version = router.query.version; //version 智能体版本号

    const [sessionId, setSessionId] = useState('');

    useEffect(() => {
        // 生成 UUID
        //   const uuid = crypto.randomUUID().replace(/-/g, '');
        const uuid = uuidv4()
        setSessionId(uuid);
    }, []);

    const pyCodeForTest = `
      import requests
      import json
      
      url = "${window.location.origin}/api/v2/openapi/chat"
      
      payload = json.dumps({
        "agent_id":${chainId},
        "session_id":"${sessionId}",
        "user": "o3oxx",
        "query": '{\\"file_list\\":[{\\"type\\":\\"image\\",\\"url\\":\\"https://p3.ssl.qhimg.com/t011e94f0b9ed8e66b0.png\\"}],\\"query\\":\\"请描述图片内容\\"}',
        "content_type": "mix",
        "stream": True,
        "verbose": True,
        "version": "${props.newVersion}",
      })
      headers = {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
      }
      
      response = requests.request("POST", url, headers=headers, data=payload)
      
      print(response.text)
        `;




    const curlCodeForTest = `
      curl --location '${window.location.origin}/api/v2/openapi/chat' \
      --header 'Authorization: Bearer your_token' \
      --header 'Content-Type: application/json' \
      --data '{
          "agent_id": ${chainId},
          "session_id": "${sessionId}",
          "user": "o3oxx",
          "query":"{\"file_list\":[{\"type\":\"image\",\"url\":\"https://p2.qhimg.com/t11be721f54bf5a550f7ce56578.png\"}],\"query\":\"订单号是多少\"}",
          "content_type": "mix"
          "stream":true,
          "verbose":true,
          "version": "${props.newVersion}"
    }'

    `;

    const jsCodeForTest = `
      const myHeaders = new Headers();
      myHeaders.append("Authorization", "Bearer your_token");
      myHeaders.append("Content-Type", "application/json");
    
      const raw = JSON.stringify({
        "agent_id": ${chainId},
        "session_id": "${sessionId}",
        "user": "o3oxx",
        "query": '{\\"file_list\\":[{\\"type\\":\\"image\\",\\"url\\":\\"https://p3.ssl.qhimg.com/t011e94f0b9ed8e66b0.png\\"}],\\"query\\":\\"请描述图片内容\\"}',
        "content_type": "mix",
        "stream": true,
        "verbose": true,
        "version": "${props.newVersion}",
    });
    
      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
      };
    
      fetch("${window.location.origin}/api/v2/openapi/chat", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
      .catch((error) => console.error(error));
      `;


    const agentData = [
        {
            title: '【第一步】鉴权',
            func: '',
            url: '',
            subTitles: `请将上面生成的token，放入到header中Authorization字段之中。
格式：Authorization：Bearer token。
例如：Authorization：Bearer gW90VHHYtMWFb526otAu**************。`,
            req: [
            ],
            res: [
            ]
        },
        {
            title: '【第二步】请求服务',
            func: 'POST',
            url: '/api/v2/openapi/chat',
            subTitles: '非流式返回',
            req: [
                {
                    name: 'body',
                    title: 'body',
                    key: '0-0',
                    location: 'body',
                    type: 'object',
                    required: false,
                    desc: '',
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>agent_id</span>
                                    <span style={{ marginRight: '12px' }}>integer</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>agent_id，int</div>
                                </div>
                            ),
                            key: '0-0-0',
                            location: 'body',
                            type: 'integer',
                            required: true,
                            desc: 'agent_id，int'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>session_id</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>会话ID，str 调用方自行维护,用于上下文查询</div>
                                </div>
                            ),
                            key: '0-0-1',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '会话ID，str 调用方自行维护,用于上下文查询'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>user</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div>用户id ，str 确认该渠道唯一用户信息</div>
                                </div>
                            ),
                            key: '0-0-2',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: '用户id ，str 确认该渠道唯一用户信息'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>query</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div>用户问题。支持多模态入参，且文件url必须为公网地址。支持类型为：excel、docx、pdf、csv、image
                                    </div>
                                </div>
                            ),
                            key: '0-0-3',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: '用户问题。支持多模态入参，且文件url必须为公网地址。支持类型为：excel、docx、pdf、csv、image'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>content_type</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>用户query是否包含多模态入参，包含为mix，否则默认为文本</div>
                                </div>
                            ),
                            key: '0-0-4',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '用户query是否包含多模态入参，包含为mix，否则默认为文本'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>stream</span>
                                    <span style={{ marginRight: '12px' }}>bool</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>是否流式返回 .默认为false，bool值</div>
                                </div>
                            ),
                            key: '0-0-5',
                            location: 'body',
                            type: 'bool',
                            required: false,
                            desc: '是否流式返回 .默认为false，bool值'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>verbose</span>
                                    <span style={{ marginRight: '12px' }}>bool</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>是否显示agent的中间过程 默认false, bool值</div>
                                </div>
                            ),
                            key: '0-0-6',
                            location: 'body',
                            type: 'bool',
                            required: false,
                            desc: '是否显示agent的中间过程 默认false, bool值'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>version</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>非必填</span>
                                    <div>{props.newVersion}</div>
                                </div>
                            ),
                            key: '0-0-7',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '智能体发布版本号'
                        },
                    ]
                }
            ],
            reqHeight: "120px",
            reqCode: `{
      "agent_id": 406,
      "session_id":"${sessionId}",
      "user": "o3oxx",
      "query": '{\\"file_list\\":[{\\"type\\":\\"image\\",\\"url\\":\\"https://p3.ssl.qhimg.com/t011e94f0b9ed8e66b0.png\\"}],\\"query\\":\\"请描述图片内容\\"}',
      "content_type": "mix",
      "stream":false, 
      "verbose":true ,
      "version": "${props.newVersion}",
    }`,
            res: [
                {
                    title: (
                        <div>
                            <span style={{ marginRight: '12px' }}>data</span>
                            <span style={{ marginRight: '12px' }}>object</span>
                            <span style={{ marginRight: '16px' }}>必填</span>
                            <div></div>
                        </div>
                    ),
                    type: 'array',
                    desc: '',
                    key: '0-0',
                    required: true,
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>message</span>
                                    <span style={{ marginRight: '12px' }}>object</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div style={{ marginRight: '16px' }}></div>
                                </div>
                            ),
                            type: 'object',
                            desc: '',
                            key: '0-0-0',
                            required: true,
                        }
                    ]
                },
                {
                    title: (
                        <div>
                            <span style={{ marginRight: '12px' }}>context</span>
                            <span style={{ marginRight: '12px' }}>object</span>
                            <span style={{ marginRight: '16px' }}>必填</span>
                            <div></div>
                        </div>
                    ),
                    type: 'object',
                    desc: '',
                    key: '1-0',
                    required: true,
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>message</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div>返回消息说明</div>
                                </div>
                            ),
                            type: 'string',
                            desc: '返回消息说明',
                            key: '1-0-0',
                            required: true,
                        },
                    ]
                }
            ],
            resHeight: "220px",
            resCode: `{
      "context": {
          "message": "OK",
          "code": 0,
          "request_id": "",
          "timestamp": 1717468850
      },
      "data": [
          {
              "message": {
                  "role": "assistant",
                  "type": "function_call",
                  "content": "{\"arguments\": \"{\\\"query__query\\\":\\\"北京\\\"}\"}",
                  "reasoning_content": "",
                  "content_type": "text",
                  "message_id": "",
                  "reply_id": "665e7ea7276d9a3f0e873606",
                  "section_id": "",
                  "extra_info": {
                      "local_message_id": "",
                      "input_tokens": "",
                      "output_tokens": "",
                      "total_tokens": "",
                      "plugin_status": "",
                      "time_cost": "3.01",
                      "workflow_tokens": "",
                      "bot_state": "",
                      "plugin_request": "",
                      "tool_name": "查询地区天气",
                      "plugin": "",
                      "mock_hit_info": "",
                      "log_id": ""
                  },
                  "status": "1"
              },
              "is_finish": true,
              "session_id": "${sessionId}",
              "seq_id": 1
          },
          {
              "message": {
                  "role": "assistant",
                  "type": "tool_response",
                  "content": "{\"query\":\"北京\",\"results\":\"2024-06-04至2024-06-04，北京市的天气情况如下：\\n06月04日(星期二)天气：多云转阴，日间气温32℃，夜间气温21℃，西南风转南风，0级；\",\"location\":\"北京市\",\"duration\":[\"2024-06-04\",\"2024-06-04\"],\"code\":\"200\"}",
                  "reasoning_content": "",
                  "content_type": "text",
                  "message_id": "",
                  "reply_id": "665e7ea7276d9a3f0e873606",
                  "section_id": "",
                  "extra_info": {
                      "local_message_id": "",
                      "input_tokens": "",
                      "output_tokens": "",
                      "total_tokens": "",
                      "plugin_status": "",
                      "time_cost": "0.82",
                      "workflow_tokens": "",
                      "bot_state": "",
                      "plugin_request": "",
                      "tool_name": "",
                      "plugin": "",
                      "mock_hit_info": "",
                      "log_id": ""
                  },
                  "status": "1"
              },
              "is_finish": true,
              "session_id": "${sessionId}",
              "seq_id": 1
          },
      ]
  }`,

        },
        {
            title: '【其他】请求服务',
            func: 'POST',
            url: '/api/v2/openapi/chat',
            subTitles: '流式返回',
            req: [
                {
                    name: 'body',
                    title: 'body',
                    key: '0-0',
                    location: 'body',
                    type: 'object',
                    required: false,
                    desc: '',
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>agent_id</span>
                                    <span style={{ marginRight: '12px' }}>integer</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>agent_id，int</div>
                                </div>
                            ),
                            key: '0-0-0',
                            location: 'body',
                            type: 'integer',
                            required: true,
                            desc: 'agent_id，int'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>session_id</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>会话ID，str 调用方自行维护,用于上下文查询</div>
                                </div>
                            ),
                            key: '0-0-1',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '会话ID，str 调用方自行维护,用于上下文查询'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>user</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div>用户id ，str 确认该渠道唯一用户信息</div>
                                </div>
                            ),
                            key: '0-0-2',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: '用户id ，str 确认该渠道唯一用户信息'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>query</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div>用户问题，str</div>
                                </div>
                            ),
                            key: '0-0-3',
                            location: 'body',
                            type: 'string',
                            required: true,
                            desc: '用户问题，str'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>stream</span>
                                    <span style={{ marginRight: '12px' }}>bool</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>是否流式返回 .默认为false，bool值</div>
                                </div>
                            ),
                            key: '0-0-4',
                            location: 'body',
                            type: 'bool',
                            required: false,
                            desc: '是否流式返回 .默认为false，bool值'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>verbose</span>
                                    <span style={{ marginRight: '12px' }}>bool</span>
                                    <span style={{ marginRight: '16px' }}>选填</span>
                                    <div>是否显示agent的中间过程 默认false, bool值，选填</div>
                                </div>
                            ),
                            key: '0-0-5',
                            location: 'body',
                            type: 'bool',
                            required: false,
                            desc: '是否显示agent的中间过程 默认false, bool值，选填'
                        },
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>version</span>
                                    <span style={{ marginRight: '12px' }}>string</span>
                                    <span style={{ marginRight: '16px' }}>非必填</span>
                                    <div>{props.newVersion}</div>
                                </div>
                            ),
                            key: '0-0-6',
                            location: 'body',
                            type: 'string',
                            required: false,
                            desc: '智能体发布版本号'
                        },
                    ]
                }
            ],
            reqHeight: "120px",
            reqCode: `{
  "agent_id": 406,
  "session_id":"${sessionId}",
  "user": "o3oxx",
  "query": '{\\"file_list\\":[{\\"type\\":\\"image\\",\\"url\\":\\"https://p3.ssl.qhimg.com/t011e94f0b9ed8e66b0.png\\"}],\\"query\\":\\"请描述图片内容\\"}',
  "content_type": "mix",
  "stream":true, 
  "verbose":true ,
  "version": "${props.newVersion}",
}`,
            res: [
                {
                    title: (
                        <div>
                            <span style={{ marginRight: '12px' }}>data</span>
                            <span style={{ marginRight: '12px' }}>object</span>
                            <span style={{ marginRight: '16px' }}>必填</span>
                            <div></div>
                        </div>
                    ),
                    type: 'object',
                    desc: '',
                    key: '0-0',
                    required: true,
                    children: [
                        {
                            title: (
                                <div>
                                    <span style={{ marginRight: '12px' }}>message</span>
                                    <span style={{ marginRight: '12px' }}>object</span>
                                    <span style={{ marginRight: '16px' }}>必填</span>
                                    <div style={{ marginRight: '16px' }}></div>
                                </div>
                            ),
                            type: 'object',
                            desc: '',
                            key: '0-0-0',
                            required: true,
                        }
                    ]
                },
            ],
            resHeight: "220px",
            resCode: `{
          data: {"message": {"role": "assistant", "type": "function_call", "content": "{\"arguments\": \"{\\\"query__query\\\":\\\"北京\\\"}\"}", "reasoning_content": "", "content_type": "text", "message_id": "", "reply_id": "665e7ff8276d9a3f0e87360d", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "6.10", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "查询地区天气", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "${sessionId}", "seq_id": 1}
          data: {"message": {"role": "assistant", "type": "tool_response", "content": "{\"query\":\"北京\",\"results\":\"2024-06-04至2024-06-04，北京市的天气情况如下：\\n06月04日(星期二)天气：多云转阴，日间气温32℃，夜间气温21℃，西南风转南风，0级；\",\"location\":\"北京市\",\"duration\":[\"2024-06-04\",\"2024-06-04\"],\"code\":\"200\"}", "reasoning_content": "", "content_type": "text", "message_id": "", "reply_id": "665e7ff8276d9a3f0e87360d", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "0.94", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 1}
          data: {"message": {"role": "assistant", "type": "answer", "content": "{\"error\":{\"code\":\"10137\",\"message\":\"Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. (request id: 20240604104623671163765aXTBVTaR)\"}}", "reasoning_content": "", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "0", "output_tokens": "0", "total_tokens": "0", "plugin_status": "", "time_cost": "8.92", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "404xxxxx", "seq_id": 0}
          data: {"message": {"role": "assistant", "type": "follow_up", "content": "天津的未来三天天气怎么样", "reasoning_content": "", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "${sessionId}", "seq_id": 1}
          data: {"message": {"role": "assistant", "type": "follow_up", "content": "北京今日空气质量怎么样", "reasoning_content": "", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "${sessionId}", "seq_id": 2}
          data: {"message": {"role": "assistant", "type": "follow_up", "content": "北京和天津天气对比", "reasoning_content": "", "content_type": "text", "message_id": "", "reply_id": "", "section_id": "", "extra_info": {"local_message_id": "", "input_tokens": "", "output_tokens": "", "total_tokens": "", "plugin_status": "", "time_cost": "", "workflow_tokens": "", "bot_state": "", "plugin_request": "", "tool_name": "", "plugin": "", "mock_hit_info": "", "log_id": ""}, "status": "1"}, "is_finish": true, "session_id": "${sessionId}", "seq_id": 3}
          data: done
  
  }`,
        },
    ]

    let pyCodeForTextInput = pyCodeForTest;

    let curlCodeForTextInput = curlCodeForTest;

    let jsCodeForTextInput = jsCodeForTest;

    return (
        <OperationDetailCommon
            pyCode={pyCodeForTextInput}
            jsCode={jsCodeForTextInput}
            curlCode={curlCodeForTextInput}
            detailData={agentData}
            isAgentDetailTitle={false}
            isAgentTitle={true}

        />
    );
};

export default OperationDetailText;
