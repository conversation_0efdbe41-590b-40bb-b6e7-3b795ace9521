
import operationListStyles from '@/styles/OperationList.module.scss'
import commonListStyles from '@/styles/CommonList.module.scss'
import styles from '@/styles/Common.module.scss'
import leftBtn from '@/images/common/leftBtn.svg'
import Router, { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { Modal, Spin, Table, Input, Button } from 'antd'
import { reqMonitorDetail, reqMonitorGantt, reqMonitorLog } from '@/service/monitor';
import apiStyles from '@/styles/Api.module.scss';
import { initJsonEditor } from './utils';
import commonOuterStyle from '@/styles/CommonListOuter.module.scss'
import copyBtn from '@/images/commonList/copy.svg'
import { copyParamValue } from '../../components/flow3.0/flowFunc';






export default function MonitorDetail() {
    const router = useRouter()
    const [logId, setLogId] = useState<any>(router.query.logId);
    const deployId = router.query.deployId;
    const teamId = router.query.teamId;
    const triggerCode = router.query.triggerCode;
    const [templateId, setTemplateId] = useState(router.query.templateId);
    const template_type = router.query.template_type;
    const [loading, setLoading] = useState(false)
    const [detailData, setDetailData] = useState<any[]>([]);  //主流程表格数据
    const [responseData, setResponseData] = useState<any[]>([]);  // 用来存储主流程接口返回的数据
    const [openDetail, setOpenDetail] = useState(false)
    const [putData, setPutData] = useState(new Array());//主流程input、output、执行日志数据

    const [isChildData, setIsChildData] = useState(false)

    const [childDetailData, setChildDetailData] = useState<any[]>([]);  // 子流程表格数据
    const [childResponseData, setChildResponseData] = useState<any[]>([]);  // 用来存储子流程接口返回的数据

    const [curMainId, setCurMainId] = useState('')
    const [codeData, setCodeData] = useState<any[]>([]);
    const [isLog, setIsLog] = useState(false)
    const [curCallBackId, setCurCallBackId] = useState('')
    const [tableHeight, setTableHeight] = useState<number>(0);
    const [openLog, setOpenLog] = useState(false)  //详细日志
    const [logData, setLogData] = useState(new Array());
    const [openGantt, setOpenGantt] = useState(false)  //甘特图
    const [dsId, setDsId] = useState('')



    //主流程
    const columns = [
        {
            title: 'block_id',
            dataIndex: 'block_id',
            key: 'block_id',
            render: (text: string, record: any) => (
                record.flow_plugin_call_back_id ? (
                    <div>
                        <span>{text}</span>
                        <span className={operationListStyles.logBtn} style={{ marginLeft: '4px' }} onClick={() => { searchChildData(record.flow_plugin_call_back_id, record.block_id) }}>查看</span>
                    </div>
                ) : (
                    <span>{text}</span>
                )
            )
        },
        {
            title: 'block_type',
            dataIndex: 'block_type',
            key: 'block_type',
            render: (text: any, record: any, index: any) => {
                const typeObj: any = {
                    'input': '开始',
                    'code': '代码',
                    'web-crawl': '网页抓取',
                    'web-crawl-batch': '批量网页抓取',
                    'web-search': '网页搜索',
                    'web-search-batch': '批量网页搜索',
                    'api': '连接器',
                    'knowledge': '知识库',
                    'output': '结束',
                    'condition_start': '条件开始',
                    'condition_end': '条件结束',
                    'llm': '大模型',
                    'llm-batch': '批量大模型',
                    'function-call': 'function-call',
                    'interaction': '交互',
                    'flow_plugin': '子流',
                    'api-batch': '批量连接器',
                    'version-api': '连接器',
                    'version-api-batch': '批量连接器',
                    'version-function-call': 'function-call',
                    'image_to_text_llm': '图片理解',
                    'datetime': '获取时间'
                }
                const type = typeObj[text]

                return <span>{type}({text})</span>;
            }

        },
        {
            title: 'block_name',
            dataIndex: 'block_name',
            key: 'block_name',
        },
        {
            title: 'status',
            dataIndex: 'status',
            key: 'status',
            render: (t: any) => {
                const typeObj: any = {
                    '-1': '异常',
                    '1': '未运行',
                    '2': '运行中',
                    '3': '运行成功',
                    '4': '运行失败',
                    '5': '已编辑待调试',
                    '6': '已取消',
                    '7': '暂停',
                    '8': '未调用',
                }
                const status = typeObj[t]
                let color = '#1F1F1F'; // 默认颜色

                if (t === 3) {
                    color = '#6AE0B5'; // 运行成功
                } else if (t === 6) {
                    color = '#FBD54A'; // 运行失败
                } else if (t === 4) {
                    color = '#47A1FF'
                } else if (t === -1) {
                    color = '#6AE0B5'; // 运行成功
                }

                return <span style={{ color }}>{status}</span>;
            }
        },
        {
            title: 'start_time',
            dataIndex: 'start_time',
            key: 'start_time',
        },
        {
            title: 'end_time',
            dataIndex: 'end_time',
            key: 'end_time',
        },
        {
            title: '运行时长',
            dataIndex: 'running_time',
            key: 'running_time',
        },
        {
            title: 'input',
            dataIndex: 'input',
            width: 100,
            key: 'input',
            render: (_: any, record: any, index: any) => {
                return <span className={operationListStyles.logBtn} onClick={() => { searchInfo(logId, 2, record.block_id, false) }}>查看</span>
            }
        },
        {
            title: 'output',
            dataIndex: 'output',
            width: 100,
            key: 'output',
            render: (_: any, record: any, index: any) => {
                return <span className={operationListStyles.logBtn} onClick={() => { searchInfo(logId, 3, record.block_id, false) }}>查看</span>
            }
        },
        {
            title: '执行日志',
            dataIndex: 'operation',
            width: 100,
            key: 'operation',
            render: (_: any, record: any, index: any) => {
                return <span className={operationListStyles.logBtn} onClick={() => { searchLogInfo(logId, 4, record.block_id, false) }}>查看</span>
            }
        },
    ];

    //子流程
    const childColumns = [
        {
            title: 'block_id',
            dataIndex: 'block_id',
            key: 'block_id',
        },
        {
            title: 'block_type',
            dataIndex: 'block_type',
            key: 'block_type',
            render: (text: any, record: any, index: any) => {
                const typeObj: any = {
                    'input': '开始',
                    'code': '代码',
                    'web-crawl': '网页抓取',
                    'web-crawl-batch': '批量网页抓取',
                    'web-search': '网页搜索',
                    'web-search-batch': '批量网页搜索',
                    'api': '连接器',
                    'knowledge': '知识库',
                    'output': '结束',
                    'condition_start': '条件开始',
                    'condition_end': '条件结束',
                    'llm': '大模型',
                    'llm-batch': '批量大模型',
                    'function-call': 'function-call',
                    'interaction': '交互',
                    'flow_plugin': '子流',
                    'image_to_text_llm': '图片理解',
                    'datetime': '获取时间'
                }
                const type = typeObj[text]

                return <span>{type}({text})</span>;
            }

        },
        {
            title: 'block_name',
            dataIndex: 'block_name',
            key: 'block_name',
        },
        {
            title: 'status',
            dataIndex: 'status',
            key: 'status',
            render: (t: any, record: any) => {
                const typeObj: any = {
                    '-1': '异常',
                    '1': '未运行',
                    '2': '运行中',
                    '3': '运行成功',
                    '4': '运行失败',
                    '5': '已编辑待调试',
                    '6': '已取消',
                    '7': '暂停',
                    '8': '未调用',
                }
                const status = typeObj[t]
                let color = '#1F1F1F'; // 默认颜色

                if (t === 3) {
                    color = '#6AE0B5'; // 运行成功
                } else if (t === 6) {
                    color = '#FBD54A'; // 运行失败
                } else if (t === 4) {
                    color = '#47A1FF'
                } else if (t === -1) {
                    color = '#6AE0B5'; // 运行成功
                }

                return <span style={{ color: color }}>{status}</span>;
            }
        },
        {
            title: 'start_time',
            dataIndex: 'start_time',
            key: 'start_time',
        },
        {
            title: 'end_time',
            dataIndex: 'end_time',
            key: 'end_time',
        },
        {
            title: '运行时长',
            dataIndex: 'running_time',
            key: 'running_time',
        },
        {
            title: 'input',
            dataIndex: 'input',
            width: 100,
            key: 'input',
            render: (_: any, record: any, index: any) => {
                return <span className={operationListStyles.logBtn} onClick={() => { searchInfo(logId, 2, record.block_id, true) }}>查看</span>
            }
        },
        {
            title: 'output',
            dataIndex: 'output',
            width: 100,
            key: 'output',
            render: (_: any, record: any, index: any) => {
                return <span className={operationListStyles.logBtn} onClick={() => { searchInfo(logId, 3, record.block_id, true) }}>查看</span>
            }
        },
        {
            title: '执行日志',
            dataIndex: 'operation',
            width: 100,
            key: 'operation',
            render: (_: any, record: any, index: any) => {
                return <span className={operationListStyles.logBtn} onClick={() => { searchLogInfo(logId, 4, record.block_id, true) }}>查看</span>
            }
        },

    ]

    useEffect(() => {
        const handleResize = () => {
            // Set table height as 70% of the window height
            setTableHeight(window.innerHeight * 0.5);
        };

        // Initial resize calculation
        handleResize();

        // Add event listener to resize
        window.addEventListener('resize', handleResize);

        // Cleanup event listener on component unmount
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);


    const searchChildData = async (flow_plugin_call_back_id: any, block_id: any) => {
        setLoading(true)
        setCurMainId(flow_plugin_call_back_id)
        setIsChildData(true)
        setCurCallBackId(flow_plugin_call_back_id)
        const res = await reqMonitorDetail({
            query_type: 1,
            log_id: flow_plugin_call_back_id,
        })
        setLoading(false)
        if(res) {
            setChildResponseData(res)
        }
    }


    const searchInfo = (log_id: any, query_type: any, block_id: any, isChild: any) => {
        setIsLog(false)
        setOpenDetail(true)
        getLogData(log_id, query_type, block_id, isChild)
    }

    //执行日志单独处理
    const searchLogInfo = (log_id: any, query_type: any, block_id: any, isChild: any) => {
        setIsLog(true)
        setOpenDetail(true)
        getLogData(log_id, query_type, block_id, isChild)

    }



    const getLogData = async (log_id: any, query_type: any, block_id: any, isChild: any) => {
        setLoading(true)
        const res = await reqMonitorDetail({
            query_type: query_type,
            log_id: isChild ? curCallBackId : log_id,
            block_index: block_id,
        })
        setLoading(false)
        if(res) {
            setPutData(res)
        }
    }

    //转换input和output数据
    const transformPutData = (data: any[]) => {
        return data.map(item => {
            const message = JSON.parse(item.message)
            // console.log('message',message)
            return message
        })
    }




    useEffect(() => {
        if (putData.length > 0) {
            const transformOutputData = transformPutData(putData)
            setCodeData(transformOutputData)
            initJsonEditor(transformOutputData, 'jsoneditor-789')
        }
    }, [putData])


    const getDetail = async (log_id: any, query_type: any) => {
        setLoading(true)
        const res = await reqMonitorDetail({
            query_type: query_type,
            log_id: log_id,
        })
        setLoading(false)
        
        setResponseData(res || [])
        setTemplateId(log_id?.split('-')?.[0] || '-')
    }
    // 数据转换函数
    const transformData = (data: any[]) => {
        return data.map(item => {
            const message = JSON.parse(item.message);
            const runningTime = parseFloat(message.running_time);

            let formattedRunningTime = '';
            // 根据 running_time 的值格式化输出
            if (runningTime >= 60) {
                // 大于等于 1 分钟，显示 "12m 22s 399ms"
                const minutes = Math.floor(runningTime / 60);
                const seconds = Math.floor(runningTime % 60);
                const milliseconds = Math.round((runningTime % 1) * 1000);
                formattedRunningTime = `${minutes}m ${seconds}s ${milliseconds}ms`;
            } else if (runningTime >= 1) {
                // 大于等于 1 秒，显示 "12s 20ms"
                const seconds = Math.floor(runningTime);
                const milliseconds = Math.round((runningTime % 1) * 1000);
                formattedRunningTime = `${seconds}s ${milliseconds}ms`;
            } else {
                // 小于 1 秒，显示 "98ms"
                const milliseconds = Math.round(runningTime * 1000);  // 转换为毫秒
                formattedRunningTime = `${milliseconds}ms`;
            }

            return {
                block_id: message.block_id,
                block_name: message.block_name,
                block_type: message.block_type,
                status: message.status,
                start_time: message.start_time,
                end_time: message.end_time,
                running_time: formattedRunningTime,  // 这里直接使用格式化后的字符串
                flow_plugin_call_back_id: message.flow_plugin_call_back_id ? message.flow_plugin_call_back_id : '',

            };
        });
    };

    useEffect(() => {
        getDetail(logId, 1)
    }, [])

    useEffect(() => {
        if (responseData.length > 0) {
            const transformed = transformData(responseData);  // 转换数据
            const sortedBlocks = transformed.sort((a, b) => {
                if (a.block_name === "开始") return -1;  // "开始" 放到第一个
                if (b.block_name === "开始") return 1;   // "开始" 放到第一个
                if (a.block_name === "结束") return 1;   // "结束" 放到最后
                if (b.block_name === "结束") return -1;  // "结束" 放到最后
                return 0;  // 其他元素保持原顺序
            });
            setDetailData(sortedBlocks);
        }
    }, [responseData]);  // 监听 responseData 的变化

    useEffect(() => {
        if (childResponseData.length > 0) {
            const transformed = transformData(childResponseData);  // 转换数据
            const sortedBlocks = transformed.sort((a, b) => {
                if (a.block_name === "开始") return -1;  // "开始" 放到第一个
                if (b.block_name === "开始") return 1;   // "开始" 放到第一个
                if (a.block_name === "结束") return 1;   // "结束" 放到最后
                if (b.block_name === "结束") return -1;  // "结束" 放到最后
                return 0;  // 其他元素保持原顺序
            });
            setChildDetailData(sortedBlocks);
        }
    }, [childResponseData])  // 监听 responseData 的变化


    //详细日志
    const getLog = async (log_id: any, type: any = '') => {
        setLoading(true)
        const res = await reqMonitorLog({
            template_id: templateId,
            deploy_id: deployId,
            template_type: template_type,
            teamId,
            log_id: log_id,
            only_block_run_log: type === 'logDetail'
        })
        setLogData(res)
        setLoading(false)
    }

    const getGanttChart = async (trigger_code: any, log_id: any) => {
        setLoading(true)
        let res = await reqMonitorGantt({
            // trigger_code: trigger_code,
            template_id: templateId,
            deploy_id: deployId,
            template_type: template_type,
            // teamId,
            log_id
        })

        // res =  {
        //     "tasks": [
        //         {
        //             "taskName": "23081",
        //             "startDate": [
        //                 1733465828408
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "12888",
        //             "startDate": [
        //                 1733465828336
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "303857",
        //             "startDate": [
        //                 1733465828274
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465828179
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465821777
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465821632
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "685951",
        //             "startDate": [
        //                 1733465821508
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "486342",
        //             "startDate": [
        //                 1733465821507
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "449419",
        //             "startDate": [
        //                 1733465793722
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:33",
        //             "isoStart": "2024-12-06 14:16:33",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:30"
        //         },
        //         {
        //             "taskName": "477148",
        //             "startDate": [
        //                 1733465793721
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:33",
        //             "isoStart": "2024-12-06 14:16:33",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:30"
        //         },
        //         {
        //             "taskName": "869748",
        //             "startDate": [
        //                 1733465763141
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:03",
        //             "isoStart": "2024-12-06 14:16:03",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:00"
        //         },
        //         {
        //             "taskName": "734813",
        //             "startDate": [
        //                 1733465763140
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:03",
        //             "isoStart": "2024-12-06 14:16:03",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:00"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465763039
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:03",
        //             "isoStart": "2024-12-06 14:16:03",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:00"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465757799
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:57",
        //             "isoStart": "2024-12-06 14:15:57",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:06"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465756644
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "293067",
        //             "startDate": [
        //                 1733465756152
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "44120",
        //             "startDate": [
        //                 1733465756162
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "875758",
        //             "startDate": [
        //                 1733465756161
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "469887",
        //             "startDate": [
        //                 1733465756153
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "59674",
        //             "startDate": [
        //                 1733465756150
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "747901",
        //             "startDate": [
        //                 1733465756077
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "23081",
        //             "startDate": [
        //                 1733465395225
        //             ],
        //             "endDate": [
        //                 1733465828445
        //             ],
        //             "executionDate": "2024-12-06 14:09:55",
        //             "isoStart": "2024-12-06 14:09:55",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:13"
        //         },
        //         {
        //             "taskName": "12888",
        //             "startDate": [
        //                 1733465395149
        //             ],
        //             "endDate": [
        //                 1733465828374
        //             ],
        //             "executionDate": "2024-12-06 14:09:55",
        //             "isoStart": "2024-12-06 14:09:55",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:13"
        //         },
        //         {
        //             "taskName": "303857",
        //             "startDate": [
        //                 1733465395089
        //             ],
        //             "endDate": [
        //                 1733465828310
        //             ],
        //             "executionDate": "2024-12-06 14:09:55",
        //             "isoStart": "2024-12-06 14:09:55",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:13"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465394794
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:09:54",
        //             "isoStart": "2024-12-06 14:09:54",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:29:09"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465385472
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:29:18"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465385377
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:29:18"
        //         },
        //         {
        //             "taskName": "685951",
        //             "startDate": [
        //                 1733465385252
        //             ],
        //             "endDate": [
        //                 1733465828245
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:22"
        //         },
        //         {
        //             "taskName": "486342",
        //             "startDate": [
        //                 1733465385251
        //             ],
        //             "endDate": [
        //                 1733465824908
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2024-12-06 14:17:04",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:19"
        //         },
        //         {
        //             "taskName": "449419",
        //             "startDate": [
        //                 1733465365304
        //             ],
        //             "endDate": [
        //                 1733465821417
        //             ],
        //             "executionDate": "2024-12-06 14:09:25",
        //             "isoStart": "2024-12-06 14:09:25",
        //             "isoEnd": "2024-12-06 14:17:01",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:36"
        //         },
        //         {
        //             "taskName": "477148",
        //             "startDate": [
        //                 1733465365303
        //             ],
        //             "endDate": [
        //                 1733465795885
        //             ],
        //             "executionDate": "2024-12-06 14:09:25",
        //             "isoStart": "2024-12-06 14:09:25",
        //             "isoEnd": "2024-12-06 14:16:35",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:10"
        //         },
        //         {
        //             "taskName": "869748",
        //             "startDate": [
        //                 1733465334504
        //             ],
        //             "endDate": [
        //                 1733465793651
        //             ],
        //             "executionDate": "2024-12-06 14:08:54",
        //             "isoStart": "2024-12-06 14:08:54",
        //             "isoEnd": "2024-12-06 14:16:33",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:39"
        //         },
        //         {
        //             "taskName": "734813",
        //             "startDate": [
        //                 1733465334503
        //             ],
        //             "endDate": [
        //                 1733465763189
        //             ],
        //             "executionDate": "2024-12-06 14:08:54",
        //             "isoStart": "2024-12-06 14:08:54",
        //             "isoEnd": "2024-12-06 14:16:03",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:08"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465327825
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:08:47",
        //             "isoStart": "2024-12-06 14:08:47",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:30:16"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465321087
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:08:41",
        //             "isoStart": "2024-12-06 14:08:41",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:30:22"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465319969
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:30:23"
        //         },
        //         {
        //             "taskName": "875758",
        //             "startDate": [
        //                 1733465319513
        //             ],
        //             "endDate": [
        //                 1733465759009
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:59",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:19"
        //         },
        //         {
        //             "taskName": "44120",
        //             "startDate": [
        //                 1733465319514
        //             ],
        //             "endDate": [
        //                 1733465763118
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:16:03",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:23"
        //         },
        //         {
        //             "taskName": "469887",
        //             "startDate": [
        //                 1733465319510
        //             ],
        //             "endDate": [
        //                 1733465761301
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:16:01",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:21"
        //         },
        //         {
        //             "taskName": "59674",
        //             "startDate": [
        //                 1733465319507
        //             ],
        //             "endDate": [
        //                 1733465759474
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:59",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:19"
        //         },
        //         {
        //             "taskName": "293067",
        //             "startDate": [
        //                 1733465319509
        //             ],
        //             "endDate": [
        //                 1733465756615
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:56",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:17"
        //         },
        //         {
        //             "taskName": "747901",
        //             "startDate": [
        //                 1733465319443
        //             ],
        //             "endDate": [
        //                 1733465756119
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:56",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:16"
        //         },
        //         {
        //             "taskName": "569042",
        //             "startDate": [
        //                 1733465111911
        //             ],
        //             "endDate": [
        //                 1733465111944
        //             ],
        //             "executionDate": "2024-12-06 14:05:11",
        //             "isoStart": "2024-12-06 14:05:11",
        //             "isoEnd": "2024-12-06 14:05:11",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:00"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465109841
        //             ],
        //             "endDate": [
        //                 1733465828210
        //             ],
        //             "executionDate": "2024-12-06 14:05:09",
        //             "isoStart": "2024-12-06 14:05:09",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:11:58"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465102414
        //             ],
        //             "endDate": [
        //                 1733465828156
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:12:05"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465102274
        //             ],
        //             "endDate": [
        //                 1733465821700
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:17:01",
        //             "status": "SUCCESS",
        //             "duration": "00 00:11:59"
        //         },
        //         {
        //             "taskName": "400320",
        //             "startDate": [
        //                 1733465102209
        //             ],
        //             "endDate": [
        //                 1733465111838
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:05:11",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:09"
        //         },
        //         {
        //             "taskName": "304049",
        //             "startDate": [
        //                 1733465102207
        //             ],
        //             "endDate": [
        //                 1733465109956
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:05:09",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:07"
        //         },
        //         {
        //             "taskName": "777052",
        //             "startDate": [
        //                 1733465102133
        //             ],
        //             "endDate": [
        //                 1733465102177
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:05:02",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:00"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733456181768
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:36:21",
        //             "isoStart": "2024-12-06 11:36:21",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:02:42"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733456152297
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:35:52",
        //             "isoStart": "2024-12-06 11:35:52",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:03:11"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733456152185
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:35:52",
        //             "isoStart": "2024-12-06 11:35:52",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:03:11"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733455885814
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:31:25",
        //             "isoStart": "2024-12-06 11:31:25",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:07:38"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733455861979
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:31:01",
        //             "isoStart": "2024-12-06 11:31:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:08:01"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733455861823
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:31:01",
        //             "isoStart": "2024-12-06 11:31:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:08:02"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733455637133
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:27:17",
        //             "isoStart": "2024-12-06 11:27:17",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:11:46"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733455613561
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:26:53",
        //             "isoStart": "2024-12-06 11:26:53",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:12:10"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733455613449
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:26:53",
        //             "isoStart": "2024-12-06 11:26:53",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:12:10"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733454314135
        //             ],
        //             "endDate": [
        //                 1733456181804
        //             ],
        //             "executionDate": "2024-12-06 11:05:14",
        //             "isoStart": "2024-12-06 11:05:14",
        //             "isoEnd": "2024-12-06 11:36:21",
        //             "status": "SUCCESS",
        //             "duration": "00 00:31:07"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733454294013
        //             ],
        //             "endDate": [
        //                 1733456181753
        //             ],
        //             "executionDate": "2024-12-06 11:04:54",
        //             "isoStart": "2024-12-06 11:04:54",
        //             "isoEnd": "2024-12-06 11:36:21",
        //             "status": "SUCCESS",
        //             "duration": "00 00:31:27"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733454293928
        //             ],
        //             "endDate": [
        //                 1733456152277
        //             ],
        //             "executionDate": "2024-12-06 11:04:53",
        //             "isoStart": "2024-12-06 11:04:53",
        //             "isoEnd": "2024-12-06 11:35:52",
        //             "status": "SUCCESS",
        //             "duration": "00 00:30:58"
        //         }
        //     ],
        //     "height": 0,
        //     "taskStatus": {
        //         "success": "success"
        //     },
        //     "ds_id": "暂无"
        // }

        const seriesData = res?.tasks || [];

        setLoading(false)
        setDsId(res?.ds_id == '暂无' ? '' : res?.ds_id)

        const echarts = await import('../../utils/echarts')
        const chartDom = document.getElementById('gantt-container');

        const taskList = seriesData.map((s: any) => { return s.taskName })

        const state: any = {
            SUBMITTED_SUCCESS: {
                id: 0,
                desc: '提交成功',
                color: '#A9A9A9',
                icon: '',
                isSpin: false,
                classNames: 'submitted_success'
            },
            RUNNING_EXECUTION: {
                id: 1,
                desc: '正在执行',
                color: '#0097e0',
                icon: '',
                isSpin: true,
                classNames: 'running_execution'
            },
            RUNNING: {
                id: 1,
                desc: '正在执行',
                color: '#0097e0',
                icon: '',
                isSpin: true,
                classNames: 'running_execution'
            },
            PAUSE: {
                id: 3,
                desc: '暂停',
                color: '#057c72',
                icon: '',
                isSpin: false,
                classNames: 'pause'
            },
            FAILURE: {
                id: 6,
                desc: '失败',
                color: '#000000',
                icon: '',
                isSpin: false,
                classNames: 'failed'
            },
            SUCCESS: {
                id: 7,
                desc: '成功',
                color: '#95DF96',
                icon: '',
                isSpin: false,
                classNames: 'success'
            },
            NEED_FAULT_TOLERANCE: {
                id: 8,
                desc: '需要容错',
                color: '#FF8C00',
                icon: '',
                isSpin: false,
                classNames: 'need_fault_tolerance'
            },
            KILL: {
                id: 9,
                desc: 'Kill',
                color: '#a70202',
                icon: '',
                isSpin: false,
                classNames: 'kill'
            },
            DELAY_EXECUTION: {
                id: 12,
                desc: '延时执行',
                color: '#5102ce',
                icon: '',
                isSpin: false,
                classNames: 'delay_execution'
            },
            FORCED_SUCCESS: {
                id: 13,
                desc: '强制成功',
                color: '#5102ce',
                icon: '',
                isSpin: false,
                classNames: 'forced_success'
            },
            DISPATCH: {
                id: 17,
                desc: '派发',
                color: '#5101be',
                icon: '',
                isSpin: false,
                classNames: 'dispatch'
            }
        }

        const data: any = {}
        Object.keys(state).forEach((key) => (data[key] = []))
        const series = Object.keys(state).map((key) => ({
            id: key,
            type: 'custom',
            name: state[key].desc,
            renderItem: renderItem,
            itemStyle: {
                opacity: 0.8,
                color: state[key].color,
                color0: state[key].color
            },
            encode: {
                x: [1, 2],
                y: 0
            },
            data: data[key]
        }))

        // format series data
        let minTime = Number.MAX_VALUE
        let maxTime = 0
        seriesData.forEach(function (task: any, index: any) {
            const start = Math.floor(task.startDate[0] / 1000) * 1000
            const end = Math.floor(task.endDate[0] / 1000) * 1000
            minTime = minTime < start ? minTime : start
            maxTime = maxTime > end ? maxTime : end
            data[task.status].push({
                name: task.taskName,
                value: [index, start, end, end - start],
                itemStyle: {
                    color: state[task.status].color
                }
            })
        })

        // customer render
        function renderItem(params: any, api: any) {
            const taskIndex = api.value(0)
            const start = api.coord([api.value(1), taskIndex])
            const end = api.coord([api.value(2), taskIndex])
            const height = api.size([0, 1])[1] * 0.6
            const rectShape = echarts.graphic.clipRectByRect(
                {
                    x: start[0],
                    y: start[1] - height / 2,
                    width: Math.max(end[0] - start[0], 1) || 1,
                    height: height
                },
                {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height
                }
            )
            return (
                rectShape && {
                    type: 'rect',
                    transition: ['shape'],
                    shape: rectShape,
                    style: api.style()
                }
            )
        }

        const option = {
            title: {
                text: '任务状态',
                textStyle: {
                    fontWeight: 'normal',
                    fontSize: 14
                },
                left: 50
            },
            tooltip: {
                formatter: function (params: any) {
                    const taskName = params.data.name
                    const data = seriesData.filter(
                        (item: any) => item.taskName === taskName
                    )
                    let str = `taskName : ${taskName}</br>`
                    str += `status : ${state[data[0].status].desc} (${data[0].status
                        })</br>`
                    str += `startTime : ${data[0].isoStart}</br>`
                    str += `endTime : ${data[0].isoEnd}</br>`
                    str += `duration : ${data[0].duration}</br>`
                    return str
                }
            },
            legend: {
                left: 150,
                padding: [5, 5, 5, 5],
                borderRadius: 0
            },
            dataZoom: [
                {
                    type: 'slider',
                    xAxisIndex: 0,
                    filterMode: 'weakFilter',
                    height: 20,
                    bottom: 0,
                    start: 0,
                    end: 100,
                    handleSize: '80%',
                    showDetail: false,
                    top: '85%'
                },
                {
                    type: 'inside',
                    filterMode: 'weakFilter'
                }
            ],
            grid: {
                height: '70%',
                top: 80
            },
            xAxis: {
                type: 'time',
                min: minTime,
                max: maxTime - minTime > 5000 ? maxTime + 1000 : minTime + 5000,
                position: 'top',
                axisTick: { show: true },
                splitLine: { show: false },
                axisLabel: {
                    formatter: '{HH}:{mm}:{ss}',
                    showMinLabel: true,
                    showMaxLabel: true,
                    hideOverlap: true
                }
            },
            yAxis: {
                axisTick: { show: false },
                splitLine: { show: false },
                axisLine: { show: false },
                data: taskList.map((item: any) => {
                    return {
                        value: item,
                        textStyle: {
                            width: 130,
                            overflow: 'truncate'
                        }
                    }
                })
            },
            series: series
        }

        var myChart = echarts.init(chartDom, {}, {});
        myChart.setOption(option);
    }

    return (
        <>
            <div className={styles.commonContent}>
                <div className={styles.top} style={{ border: 0 }}>
                    <div className={styles.title}>
                        <div className={operationListStyles.promptTitle} >
                            <img src={leftBtn.src} className={styles.leftBtn} onClick={() => {
                                Router.push('./monitor?deployId=' + deployId + '&templateId=' + templateId + '&teamId=' + teamId + '&template_type=' + template_type)
                            }} />
                            <span className={styles.breadTitle}>详情</span>
                            <span style={{ marginLeft: '16px', fontSize: '14px', color: '#626f84' }}>callbackId:</span>
                            {/* <span style={{ fontSize: '14px', color: '#626f84' }}>{logId}</span> */}
                            <Input
                                style={{ width: '320px', marginLeft: '8px' }}
                                placeholder="请输入callbackId回车搜索"
                                value={logId}
                                onChange={(e: any) => {
                                    setLogId(e.target.value)
                                }}
                                onPressEnter={(e: any) => {
                                    getDetail(e.target.value, 1)
                                }}
                            />
                        </div>
                    </div>
                </div>
                <div style={{ paddingBottom: 1 }}>
                    <div className={operationListStyles.monitorLogTable}>
                        <div className={styles.title}>
                            <div className={operationListStyles.promptTitle} style={{ fontSize: '16px' }}>
                                <span>主流程</span>
                                <span style={{ marginLeft: '16px', fontSize: '14px', color: '#626f84' }}>flow_id:</span>
                                <span style={{ fontSize: '14px', color: '#626f84' }}>{templateId}</span>
                            </div>
                            <div style={{ marginLeft: 'auto' }}>
                                <Button type="primary" className={commonOuterStyle.btn} style={{ background: '#1D7CFF', borderRadius: '4px' }} onClick={() => { setOpenLog(true); getLog(logId) }}>详细日志</Button>
                                <Button type="primary" className={commonOuterStyle.btn} style={{ background: '#1D7CFF', borderRadius: '4px', marginLeft: '16px' }} onClick={() => { setOpenGantt(true); getGanttChart(triggerCode, logId) }}>甘特图</Button>
                            </div>
                        </div>
                        <Spin spinning={loading}>
                            <div className={commonListStyles.tableList + ' normalFont ' + operationListStyles.monitorTable}>
                                <Table
                                    columns={columns}
                                    dataSource={detailData}
                                    pagination={false}
                                    className='commonTable normalFont logTable'
                                    scroll={{ y: tableHeight }}
                                />
                            </div>
                        </Spin>
                    </div>
                    {isChildData && <div className={operationListStyles.monitorLogTable}>
                        <div className={styles.title}>
                            <div className={operationListStyles.promptTitle} style={{ fontSize: '16px' }}>
                                <span>子流程</span>
                                <span style={{ marginLeft: '16px', fontSize: '14px', color: '#626f84' }}>flow_id:</span>
                                <span style={{ fontSize: '14px', color: '#626f84' }}>{curMainId?.split('-')[0]}</span>
                            </div>
                        </div>
                        <Spin spinning={loading}>
                            <div className={commonListStyles.tableList + ' normalFont ' + operationListStyles.monitorTable}>
                                <Table
                                    columns={childColumns}
                                    dataSource={childDetailData}
                                    pagination={false}
                                    className='commonTable normalFont logTable'
                                    scroll={{ y: tableHeight }}
                                />
                            </div>
                        </Spin>
                    </div>
                    }
                </div>
            </div>
            <Modal
                title="详情"
                open={openDetail}
                className={operationListStyles.logModal + ' commonModal noFooterModal'}
                onCancel={() => { setOpenDetail(false) }}
                footer={<></>}
            >
                <div className={operationListStyles.monitorLog}>
                    <Spin spinning={loading} className='gantt-container'>
                        {putData && putData.length > 0 && (
                            <div>
                                {isLog && <div className={styles.textAreaWrapper}>{isLog && (<div className={operationListStyles.logWrapper}>{putData.map(log => {
                                    return <div className={operationListStyles.logItem} key={log}>
                                        <div className={operationListStyles.logTitle}> {log.timestamp} {log.host} </div>
                                        <div>{log.message}</div>
                                    </div>
                                })}</div>)}</div>}
                                {
                                    !isLog &&
                                    <div className={styles.textAreaWrapper}>
                                        <div id={"jsoneditor-789"} className={apiStyles.jsoneditor}></div>
                                    </div>
                                }
                            </div>
                        )}
                    </Spin>
                </div>
            </Modal>
            <Modal
                title="日志"
                open={openLog}
                className={operationListStyles.logModal + ' commonModal noFooterModal'}
                onCancel={() => { setOpenLog(false) }}
                footer={<></>}
            >
                <div className={operationListStyles.monitorLog}>
                    <Spin spinning={loading} className='gantt-container'>
                        <div className={operationListStyles.monitorLogTitle}> 近50条被调用的日志内容：</div>
                        {logData && logData.length > 0 && (
                            <div className={operationListStyles.logWrapper}>{logData.map(log => {
                                return <div className={operationListStyles.logItem}>
                                    <div className={operationListStyles.logTitle}> {log.timestamp} {log.host} </div>
                                    <div>{log.message}</div>
                                </div>
                            })}</div>
                        )}
                    </Spin>
                </div>
            </Modal>
            <Modal
                open={openGantt}
                className={operationListStyles.detailModal + ' commonModal noFooterModal'}
                onCancel={() => setOpenGantt(false)}
                title={<>甘特图{dsId ? '（' + dsId + '）' : ''}{dsId ? <img src={copyBtn.src} onClick={() => {
                    dsId && copyParamValue(dsId)
                }} style={{ margin: '-4px 0 0 5px', cursor: 'pointer' }} /> : ''}</>}
                footer={<></>}
            >
                <Spin spinning={loading} className='gantt-container'>
                    <div id='gantt-container' style={{ width: 1170, height: 600 }}></div>
                </Spin>
            </Modal>
        </>
    )
}