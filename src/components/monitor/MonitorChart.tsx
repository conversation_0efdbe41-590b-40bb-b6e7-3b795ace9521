import { useEffect, useState } from 'react';
import { Empty, Select, Table, Modal, Input, Spin } from 'antd';

import { reqMonitorBar, reqMonitorLog, reqMonitorGantt, reqMonitorList } from '@/service/monitor';

import Router, { useRouter } from 'next/router'

import operationListStyles from '@/styles/OperationList.module.scss'
import styles from '@/styles/Common.module.scss'
import commonListStyles from '@/styles/CommonList.module.scss'

import leftBtn from '@/images/common/leftBtn.svg'
import copyBtn from '@/images/commonList/copy.svg'

import { copyParamValue } from '../../components/flow3.0/flowFunc';

export default function Monitor() {

    const router = useRouter()
    const templateId = router.query.templateId;
    const teamId = router.query.teamId;
    const deployId = router.query.deployId;
    const template_type = router.query.template_type


    const [pageSize, setPageSize] = useState(10)
    const [total, setTotal] = useState(0)
    const [curPage, setCurPage] = useState(1)
    const [openLog, setOpenLog] = useState(false)
    const [openDetail, setopenDetail] = useState(false)
    const [loading, setLoading] = useState(false)
    const [chartLoading, setChartLoading] = useState(false)

    const [dsId, setDsId] = useState('')
    const [chartData, setChartData] = useState(new Array());
    const [logData, setLogData] = useState(new Array());
    const [list, setList] = useState(new Array());
    const [intervalType, setIntervalType] = useState(5);
    const [deployInfo, setDeployInfo] = useState({
        chain_name: '',
        team_id: '',
        chain_type: 1
    })
    const [logId, setLogId] = useState('')
    const [logDetail, setLogDetail] = useState(false)
    const [accessStatus, setAccessStatus] = useState<any>('')


    const getList = async () => {
        if (templateId) {
            setLoading(true)
            let data: any = {
                template_id: templateId,
                deploy_id: deployId,
                template_type: template_type,
                teamId,
                page: curPage,
                page_size: pageSize,
                log_id: logId,
            }
            if(accessStatus != '') {
                data['access_status'] = accessStatus
            }
            const list = await reqMonitorList(data)
            if (list) {
                setTotal(list.total)
                setList(list.data)
            }
            setLoading(false)
        }
    }

    useEffect(() => {
        getList()
    }, [curPage, pageSize, logId, accessStatus])

    const getMonitor = async () => {
        setChartLoading(true)
        const res = await reqMonitorBar({
            template_id: templateId,
            deploy_id: deployId,
            interval: intervalType,
            template_type: template_type,
            teamId
        })
        setChartData(res?.list)
        res && setDeployInfo({
            chain_name: res.data.chain_name,
            team_id: res.data.team_id,
            chain_type: res.data.template_type
        })
        setChartLoading(false)
        getList()
    }

    useEffect(() => {
        if (templateId) {
            getMonitor()
        }
    }, [templateId, intervalType])

    const getChart = async () => {
        const G2Plot = await import('../../utils/g2plot')
        const { Column } = G2Plot;
        const stackedColumnPlot = new Column('container', {
            data: chartData,
            isGroup: true,
            height: 330,
            xField: 'date',
            yField: 'count',
            seriesField: 'name',
            /** 设置颜色 */
            color: ['#47A1FF', '#74E0B7', '#FFCD58', '#BFC9D5', '#FF5E68'],
            /** 设置间距 */
            marginRatio: 0.1,
            label: {
                // 可手动配置 label 数据标签位置
                position: 'middle', // 'top', 'middle', 'bottom'
                // 可配置附加的布局方法
                layout: [
                    // 柱形图数据标签位置自动调整
                    { type: 'interval-adjust-position' },
                    // 数据标签防遮挡
                    { type: 'interval-hide-overlap' },
                    // 数据标签文颜色自动调整
                    { type: 'adjust-color' },
                ]
            },
            legend: { position: 'top' }
        });
        stackedColumnPlot.render();
    }

    useEffect(() => {
        document.getElementById('container')!.innerHTML = ''
        if (!chartData?.length) {
            return;
        }
        getChart()
    }, [chartData?.length])

    const getStr = (num: any) => {
        return num < 10 ? '0' + num : num
    }

    const getTimeStr = (t: any) => {
        const date = new Date(t * 1000);
        return date.getFullYear() + '-' + getStr(date.getMonth() + 1) + '-' + getStr(date.getDate()) + ' ' + getStr(date.getHours()) + ':' + getStr(date.getMinutes()) + ':' + getStr(date.getSeconds())
    }
    const getLog = async (log_id: any, type: any = '') => {
        setLoading(true)
        const res = await reqMonitorLog({
            template_id: templateId,
            deploy_id: deployId,
            template_type: template_type,
            teamId,
            log_id: log_id,
            only_block_run_log: type === 'logDetail'
        })
        setLogData(res)
        setLoading(false)
    }


    const columns = [
        {
            title: '序号',
            dataIndex: 'id',
            key: 'id',
            width: 100,
            render: (_: any, record: any, index: any) => {
                return index + 1
            }

        },
        {
            title: '渠道',
            dataIndex: 'channel',
            width: 100,
            key: 'channel'
        },
        {
            title: '执行名称',
            dataIndex: 'log_id',
            width: 100,
            key: 'log_id'
        },
        {
            title: '请求方式',
            dataIndex: 'trigger_type',
            width: 100,
            key: 'trigger_type',
            render: (t: any) => {
                const typeObj: any = {
                    '0': '默认',
                    '1': 'api',
                    '2': '定时',
                    '3': 'webhook',
                }
                return typeObj[t]
            }
        },
        {
            title: '执行状态',
            dataIndex: 'access_status',
            width: 100,
            key: 'access_status',
            render: (s: any) => {
                //  0:其他 1:成功 2:失败 3:取消 4.异常
                const statusObj: any = {
                    '0': '运行中',
                    '1': '成功',
                    '2': '失败',
                    '3': '取消',
                    '4': '异常',
                }
                return statusObj[s]
            }
        },
        {
            title: '开始时间',
            dataIndex: 'start_time',
            width: 100,
            key: 'start_time',
            render: (t: any) => {
                return getTimeStr(t)
            }
        },
        {
            title: '结束时间',
            dataIndex: 'end_time',
            width: 100,
            key: 'end_time',
            render: (t: any) => {
                return getTimeStr(t)
            }
        },
        {
            title: '运动时长',
            dataIndex: 'timeCount',
            width: 100,
            key: 'timeCount',
            render: (_: any, record: any) => {
                // 秒
                if (record.end_time == 0) {
                    return '0秒'
                }
                const timeCount = record.end_time - record.start_time;
                let resTimeCount;
                const hourNum = 60 * 60
                const minuteNum = 60
                if (timeCount > hourNum) {
                    const hour = Math.floor(timeCount / hourNum);
                    const minute = Math.floor((timeCount - hour * hourNum) / minuteNum);
                    resTimeCount = hour + '时' + minute + '分' + (timeCount - hour * hourNum - minute * minuteNum) + '秒'
                } else if (timeCount > minuteNum) {
                    const minute = Math.floor(timeCount / minuteNum);
                    resTimeCount = minute + '分' + (timeCount - minute * minuteNum) + '秒'
                } else {
                    resTimeCount = timeCount + '秒'
                }
                return resTimeCount;
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 100,
            key: 'operation',
            render: (_: any, record: any, index: any) => {
                return <><span className={operationListStyles.logBtn} onClick={() => {
                    setOpenLog(true)
                    getLog(record.log_id)
                }}>日志</span>
                    <span className={operationListStyles.logBtn} onClick={() => {
                        Router.push('./monitorDetail?deployId=' + deployId + '&templateId=' + templateId + '&logId=' + record.log_id + '&triggerCode=' + record.trigger_code + '&teamId=' + teamId + '&template_type=' + template_type)
                        // setOpenLog(true)
                        // getDetail(record.log_id);
                        setLogDetail(true);
                    }}>详情</span>
                    <span className={operationListStyles.oDetailBtn} onClick={() => {
                        setopenDetail(true)
                        // document.getElementById('gantt-container')!.innerHTML= ''
                        getGanttChart(record.trigger_code, record.log_id)
                    }}>甘特图</span>
                </>
            }
        }
    ]

    const getGanttChart = async (trigger_code: any, log_id: any) => {
        setLoading(true)
        let res = await reqMonitorGantt({
            // trigger_code: trigger_code,
            template_id: templateId,
            deploy_id: deployId,
            template_type: template_type,
            // teamId,
            log_id
        })

        // res =  {
        //     "tasks": [
        //         {
        //             "taskName": "23081",
        //             "startDate": [
        //                 1733465828408
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "12888",
        //             "startDate": [
        //                 1733465828336
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "303857",
        //             "startDate": [
        //                 1733465828274
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465828179
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:08",
        //             "isoStart": "2024-12-06 14:17:08",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:21:55"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465821777
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465821632
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "685951",
        //             "startDate": [
        //                 1733465821508
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "486342",
        //             "startDate": [
        //                 1733465821507
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:17:01",
        //             "isoStart": "2024-12-06 14:17:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:02"
        //         },
        //         {
        //             "taskName": "449419",
        //             "startDate": [
        //                 1733465793722
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:33",
        //             "isoStart": "2024-12-06 14:16:33",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:30"
        //         },
        //         {
        //             "taskName": "477148",
        //             "startDate": [
        //                 1733465793721
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:33",
        //             "isoStart": "2024-12-06 14:16:33",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:22:30"
        //         },
        //         {
        //             "taskName": "869748",
        //             "startDate": [
        //                 1733465763141
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:03",
        //             "isoStart": "2024-12-06 14:16:03",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:00"
        //         },
        //         {
        //             "taskName": "734813",
        //             "startDate": [
        //                 1733465763140
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:03",
        //             "isoStart": "2024-12-06 14:16:03",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:00"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465763039
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:16:03",
        //             "isoStart": "2024-12-06 14:16:03",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:00"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465757799
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:57",
        //             "isoStart": "2024-12-06 14:15:57",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:06"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465756644
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "293067",
        //             "startDate": [
        //                 1733465756152
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "44120",
        //             "startDate": [
        //                 1733465756162
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "875758",
        //             "startDate": [
        //                 1733465756161
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "469887",
        //             "startDate": [
        //                 1733465756153
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "59674",
        //             "startDate": [
        //                 1733465756150
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "747901",
        //             "startDate": [
        //                 1733465756077
        //             ],
        //             "endDate": [
        //                 1737441543862
        //             ],
        //             "executionDate": "2024-12-06 14:15:56",
        //             "isoStart": "2024-12-06 14:15:56",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:23:07"
        //         },
        //         {
        //             "taskName": "23081",
        //             "startDate": [
        //                 1733465395225
        //             ],
        //             "endDate": [
        //                 1733465828445
        //             ],
        //             "executionDate": "2024-12-06 14:09:55",
        //             "isoStart": "2024-12-06 14:09:55",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:13"
        //         },
        //         {
        //             "taskName": "12888",
        //             "startDate": [
        //                 1733465395149
        //             ],
        //             "endDate": [
        //                 1733465828374
        //             ],
        //             "executionDate": "2024-12-06 14:09:55",
        //             "isoStart": "2024-12-06 14:09:55",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:13"
        //         },
        //         {
        //             "taskName": "303857",
        //             "startDate": [
        //                 1733465395089
        //             ],
        //             "endDate": [
        //                 1733465828310
        //             ],
        //             "executionDate": "2024-12-06 14:09:55",
        //             "isoStart": "2024-12-06 14:09:55",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:13"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465394794
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:09:54",
        //             "isoStart": "2024-12-06 14:09:54",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:29:09"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465385472
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:29:18"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465385377
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:29:18"
        //         },
        //         {
        //             "taskName": "685951",
        //             "startDate": [
        //                 1733465385252
        //             ],
        //             "endDate": [
        //                 1733465828245
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:22"
        //         },
        //         {
        //             "taskName": "486342",
        //             "startDate": [
        //                 1733465385251
        //             ],
        //             "endDate": [
        //                 1733465824908
        //             ],
        //             "executionDate": "2024-12-06 14:09:45",
        //             "isoStart": "2024-12-06 14:09:45",
        //             "isoEnd": "2024-12-06 14:17:04",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:19"
        //         },
        //         {
        //             "taskName": "449419",
        //             "startDate": [
        //                 1733465365304
        //             ],
        //             "endDate": [
        //                 1733465821417
        //             ],
        //             "executionDate": "2024-12-06 14:09:25",
        //             "isoStart": "2024-12-06 14:09:25",
        //             "isoEnd": "2024-12-06 14:17:01",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:36"
        //         },
        //         {
        //             "taskName": "477148",
        //             "startDate": [
        //                 1733465365303
        //             ],
        //             "endDate": [
        //                 1733465795885
        //             ],
        //             "executionDate": "2024-12-06 14:09:25",
        //             "isoStart": "2024-12-06 14:09:25",
        //             "isoEnd": "2024-12-06 14:16:35",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:10"
        //         },
        //         {
        //             "taskName": "869748",
        //             "startDate": [
        //                 1733465334504
        //             ],
        //             "endDate": [
        //                 1733465793651
        //             ],
        //             "executionDate": "2024-12-06 14:08:54",
        //             "isoStart": "2024-12-06 14:08:54",
        //             "isoEnd": "2024-12-06 14:16:33",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:39"
        //         },
        //         {
        //             "taskName": "734813",
        //             "startDate": [
        //                 1733465334503
        //             ],
        //             "endDate": [
        //                 1733465763189
        //             ],
        //             "executionDate": "2024-12-06 14:08:54",
        //             "isoStart": "2024-12-06 14:08:54",
        //             "isoEnd": "2024-12-06 14:16:03",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:08"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465327825
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:08:47",
        //             "isoStart": "2024-12-06 14:08:47",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:30:16"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465321087
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:08:41",
        //             "isoStart": "2024-12-06 14:08:41",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:30:22"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465319969
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 00:30:23"
        //         },
        //         {
        //             "taskName": "875758",
        //             "startDate": [
        //                 1733465319513
        //             ],
        //             "endDate": [
        //                 1733465759009
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:59",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:19"
        //         },
        //         {
        //             "taskName": "44120",
        //             "startDate": [
        //                 1733465319514
        //             ],
        //             "endDate": [
        //                 1733465763118
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:16:03",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:23"
        //         },
        //         {
        //             "taskName": "469887",
        //             "startDate": [
        //                 1733465319510
        //             ],
        //             "endDate": [
        //                 1733465761301
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:16:01",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:21"
        //         },
        //         {
        //             "taskName": "59674",
        //             "startDate": [
        //                 1733465319507
        //             ],
        //             "endDate": [
        //                 1733465759474
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:59",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:19"
        //         },
        //         {
        //             "taskName": "293067",
        //             "startDate": [
        //                 1733465319509
        //             ],
        //             "endDate": [
        //                 1733465756615
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:56",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:17"
        //         },
        //         {
        //             "taskName": "747901",
        //             "startDate": [
        //                 1733465319443
        //             ],
        //             "endDate": [
        //                 1733465756119
        //             ],
        //             "executionDate": "2024-12-06 14:08:39",
        //             "isoStart": "2024-12-06 14:08:39",
        //             "isoEnd": "2024-12-06 14:15:56",
        //             "status": "SUCCESS",
        //             "duration": "00 00:07:16"
        //         },
        //         {
        //             "taskName": "569042",
        //             "startDate": [
        //                 1733465111911
        //             ],
        //             "endDate": [
        //                 1733465111944
        //             ],
        //             "executionDate": "2024-12-06 14:05:11",
        //             "isoStart": "2024-12-06 14:05:11",
        //             "isoEnd": "2024-12-06 14:05:11",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:00"
        //         },
        //         {
        //             "taskName": "860691",
        //             "startDate": [
        //                 1733465109841
        //             ],
        //             "endDate": [
        //                 1733465828210
        //             ],
        //             "executionDate": "2024-12-06 14:05:09",
        //             "isoStart": "2024-12-06 14:05:09",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:11:58"
        //         },
        //         {
        //             "taskName": "376430",
        //             "startDate": [
        //                 1733465102414
        //             ],
        //             "endDate": [
        //                 1733465828156
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:17:08",
        //             "status": "SUCCESS",
        //             "duration": "00 00:12:05"
        //         },
        //         {
        //             "taskName": "575899",
        //             "startDate": [
        //                 1733465102274
        //             ],
        //             "endDate": [
        //                 1733465821700
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:17:01",
        //             "status": "SUCCESS",
        //             "duration": "00 00:11:59"
        //         },
        //         {
        //             "taskName": "400320",
        //             "startDate": [
        //                 1733465102209
        //             ],
        //             "endDate": [
        //                 1733465111838
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:05:11",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:09"
        //         },
        //         {
        //             "taskName": "304049",
        //             "startDate": [
        //                 1733465102207
        //             ],
        //             "endDate": [
        //                 1733465109956
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:05:09",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:07"
        //         },
        //         {
        //             "taskName": "777052",
        //             "startDate": [
        //                 1733465102133
        //             ],
        //             "endDate": [
        //                 1733465102177
        //             ],
        //             "executionDate": "2024-12-06 14:05:02",
        //             "isoStart": "2024-12-06 14:05:02",
        //             "isoEnd": "2024-12-06 14:05:02",
        //             "status": "SUCCESS",
        //             "duration": "00 00:00:00"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733456181768
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:36:21",
        //             "isoStart": "2024-12-06 11:36:21",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:02:42"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733456152297
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:35:52",
        //             "isoStart": "2024-12-06 11:35:52",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:03:11"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733456152185
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:35:52",
        //             "isoStart": "2024-12-06 11:35:52",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:03:11"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733455885814
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:31:25",
        //             "isoStart": "2024-12-06 11:31:25",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:07:38"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733455861979
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:31:01",
        //             "isoStart": "2024-12-06 11:31:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:08:01"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733455861823
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:31:01",
        //             "isoStart": "2024-12-06 11:31:01",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:08:02"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733455637133
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:27:17",
        //             "isoStart": "2024-12-06 11:27:17",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:11:46"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733455613561
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:26:53",
        //             "isoStart": "2024-12-06 11:26:53",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:12:10"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733455613449
        //             ],
        //             "endDate": [
        //                 1737441543863
        //             ],
        //             "executionDate": "2024-12-06 11:26:53",
        //             "isoStart": "2024-12-06 11:26:53",
        //             "isoEnd": "2025-01-21 14:39:03",
        //             "status": "RUNNING",
        //             "duration": "46 03:12:10"
        //         },
        //         {
        //             "taskName": "427441",
        //             "startDate": [
        //                 1733454314135
        //             ],
        //             "endDate": [
        //                 1733456181804
        //             ],
        //             "executionDate": "2024-12-06 11:05:14",
        //             "isoStart": "2024-12-06 11:05:14",
        //             "isoEnd": "2024-12-06 11:36:21",
        //             "status": "SUCCESS",
        //             "duration": "00 00:31:07"
        //         },
        //         {
        //             "taskName": "299205",
        //             "startDate": [
        //                 1733454294013
        //             ],
        //             "endDate": [
        //                 1733456181753
        //             ],
        //             "executionDate": "2024-12-06 11:04:54",
        //             "isoStart": "2024-12-06 11:04:54",
        //             "isoEnd": "2024-12-06 11:36:21",
        //             "status": "SUCCESS",
        //             "duration": "00 00:31:27"
        //         },
        //         {
        //             "taskName": "80346",
        //             "startDate": [
        //                 1733454293928
        //             ],
        //             "endDate": [
        //                 1733456152277
        //             ],
        //             "executionDate": "2024-12-06 11:04:53",
        //             "isoStart": "2024-12-06 11:04:53",
        //             "isoEnd": "2024-12-06 11:35:52",
        //             "status": "SUCCESS",
        //             "duration": "00 00:30:58"
        //         }
        //     ],
        //     "height": 0,
        //     "taskStatus": {
        //         "success": "success"
        //     },
        //     "ds_id": "暂无"
        // }

        const seriesData = res?.tasks || [];

        setLoading(false)
        setDsId(res?.ds_id == '暂无' ? '' : res?.ds_id)

        const echarts = await import('../../utils/echarts')
        const chartDom = document.getElementById('gantt-container');

        const taskList = seriesData.map((s: any) => { return s.taskName })

        const state: any = {
            SUBMITTED_SUCCESS: {
                id: 0,
                desc: '提交成功',
                color: '#A9A9A9',
                icon: '',
                isSpin: false,
                classNames: 'submitted_success'
            },
            RUNNING_EXECUTION: {
                id: 1,
                desc: '正在执行',
                color: '#0097e0',
                icon: '',
                isSpin: true,
                classNames: 'running_execution'
            },
            RUNNING: {
                id: 1,
                desc: '正在执行',
                color: '#0097e0',
                icon: '',
                isSpin: true,
                classNames: 'running_execution'
            },
            PAUSE: {
                id: 3,
                desc: '暂停',
                color: '#057c72',
                icon: '',
                isSpin: false,
                classNames: 'pause'
            },
            FAILURE: {
                id: 6,
                desc: '失败',
                color: '#000000',
                icon: '',
                isSpin: false,
                classNames: 'failed'
            },
            SUCCESS: {
                id: 7,
                desc: '成功',
                color: '#95DF96',
                icon: '',
                isSpin: false,
                classNames: 'success'
            },
            NEED_FAULT_TOLERANCE: {
                id: 8,
                desc: '需要容错',
                color: '#FF8C00',
                icon: '',
                isSpin: false,
                classNames: 'need_fault_tolerance'
            },
            KILL: {
                id: 9,
                desc: 'Kill',
                color: '#a70202',
                icon: '',
                isSpin: false,
                classNames: 'kill'
            },
            DELAY_EXECUTION: {
                id: 12,
                desc: '延时执行',
                color: '#5102ce',
                icon: '',
                isSpin: false,
                classNames: 'delay_execution'
            },
            FORCED_SUCCESS: {
                id: 13,
                desc: '强制成功',
                color: '#5102ce',
                icon: '',
                isSpin: false,
                classNames: 'forced_success'
            },
            DISPATCH: {
                id: 17,
                desc: '派发',
                color: '#5101be',
                icon: '',
                isSpin: false,
                classNames: 'dispatch'
            }
        }

        const data: any = {}
        Object.keys(state).forEach((key) => (data[key] = []))
        const series = Object.keys(state).map((key) => ({
            id: key,
            type: 'custom',
            name: state[key].desc,
            renderItem: renderItem,
            itemStyle: {
                opacity: 0.8,
                color: state[key].color,
                color0: state[key].color
            },
            encode: {
                x: [1, 2],
                y: 0
            },
            data: data[key]
        }))

        // format series data
        let minTime = Number.MAX_VALUE
        let maxTime = 0
        seriesData.forEach(function (task: any, index: any) {
            const start = Math.floor(task.startDate[0] / 1000) * 1000
            const end = Math.floor(task.endDate[0] / 1000) * 1000
            minTime = minTime < start ? minTime : start
            maxTime = maxTime > end ? maxTime : end
            data[task.status].push({
                name: task.taskName,
                value: [index, start, end, end - start],
                itemStyle: {
                    color: state[task.status].color
                }
            })
        })

        // customer render
        function renderItem(params: any, api: any) {
            const taskIndex = api.value(0)
            const start = api.coord([api.value(1), taskIndex])
            const end = api.coord([api.value(2), taskIndex])
            const height = api.size([0, 1])[1] * 0.6
            const rectShape = echarts.graphic.clipRectByRect(
                {
                    x: start[0],
                    y: start[1] - height / 2,
                    width: Math.max(end[0] - start[0], 1) || 1,
                    height: height
                },
                {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height
                }
            )
            return (
                rectShape && {
                    type: 'rect',
                    transition: ['shape'],
                    shape: rectShape,
                    style: api.style()
                }
            )
        }

        const option = {
            title: {
                text: '任务状态',
                textStyle: {
                    fontWeight: 'normal',
                    fontSize: 14
                },
                left: 50
            },
            tooltip: {
                formatter: function (params: any) {
                    const taskName = params.data.name
                    const data = seriesData.filter(
                        (item: any) => item.taskName === taskName
                    )
                    let str = `taskName : ${taskName}</br>`
                    str += `status : ${state[data[0].status].desc} (${data[0].status
                        })</br>`
                    str += `startTime : ${data[0].isoStart}</br>`
                    str += `endTime : ${data[0].isoEnd}</br>`
                    str += `duration : ${data[0].duration}</br>`
                    return str
                }
            },
            legend: {
                left: 150,
                padding: [5, 5, 5, 5],
                borderRadius: 0
            },
            dataZoom: [
                {
                    type: 'slider',
                    xAxisIndex: 0,
                    filterMode: 'weakFilter',
                    height: 20,
                    bottom: 0,
                    start: 0,
                    end: 100,
                    handleSize: '80%',
                    showDetail: false,
                    top: '85%'
                },
                {
                    type: 'inside',
                    filterMode: 'weakFilter'
                }
            ],
            grid: {
                height: '70%',
                top: 80
            },
            xAxis: {
                type: 'time',
                min: minTime,
                max: maxTime - minTime > 5000 ? maxTime + 1000 : minTime + 5000,
                position: 'top',
                axisTick: { show: true },
                splitLine: { show: false },
                axisLabel: {
                    formatter: '{HH}:{mm}:{ss}',
                    showMinLabel: true,
                    showMaxLabel: true,
                    hideOverlap: true
                }
            },
            yAxis: {
                axisTick: { show: false },
                splitLine: { show: false },
                axisLine: { show: false },
                data: taskList.map((item: any) => {
                    return {
                        value: item,
                        textStyle: {
                            width: 130,
                            overflow: 'truncate'
                        }
                    }
                })
            },
            series: series
        }

        var myChart = echarts.init(chartDom, {}, {});
        myChart.setOption(option);
    }

    return (
        <>
            <div className={styles.commonContent}>
                <div className={styles.top} style={{ border: 0 }}>
                    <div className={styles.title}>
                        <div className={operationListStyles.promptTitle}>
                            {/* <a href={(deployInfo.chain_type == 1 ? '/textPromptDetail?id=' : '/flowDetailV3?id=') +deployInfo.chain_no +'&groupId='+deployInfo.group_id+'&teamId='+deployInfo.team_id}>{deployInfo.chain_name}</a>
                        <span>/</span> */}
                            <img src={leftBtn.src} className={styles.leftBtn} onClick={() => {
                                Router.push('/projectList?teamId=' + teamId + '&tab=1')
                            }} />
                            <span className={styles.breadTitle}>服务监控</span>
                            <span style={{ marginLeft: '16px', fontSize: '14px', color: '#626f84' }}>名称：</span>
                            <span style={{ fontSize: '14px', color: '#626f84' }}>{deployInfo.chain_name}</span>
                        </div>
                    </div>
                </div>
                <div style={{ paddingBottom: 1 }}>
                    <div className={operationListStyles.monitorChart}>
                        <div className={operationListStyles.monitorChartTitle}>
                            <div className={operationListStyles.monitorChartTitleContent}>监控数据</div>
                            <div>
                                <div className={operationListStyles.monitorChartLabel}>时间周期</div>
                                <Select
                                    className='noBorderSelector'
                                    style={{ width: 150 }}
                                    options={[
                                        {
                                            label: '今天',
                                            value: 5
                                        },
                                        {
                                            label: '昨天',
                                            value: 4

                                        },
                                        {
                                            label: '最近7天',
                                            value: 1
                                        },
                                        {
                                            label: '最近30天',
                                            value: 2
                                        },
                                        {
                                            label: '最近12个月',
                                            value: 3
                                        },
                                    ]}
                                    value={intervalType}
                                    onChange={v => {
                                        setIntervalType(v)
                                    }}
                                ></Select>
                            </div>

                        </div>

                        <Spin spinning={chartLoading}><div id="container" className={operationListStyles.container}></div></Spin>
                        {chartData?.length ? '' : <div className={operationListStyles.barEmptyWrapper}><Empty description="暂无记录" /></div>}
                    </div>
                    {/* <div className={operationListStyles.monitorLog}>
                    <div> 近50条被调用的日志内容：</div>

                    {logData && logData.length ? <div className={operationListStyles.logWrapper}>{logData.map(log => {
                        return <div className={operationListStyles.logItem}>
                                <div className={operationListStyles.logTitle}> {log.timestamp} {log.host} </div>
                                <div>{log.message}</div>
                                </div>
                    })}</div> : <div className={operationListStyles.emptyWrapper}><Empty description="暂无日志" /></div>} 
                </div> */}

                    <div className={operationListStyles.monitorLogTable}>
                        <div className={operationListStyles.monitorLogTitle}>
                            <span>执行记录</span>

                            <div>
                                {/* 0:其他 1:成功 2:失败 3:取消 4.异常 */}
                                <Select
                                    style={{ width: '100px', marginRight: '8px' }}
                                    options={[
                                        { value: '', label: <span>全部</span> },
                                        { value: 5, label: <span>运行中</span> },
                                        { value: 1, label: <span>成功</span> },
                                        { value: 2, label: <span>失败</span> },
                                        { value: 3, label: <span>取消 </span> },
                                        { value: 4, label: <span>异常</span> },
                                    ]}
                                    value={accessStatus}
                                    onChange={(value: any) => {
                                        setPageSize(10)
                                        setCurPage(1)
                                        setAccessStatus(value)
                                    }}
                                />
                                <Input
                                    style={{ width: '400px' }}
                                    placeholder="请输入执行名称搜索"
                                    onChange={(e: any) => {
                                        setPageSize(10)
                                        setCurPage(1)
                                        setLogId(e.target.value)
                                    }}
                                />
                            </div>
                        </div>
                        <Spin spinning={loading}>
                            <div className={commonListStyles.tableList + ' normalFont ' + operationListStyles.monitorTable}>
                                <Table
                                    columns={columns}
                                    dataSource={list}
                                    pagination={{
                                        current: curPage,
                                        pageSize: pageSize,
                                        total: total,
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total: any) => `共 ${total} 条记录`
                                    }}
                                    onChange={(pagination) => {
                                        setCurPage(pagination.current || 1)
                                        setPageSize(pagination.pageSize || 20)
                                    }}
                                    className='commonTable normalFont logTable'
                                />
                            </div>
                        </Spin>
                    </div>
                </div>
            </div>
            <Modal
                open={openLog}
                className={operationListStyles.logModal + ' commonModal noFooterModal'}
                onCancel={() => { setOpenLog(false); setLogDetail(false) }}
                title={logDetail ? '详情' : '日志'}
                footer={<></>}
            >
                <div className={operationListStyles.monitorLog}>
                    <Spin spinning={loading} className='gantt-container'>
                        {!logDetail && <div className={operationListStyles.monitorLogTitle}> 近50条被调用的日志内容：</div>}
                        {
                            !logDetail ? (
                                logData && logData.length ? (
                                    <div className={operationListStyles.logWrapper}>{logData.map(log => {
                                        return <div className={operationListStyles.logItem}>
                                            <div className={operationListStyles.logTitle}> {log.timestamp} {log.host} </div>
                                            <div>{log.message}</div>
                                        </div>
                                    })}</div>

                                ) : (<div className={operationListStyles.emptyWrapper}><Empty description="暂无日志" /></div>)

                            ) : null
                        }
                    </Spin>
                </div>
            </Modal>
            <Modal
                open={openDetail}
                className={operationListStyles.detailModal + ' commonModal noFooterModal'}
                onCancel={() => setopenDetail(false)}
                title={<>甘特图{dsId ? '（' + dsId + '）' : ''}{dsId ? <img src={copyBtn.src} onClick={() => {
                    dsId && copyParamValue(dsId)
                }} style={{ margin: '-4px 0 0 5px', cursor: 'pointer' }} /> : ''}</>}
                footer={<></>}
            >
                <Spin spinning={loading} className='gantt-container'>
                    <div id='gantt-container' style={{ width: 1170, height: 600 }}></div>
                </Spin>
            </Modal>
        </>
    )
}
