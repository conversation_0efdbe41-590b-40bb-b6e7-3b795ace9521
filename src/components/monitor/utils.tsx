/*
 * @Author: zx
 * @Date: 2024-11-12 14:56:09
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-11-15 07:59:57
 * @FilePath: \prompt-web\src\components\api\utils.tsx
 */
export const initJsonEditor = async (obj: object, id: any) => {
    const { JSONEditor } = await import('@/utils/vanilla-jsoneditor');
    let content = {
        text: undefined,
        json: obj
    };

    // 获取目标元素
    const targetElement = document.getElementById(id);

    // 如果目标元素不存在，打印错误并返回
    if (!targetElement) {
        console.error(`元素 with id "${id}" not found`);
        return;
    }

    // 清空元素内容
    targetElement.innerHTML = '';

    // 初始化编辑器
    const editor = new (JSONEditor as any)({
        target: targetElement,
        props: {
            content,
            mode: 'text',
            readOnly: true
        }
    });
};
