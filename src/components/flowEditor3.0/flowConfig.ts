// newflow icon
import memoryFlow from '@/images/newFlow/memory.svg'
import inputFlow from '@/images/newFlow/start.svg'
import outputFlow from '@/images/newFlow/end.svg'
import llmFlow from '@/images/newFlow/llm.svg'
import codeFlow from '@/images/newFlow/code.svg'
import grabFlow from '@/images/newFlow/grab.svg'
import searchFlow from '@/images/newFlow/search.svg'
import apiFlow from '@/images/newFlow/api.svg'
import functioncallFlow from '@/images/newFlow/function-call.svg'
import conditionFlow from '@/images/newFlow/condition.svg';
import interaction from '@/images/newFlow/interaction.svg';
import gui from '@/images/newFlow/GUI.svg';
import loop from '@/images/newFlow/loop.svg';
import loopEnd from '@/images/newFlow/loopEnd.svg';
import setVar from '@/images/newFlow/setVar.svg';
import flowDateTime from '@/images/flowDateTime.svg';
import flowCombinedText from '@/images/flowCombinedText.svg';
import flowSplitText from '@/images/flowSplitText.svg';
import flowReplaceText from '@/images/flowReplaceText.svg';
interface iTypeObj {
    [input:string]: string;
    output: string;
    llm: string;
    "web-search": string;
    http: string;
    code: string;
    "web-crawl" : string;
    "web-crawl-batch" : string;
    "web-search-batch": string;
    'function-call': string;
    "interaction": string;
    "flow_plugin": string;
}

export const typeImgObj:iTypeObj = {
    'input': inputFlow.src,
    'output': outputFlow.src,
    'llm': llmFlow.src,
    'llm-batch': llmFlow.src,
    'web-search': searchFlow.src,
    'http': apiFlow.src,
    'api': apiFlow.src,
    'version-api': apiFlow.src,
    'api-batch': apiFlow.src,
    'version-api-batch': apiFlow.src,
    'code': codeFlow.src,
    'web-crawl': grabFlow.src,
    'web-crawl-batch': grabFlow.src,
    'knowledge-base': memoryFlow.src,
    'knowledge': memoryFlow.src,
    "web-search-batch": searchFlow.src,
    "function-call": functioncallFlow.src,
    'version-function-call': functioncallFlow.src,
    "condition_start": conditionFlow.src,
    "condition_end": conditionFlow.src,
    "interaction": interaction.src,
    "flow_plugin": interaction.src,
    'loop':loop.src,
    "loop_break": loopEnd.src,
    "set_variable": setVar.src,
    "image_to_text_llm": gui.src,
    "datetime": flowDateTime.src,
    "combined_text": flowCombinedText.src,
    "split_text": flowSplitText.src,
    "replace_text": flowReplaceText.src,
}

// 连接器节点, functionCall type
export const blockConnectorType = ['api', 'version-api', 'api-batch', 'version-api-batch'];
export const blockConnectorTypeApi = ['api', 'version-api'];
export const blockConnectorTypeApiBatch = ['api-batch', 'version-api-batch'];
export const blockFunctionCallType = ['function-call', 'version-function-call'];
export const blockApiVersion = ['version-api', 'version-api-batch', 'version-function-call'];
export const blockConnectorTypeVersion = ['version-api', 'version-api-batch'];

export const blockImgToText = ['image_to_text_llm'];



export const colorRowList = [[
    '#8080FE',
    '#9C89B8',
    '#B8BEDD',
    '#E4B7FF',
    '#64B5F6',
    '#4DD0E1',
    '#4DDBBB',
],[
    '#7EE787',
    '#A3B18A',
    '#E8D45A',
    '#FFA07A',
    '#FF7F7F',
    '#FF8FAB',
    '#D4A373',
],[

    '#E3684D',
    '#D86E77',
    '#FF9C50',
    '#C17E9C',
    '#CF9CD5',
    '#B16EC7',
    '#8D5C86',
],[
    '#F3BC53',
    '#6AB693',
    '#81ADED',
    '#5876BF',
    '#D58043',
    '#AD98FF',
    '#FD7462',
], [
    '#3B8096',
    '#609749',
    '#52B0B2',
    '#88D6A3',
    '#B2DF7A',
    '#EDD900',
    '#DADB64',
], [
    '#468CFF',
    '#4A74DE',
    '#118358',
    '#56A0D3',
    '#80D6E4',
    '#A0D6A0',
    '#B9C7BC',
]
]

export const colorList = colorRowList.flat()

import icon1 from "@/images/flow3.0/iconList/1.svg"
import icon2 from "@/images/flow3.0/iconList/2.svg"
import icon3 from "@/images/flow3.0/iconList/3.svg"
import icon4 from "@/images/flow3.0/iconList/4.svg"
import icon5 from "@/images/flow3.0/iconList/5.svg"
import icon6 from "@/images/flow3.0/iconList/6.svg"
import icon7 from "@/images/flow3.0/iconList/7.svg"
import icon8 from "@/images/flow3.0/iconList/8.svg"
import icon9 from "@/images/flow3.0/iconList/9.svg"
import icon10 from "@/images/flow3.0/iconList/10.svg"
import icon11 from "@/images/flow3.0/iconList/11.svg"
import icon12 from "@/images/flow3.0/iconList/12.svg"
import icon13 from "@/images/flow3.0/iconList/13.svg"
import icon14 from "@/images/flow3.0/iconList/14.svg"
import icon15 from "@/images/flow3.0/iconList/15.svg"
import icon16 from "@/images/flow3.0/iconList/16.svg"
import icon17 from "@/images/flow3.0/iconList/17.svg"
import icon18 from "@/images/flow3.0/iconList/18.svg"
import icon19 from "@/images/flow3.0/iconList/19.svg"
import icon20 from "@/images/flow3.0/iconList/20.svg"
import icon21 from "@/images/flow3.0/iconList/21.svg"
import icon22 from "@/images/flow3.0/iconList/22.svg"
import icon23 from "@/images/flow3.0/iconList/23.svg"
import icon24 from "@/images/flow3.0/iconList/24.svg"
import icon25 from "@/images/flow3.0/iconList/25.svg"
import icon26 from "@/images/flow3.0/iconList/26.svg"
import icon27 from "@/images/flow3.0/iconList/27.svg"
import icon28 from "@/images/flow3.0/iconList/28.svg"

export const iconList = [
    {
        image: icon1,
        name: '智能体'
    },
    {
        image: icon2,
        name: '条件'
    },
    {
        image: icon3,
        name: '迭代'
    },
    {
        image: icon4,
        name: '智能'
    },
    {
        image: icon5,
        name: '助手'
    },
    {
        image: icon6,
        name: '魔法棒'
    },
    {
        image: icon7,
        name: '连接'
    },
    {
        image: icon8,
        name: '机器人'
    },
    {
        image: icon9,
        name: '想法'
    },
    {
        image: icon10,
        name: '语音'
    },
    {
        image: icon11,
        name: '图片'
    },
    {
        image: icon12,
        name: '组件'
    },
    {
        image: icon13,
        name: '循环'
    },
    {
        image: icon14,
        name: '盒子'
    },
    {
        image: icon15,
        name: '智能体'
    },
    {
        image: icon16,
        name: '圆球'
    },
    {
        image: icon17,
        name: '正负线'
    },
    {
        image: icon18,
        name: '建筑'
    },
    {
        image: icon19,
        name: '视频'
    },
    {
        image: icon20,
        name: '代码'
    },
    {
        image: icon21,
        name: '智能体'
    },
    {
        image: icon22,
        name: '数据'
    },
    {
        image: icon23,
        name: '设备'
    },
    {
        image: icon24,
        name: '提速'
    },
    {
        image: icon25,
        name: '链接'
    },
    {
        image: icon26,
        name: '安全'
    },
    {
        image: icon27,
        name: '日期'
    },
    {
        image: icon28,
        name: '向标'
    }
]


export const  hexWithOpacity = (hex, opacity) => {
    const opacityHex = Math.round(Math.min(255, Math.max(0, opacity * 255))).toString(16).padStart(2, '0');
    return `${hex}${opacityHex}`;
}

export const getOS = (): 'windows' | 'mac' | 'linux' | 'other' => {
    const ua = navigator.userAgent.toLowerCase();
  
    if (ua.includes('win') || ua.includes('windows')) {
      return 'windows';
    } else if (ua.includes('mac') || ua.includes('darwin')) {
      return 'mac';
    } else if (ua.includes('linux')) {
      return 'linux';
    } else {
      return 'other';
    }
  };


  import drag1 from "@/images/flow3.0/drag1.png"
  import drag2 from "@/images/flow3.0/drag2.png"
  import drag3 from "@/images/flow3.0/drag3.png"

  export const dragImgObj = {
    nami_agent: drag1.src,
    nami_agent_condition: drag2.src,
    nami_agent_loop: drag3.src
}