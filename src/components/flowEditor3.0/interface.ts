/*
 * @Author: yh
 * @Date: 2025-06-06 14:20:57
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-07 15:02:51
 * @FilePath: \prompt-web\src\components\flowEditor\interface.ts
 */
export enum FlowCard {
    Start = "start",
    End = 'end',
    LLM = "llm",
    Code = "code",
    API = "version-api",
    GRAB = "grab",
    SEARCH = "search",
    MEMORY = 'memory',
    KNOWLEDGEADD = 'knowledge_add',
    KNOWLEDGERECALL = 'knowledge_recall',
    KNOWLEDGEQA = 'knowledge_qa',
    FC = 'version-function-call',
    Condition = 'condition',
    GUI = 'interaction',
    Playbook = 'flow_plugin',
    Copy = 'copy',
    Loop = 'loop',
    LoopEnd = 'loop_break',
    SetVariable= 'set_variable',
    Group = 'group',
    ImgToText = 'image_to_text_llm',
    DateTime = 'datetime',
    CombinedText = 'combined_text',
    SplitText = 'split_text',
    ReplaceText = 'replace_text',
    MCP = 'mcp',
    RandomValue = 'random_value',
    QueryRewriting = 'query_optimization',
    IntentionRecognition = 'query_classification',
    MergeVariable = 'variable_merge',
    AIAgent = 'agent',
    FORMINTERACTIVE = 'form_interaction',
    NmAIAgent = 'nami_agent',
    NmMCP = 'nami_mcp'
}

export enum InputType {
  Str = "string",
  ArrayObject = "arr[object]",
  Object = "object",
  Number = "number",
  Integer = "integer",
  Boolean = "boolean",
  ArrayNumber = "arr[number]",
  ArrayString = "arr[string]",
}
export interface Param {
  value: string;
  type: string;
}

