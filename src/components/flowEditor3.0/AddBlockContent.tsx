/*
 * @Author: yh
 * @Date: 2025-06-06 15:59:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-12 10:35:20
 * @FilePath: \prompt-web\src\components\flowEditor\AddBlockContent.tsx
 */
import { Avatar, Button, Tooltip, Input, Tabs, Divider, Collapse, Empty } from 'antd';

import { PlusOutlined } from '@ant-design/icons';
import { useEffect, useState, useRef } from 'react';
import Styles from '@/styles/newFlow/flowSide.module.scss';
import LLM from '@/images/flow3.0/llm.svg';
import Loop from '@/images/flow3.0/loop.svg';
import Condition from '@/images/flow3.0/condition.svg'
import { useClientContext  } from '@q/flowgram.ai.free-layout-editor';

import { curLineInfoAtom, isDisabledAtom } from '@/atoms/flowEditorAtoms' 
import { dragImgObj } from './flowConfig'; 


import { useRouter } from "next/router";

import {
    dragType<PERSON>tom, playground<PERSON>tom
} from "@/atoms/flowEditorAtoms";

import { useSetRecoilState, useRecoilValue } from "recoil";


let isClickable = true
const imgDom = new Array();

export default (props: any) => {
    const { addNode, blocks, sourceId, targetId, sourcePortId, setOpenAgent, position={x:0,y:0} } = props;

    const panelProps = props.panelProps;
    const [positionArr, setPositionArr] = useState(new Array())

    const setDragType = useSetRecoilState(dragTypeAtom)
    const setCurLineInfo = useSetRecoilState(curLineInfoAtom)
    const isDisabled = useRecoilValue(isDisabledAtom)

    const [addIndex, setAddIndex] = useState(0);
    const [addY, setAddY] = useState(0);
    const playgroundInfo = useRecoilValue(playgroundAtom);
    const [dragPosition, setDragPosition] = useState<any>('');
    const dragPositionRef = useRef<any>(null);

    // 画布对象
    const context = useClientContext();
    const router = useRouter();
    const chainId = router.query.id;


    const onDragStart = (event: any, nodeType: any) => {
        if (typeof setDragType === 'function') {
            setDragType(nodeType);
        }
        event.dataTransfer.effectAllowed = 'move';
    };

    useEffect(() => {
        dragPositionRef.current = dragPosition;
    }, [dragPosition]);

    useEffect(()=> {
        // init
        Object.keys(dragImgObj).forEach(t => {
            const dom = document.createElement("img");
            dom.src = dragImgObj[t];
            imgDom.push(dom)
        })

    },[])
      
    useEffect(() => {
        console.log('addindex', playgroundInfo)
        setAddY(0)
        setAddIndex(0)
    },[playgroundInfo.zoom, playgroundInfo.scrollX, playgroundInfo.scrollY])

    return (
        <>
        <div 
        className={position.x ? 'add-block-content' : ''}
        style={
            position.x ?
            {
                position: 'absolute',
                background: '#fff',
                left: position.x,
                top: position.y,
                borderRadius: '8px'
            } : {}}>
            <div className={Styles.flowSideList} 
                // onMouseEnter={() => { 
                //     setOpen && setOpen(true) 
                //     setIsInBlockContent && setIsInBlockContent(true)
                // }} 
                // onMouseLeave={() => { 
                //     setOpen && setOpen(false) 
                //     setIsInBlockContent && setIsInBlockContent(false)
                // }} 
            >   
                {[{
                    icon: LLM.src,
                    title: '专家智能体',
                    desc: '通过大模型自主使用MCP工具、知识库，完成推理任务',
                    type: 'nami_agent'
                },
                {
                    icon: Condition.src,
                    title: '条件智能体',
                    desc: '通过设定条件连接判断下游分支，完成流程分支',
                    type: 'nami_agent_condition'
                },
                {
                    icon: Loop.src,
                    title: '迭代智能体',
                    desc: '通过设定迭代条件和逻辑，重复执行一系列任务',
                    type: 'nami_agent_loop'
                }].map((iItem:any, index) => {
                    return <div
                    className={Styles.flowSideItem}
                    onDragStart={(event) => {
                        onDragStart(event, iItem.type);
                        // event.currentTarget.style.borderRadius = '8px'
                        event.dataTransfer.setDragImage(imgDom[index], 10, 10);
                    }}
                    draggable={true}
                    onClick={() => {
                        if(!isClickable) return;
                        isClickable = false;
                        const addItemY = iItem.type == 'nami_agent_loop' ? 246 : 146;
                        let position = context.playground.config.getPosFromMouseEvent({ clientX: 300, clientY: 300 + addY * playgroundInfo.zoom}, true)

                        positionArr.forEach(p => {
                            if(p.x == position.x  && p.y == position.y) {
                                console.log(positionArr, '---发现位置一样的---')
                                // 发现位置一样的 则基于旧位置加此元素记录的高度
                                position.y += p.addItemY;
                            }
                        })

                        console.log(addY, playgroundInfo, '---addIndex---')
                        
                        // 在边上新建节点 不需要位置
                        if(panelProps?.addNode) {
                            console.log(panelProps.nodePosition, panelProps,'----position1111')
                            panelProps.addNode({
                                blocks: panelProps.blocks,
                                type: iItem.type,
                                sourceId: panelProps.sourceId,
                                targetId: panelProps.targetId,
                                sourcePortId: panelProps.sourcePortId,
                                position: panelProps.nodePosition,
                                // 从拖拽的边上添加的
                                isDragEdgeAdd: true
                            })
                        }else {
                            addNode({
                                blocks,
                                type: iItem.type,
                                sourceId,
                                targetId,
                                sourcePortId,
                                ...(!sourceId ? { position } : {}),
                            })
                        }   
                        setAddIndex(preAddIndex => preAddIndex + 1)
                        setAddY(preAddY =>  preAddY + addItemY);
                        setPositionArr(pre => {
                            pre.push({
                                addItemY,
                                ...position
                            })
                            return pre
                        })
                        setTimeout(() => {
                            isClickable = true; // 重置为可点击状态
                        }, 1000); 
                    }}
                >
                    <img src={iItem.icon} alt="" />
                    <div>
                        <div className={Styles.title}>{iItem.title}</div>
                        <div className={Styles.desc}>{iItem.desc}</div>
                    </div>
                </div>
                })}
            </div>
            <div  className={Styles.btnWrapper} onClick={() => {
                if(panelProps?.setOpenAgent){
                    panelProps.setOpenAgent(true)
                    setCurLineInfo({
                        sourceId: panelProps.sourceId,
                        targetId: panelProps.targetId,
                        sourcePortId: panelProps.sourcePortId,
                        position: panelProps.nodePosition
                    })
                }else{
                    setOpenAgent(true)
                    setCurLineInfo({
                        sourceId,
                        targetId,
                        sourcePortId,
                        position: {}
                    })
                }
            }}><Button style={{width: '100%'}} type='primary' ghost icon={<PlusOutlined />}>从智能体广场添加</Button></div>
            {isDisabled ? <div className={Styles.flowSideListMask}></div> : ''}
        </div>
        </>
    );
};