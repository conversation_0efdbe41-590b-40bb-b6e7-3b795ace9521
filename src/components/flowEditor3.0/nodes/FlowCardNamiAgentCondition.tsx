import React, {useEffect, useRef, useState} from "react";
import styles from "@/styles/newFlow/card.module.scss";
import FlowCardHeader from "./Components/FlowCardHeader";
import FlowCardTool from './Components/FlowCardTool'
import FlowCardEditor from "./Components/FlowCardEditor"
import Branch from "./Components/FlowCardConditionBranch";
import Condition from "@/images/newFlow/condition.svg";
import { WorkflowPortRender, useNodeRender, WorkflowNodePortsData } from '@q/flowgram.ai.free-layout-editor';
import {
  updatePortBlockAtom,
  edgesAtom,
  loopConfigsAtom
} from "@/atoms/flowEditorAtoms";
import { hexWithOpacity, iconList } from "../flowConfig";
import { useRecoilCallback, useRecoilValue } from "recoil";


// 自定义节点组件
const FlowCardNamiAgentCondition = ({ data }: any) => {

  const [isEditActive, setIsEditActive] = useState(false)
	const {
    data: { meta, id },
    onNodeMenuClick,
    form,
    linesManager
  } = data;
	
  const { branches } = data.data;
  const { ports, node } = useNodeRender();
  const parentRef = useRef(null);
  const updatePortBlock = useRecoilValue(updatePortBlockAtom)
  const edges =  useRecoilValue(edgesAtom)
  const loopConfigs = useRecoilValue(loopConfigsAtom)

  // useEffect(() => {
  //   // 由于框架会去掉不属于初始化定义的类
  //   // 只能在index里面先加上my-hovered 再去掉
  //   if(branches.length) {
  //       document.querySelectorAll('.my-hovered')?.forEach(dom => {
  //         dom?.setAttribute('class', dom.getAttribute('class').replaceAll('my-hovered',  ''))
  //       }) 
  //       setTimeout(() => {
  //         document.querySelectorAll('.my-hovered')?.forEach(dom => {
  //           dom?.setAttribute('class', dom.getAttribute('class').replaceAll('my-hovered',  ''))
  //         }) 
  //       }, 100);
  //   } 
  // }, [branches.length]);

  // useEffect(() => {
  //   if(updatePortBlock === data.id){
  //     const ports = node.getData(WorkflowNodePortsData)
  //     ports.updateDynamicPorts()
  //   }
  // },[updatePortBlock])

    useEffect(() => {
      const ports = node.getData(WorkflowNodePortsData)
      ports.updateDynamicPorts()
  },[branches.length])

  const isShowPort = (curPortId: any) => {
    let res = false
    console.log(edges,'---edges--')
    edges.forEach(e => {
      if(e.sourcePortID == curPortId && !!curPortId && e.targetNodeID) {
        res = true;
      }
    })
    Object.keys(loopConfigs).forEach(loop_id => {
      loopConfigs[loop_id].edges.forEach(e => {
        if(e.sourcePortID == curPortId && !!curPortId && e.targetNodeID) {
          res = true;
        }
      })
    })
    return res;
  }
  
  return (
    <div className={styles.cardWrapper + ' cardWrapper'} d-color={meta?.color} style={{
      // 外边界用outline代替border，以防连接点计算位置偏离
      // borderColor: hexWithOpacity(meta?.color, 0.25)
      outlineColor: hexWithOpacity(meta?.color, 0.25)
    }}>

      <div className={styles.cardInnerWrapper} d-color={meta?.color}>
        <div id={'cardContainer'+id} className={styles.cardContainer +' cardContainer'} d-color={meta?.color} ref={parentRef} style={{
          borderColor: meta?.color,
          background: hexWithOpacity(meta?.color, 0.08),
        }}>
        <FlowCardTool 
          onNodeMenuClick={onNodeMenuClick}
          data={data}
          isEditActive={isEditActive}  
          setIsEditActive={setIsEditActive} />
        <div onClick={() => {setIsEditActive(false)}} style={{
          height: branches.length * 25 + 25,
          minHeight: 75
        }}>
          <FlowCardHeader
            onNodeMenuClick={onNodeMenuClick}
            data={data}
            icon={meta?.iconUrl || iconList[meta?.iconIndex]?.image}
            parentRef={parentRef}
          />
        </div>
        {isEditActive ? <FlowCardEditor
          data={data}
          onNodeMenuClick={onNodeMenuClick}
          form={form}
          linesManager={linesManager}
        /> : ''}
        {branches?.map((p:any, index:any) => {
          
          return <>
          <div key={p.branch_id} className='condition-if-port' data-port-id={'condition-if-'+p.branch_id} data-port-type="output" style={{
            position: 'absolute',
            right: '-3px',
            top: branches.length == 1 ? 37.5 : 25 * index + 25 + (!(branches.length % 2) && branches.length / 2 <= index ? 2 : 0),
          }}></div>
          {isShowPort(p.branch_id) ? <span style={{
            position: 'absolute',
            right: '-20px',
            color: meta?.color,
            fontSize: '14px',
            top: branches.length == 1 ? 6 : 25 * index + 6,
          }}>
          {index}
          </span> : ''}
          </>
    })}

      </div>
    </div>
    </div>
    
  );
};
export default FlowCardNamiAgentCondition;
