import { WorkflowNodeRegistry } from '@q/flowgram.ai.free-layout-editor';

export const nodeRegistries: WorkflowNodeRegistry[] = [
  {
    type: 'nami_agent_condition',
    meta: {
      defaultPorts: [ { type: 'input' }],
      useDynamicPort: true
    }
  },
  {
    type: 'nami_agent_loop',
    meta: {
      defaultPorts: [{ type: 'output' }, { type: 'input' }],
      useDynamicPort: true,
      /**
       * Mark as subcanvas
       * 子画布标记
       */
      isContainer: true,
      /**
       * The subcanvas default size setting
       * 子画布默认大小设置
       * 设置大小 padding才生效
       */
      size: {
        width: 432,
        height: 176, // agent节点高度为80+48*2 =176, 但是实际渲染的内容有14px差
      },
      /**
       * The subcanvas padding setting
       * 子画布 padding 设置
       */
      padding: (e) => {
        let paddingTop = 48;
        try{
          const showDropTips = JSON.parse(localStorage.getItem('showDropTips'))
          if(showDropTips[e.entity.id]) {
            // 有提示时，padding增大
            paddingTop = 84
          }
        }catch(e){

        }
        return {
          top: paddingTop, // 这里原先设置是90，应该是包含了头部的高度；为了连接节点居中，将头部高度从文档流去除，这里就减去了头部的高度。
          bottom: 48, // 上下边距保持一致，可以居中。这里需要与div.sub-canvas-render height以及上面的size高度一同修改。
          left: 80,
          right: 80,
        }
      },  
      expandable: false,
    }
  }
];
