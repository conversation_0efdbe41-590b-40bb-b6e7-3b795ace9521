/*
 * @Author: zx
 * @Date: 2025-02-14 16:04:06
 * @LastEditors: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-28 14:31:14
 * @FilePath: \prompt-web\src\components\NewFlow\FlowNodes\FlowCardLLM.tsx
 */
import React, { useEffect, useState, useRef } from "react"
import { Handle, Position } from "@xyflow/react"
import styles from "@/styles/newFlow/card.module.scss"
import FlowCardHeader from "./Components/FlowCardHeader"
import FlowCardTool from './Components/FlowCardTool'
import FlowCardEditor from "./Components/FlowCardEditor"
import FlowCardParams from "./Components/FlowCardParams"
import FlowCardModel from "./Components/FlowCardModel"
import { FlowCard } from "../interface"
import { hexWithOpacity, iconList } from "../flowConfig";
// 自定义节点组件
const FlowCardNamiAgent = ({ data }: any) => {
  const [isEditActive, setIsEditActive] = useState(false)
  const {
    data: { inputParams, outputParams, provider_name, meta, id},
    onNodeMenuClick,
    form,
    linesManager
  } = data
  const modelInfo = {
    title: "模型",
    icon: "",
    subTitle: data.data.model_type,
    provider_name
  }
  // console.log(onNodeMenuClick,'-----llm卡片')
  const parentRef = useRef(null)


  return (
    <>
    <div className={styles.cardWrapper + ' cardWrapper'} d-color={meta?.color} style={{
      // 外边界用outline代替border，以防连接点计算位置偏离
      // borderColor: hexWithOpacity(meta?.color, 0.25),
      outlineColor: hexWithOpacity(meta?.color, 0.25),
    }}>
      <div className={styles.cardInnerWrapper} d-color={meta?.color}>
        <div id={'cardContainer'+id} className={styles.cardContainer +' cardContainer'} d-color={meta?.color} ref={parentRef} style={{
          borderColor: meta?.color,
          background: hexWithOpacity(meta?.color, 0.08),
        }}>
          <FlowCardTool 
            onNodeMenuClick={onNodeMenuClick}
            data={data}
            isEditActive={isEditActive}  
            setIsEditActive={setIsEditActive} />
            <div onClick={() => {setIsEditActive(false)}}>
              <FlowCardHeader
                onNodeMenuClick={onNodeMenuClick}
                data={data}
                icon={meta?.iconUrl || iconList[meta?.iconIndex]?.image}
                parentRef={parentRef}
              />
            </div>
          {isEditActive ? <FlowCardEditor
            data={data}
            onNodeMenuClick={onNodeMenuClick}
            form={form}
            linesManager={linesManager}
          /> : ''}
          
        </div>
      </div>
    </div>
    </>
  )
}
export default FlowCardNamiAgent
