
import FlowCardNamiAgent from '../nodes/FlowCardNamiAgent'
import FlowCardNamiAgentCondition from '../nodes/FlowCardNamiAgentCondition'
import FlowCardNamiAgentLoop from '../nodes/FlowCardNamiAgentLoop'
import {
    activeBlockIdAtom,
    blocksAtom,
    loopConfigsAtom
} from "@/atoms/flowEditorAtoms";
import { useRecoilValue } from "recoil";
import { 
    WorkflowPortRender, 
    useNodeRender, 
    useClientContext, 
    useService, 
    WorkflowSelectService, WorkflowDragService, WorkflowOperationBaseService 
} from '@q/flowgram.ai.free-layout-editor';
import { useCallback, useEffect, useState } from 'react';
import { NodeIntoContainerService } from '@flowgram.ai/free-container-plugin';


export const renderNode = (nodeType:any, data:any, form?:any, linesManager?:any, setOpenAgent?:any) => {
    const activeBlockId = useRecoilValue(activeBlockIdAtom)
    const blocks:any = useRecoilValue(blocksAtom)
    const loopConfigs:any = useRecoilValue(loopConfigsAtom)
    const [mouseDownPosition, setMouseDownPosition] = useState({screenX: 0, screenY: 0})

    const nodeIntoContainerService = useService(NodeIntoContainerService);
    const selectService = useService(WorkflowSelectService);
    const dragService = useService(WorkflowDragService);
    const operationBaseService:any = useService(WorkflowOperationBaseService)
    const { ports, node } = useNodeRender();
	const context = useClientContext();
    
    const nodeTypes:any = {
        nami_agent: FlowCardNamiAgent,
        nami_agent_condition: FlowCardNamiAgentCondition,
        nami_agent_loop: FlowCardNamiAgentLoop
    };
    const Component:any = nodeTypes[nodeType]

    // console.log({
    //     ...data,
    //     ...blocksProperty[data.id],
    //     nodeIndex: data.nodeIndex
    // },'====blockProperty')


    return <>{Component? 
        <div 
            tabIndex={0}
            style={{
                outline: 0,
                height:'100%'
            }}
            className={(activeBlockId == data.id ? 'selectedNodeWrapper' : '') + ' nodeWrapper' + (data?.type == "nami_agent_loop" ? ' loopNodeWrapper' : '')}
            onKeyDown={(event:React.KeyboardEvent) => {
                // event.stopPropagation();
                console.log(event, 'mouseDownPosition')
                if (event.key === 'Delete' || event.key === 'Backspace') {
                    // event.preventDefault();
                     const target = event.target as HTMLElement;
                     const isInputOrTextarea = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA';
                     const isContentEditable = target.isContentEditable;

                     console.log(isInputOrTextarea,isContentEditable,33213122111)
                    // 如果不是输入元素，则阻止浏览器回退
                    if (!isInputOrTextarea && !isContentEditable) {
                        event.preventDefault();
                    }
                     const className = target.getAttribute('class') 
                     if(className?.includes('renameInput') || className?.includes('css-dev-only-do-not') || className?.includes('input')) {
                        return;
                     }
                    data.onNodeDeleteHandle(data.id)
                }
            }}
            onMouseDown={(e) => {
                // 记录鼠标位置 区分是拖拽还是点击
                setMouseDownPosition({
                    screenX: e.screenX || 0,
                    screenY: e.screenY || 0
                })
            }}
            draggable={true}
            onDragStart={(e) => {
                if(blocks[data.id].type != 'nami_agent_loop' && blocks[data.id].loop_id) {
                    // 仅需要监听迭代内节点
                    const position = context.playground.config.getPosFromMouseEvent(e, true);
                    data.dragBlockOutLoop(e, data.id, position, node, nodeIntoContainerService, selectService, dragService, operationBaseService)
                }
            }}
            onClick={(e) => {
                console.log(e,mouseDownPosition, e.screenX,e.screenY,'mouseDownPosition')
                if(mouseDownPosition.screenX == e.screenX && mouseDownPosition.screenY == e.screenY) {
                    data.onNodeClick(data)
                }else {
                    if(blocks[data.id].type != 'nami_agent_loop') {
                        const position = context.playground.config.getPosFromMouseEvent(e, true);
                        data.dragBlockInLoop(e, data.id, position, node, context, nodeIntoContainerService, operationBaseService)
                    }
                }
            }}
        >
            <Component data={{
                data : {
                    ...data,
                    ...blocks[data.id]
                },
                loopConfig: loopConfigs[data.id],
                onNodeMenuClick: data?.onNodeMenuClick || (() => {}), // 适配原来的数据结构
                form: form, // 传递 form 对象
                linesManager: linesManager, // 传递 linesManager 对象
                setOpenAgent: setOpenAgent
            }} />
            {ports.map((p) => {
                return  <WorkflowPortRender
                    key={p.id}
                    entity={p}
                    className={(p.portType == 'output' ? "output-port" : "") + " portStyle" + (blocks[data.id]?.meta?.colorIndex || 0)}
                />
            })}
        </div>  : ''}</>
}