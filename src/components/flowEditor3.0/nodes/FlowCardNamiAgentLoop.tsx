import React, { useEffect, useState, useRef } from "react";
import FlowCardHeader from "./Components/FlowCardHeader";
import FlowCardTool from './Components/FlowCardTool'
import FlowCardEditor from "./Components/FlowCardEditor"
import loopBodyStyles from "@/styles/newFlow/loopBody.module.scss";
import cardStyles from "@/styles/newFlow/card.module.scss";
import AddBlockContent from "../AddBlockContent";
import { Popover } from "antd";
import { SubCanvasRender } from '@flowgram.ai/free-container-plugin';
import { hexWithOpacity, iconList } from "../flowConfig";
import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import { useRecoilState, useRecoilValue } from "recoil";
import close from '@/images/flow3.0/close.svg'
import { getOS } from "../flowConfig";


import {
  blocksAtom, isDisabledAtom, showDropTipsAtom
} from "@/atoms/flowEditorAtoms";

// 自定义节点组件
const FlowCardNamiAgentLoop = (props: any) => {
  const { data } = props
  let {data: { onAddNode, id, meta }, loopConfig, onNodeMenuClick, form, linesManager, setOpenAgent} = data;

  const [isEditActive, setIsEditActive] = useState(false)
  const atomBlocks = useRecoilValue(blocksAtom)
  const isDisabled = useRecoilValue(isDisabledAtom)
  const [showDropTips, setShowDropTips] = useRecoilState(showDropTipsAtom)


  const color = meta?.color
  const icon = meta?.iconIndex

  const parentRef = useRef(null)

  const [isShowCardList, setIsShowCardList] = useState(false);
  const addShowCardList = (open: boolean) => {
    setIsShowCardList(open)
  }
  const all_block_keys = loopConfig?.all_block_keys;

  return (
    <>
      <div className={loopBodyStyles.groupBox} style={{height: '100%'}}>

        <FlowCardTool 
          onNodeMenuClick={onNodeMenuClick}
          data={data}
          isEditActive={isEditActive}  
          setIsEditActive={setIsEditActive}
          isLoop={true}
        />
        <div 
          className={cardStyles.groupHeader+ ' groupHeader'} 
          onClick={() => {setIsEditActive(false)}}
        >
          <FlowCardHeader
            onNodeMenuClick={onNodeMenuClick}
            data={data}
            icon={meta?.iconUrl || iconList[meta?.iconIndex]?.image}
            parentRef={parentRef}
            isLoop={true}
          />
        </div>

        {isEditActive ? <FlowCardEditor
            data={data}
            onNodeMenuClick={onNodeMenuClick}
            form={form}
            linesManager={linesManager}
          /> : ''}
        <div 
          onClick={() => {setIsEditActive(false)}}
          className={loopBodyStyles.groupWrapper + ' groupWrapper'} 
          style={{
            // 外边界用outline代替border，以防连接点计算位置偏离
            // borderColor:  hexWithOpacity(meta?.color, 0.15),
            outlineColor: hexWithOpacity(meta?.color, 0.15),

          }}
          ref={parentRef}
        >
          <div className={loopBodyStyles.groupOuterWrapper + ' groupOuterWrapper'} style={{
            borderColor: color
          }}>
            <div className={loopBodyStyles.groupContent} style={{
              background: hexWithOpacity(meta?.color, 0.05)
            }}>
              <div 
              className="loop-node-wrapper"
              style={{
                position: "absolute",
                width: "calc(100% - 34px)",
                height: "36px",
                lineHeight: "36px",
                top: "18px",
                left: "17px",
                zIndex: 11,
                padding: '0px 24px',
                borderRadius: "16px 16px 0 0",
                fontSize: "14px",
                background: "linear-gradient(0deg, " + hexWithOpacity(meta?.color, 0.2) +" 0%, "+hexWithOpacity(meta?.color, 0.2)+" 100%), #FFF",
                justifyContent: "center",
                color: '#1D2531',
                display: showDropTips[id] ? 'flex' : 'none'
              }}>
                按住{getOS() != 'mac' ? 'Ctrl' : 'Cmd ⌘'}同时拖拽节点即可拖出
                <img src={close.src} alt="" style={{cursor: 'pointer', position:'absolute', top:'11px',right: '24px'}} width={16} onClick={() => {
                  
                  setShowDropTips(pre => {
                    localStorage.setItem('showDropTips', JSON.stringify({
                      ...pre,
                      [id]: false
                    }))
                    return {
                      ...pre,
                      [id]: false
                    }
                  })
                }}/>
              </div>
              <SubCanvasRender />
            </div>
          </div>
        </div>

      </div>
      {
        all_block_keys && Array.isArray(all_block_keys) && all_block_keys.length === 0 && !isDisabled? <div 
        className={loopBodyStyles.groupAdd}  
        d-sourceid={id}
        onClick={(e) => {e.stopPropagation()}}
        style={{
          top: showDropTips[id] ? '60%' : '50%'
        }}
      >
          <Popover
              content={<AddBlockContent 
                addNode={onAddNode}
                blocks={atomBlocks}
                sourceId={id}  
                targetId={''}
                setOpen={setIsShowCardList}
                setOpenAgent={setOpenAgent}
                sourcePortId={''}
              />}
              trigger="hover"
              arrow={false}
              placement={"right"}
              open={isShowCardList}
              onOpenChange={addShowCardList}
              overlayClassName={flowStyles.menubarPopover}
          > 
            <div className={loopBodyStyles.addAgentBtn} style={{borderColor: color, color}}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill={color}/>
              </svg>
                <span>添加智能体</span>  
            </div>
          </Popover>
        </div> : ''
      }
    </>
  );
};
export default FlowCardNamiAgentLoop;
