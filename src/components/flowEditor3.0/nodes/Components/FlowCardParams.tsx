/*
 * @Author: zx
 * @Date: 2025-01-09 17:42:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-01-10 10:56:45
 * @FilePath: \prompt-web\src\components\NewFlow\FlowNodes\Components\FlowCardParams.tsx
 */
import React, { useEffect, useRef, useState } from "react";
import styles from "@/styles/newFlow/card.module.scss";
import { InputType } from "../../interface";
import Str from "@/images/newFlow/Str.svg";
import Object from "@/images/newFlow/object.svg";
import ArrayStr from "@/images/newFlow/arrayStr.svg";
import ArrayNum from "@/images/newFlow/arrayNum.svg";
import ArrayObject from "@/images/newFlow/arrayObject.svg";
import Number from "@/images/newFlow/Num.svg";
import Bool from "@/images/newFlow/bool.svg";
import more from "@/images/newFlow/more.svg";
import undefined from "@/images/newFlow/undefinedStr.svg"
import { Popover } from "antd";

// 自定义节点组件
const FlowCardParams = ({ data, title }: any) => {
  const params = data;
  const containerRef = useRef<HTMLDivElement>(null);
  const [isOverflow, setIsOverflow] = useState(false);
// console.log(`params:${JSON.stringify(params)}`)
  useEffect(() => {
    const checkOverflow = () => {
      const container = containerRef.current;
      if (container) {
        setIsOverflow(container.scrollHeight > container.clientHeight);
      }
    };
    setTimeout(checkOverflow, 100); // 延迟检查以确保在初始渲染后获取正确的高度
  }, []);

  // 判断 params 是否是数组
  if (!Array.isArray(params)) {
    return null; // 返回 null 而不是空 JSX
  }

  // 创建一个映射对象来获取图标
  const iconMapping: { [key: string]: string } = {
    [InputType.Str]: Str.src,
    [InputType.ArrayString]: ArrayStr.src,
    [InputType.ArrayNumber]: ArrayNum.src,
    [InputType.ArrayObject]: ArrayObject.src,
    [InputType.Number]: Number.src,
    [InputType.Boolean]: Bool.src,
    [InputType.Object]: Object.src,
    [InputType.Integer]: Number.src,
  };

  const renderItem = (item: any, index: number) => {
    const icon = iconMapping[item.type] || ""; // 获取对应的图标
    const isUndefined = !item.value || item.value.length === 0;

    return (
      <div className={styles.tagItemWrapper} key={`${index}`}>
        <div className={isUndefined ? styles.undefinedItem : styles.normalItem}>
          {
           icon.length > 0 ? <img src={isUndefined?undefined.src:icon} alt="" /> : ''
          }
          <span className={styles.parameterValue}>{isUndefined ? "未定义" : item.value}</span>
        </div>
      </div>
    );
  };

  return (
    <>
      <span className={styles.title}>{title}</span>
      <div className={styles.paramTagContainer} ref={containerRef}>
        {params.map(renderItem)}
        {isOverflow && (
          <Popover
            content={
              <div className={`${styles.paramTagContainer} ${styles.moreListContainer}`}>
                {params.map(renderItem)}
              </div>
            }
            title=""
            trigger="hover"
            arrow={false}
            placement={"bottomLeft"}
          >
            <div className={styles.moreContainer}>
              <img
                src={more.src}
                alt=""
                className={styles.more}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </Popover>
        )}
      </div>
    </>
  );
};

export default FlowCardParams;
