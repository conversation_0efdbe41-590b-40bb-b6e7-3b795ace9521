import React, { useEffect, useState, useRef } from "react";
import styles from "@/styles/newFlow/card.module.scss";
import { Dropdown, Tooltip, message } from "antd";
import type { MenuProps } from "antd";
import more from "@/images/newFlow/more.svg";
import FlowCardStatus from "./FlowCardStatus";
import { copyParamValue } from '@/utils/common';
import FlowCardResponse from "./FlowCardResponse";
import useElementSize from "@/hooks/useElementSize";
import { hexWithOpacity } from "../../flowConfig";
import { useRecoilValue } from "recoil";
import { runNodeResultAtom } from '@/atoms/flowEditorAtoms';
interface FlowCardHeaderProps {
  data: any;
  icon: any;
  children?: React.ReactNode;
  onNodeMenuClick: (type: string, data: Object) => void;
  parentRef?: any;
  isLoop?: boolean;
}

// 自定义节点组件
const FlowCardHeader = ({
  data,
  icon,
  children,
  onNodeMenuClick,
  parentRef,
  isLoop = false,
}: FlowCardHeaderProps) => {
  const [isHiddenMore, setIsHiddenMore] = useState(false);
  const [isShowRenameInput, setIsShowRenameInput] = useState(false);
  const [inputValue, setInputValue] = useState(data.data.name);
  const inputRef = useRef<HTMLInputElement>(null);
  const parentRefObj = useElementSize(parentRef);
  const nameRef = useRef(null);
  // const nameRefObj = useElementSize(nameRef);
  const runNodeResult  = useRecoilValue(runNodeResultAtom);
  const [curRunResultValue, setCurRunResultValue] = useState<any>({});

  useEffect(() => {
    if (runNodeResult[data?.data?.id]) {
      let curData = runNodeResult[data?.data?.id];
      let inputs = curData?.inputs || {};
      let result = curData?.result || {};
      let inputsValue = {
        // ...(inputs?.node_ref_val || {})
        user_prompt: inputs?.user_prompt || '',
      };
      // if ('query' in inputs) {
      //   inputsValue.query = inputs.query;
      // }
      setCurRunResultValue({
        type: curData?.type,
        status: curData?.status,
        inputs: inputsValue,
        result: {
          output: result?.output || ''
        }
      })
    }
  }, [JSON.stringify(runNodeResult[data?.data?.id || ''])])


  // console.log(data)
  useEffect(() => {
    if (data.data?.type === "input" || data.data?.type === "output" || data.data?.type === 'condition_end') {
      setIsHiddenMore(true);
    } else {
      setIsHiddenMore(false);
    }
  }, [data.data.type]);
  const onBlur = () => {
    setIsShowRenameInput(false);
    data.data.name = inputValue;
    onNodeMenuClick("rename", data.data);
  };
  const handleMenuClick: MenuProps["onClick"] = (e) => {
    const event = e.domEvent;
    event.stopPropagation();
    //整个flow正在运行或改节点正在调试的时候，禁止操作
    if(data.data?.flowLoading || data.data?.status == 2) {
      let msg = ''
      if(data.data?.flowLoading ) {
        msg = '整个flow正在运行中, 禁止操作'
      } else {
        msg = '该节点正在调试中, 禁止操作'
      }
      message.error(msg)
      return
    }
    switch (e.key) {
      case "1":
        setIsShowRenameInput(true);
        setTimeout(() => {
          inputRef.current && inputRef.current.focus();
        }, 100);

        break;
      case "2":
        onNodeMenuClick("copy", data.data);
        break;
      case "3":
        onNodeMenuClick("remove", data.data);
        break;
    }
  };
  const handleDoubleClick = (e) => {
     e.stopPropagation();
     //整个flow正在运行或改节点正在调试的时候，禁止操作
     if(data.data?.flowLoading || data.data?.status == 2) {
      let msg = ''
      if(data.data?.flowLoading ) {
        msg = '整个flow正在运行中, 禁止操作'
      } else {
        msg = '该节点正在调试中, 禁止操作'
      }
      message.error(msg)
      return
    }
    setIsShowRenameInput(true);
    setTimeout(() => {
      inputRef.current && inputRef.current.focus();
    }, 100);
  }
  let items: MenuProps["items"] = []
  if(data.data?.type === "condition_start") {
    items.push({
      label: "删除",
      key: "3"
    })
  } else if(data.data?.type === "loop"){
    items.push(
      ...[
          {
            label: "重命名",
            key: "1",
          },
          {
            label: "删除",
            key: "3",
          },
      ]
    )


  }else if(data.data?.type === "set_variable"){
    items.push(
      ...[
          {
            label: "重命名",
            key: "1",
          },
          {
            label: "删除",
            key: "3",
          },
      ]
    )
  }else if(data.data?.type === "loop_break"){
    items.push(
      ...[
          {
            label: "删除",
            key: "3",
          },
      ]
    )
  }else {
    items.push(
      ...[
          {
            label: "重命名",
            key: "1",
          },
          {
            label: "创建副本",
            key: "2",
          },
          {
            label: "删除",
            key: "3",
          },
      ]
    )
  }

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };
  const params = data?.data;

  const color = data?.data?.meta?.color

  return (
    <>
      {[2, 3, 4, 5, 6, 7].includes(curRunResultValue?.status || '') && params.type !== "input" ? <div className={styles.flowCardResponse} style={{
        top: (parentRefObj.height + 5 + (isLoop ? 52 : 0)) + 'px',
        width: parentRefObj.width + 'px'
      }} onClick={(e: any) => e.stopPropagation()}>
        <FlowCardResponse data={curRunResultValue} />
      </div> : ''}
      <div className={styles.headerContainer + ' headerContainer'}>
        <div className={styles.titleContainer}>
          <span
            className={data?.data?.meta?.iconUrl ? styles.iconWrapperWithUrl : styles.iconWrapper}
            style={data?.data?.meta?.iconUrl ? {} : { background: color }}
          >
            <img src={icon?.src ? icon.src : icon} className={styles.icon} />
          </span>
          {isShowRenameInput ? (
            <input
              ref={inputRef}
              type="text"
              className={styles.renameInput}
              defaultValue={params?.name}
              onChange={(e) => {
                setInputValue(e.target.value);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setTimeout(() => {
                    onBlur();
                  }, 100);
                }
              }}
              onBlur={(e) => {
                setTimeout(() => {
                  onBlur();
                }, 100);
              }}
            />
          ) : (
            <div style={{ overflow: "hidden", width: '100%' }} 
            // onDoubleClick={handleDoubleClick}
            >
              <span className={styles.title}>
                <span
                title={params?.name}
                ref={nameRef} 
                className={styles.name}>{params?.name}</span>  
                {/* <span 
                className={styles.agent}
                style={{
                  color,
                  borderColor: hexWithOpacity(color, 0.4)
                }}>智能体</span> */}
              </span>
              <span className={styles.desc} title={params?.desc} style={{
                // maxWidth: ((nameRefObj.width + 75) > 150 ? (nameRefObj.width + 75) : 150) + 'px'
              }}>{params?.desc || ''}</span>
              <div className="slot-content">{children}</div>
            </div>
          )}
        </div>
        {/* <div className={styles.nodeInfoContainer}>
          <Tooltip title="点击可以复制节点id">
            <span className={styles.nodeInfoId} onClick={() => {
                copyParamValue(params?.id)
            }}>id:{params?.id}</span>  
          </Tooltip>
          {!isHiddenMore && (
            <Dropdown menu={menuProps} overlayClassName={styles.cardItemDropdown}>
              <img
                src={more.src}
                alt=""
                className={styles.itemMoreImg}
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          )}
        </div> */}
      </div>
      <span style={{
        position: 'absolute',
        top: "5px",
        right: "10px",
        color: '#ccc',
        fontSize: '12px'
      }}>{localStorage.getItem('isAgentTest') ? params?.id : ''} </span>
    </>
  );
};
export default FlowCardHeader;
