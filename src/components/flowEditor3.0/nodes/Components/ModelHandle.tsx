import { useState,useEffect } from 'react';
import {useRouter} from 'next/router';
import BlockNmLlmSlice from '@/components/flow3.0/block/BlockNmLlmSlice';
import { waiWangHosts } from '@/config/commonConfig';
import {
    reqPromptList
} from '@/service/flow3.0';
import { reqGetTeamModels, reqGetModelsDefaultParams } from '@/service/background/project'
import { initPromptList } from '@/config/flowConfig'
import modelIcon from '@/components/flow3.0/block/commons/modelIcon'
import { prompt_authorization } from '@/constants/appConstants';
import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import {Input, Button} from 'antd'
const {TextArea} = Input;
const ModelHandle = (props: any) => {
    const {blocks, targetId, setBlocks} = props;
    const router = useRouter();
    const [teamId, setTeamId] = useState('');
    const [pLoading, setPLoading] = useState(false);
    const [isShowLlmParam, setIsShowLlmParam] = useState(false) // 添加是否显示LLM参数的状态
    const [changeStamp, setChangeStamp] = useState(0)
    const [aiModuleConfig, setAiModuleConfig] = useState(new Array());
    const [waiwangLlmList, setWaiwangLlmList] = useState([])
    const [llmList, setLlmList] = useState([]); // 添加llmList状态
    const [isWaiWang, setIsWaiWang] = useState(false);
    const [modelList, setModelList] = useState([]); // 添加modelList状态
    const [promptList, setPromptList] = useState(initPromptList);
    const [strategyContent, setStrategyContent] = useState(''); // 添加strategy

    useEffect(() => {
        setTeamId(router?.query?.teamId as string)
    }, [router.query.teamId])

    useEffect(() => {
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname;
            setIsWaiWang(waiWangHosts.includes(hostname))
        }
        if (blocks[targetId]?.prompts?.nami_system) {
            setStrategyContent(blocks[targetId]?.prompts?.nami_system || '')
        }
        getPromptList();
    }, [])

    // 处理llmList，使其格式符合BlockLlmSlice组件的要求
    useEffect(() => {
        let isProviderExit = false
        setLlmList(modelList.filter((model: any) => {
            const isActive = model.status === 'active'
            if (isActive && blocks[targetId]?.provider_name == model.provider) {
                isProviderExit = true;
            }
            return isActive
        }).map((model: any) => {
            return {
                label: <span style={{ fontSize: '14px', color: '#657083' }}>{model.label.zh_Hans}<span style={{ color: '#9EA7B8', marginLeft: '3px' }}>{model.models.length}</span></span>,
                title: model.provider,
                options: model.models.filter((item: any) => {
                    return item.status === 'active'
                }).map((item: any) => {
                    return { value: model.provider + ' ' + item.model, label: item.label.zh_Hans, provider: model.provider }
                })
            }
        }))
        console.error(blocks[targetId])
        if (!isProviderExit && modelList.length && blocks[targetId]?.provider_name) {
            blocks[targetId].provider_name = 'zhihuiyun';
            setBlocks(prev => ({...blocks}));
        }
    }, [modelList, targetId])
    const getPromptList = async () => {
        const res = await reqPromptList((teamId ? {
            team_id: teamId
        } : {}));
        if (res) {
            setPromptList([...initPromptList, ...res]);
        }

        const list = await reqGetTeamModels('llm')

        list.forEach((item: any) => {
            const prefix = window.location.origin
            const iconPath = item['icon_small'] ? item['icon_small']['zh_Hans'] : `/api/provider/model-providers/zhinao/icon/icon_small/zh_Hans`
            modelIcon.setModelIcon(item.provider, prefix + iconPath)

            // 直接在这里获取图标并缓存到modelIcon中
            try {
                fetch(prefix + iconPath, {
                    headers: {
                        'Authorization': localStorage.getItem(prompt_authorization) || '',
                        'teamId': teamId
                    }
                }).then(response => {
                    if (response.ok) {
                        return response.blob();
                    }
                    return null;
                }).then(blob => {
                    if (blob) {
                        const imageUrl = URL.createObjectURL(blob);
                        modelIcon.setModelIconCache(item.provider, imageUrl);
                    }
                });
            } catch (error) {
                console.error(`获取提供商 ${item.provider} 图标失败:`, error);
            }
        })

        if (list) {
            setModelList(list)
        }
    }
    const getModelParam = async (providerName: any, modelType: any, isChange: any) => {
        setPLoading(true)
        const param = await reqGetModelsDefaultParams(providerName, modelType)
        const modelParamList = new Array();
        const modelParam: any = new Object()
        param.forEach((p: any) => {
            modelParam[p.name] = p.default
            let component = 'slider'
            if (p.type === 'float' || p.type === 'int') {
                if ((p.min || p.min === 0) && (p.max || p.max === 0)) {
                    component = 'slider'
                } else {
                    component = 'inputNumber'
                }
            } else if (p.type === 'string') {
                if (p.options.length) {
                    component = 'selector'
                } else {
                    component = 'input'
                }
            } else if (p.type === 'boolean') {
                component = 'radio'
            }

            modelParamList.push({
                name: p.label.zh_Hans,
                component: component,
                key: p.name,
                value: p.default,
                step: p.type === 'int' ? 1 : 0.1,
                min: p.min,
                max: p.max,
                options: p.options,
                type: p.type
            })
        })
        // 更改模型 或者 初始化时没有参数 都需要初始化参数
        if (isChange || !blocks[targetId].llm_params || JSON.stringify(blocks[targetId].llm_params) == '{}') {
            blocks[targetId].llm_params = modelParam;
            blocks[targetId].team_id = teamId
            setBlocks(prev => ({...blocks}));
        }
        setAiModuleConfig(modelParamList)
        setPLoading(false)
    }

    useEffect(() => {
        if (changeStamp) {
            getModelParam(blocks[targetId]?.provider_name, blocks[targetId]?.model, true)
        }
    }, [changeStamp])

    useEffect(() => {
        blocks[targetId].prompts.nami_system = strategyContent;
        setBlocks(prev => ({...blocks}));
        // todo 更新到服务端
    }, [strategyContent])
    return (
        <div className={flowStyles.modelHandleBox}>
            <div className={flowStyles.modelHandleTitle}>参数过滤策略</div>
                {/* 大模型配置 */}
            <BlockNmLlmSlice
                isWaiWang={isWaiWang}
                waiwangLlmList={waiwangLlmList}
                llmList={llmList}
                activeBlockId={targetId}
                blocks={blocks}
                setBlocks={setBlocks}
                teamId={teamId}
                isShowLlmParam={isShowLlmParam}
                setIsShowLlmParam={setIsShowLlmParam}
                setChangeStamp={setChangeStamp}
                aiModuleConfig={aiModuleConfig}
                pLoading={pLoading}
                isUserFullScreen={false}
                isSystemFullScreen={false}
                isFunctionCall={true}
            />
            <div className={flowStyles.namiAiContent}>
                <TextArea style={{ height: '100%', background: '#fff' }} value={strategyContent} onChange={(e) => {
                    setStrategyContent(e.target.value);
                }} />
            </div>
            <div className={flowStyles.modelHandleBtn}>
                <Button type="primary" size="small" style={{
                    width: '64px',
                    height: '24px',
                    padding: '5px 8px',
                    fontSize: '12px',
                    borderRadius: '8px',
                    lineHeight: '20px'
                }} onClick={() => {
                    blocks[targetId].prompts.nami_system = strategyContent;
                    setBlocks(prev => ({...blocks}));
                    // todo 更新到服务端
                }}>保存</Button>
            </div>
        </div>
    )
}
export default ModelHandle;