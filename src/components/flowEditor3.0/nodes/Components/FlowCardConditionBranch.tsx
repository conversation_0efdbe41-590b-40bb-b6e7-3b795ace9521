import React, {
    useEffect,
    useRef,
    useState,
  } from "react";
  import styles from "@/styles/newFlow/card.module.scss";
  import more from "@/images/newFlow/more.svg";
  import { Popover } from "antd";
  
  // 自定义节点组件
  const FlowCardConditionBranch = ({ data }: any) => {
    
    const {name,condition_group} = data;
    const containerRef = useRef<HTMLDivElement>(null);
    const [isOverflow, setIsOverflow] = useState(false);
    useEffect(() => {
      const checkOverflow = () => {
        const container = containerRef.current;
        if (container) {
          setIsOverflow(container.scrollHeight > container.clientHeight);
        }
      };
  
      setTimeout(checkOverflow, 100); // 延迟检查以确保在初始渲染后获取正确的高度
    }, []);
    
    
    const items = condition_group[0] && condition_group[0].map((item:any,index:number) => {
      if (!item.name) {
        return (
          <div className={styles.undefinedItem} key={`${item.name.replace(/\{\{(\w+)\}\}/, '$1')} + ${item.op} + ${index}`}>
            <span className={styles.parameterValue}>未定义</span>
          </div>
        );
      } else {
        return (
          <div className={styles.normalItem} key={`${item.name} + ${item.op} + ${index}`}>
            <span className={styles.parameterValue}>{`${item.name.replace(/\{\{(\w+)\}\}/, '$1')}${item.op}${item.value}`}</span>
          </div>
        );
      }
    });
    return (
      <>
        <span className={styles.title}>{name}</span>
        <div className={styles.paramTagContainer} style={{maxHeight: '28px'}} ref={containerRef}>
          {items}
          {isOverflow && (
            <Popover
              content={
               (
                <div
                className={`${styles.paramTagContainer} ${styles.moreListContainer}`}
              >
                {items}
              </div>
               )
              }
              title=""
              trigger="hover"
              arrow={false}
              placement={"bottomLeft"}
            >
              <div className={styles.moreContainer}>
                <img
                  src={more.src}
                  alt=""
                  className={styles.more}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </Popover>
          )}
        </div>
      </>
    );
  };
  export default FlowCardConditionBranch;
  