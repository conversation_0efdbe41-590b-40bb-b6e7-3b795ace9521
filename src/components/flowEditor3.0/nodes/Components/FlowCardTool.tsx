import copy from "@/images/flow3.0/copy.svg"
import copy_disabled from "@/images/flow3.0/copy_disabled.svg"
import edit from "@/images/editBtn.svg"
import deleteBtn from "@/images/flow3.0/delete.svg"
import deleteBtnDisabled from "@/images/flow3.0/delete_disabled.svg"

import edit_disabled from "@/images/editBtnDisable.svg"
import editActive from "@/images/editBtnActive.svg"

import Styles from "@/styles/flow3.0/FlowCardTool.module.scss"
import { useState, useEffect } from "react"

import { isEditActiveAtom, blocksAtom, isDisabledAtom } from '@/atoms/flowEditorAtoms'
import { useRecoilCallback, useRecoilValue, useSetRecoilState } from "recoil"

const FlowCardTool = (props) => {
    const {isEditActive, setIsEditActive, onNodeMenuClick, data, isLoop = false} = props
    const setIsEditActiveAtom = useSetRecoilState(isEditActiveAtom)
    const isDisabled = useRecoilValue(isDisabledAtom)

    useEffect(() => {
        setIsEditActiveAtom(isEditActive)
    },[isEditActive])

    const copyFun = useRecoilCallback(({ snapshot }) => async (id:any) => { 
        const blocks = await snapshot.getPromise(blocksAtom);
        setIsEditActive(false)
        onNodeMenuClick("copy", blocks[id]);
    },[])

    const removeFun = useRecoilCallback(({ snapshot }) => async (id:any) => { 
        const blocks = await snapshot.getPromise(blocksAtom);
        onNodeMenuClick("remove", blocks[id]);
    },[])

    return <div className={Styles.toolContainer + ' toolContainer ' + (isLoop ? Styles.toolContainerLoop : '')}>
        <span className={isDisabled ? Styles.disabledSpan : ''} style={isDisabled ? {
            color: '#ccc',
            cursor: 'not-allowed'
        } : {}} onClick={() => {
            if(!isDisabled) {
                copyFun(data.data.id)
            }
        }}><img src={isDisabled ? copy_disabled.src : copy.src} alt="" />复制</span>
        <span 
        style={isDisabled ? {
            color: '#ccc',
            cursor: 'not-allowed'
        } : {}} 
        onClick={() => {
            if(!isDisabled) {
                setIsEditActive(!isEditActive)
            }
        }}
        className={(isEditActive ? Styles.activedBtn : '') +' ' + (isDisabled ? Styles.disabledSpan : '')}> <img src={isEditActive ? editActive.src : (isDisabled ? edit_disabled.src : edit.src)} alt="" />编辑</span>

        <span
            style={isDisabled ? {
                color: '#ccc',
                cursor: 'not-allowed'
            } : {}} 
            onClick={() => {
                if(!isDisabled) {
                    removeFun(data.data.id)
                }}
            }
        > 
            <img src={isDisabled ? deleteBtnDisabled.src : deleteBtn.src} />
            删除
        </span>
    </div>
}

export default FlowCardTool;