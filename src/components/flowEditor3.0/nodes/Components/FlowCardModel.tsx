import React from "react";
import styles from "@/styles/newFlow/card.module.scss";
import llm from "@/images/newFlow/llm-default.svg";
import modelIcon from '../../../flow3.0/block/commons/modelIcon'
// 自定义节点组件
const FlowModel = ({ data }: any) => {
  const { title, icon, subTitle, provider_name } = data
  return (
    <>
      <span className={`${styles.title} ${styles.alignCenter}`}>{title}</span>
      <div className={styles.model}>
        <img src={ modelIcon.getModelIconCache(provider_name) || icon ||llm.src } className={styles.modelIcon} />
        <span className={styles.modelText}>{subTitle}</span>
      </div>
    </>
  );
};
export default FlowModel;
