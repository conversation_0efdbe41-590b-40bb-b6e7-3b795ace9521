import { Input, Tabs } from "antd"
import Styles from "@/styles/flow3.0/FlowCardEditor.module.scss"

import inputok from "@/images/flow3.0/inputok.svg"
import inputcancel from "@/images/flow3.0/inputcancel.svg"

import { useEffect, useState } from "react"
import { colorList, iconList } from '../../flowConfig'

let isClick = true
const FlowCardEditor = (props) => {
    const { data, onNodeMenuClick, form, linesManager } = props
    const blockData = data?.data
    const [colorIndex, setColorIndex] = useState(-1)
    const [iconIndex, setIconIndex] = useState(-1)
    const [activeInputIndex, setActiveInputIndex] = useState(-1)
    const [name, setName] = useState(blockData.name)
    const [desc, setDesc] = useState(blockData.desc)
    const [activeTab, setActiveTab] = useState('color')

    useEffect(() => { 
        const { meta = {} } = blockData;
        // 初始化
        const { colorIndex, iconIndex } = meta;
        if(colorIndex || colorIndex == 0) {
            setColorIndex(colorIndex)
        }
        if(iconIndex || iconIndex == 0) {
            setIconIndex(iconIndex)
        }
    }, [])
    return <div className={Styles.cardEditorWrapper +' cardEditorWrapper'} onClick={(e) => {
        const target = e.target as HTMLElement | null;
        e.preventDefault()
        e.stopPropagation()
        // 获取父元素
        target.parentElement.click()

    }}>
        <div>
            <div className={Styles.title}>基本信息</div>
            <div className={Styles.inputWrapper}>
                <Input
                    className={(activeInputIndex == 0 ? Styles.activedInput : '') + ' ' +Styles.input}
                    onFocus={() => setActiveInputIndex(0)}
                    placeholder=""
                    onChange={(e) => setName(e.target.value)}
                    // onBlur={() => setActiveInputIndex(-1)}
                    value={name}
                    maxLength={50}

                    onMouseDown={(e) => e.stopPropagation()}
                    onTouchStart={(e) => e.stopPropagation()}
                    onKeyDown={(e: any) => {
                        if (e.key === 'Enter') {
                            const newName = e.target.value;
                            form?.setValueIn('name', newName);
                            linesManager?.forceUpdate();
                            onNodeMenuClick("rename", { ...data.data, name: newName });
                            setActiveInputIndex(-1);
                            e.target.blur();
                        }
                    }}
                    prefix={<div className={Styles.preFix}>标题 <span>|</span></div>}
                />
                <span className={(activeInputIndex == 0 ? Styles.activedBtn : '') + ' ' + Styles.btn}>
                    <span onClick={() => {
                        form?.setValueIn('name', name);
                        // linesManager?.forceUpdate();
                        onNodeMenuClick("rename", { ...data.data, name: name });
                        setActiveInputIndex(-1);
                    }}><img src={inputok.src} alt="" /></span>
                    <span><img src={inputcancel.src} alt="" onClick={() => {
                        setName('')
                        setActiveInputIndex(-1);
                    }} /></span>
                </span>
            </div>
            <div className={Styles.inputWrapper}>
                <Input
                    className={(activeInputIndex == 1 ? Styles.activedInput : '') + ' ' +Styles.input}
                    onFocus={() => setActiveInputIndex(1)}
                    placeholder=""
                    onChange={(e) => setDesc(e.target.value)}
                    // onBlur={() => setActiveInputIndex(-1)}
                    value={desc}
                    onMouseDown={(e) => e.stopPropagation()}
                    onTouchStart={(e) => e.stopPropagation()}
                    onKeyDown={(e: any) => {
                        if (e.key === 'Enter') {
                            const newDesc = e.target.value;
                            form?.setValueIn('desc', newDesc);
                            // linesManager?.forceUpdate();
                            onNodeMenuClick("rename", { ...data.data, desc: newDesc });
                            setActiveInputIndex(-1);
                            e.target.blur();
                        }
                    }}
                    prefix={<div className={Styles.preFix}>描述 <span>|</span></div>}
                />
                <span className={(activeInputIndex == 1 ? Styles.activedBtn : '') + ' ' + Styles.btn}>
                    <span><img src={inputok.src} alt="" onClick={() => {
                        form?.setValueIn('desc', desc);
                        // linesManager?.forceUpdate();
                        onNodeMenuClick("rename", { ...data.data, desc: desc });
                        setActiveInputIndex(-1);
                    }}/></span>
                    <span><img src={inputcancel.src} alt="" onClick={() => {
                        setDesc('')
                        setActiveInputIndex(-1);
                    }} /></span>
                </span>
            </div>
        </div>
        <div>
            <div className={Styles.title} style={{marginBottom: 5}}>自定义样式</div>
            <Tabs 
                className="common_tabs full_tabs"
                onChange={(v) => {
                    setActiveTab(v);
                }}
                items={[{
                    key: 'color',
                    label: '背景色',
                    children: ''
                }, {
                    key: 'icon',
                    label: '图标',
                    children: ''
                }]}
            />
            <div style={activeTab == 'color' ? {display: 'block'} : {display: 'none'}} className={Styles.colorList}>
                {colorList.map((color,index) => {
                    return <span className={Styles.colorItem + (colorIndex == index ? " " + Styles.activeColorItem : '')} onClick={() => {
                        if(!isClick) {
                            return
                        }
                        isClick = false;
                        setColorIndex(index)
                        const newMeta = {
                            ...data.data.meta,
                            colorIndex: index,
                            color: colorList[index]
                        };
                        form?.setValueIn('meta', newMeta);
                        linesManager?.forceUpdate();
                        onNodeMenuClick("recolor", { ...data.data, meta: newMeta });
                        setTimeout(() => {
                            isClick = true;
                        }, 500);
                    }}><span style={{background: color}}></span></span>
                })}
            </div>  
            <div style={activeTab == 'icon' ? {display: 'block'} : {display: 'none'}} className={Styles.iconList}>
                {iconList.map((icon, index) => {
                    return <div className={Styles.iconItem + (iconIndex == index ? " " + Styles.activeIconItem : '')} onClick={() => {
                        if(!isClick) {
                            return
                        }
                        isClick = false;
                        const newMeta = {
                            ...data.data.meta,
                            iconIndex: index
                        };
                        form?.setValueIn('meta', newMeta);
                        linesManager?.forceUpdate();
                        onNodeMenuClick("rename", { ...data.data, meta: newMeta });
                        setIconIndex(index)
                        setTimeout(() => {
                            isClick = true;
                        }, 500);

                    }}>
                    <span className={Styles.imageBorderItem}>
                        <span className={Styles.imageItem} style={{background: colorList[colorIndex]}}>
                            <img src={icon.image.src} alt=""  width={24} height={24}/>
                        </span>
                    </span>
                    <span className={Styles.iconName}>{icon.name}</span></div>
                })}
            </div>
        </div>
    </div>
}
export default FlowCardEditor