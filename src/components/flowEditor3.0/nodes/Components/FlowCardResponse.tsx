import React, {useEffect, useState } from "react";
import styles from "@/styles/newFlow/card.module.scss";
import FlowCardStatus from "./FlowCardStatus";
import ArrowRight from '@/images/arrowRight.svg';
import JsonViewCom from '@/components/commonComponents/jsonView/index';
// import DarkMarkdownRenderer from '@/components/commonComponents/answerMarkdown/dark'
// import {copyToClipboard} from '@/utils/utils'
// import ImgCopyCode from '@/images/copyCode.svg'

// 自定义节点组件
const FlowCardHeader = ({
  data,
}: any) => {
  const [isShowDetail, setIsShowDetail] = useState(false);
  // const params = data?.data;
  // const [inputValue, setInputValue] = useState({});
  // const [resultValue, setResultValue] = useState({});
  // const [resultList, setResultList] = useState([
  //       {
  //           title: '输入',
  //           value: ''
  //       },
  //       {
  //           title: '输出',
  //           value: ''
  //       },
  //   ]);
  // useEffect(() => {
  //   // console.error('data', data.data)
  //   if (data.data) {
  //     let inputs = data.data.inputs || {};
  //     let result = data.data.result || {};
  //     setInputValue({
  //       user_prompt: inputs.user_prompt
  //     })
  //     setResultValue({
  //       output: result.output
  //     })
  //   }
  // }, [data?.data])

  return (
    <div className={styles.flowCardResponseContainer}>
      <div className={styles.flowCardResponseStatus} onClick={() => {
          setIsShowDetail(!isShowDetail);
      }}>
          <FlowCardStatus data={data}></FlowCardStatus>
          <div className="flex items-center justify-center"><img width={20} height={20} src={ArrowRight.src} style={{
              transform: isShowDetail ? "rotate(90deg)" : "rotate(-180deg)"
          }} /></div>
      </div>
      {
        isShowDetail ? (
            <>
                <JsonViewCom data={data?.inputs || {}} title={'输入'} style={{
                  maxHeight: '150px'
                }} />
                <JsonViewCom data={data?.result || {}} title={'输出'} style={{backgroundColor: '#F7F9FA', maxHeight: '150px'}} preview={'output'} />
            </>
            // <>
            //   {
            //             resultList.map(item => {
            //                 return (
            //                     <div className='flex flex-col gap-2'>
            //                         <div className='flex justify-between'>
            //                             <div className='flex items-center justify-center text-[#1D2531] text-sm font-semibold'>{item.title}</div>
            //                             <div className='flex items-center justify-center cursor-pointer' onClick={() => {
            //                                 copyToClipboard(item.value)
            //                             }}><img src={ImgCopyCode.src} /></div>
            //                         </div>
            //                         <DarkMarkdownRenderer content={item.value} className="max-h-80" />
            //                     </div>
            //                 )
            //             })
            //         }
            // </>
        ) : (
          ""
        )
      }
    </div>
  );
};
export default FlowCardHeader;
