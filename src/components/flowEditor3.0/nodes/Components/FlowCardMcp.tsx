import React from "react";
import styles from "@/styles/newFlow/card.module.scss";
// 自定义节点组件
const FlowModel = ({ data }: any) => {
  const { title, icon, subTitle, provider_name } = data
  return (
    <>
      <span className={`${styles.title} ${styles.alignCenter}`}>{title}</span>
      <div className={styles.model}>
        {icon && <img src={icon} className={styles.modelIcon} />}
        {subTitle && <span className={styles.modelText}>{subTitle}</span>}
      </div>
    </>
  );
};
export default FlowModel;
