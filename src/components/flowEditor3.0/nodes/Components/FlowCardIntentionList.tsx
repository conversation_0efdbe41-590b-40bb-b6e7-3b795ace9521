import React, {
    useEffect,
    useRef,
    useState,
  } from "react";
  import styles from "@/styles/newFlow/card.module.scss";
  import more from "@/images/newFlow/more.svg";
  import { Popover } from "antd";
  
  // 自定义节点组件
  const FlowCardIntentionList = ({ index, data }: any) => {
    
    const {content} = data;
    const containerRef = useRef<HTMLDivElement>(null);
    const [isOverflow, setIsOverflow] = useState(false);
    useEffect(() => {
      const checkOverflow = () => {
        const container = containerRef.current;
        if (container) {
          setIsOverflow(container.scrollHeight > container.clientHeight);
        }
      };
  
      setTimeout(checkOverflow, 100); // 延迟检查以确保在初始渲染后获取正确的高度
    }, []);
    
    console.log(data, index)

    return (
      <>
        <span style={{textAlign:"left"}} className={styles.title}>
          {data.type !== "default" ? `选项${index + 1}` : "其他0"}
          
          </span>
        
        <div className={styles.paramTagContainer} style={{maxHeight: '28px', fontSize: "14px"}} ref={containerRef}>
          {content}
          {isOverflow && (
            <Popover
              content={
               (
                <div
                className={`${styles.paramTagContainer} ${styles.moreListContainer}`}
              >
                {content}
              </div>
               )
              }
              title=""
              trigger="hover"
              arrow={false}
              placement={"bottomLeft"}
            >
              <div className={styles.moreContainer}>
                <img
                  src={more.src}
                  alt=""
                  className={styles.more}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </Popover>
          )}
        </div>
      </>
    );
  };
  export default FlowCardIntentionList;
  