/*
 * @Author: yh
 * @Date: 2025-06-12 15:44:42
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-13 13:50:58
 * @FilePath: \prompt-web\src\components\flowEditor\nodes\Components\FlowCardStatus.tsx
 */
import React, { useEffect, useState } from "react";
import styles from "@/styles/newFlow/card.module.scss";
import llm from "@/images/newFlow/llm-default.svg";
import Cancel from "@/images/newFlow/icon-status-cancel.svg";
import Exception from "@/images/newFlow/icon-status-exception.svg";
import Fail from "@/images/newFlow/icon-status-fail.svg";
import Pause from "@/images/newFlow/icon-status-pause.svg";
import Success from "@/images/newFlow/icon-status-success.svg";
import Noupdate from "@/images/newFlow/icon-status-noupdate.svg";
import Unused from "@/images/newFlow/icon-status-unused.svg";
import Running from "@/images/newFlow/icon-status-running.svg";

type StatusKey = keyof typeof statusMapping;
const statusMapping = {
  "-1": { label: "异常", color: "#FFECE8", icon: Exception },
  "1": { label: "未调试", color: "#FFECE8", icon: Unused },
  "2": { label: "调试中", color: "#E8F7FF", icon: Running },
  "3": { label: "调试成功", color: "#E8FFEA", icon: Success },
  "4": { label: "调试失败", color: "#FFECE8", icon: Fail },
  "5": { label: "已编辑待更新", color: "#FFF7E8", icon: Noupdate },
  "6": { label: "已取消", color: "#EDF1F5", icon: Cancel },
  "7": { label: "暂停", color: "#FFF7E8", icon: Pause },
  "8": { label: "未调用", color: "#EDF1F5", icon: Unused },
};
// 自定义节点组件
const FlowCardStatus = ({ data }: any) => {
  const { status, type } = data;
  const [rotate,setRotate] = useState<Boolean>(false)
  // 将status转换成字符串
  const statusStr = status?.toString() as StatusKey;
  useEffect(() => {
    if (statusStr === "2") {
      setRotate(true);
    } else {
      setRotate(false); // 其他状态时重置旋转状态
    }
  }, [statusStr]); // 依赖于 statusStr


  useEffect(() => {
    if(type == 'form_interaction' && status == 7) {
      statusMapping['7'] = { label: "调式中", color: "#E8F7FF", icon: Running };
      setRotate(true);
    }
  }, [type, status])

  if (!status || +status === 1) {
    return <></>;
  }

 const getStatusDetails = (status: StatusKey) => statusMapping[statusStr];
  const { label, color, icon } = getStatusDetails(statusStr);
  return (
    <>
      <div
        className={styles.statusContainer}
        style={{ backgroundColor: color }}
      >
        <div className='flex items-center justify-center'><img src={icon.src} className={`${styles.statusIcon} ${rotate ? styles.rotate : ''}`} /></div>
        <div className={`${styles.statusTitle} ${styles.alignCenter}`}>
          {label}
        </div>
      </div>
    </>
  );
};
export default FlowCardStatus;
