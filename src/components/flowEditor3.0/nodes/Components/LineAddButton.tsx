import React, { useCallback, useEffect, useState } from "react";
import ADDLine from "@/images/flow3.0/addNew.svg";
import ADDSettings from "@/images/addSettings.svg";
import AddBlockContent from "../../AddBlockContent";
import { Popover } from "antd";
import ModelHandle from "./ModelHandle";
import { useRouter } from 'next/router';

import flowStyles from '@/styles/flow3.0/Flow.module.scss'
import { getOS } from "../../flowConfig";

import {
  activeBlockIdAtom,
  isDisabledAtom
} from "@/atoms/flowEditorAtoms";

import { useRecoilState, useRecoilValue, useSetRecoilState } from "recoil";

const LineAddButton = (props: any) => {
  const { line, color, flowType, onAddNode, blocks, setBlocks, setOpenAgent, setActiveBlockId } = props
  const activeBlockId = useRecoilValue(activeBlockIdAtom);
  const isDisabled = useRecoilValue(isDisabledAtom)
  const branchId = line?.info?.fromPort?.replace("condition-if-",'')

  const [isAddCondition, setIsAddCondition] = useState(true)
  const [isShowCardList, setIsShowCardList] = useState(false);
  const [isShowModelHandle, setIsShowModelHandle] = useState(false);

  const router = useRouter()

  const [urlFlowType, setUrlFlowType] = useState('')

  // 渐变 + 号节点
  const fromNode = line?.from?.toJSON()
  const toNode = line?.to?.toJSON()
  const fromColor = fromNode?.data?.meta?.color
  const toColor = toNode?.data?.meta?.color
  const uniqueId =  fromNode?.data?.id + toNode?.data?.id

  useEffect(() => {
    setUrlFlowType(router.query.flow_type + '' || '1')
  }, [router.query.flow_type])
  const addShowCardList = (open: boolean) => {
    setIsShowCardList(open)
    // let conditionBlockCount = 0;
    // Object.keys(blocks).forEach((id:any) => {
    //     if(blocks[id].type === 'condition_start'){
    //         conditionBlockCount++;
    //     }
    // })
    // if(branchId && threeLevelBranches.includes(branchId)) {
    //   setIsAddCondition(false)
    //   return 
    // }
    // if(conditionBlockCount > 9) {
    //   setIsAddCondition(false)
    // } else {
    //   setIsAddCondition(true)
    // }
  };
  const handleModelHandleChange = (open: boolean) => {
    setIsShowModelHandle(open);
  }
  return (
    <div 
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        display: isDisabled || (!line?.info?.from || !line?.info?.to) ? 'none' : 'block',
      }}
      d-sourceid={line?.info?.from}
      d-targetid={line?.info?.to}
      d-sourceportid={line?.info?.fromPort}
      d-branchid={branchId}
      onClick={() => {
        setActiveBlockId('')
      }}
      className={flowStyles.addbtnLine + " " + (isShowCardList ? flowStyles.addLineActive : "")}
    >
      {line?.info?.fromPort === "loop-bottom" && line?.info?.toPort === "loop-body-top" ? '' :  <Popover
        content={<AddBlockContent 
          addNode={onAddNode}
          blocks={blocks}
          activeBlockId={activeBlockId}
          sourceId={line?.info?.from}  
          targetId={line?.info?.to}
          setOpen={setIsShowCardList}
          setOpenAgent={setOpenAgent}
          sourcePortId={line?.info?.fromPort?.replace("condition-if-",'')?.replace('query_classification-','')}
        />}
        trigger="click"
        arrow={false}
        placement={"right"}
        open={isShowCardList}
        onOpenChange={addShowCardList}
        overlayClassName={flowStyles.menubarPopover}
      > 
        {/* <div className={(getOS() != 'mac' ? flowStyles.addLineBoxNewImgWindows : '') +" "+ flowStyles.addLineBoxNewImg + " " + (isShowCardList ? flowStyles.addLineBoxNewImgActive : "")}>
          <span style={{
            background: toColor ? "linear-gradient(92deg, " + fromColor + " -1.64%, " + toColor + " 112.02%)" : fromColor
          }}>
            <img
              d-sourceid={line?.info?.from}
              d-targetid={line?.info?.to}
              d-sourceportid={line?.info?.fromPort}
              d-branchid={branchId}
              src={ADDLine.src}
              className={flowStyles.addBtn}
            />
          </span>
        </div> */}
        <div className={flowStyles.addLineBoxNewImg}
              d-sourceid={line?.info?.from}
              d-targetid={line?.info?.to}
              d-sourceportid={line?.info?.fromPort}
              d-branchid={branchId}>
          {/* <span>
            <img
              d-sourceid={line?.info?.from}
              d-targetid={line?.info?.to}
              d-sourceportid={line?.info?.fromPort}
              d-branchid={branchId}
              src={ADDLine.src}
              className={flowStyles.addBtn}
            /> */}
            <svg width="26" height="26" viewBox="0 0 100 100"
                xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id={`${uniqueId}`} x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stop-color={fromColor} />
                  <stop offset="100%" stop-color={toColor || fromColor} />
                </linearGradient>
              </defs>
              <circle cx="50" cy="50" r="48" fill={`url(#${uniqueId})`} />
              <line x1="50" y1="30" x2="50" y2="70" stroke="white" stroke-width="8" stroke-linecap="round"/>
              <line x1="30" y1="50" x2="70" y2="50" stroke="white" stroke-width="8" stroke-linecap="round"/>
            </svg>
          {/* </span> */}
        </div>
      </Popover>}
      {/* <div className={flowStyles.addLineBoxNew}>
      <div style={{
        width: '1px',
        height: '12px',
        borderRadius: '2px',
        opacity: 0.5,
        background: '#ffffff'
      }}></div>
      <Popover
        content={<ModelHandle 
          flowType={flowType}
          addNode={onAddNode}
          blocks={blocks}
          setBlocks={setBlocks}
          activeBlockId={activeBlockId}
          sourceId={line?.info?.from}  
          targetId={line?.info?.to}
          setOpen={setIsShowCardList}
          sourcePortId={line?.info?.fromPort?.replace("condition-if-",'')?.replace('query_classification-','')}
        />}
        trigger="hover"
        arrow={false}
        placement={"right"}
        open={isShowModelHandle}
        onOpenChange={handleModelHandleChange}
        overlayClassName={flowStyles.menubarPopover}
      > 
        <div className={flowStyles.addLineBoxNewImg}>
          <img
            d-sourceid={line?.info?.from}
            d-targetid={line?.info?.to}
            d-sourceportid={line?.info?.fromPort}
            d-branchid={branchId}
            src={ADDSettings.src}
            className={flowStyles.addBtn}
            style={{
              width: "20px",
              height: "20px",
            }}
          />
        </div>
        
      </Popover>
      </div> */}
      </div>
  );
};
export default LineAddButton;
