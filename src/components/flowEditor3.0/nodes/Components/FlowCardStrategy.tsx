import React from "react";
import styles from "@/styles/newFlow/card.module.scss";

// 定义策略类型映射
const strategyMapping: { [key: string]: string } = {
  "react": "ReAct",
};

// 自定义节点组件
const FlowCardStrategy = ({ strategy }: any) => {
  // 获取映射后的策略名称，如果没有映射则使用原始值
  const displayStrategy = strategyMapping[strategy] || strategy;
  
  return (
    <>
      <span className={styles.title}>Agent策略</span>
      <div className={styles.paramTagContainer} style={{ maxHeight: 'none', overflow: 'visible' }}>
        <div className={styles.tagItemWrapper}>
          <div className={styles.normalItem}>
            <span className={styles.parameterValue}>{displayStrategy}</span>
          </div>
        </div>
      </div>
    </>
  );
};

export default FlowCardStrategy;
