import { useCallback, useEffect, useState } from 'react';

import TabsStyles from '@/styles/flow3.0/Tab.module.scss';
import ShowMore from '@/images/flow3.0/showmore.svg'
import { Popover } from 'antd';
import { slice } from 'ramda';

export const ToolTabs = (props: any) => { 
    const {tags, setActiveTab} = props

    const sliceIndex = 5
    const [alltags, setAlltags] = useState([])
    const [tagsList, setTagsList] = useState([])  
    const [tagsMoreList, setTagsMoreList] = useState([])   
    const [innerActiveTab, setInnerActiveTab] = useState(0)
    
    useEffect(() => {
        // 初始化
        setAlltags(tags)
    }, [tags])
    useEffect(() => {
        // 获取标签列表
        setTagsList(alltags.slice(0, sliceIndex))
        // 获取更多标签列表
        setTagsMoreList(alltags.slice(sliceIndex))
    }, [alltags])

    useEffect(() => {
        if(innerActiveTab >= sliceIndex) {
            setAlltags(prev => {
                // 交换位置
                const arr = [...prev];
                [arr[sliceIndex-1], arr[innerActiveTab]] = [arr[innerActiveTab], arr[sliceIndex-1]];
                return arr;
            })
            setInnerActiveTab(sliceIndex - 1)
        }
        if(alltags.length){
            setActiveTab(alltags[innerActiveTab].id)
        }
    },[innerActiveTab])

    return <div className={TabsStyles.tabWrapper}><div className={TabsStyles.tabList}>
        {tagsList.map((tag: any, index: number) => {
            return <div key={tag.id} className={`${TabsStyles.tab} ${innerActiveTab == index ? TabsStyles.activeTab : ''}`} onClick={() => {
                setInnerActiveTab(index)
            }}>
                {tag.name}
            </div>
        })}
    </div>
    <Popover
        trigger="hover"
        arrow={false}
        // open={true}
        placement='bottom'
        content={<div className={TabsStyles.moreList}>
            {tagsMoreList.map((tag: any, index: number) => { 
                return <div key={tag.id} className={`${TabsStyles.tab} ${innerActiveTab == index + sliceIndex ? TabsStyles.activeTab : ''}`} onClick={() => {
                    setInnerActiveTab(index + sliceIndex)
                }}>
                    {tag.name}
                </div>
            })}
        </div>}
    >
        <img src={ShowMore.src}  width='16px' className={TabsStyles.moreBtn} />
    </Popover>
    
    </div>

}