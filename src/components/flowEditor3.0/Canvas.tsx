import { useClientContext } from '@q/flowgram.ai.free-layout-editor';
import { useEffect } from 'react';


import { playgroundAtom } from '@/atoms/flowEditorAtoms'
import { useSetRecoilState } from 'recoil';
const Canvas = (props) => {
	const { handleDrop, handleDragOver, onClick } = props;
	const context = useClientContext();
	const setPlaygroundAtom = useSetRecoilState(playgroundAtom)


	useEffect(() => {
		setPlaygroundAtom({
			zoom: context.playground.config.config.zoom || 1,
			scrollX: context.playground.config.config.scrollX || 0,
			scrollY: context.playground.config.config.scrollY || 0
		})
	},[context.playground.config.config.zoom, context.playground.config.config.scrollX, context.playground.config.config.scrollY])
	

	return (
		<div
			id="canvas"
			style={{
				width: "100%",
				height: "100%"
			}}
			onDrop={(event) => {
				const position = context.playground.config.getPosFromMouseEvent(event, true);
				console.log(position,'计算拖入的迭代节点')
				handleDrop(event, position)
			}}
			onDragOver={handleDragOver}
			onClick={onClick}
		>
			{ props.children }
		</div>
	)
}

export default Canvas;