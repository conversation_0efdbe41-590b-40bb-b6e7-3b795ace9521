import { FreeLayoutEditorProvider, FreeLayoutPlugin<PERSON><PERSON><PERSON><PERSON>, EditorRenderer, useService, delay } from '@q/flowgram.ai.free-layout-editor';
import Canvas from './Canvas'

import { useEditorProps } from './hooks';
import React, {
    useCallback,
    useEffect,
    useRef,
    useState,
} from "react";

import { debounce } from 'lodash'

import {
  activeBlockIdAtom, blocksAtom, edgesAtom, blocksPropertyAtom, deletedBranchInfo<PERSON>tom,
  showDropTipsAtom,
  createEdgeInfoAtom,
  dragTypeAtom, cloneBlock<PERSON>tom, isRunTestStatusAtom, deleteInfoAtom, updatePortBlock<PERSON>tom, loopConfigsAtom, isDisabledAtom
} from "@/atoms/flowEditorAtoms";
import { useRecoilValue, useSetRecoilState, useRecoilState, useRecoilCallback, useResetRecoilState } from "recoil";

import { clone, set } from "ramda";
import { getAllNextBlocks, getCardType } from "../flow3.0/flowFunc";
import { blockConnectorType, colorList } from './flowConfig'
import AddBlockContent from "./AddBlockContent";


import { reqUpdateBlock, reqDeleteEdge, reqCreateEdge, reqSaveFlowPosition, reqSaveFlow, reqCopyUImcp } from "@/service/flow3.0";
import { ToolBar } from './Toolbar';
import { message } from 'antd';
import { version } from 'os';

const initDeleteEdge = {
  sourceId: "",
  targetId: "",
  sourcePortId: ''
}

let deleteEdge = initDeleteEdge;
let deleteNodeId = '';
let isInit = true
export const FlowEditor = (props:any) => {
  const {
    blocks,
    blocksPosition,
    setBlocksPosition,
    setBlocks,
    setActiveBlockId,
    setIsShowSet,
    addBlock,
    deleteBlock,
    blockLoading,
    flowLoading,
    chainId,
    teamId,
    flowType,
    setCloneBlockItem,
    blockIds,
    activeBlockId,
    isVersion,
    setInitLoading,
    isShowRight,
    isUserFullScreen,
    saveFlow,
    clearStamp,
    setClearStamp,
    setBlockIds,
    firstInitMATValue
  } = props;

  const blocksPositionRef = useRef<any>(null);
  const blocksRef = useRef<any>(null);
  const isCommandPressedRef = useRef<any>(null);

  useEffect(() => {
    blocksPositionRef.current = blocksPosition;
  }, [blocksPosition]);

  useEffect(() => {
    blocksRef.current = blocks;
  }, [blocks]);


  const ref:any = useRef<FreeLayoutPluginContext | undefined>();

  const [activeBlockIdAtomData, setActiveBlockIdAtom] = useRecoilState<any>(activeBlockIdAtom);
  const [atomBlocks, setAtomBlocks] = useRecoilState(blocksAtom)
  const [atomEdges, setAtomEdges] = useRecoilState(edgesAtom)
  const setShowDropTips = useSetRecoilState(showDropTipsAtom)
  const setCreateEdgeInfo = useSetRecoilState(createEdgeInfoAtom)

  const [loopConfigs, setLoopConfigsAtom] = useRecoilState(loopConfigsAtom)
  const setCloneBlockAtom = useSetRecoilState(cloneBlockAtom)
  const [openAgent, setOpenAgent] = useState(false)
  const [isCommandPressed, setIsCommandPressed] = useState(false);

  useEffect(() => {
    isCommandPressedRef.current = isCommandPressed;
  }, [isCommandPressed]);


  const setAtomBlocksProperty = useSetRecoilState(blocksPropertyAtom)
  const setUpdatePortBlock = useSetRecoilState(updatePortBlockAtom)
  const dragType = useRecoilValue(dragTypeAtom)
  const [mousePosition, setMousePosition] = useState({x: 0, y: 0})
  const isRunTestStatus = useRecoilValue(isRunTestStatusAtom);
 const isRunTestRef = useRef(isRunTestStatus);
 const deletedBranchInfo = useRecoilValue(deletedBranchInfoAtom);
  useEffect(() => {
      isRunTestRef.current = isRunTestStatus;
  }, [isRunTestStatus]);
  useEffect(() => {
    // 更新activeBlockId
    setActiveBlockIdAtom(activeBlockId)
    deleteNodeId = activeBlockId
  },[activeBlockId])


  useEffect(() => { 
    console.log('PPPPsaveFlowPPPP', blocks, isInit)
    setAtomBlocks(clone(blocks))
    !isInit && saveFlow()
  },[blocks])

  useEffect(() => {
    if (firstInitMATValue) {
      setOpenAgent(true);
    }
  }, [firstInitMATValue])

  const onNodeDeleteHandle = useCallback(
    (id: any) => {
      if(id) {
        deleteBlock(id);
      }
    },
    [deleteBlock]
  );

  const upDatedBlock = useCallback(
    async (block: any) => {
      setBlocks(prev => ({
        ...prev,
        [block.id]: block
      }));
    },
    [blocks, setBlocks]
  );

  const nodeMenuClick = useCallback(
    (eventType: any, data: any) => {
      console.log("eventType", eventType, data);
      switch (eventType) {
        case "remove":
          onNodeDeleteHandle(data.id);
          break;
        case "copy": {
          const curBlockItem: any = clone(data);
          if(data.type == 'nami_agent_loop') {
            // 迭代节点
            // 复制迭代节点以及内部节点和边
            addLoopBodyBlocks(curBlockItem)
          }else {
            const oldPosition = blocksPositionRef.current;
            const newId = Math.ceil(Math.random() * 1000000) + ""
            const cloneItem = {
              ...curBlockItem,
              name: '复制_' + curBlockItem.name,
              id: newId
            };
            // 复制 循环体内复制
            onAddNode({
              blocks, 
              type: data.type, 
              sourceId:  cloneItem?.loop_id, 
              targetId: '',
              cloneBlockItem: cloneItem,
              position: {
                x: oldPosition[data.id]?.x,
                y: oldPosition[data.id]?.y + 200,
              },
            })
  
            // 复制用户界面
            reqCopyUImcp({
              "flow_id": chainId,
              "source_block_key": curBlockItem.id,
              "target_block_key": newId
            })
          }
          break;
        }
        case "rename":
          upDatedBlock(data);
          break;
        case "recolor":
          upDatedBlock(data);
          break;
        default:
          break;
      }
    },
    [onNodeDeleteHandle, setCloneBlockItem]
  );
  const nodeClick = (data: any) => {
    console.log('nodeClick', isRunTestRef.current)
    if (isRunTestRef.current) {
      return;
    }
    setActiveBlockId(data.id)
    setIsShowSet(true)
  }

  const onAddNode = useCallback((params:any) => {
    const { blocks, type, sourceId, targetId, sourcePortId='', cloneBlockItem={}, position, isDragEdgeAdd=false } = params;

    if (!type) {
      return;
    }
    
    addBlock({type, p_preBlockId: sourceId , p_targetBlockId: targetId, p_sourcePortId: sourcePortId, position, isDragEdgeAdd}, blocks, cloneBlockItem);
  }, [Object.keys(blocks)])

  const replaceBlockKeysAndEdges = (
    all_block_keys: string[],
    edges: Array<{ sourceNodeID: string; sourcePortID: string; targetNodeID: string }>,
    blocks: any,
    newLoopId: any
  ) => {
    const keyMap = {}; // 存储新旧键的映射关系
    const newBlockKeys = [];
    const addBlocks = {}
    const addBlocksPosition = {}
    const oldPosition = blocksPositionRef.current
  
    // 生成新键并建立映射
    for (const oldKey of all_block_keys) {
      const newKey = Math.ceil(Math.random() * 1000000) + "";
      keyMap[oldKey] = newKey;
      newBlockKeys.push(newKey);
      addBlocks[newKey] = {
        ...blocks[oldKey],
        id: newKey,
        name: '复制_' + blocks[oldKey].name,
        loop_id: newLoopId
      };
      addBlocksPosition[newKey] = oldPosition[oldKey];
      // 复制用户界面
      reqCopyUImcp({
        "flow_id": chainId,
        "source_block_key": oldKey,
        "target_block_key": newKey
      })
    }
  
    // 更新 edges 中的节点 ID
    const newEdges = edges.map((edge) => ({
      ...edge,
      sourceNodeID: keyMap[edge.sourceNodeID],
      targetNodeID: keyMap[edge.targetNodeID]
    }));
    
  
    return {
      addBlocks,
      newBlockKeys,
      newEdges,
      addBlocksPosition
    };
  }

  const addLoopBodyBlocks = useRecoilCallback(({ snapshot }) => async (data:any) => {

    const loopid = data.id
    const newLoopId = Math.ceil(Math.random() * 1000000) + ""
    const loopConfigs = await snapshot.getPromise(loopConfigsAtom);
    const blocks = await snapshot.getPromise(blocksAtom);
    const config = loopConfigs[loopid];
    const edges = config.edges
    const all_block_keys = config.all_block_keys

    const { addBlocks, newBlockKeys, newEdges, addBlocksPosition } = replaceBlockKeysAndEdges(all_block_keys, edges, blocks, newLoopId)

    // 保存loopconfigs
    setLoopConfigsAtom({
      ...loopConfigs,
      [newLoopId]: {
        blocks: {},
        all_block_keys: newBlockKeys,
        edges: newEdges
      }
    })
    // 保存位置
    const oldPosition = blocksPositionRef.current
    
    const p = {
      ...blocksPositionRef.current,
      ...addBlocksPosition,
      [newLoopId]: {
        x: oldPosition[loopid]?.x,
        y: oldPosition[loopid]?.y + ((document.getElementById(loopid).offsetHeight + 200) || 200),
      }
    }
    await reqSaveFlowPosition({
      template_id: chainId,
      meta: JSON.stringify(p)
    })
    setBlocksPosition(p)
    // 保存blocks
    console.log(addBlocks,'====addBlocks====')
    setBlocks(prev => {
      return {
        ...prev,
        [newLoopId]: {
          ...data,
          name: '复制_' + data.name,
          id: newLoopId
        },
        ...addBlocks
      }
    })

    setBlockIds(pre => {
      return [...pre, newLoopId,...Object.keys(addBlocks)]
    })

    // 复制迭代节点的用户界面
    reqCopyUImcp({
      "flow_id": chainId,
      "source_block_key": loopid,
      "target_block_key": newLoopId
    })

  }, [])

  useEffect(() => {
    if(clearStamp){
      // 清除
      // todo 点击画布会保存一下 需要改下
      // const allLines = ref.current.document.linesManager.getAllLines();
      // allLines?.forEach((l:any) => {
      //   const item = l.toJSON();
      //   // 删除只有起点 没有终点的边
      //   if(item.sourceNodeID && !item.targetNodeID){
      //     l.dispose();
      //   }
      // })
      // if(document.getElementsByClassName('add-block-content').length) {
      //   document.getElementsByClassName('add-block-content')[0].remove()
      // }
    }
  },[clearStamp])

  const reload = useRecoilCallback(({ snapshot }) => async (nodeList:any, edgeList:any) => {
    
    const d:any = await snapshot.getPromise(deleteInfoAtom);
    const loopConfigs = await snapshot.getPromise(loopConfigsAtom)
    const blocks = await snapshot.getPromise(blocksAtom)
    await ref.current.document.fromJSON({
      nodes: nodeList,
      edges: edgeList.filter(e => e.sourceNodeID && e.targetNodeID), //过滤没有终点的边 否则回退时报错
    })
    console.log(nodeList, edgeList,'--reloadinfo1--')
    console.log(loopConfigs, blocks, atomEdges, d,'--reloadinfo2--')
    const allLines = ref.current.document.linesManager.getAllLines();
    !isInit && saveFlow()
  
    d.node.forEach(b => {
      ref.current.document.removeNode(b)
    })

    d.edge.forEach(e => {
      const {sourceNodeID, sourcePortID, targetNodeID} = e
      allLines?.forEach((l:any) => {
        const item = l.toJSON();

        if(item.sourceNodeID == sourceNodeID && item.targetNodeID == targetNodeID && (item.sourcePortID?.replace('condition-if-','') == sourcePortID || !item.sourcePortID)){
          l.dispose();
        }
      })
    })
    allLines?.forEach((l:any) => {
      const item = l.toJSON();

      // 删除只有起点 没有终点的边
      if(item.sourceNodeID && !item.targetNodeID ){
        l.dispose();
      }
    })
    // 不能直接remove 否则销毁的时候找不到元素
    const l =document.getElementsByClassName('add-block-content').length
    if(l) {
      for(let i = 0; i < l; i++){
        document.getElementsByClassName('add-block-content')[i].setAttribute('class', 'add-block-content hide')
      }
    }
    isInit = false
    
}, [blocks])

const dragBlockOutLoop = useRecoilCallback(({ snapshot }) => async (e:any, dragblockid:string, position:any, node:any, nodeIntoContainerService:any, selectService:any, dragService:any, operationBaseService:any) => {
  console.log('dragBlockOutLoop', position)
  if(isCommandPressedRef.current) {
    console.log('dragBlockOutLoop', nodeIntoContainerService.moveOutContainer)

    // 节点移除容器
    nodeIntoContainerService.moveOutContainer({ node });

    operationBaseService.updateNodePosition(node, {
      x: position.x,
      y: position.y
    })

    // select node - 选中节点
    selectService.selectNode(node);
    // start drag node - 开始拖拽
    dragService.startDragSelectedNodes(e);

    

    const currentEdges = await snapshot.getPromise(edgesAtom);
    const blocks = await snapshot.getPromise(blocksAtom);
    const l = await snapshot.getPromise(loopConfigsAtom);
  
    const blocksIds = Object.keys(blocks)

    const fromLoopid = blocks[dragblockid].loop_id
    
    // 更新位置
    // const oldPosition = blocksPositionRef.current
    // // oldPosition[fromLoopid].x oldPosition[fromLoopid].y
    // const p = {
    //   ...blocksPositionRef.current,
    //   [dragblockid]: {
    //     x: oldPosition[fromLoopid].x + oldPosition[dragblockid].x,
    //     y: oldPosition[fromLoopid].y + oldPosition[dragblockid].y
    //   }
    // }
    // setBlocksPosition(p)

    // await reqSaveFlowPosition({
    //   template_id: chainId,
    //   meta: JSON.stringify(p)
    // })

    // 更新节点
    upDatedBlock({
      ...blocks[dragblockid],
      loop_id: ''
    })
    
    //  删除关联的边
    const allLines = ref.current.document.linesManager.getAllLines();
    allLines?.forEach((l:any) => {
      const item = l.toJSON();
      if(item.sourceNodeID == dragblockid || item.targetNodeID == dragblockid) {
        l.dispose();
      }
    })
    
    // 更新迭代配置
    if(fromLoopid) {
      setLoopConfigsAtom({
        ...l,
        [fromLoopid]: {
          ...l[fromLoopid],
          blocks: {},
          all_block_keys: l[fromLoopid].all_block_keys.filter(item => item != dragblockid),
          edges: l[fromLoopid].edges.filter(edge => edge.sourceNodeID != dragblockid && edge.targetNodeID != dragblockid)
        },
      })
    }
    // setClearStamp(new Date().getTime())
    saveFlow()
  }
},[])

const dragBlockInLoop = useRecoilCallback( ({ snapshot }) => async (e:any, dragblockid:string, position:any, node:any, context:any, nodeIntoContainerService:any, operationBaseService:any) => {
  const currentEdges = await snapshot.getPromise(edgesAtom);
  const blocks = await snapshot.getPromise(blocksAtom);
  const l = await snapshot.getPromise(loopConfigsAtom);

  const blocksIds = Object.keys(blocks)

  // in
  const {left, right, top, bottom} = document.getElementById(dragblockid).getBoundingClientRect()
  const dragBlockLeft = left
  const dragBlockRight = right
  const dragBlockTop = top
  const dragBlockBottom = bottom
  let toLoopid = ''
  let fromLoopid = '' 
  blocksIds.forEach(id => {
    if(blocks[id].type == 'nami_agent_loop') {
      // 用当前拖拽的节点位置 去和所有迭代节点的位置对比
      const {left, right, top, bottom} = document.getElementById(id).getBoundingClientRect()
      if(dragBlockLeft > left && dragBlockRight < right && dragBlockTop > top && dragBlockBottom < bottom) {
        console.log(id,dragblockid, position,'计算拖入的迭代节点')
        if(blocks[dragblockid].loop_id != id){
          // 在迭代体内拖拽排除
          toLoopid = id;
          fromLoopid = blocks[dragblockid].loop_id
        }
      }
    }
  })
  if(toLoopid) {
    setShowDropTips(pre => {
      localStorage.setItem('showDropTips', JSON.stringify({
        ...pre,
        [toLoopid]: true
      }))
      return {
        ...pre,
        [toLoopid]: true
      }
    })
    console.log(context.document.getNode,'--context.document.getNode')
    nodeIntoContainerService.moveIntoContainer({
      node,
      containerNode: context.document.getNode(toLoopid)
    })
    
    const oldPosition = blocksPositionRef.current
    operationBaseService.updateNodePosition(node, {
      x: position.x - oldPosition[toLoopid].x,
      y: position.y - oldPosition[toLoopid].y 
    })
   

    // // 更新位置
    // const p = {
    //   ...blocksPositionRef.current,
    //   [dragblockid]: {
    //     x: position.x - oldPosition[toLoopid].x - 30,
    //     y: position.y - oldPosition[toLoopid].y - 30
    //   }
    // }

    // setBlocksPosition(p)
    
    // await reqSaveFlowPosition({
    //   template_id: chainId,
    //   meta: JSON.stringify(p)
    // })


    // 更新节点
    upDatedBlock({
      ...blocks[dragblockid],
      loop_id: toLoopid
    })
    
    //  删除关联的边
    setAtomEdges(currentEdges.filter((edge: any) => edge.sourceNodeID != dragblockid && edge.targetNodeID != dragblockid))

    const allLines = ref.current.document.linesManager.getAllLines();
    allLines?.forEach((l:any) => {
      const item = l.toJSON();
      if(item.sourceNodeID == dragblockid || item.targetNodeID == dragblockid) {
        l.dispose();
      }
    })
    
    // 更新迭代配置
    if(!l[toLoopid].all_block_keys.includes(dragblockid)) {
      if(fromLoopid) {
        setLoopConfigsAtom({
          ...l,
          [fromLoopid]: {
            ...l[fromLoopid],
            blocks: {},
            edges: l[fromLoopid].edges.filter(edge => edge.sourceNodeID != dragblockid && edge.targetNodeID != dragblockid)
          },
          [toLoopid]: {
            ...l[toLoopid],
            blocks: {},
            all_block_keys: l[toLoopid].all_block_keys.concat([dragblockid]),
          }
        })
      }else {
        setLoopConfigsAtom({
          ...l,
          [toLoopid]: {
            ...l[toLoopid],
            blocks: {},
            all_block_keys: l[toLoopid].all_block_keys.concat([dragblockid]),
          }
        })
      }
    } 
    // setClearStamp(new Date().getTime())
    saveFlow()
  }

  
},[])

  useEffect(() => {
    if (
        blocks &&
        atomEdges 
    ) { 
        // flow节点、边映射到画布 结构转化
        const nodeList: any[] = [];
        let edgeList: any[] = [];
        // const localData = localStorage.getItem(chainId + teamId)
        let oldPosition = blocksPosition
        // console.log(oldPosition,blocksPosition,'===allNextBlocksallNextBlocks')
        // 新增节点后面的节点
        let allNextBlocks = []
        // 新增的block
        let newBlockId = ''
        let newBlockPosition;
        blockIds.forEach((key) => {
          const isHasX = oldPosition[key]?.x;
          // console.log(oldPosition[key],'===allNextBlocksallNextBlocksisHasX')  
          if(!(isHasX || isHasX == 0)) {
            newBlockId = key
            allNextBlocks = getAllNextBlocks(key, blocks, atomEdges, loopConfigs)
            // 后续的第一个节点 取此节点的位置给新增的节点
            newBlockPosition = oldPosition[allNextBlocks[0]]
            // console.log(newBlockPosition,newBlockId,allNextBlocks,'---allNextBlocksallNextBlocks')

          }
        })
        // 主干的节点渲染 包括分支节点
        blockIds.map((key: string) => {
            const item = blocks[key];
            if(!item) {
              return;
            }

            // 处理位置信息
            const defaultPosition = {
              x: 500,
              y: 550,
            };
            const isHasX = oldPosition[item.id]?.x;
            let newPosition = ((isHasX || isHasX == 0) ? oldPosition[item.id] :  newBlockPosition) || defaultPosition;
            console.log(newPosition, isHasX, '---allNextBlocksallNextBlocks')
            if(allNextBlocks.includes(key)) {
              // 新建节点后面的节点往后移动400
              newPosition = {
                ...newPosition,
                x: newPosition.x + 400
              }
            }
            const node = {
              id: item.id,
              type: item.type,
              meta: {
                position: newPosition,
              },
              data: {
                  ...item,
                  onNodeClick: nodeClick,
                  dragBlockInLoop,
                  dragBlockOutLoop,
                  onNodeDeleteHandle,
                  onAddNode,
                  onNodeMenuClick: nodeMenuClick, // more菜单对应的功能
              }
            }
            if(item.type === "nami_agent_loop") {
              // 循环节点
              // nodeList.push(node)

              nodeList.push({
                id: item.id,
                type: 'nami_agent_loop',
                meta: {
                  position: newPosition,
                },
                data: {
                  ...item,
                  id: item.id,
                  type: 'nami_agent_loop',
                  name: item.name,
                  all_block_keys: loopConfigs[item.id]?.all_block_keys,
                  onNodeClick: nodeClick,
                  dragBlockInLoop,
                  dragBlockOutLoop,
                  onNodeDeleteHandle,
                  onAddNode,
                  onNodeMenuClick: nodeMenuClick, // more菜单对应的功能
                },
                blocks: [],
                edges: []
              })
            }else {
              if(!item.loop_id){
                nodeList.push(node)
              }
            }
        })

        // 循环体内节点、边渲染
        Object.keys(loopConfigs).forEach((itemLoop: any) => {
          let curBlockIds = loopConfigs[itemLoop].all_block_keys;
          const curEdges = loopConfigs[itemLoop].edges;
          if(curBlockIds?.length) {

            // 新增节点后面的节点
            let allNextBlocks = []
            // 新增的block
            let newBlockId = ''
            let newBlockPosition;
            curBlockIds.forEach((key) => {
              const isHasX = oldPosition[key]?.x;
              if(!(isHasX || isHasX == 0)) {
                newBlockId = key
                allNextBlocks = getAllNextBlocks(key, blocks, atomEdges, loopConfigs)
                // 后续的第一个节点 取此节点的位置给新增的节点
                newBlockPosition = oldPosition[allNextBlocks[0]]

                console.log(newBlockPosition,newBlockId,allNextBlocks,'---allNextBlocksallNextBlocks')
              }
            })

            curBlockIds.forEach((key: string, index:number) => {
              const item = blocks[key];
              if(item?.loop_id) {
                // 循环体内节点
                nodeList.forEach((node:any) => {
                  if(node.id === item.loop_id) {
                    // 处理位置信息
                    const defaultPosition = {
                      x: 50,
                      y: 50,
                    };

                    const isHasX = oldPosition[item.id]?.x;
                    let newPosition = ((isHasX || isHasX == 0) ? oldPosition[item.id] : newBlockPosition) || defaultPosition;
                    console.log(allNextBlocks,'---allNextBlocksallNextBlocks')
                    if(allNextBlocks.includes(key)) {
                      // 新建节点后面的节点往后移动400
                      newPosition = {
                        ...newPosition,
                        x: newPosition.x + 400
                      }
                    }
                    if(1) {
                      // 触发循环体内节点位置变化
                      newPosition = {
                        ...newPosition,
                        x: newPosition.x + 0.1
                      }
                    }
                  
                    node.blocks.push({
                      id: item.id,
                      type: item.type,
                      meta: {
                        position: newPosition,
                      },
                      data: {
                          ...item,
                          onNodeClick: nodeClick,
                          dragBlockInLoop,
                          dragBlockOutLoop,
                          onNodeDeleteHandle,
                          onAddNode,
                          onNodeMenuClick: nodeMenuClick, // more菜单对应的功能
                      }
                    })
                    node.edges = curEdges.map(e => {
                      let sourcePortID = ''
                      if(e.sourcePortID){
                        sourcePortID = 'condition-if-'+e.sourcePortID;
                      }
                      return {
                        ...e,
                        sourcePortID
                      };
                    });
                  }
                })
              }
            })
          }
        })
        // 主线节点连线
        edgeList = [
          ...edgeList,
          ...atomEdges.map(e => {
            let sourcePortID = ''
            let sourceNodeID = e.sourceNodeID
            let targetNodeID = e.targetNodeID
            if(e.sourcePortID){
              sourcePortID = 'condition-if-'+e.sourcePortID;
            }
            return {
              ...e,
              sourcePortID,
              sourceNodeID,
              targetNodeID
            };
          })
        ];
        console.log(blocks, nodeList, edgeList,'--nodeList--')
        const p = getPosition(clone(nodeList))
        // 数据变化 不会触发画布监听 主动保存位置信息
        // reqSaveFlowPosition({
        //   template_id: chainId,
        //   meta: JSON.stringify(p)
        // })
        setBlocksPosition(p)
        reload(nodeList, edgeList)
        setInitLoading(false)
    }
  },[Object.keys(blocks).join(','), flowType, clearStamp])

  // console.log(blocksPosition,'---blocks')

  // useEffect(() => { 
  //   if (
  //     blocks &&
  //     atomEdges 
  //   ) {
  //     const blocksProperty:any = {}
  //     Object.keys(blocks).map((key: string, index: number) => {
  //       const item = blocks[key];
  //       if(!item) {
  //         return;
  //       }
        
  //       const p = {
  //         ...item,
  //         onNodeClick: nodeClick,
  //         onNodeDeleteHandle,
  //         onAddNode,
  //         onNodeMenuClick: nodeMenuClick,
  //       }
  //       blocksProperty[key] = p
  //     })
  //     setAtomBlocksProperty(clone(blocksProperty))
  //   }
  // }, [JSON.stringify(Object.keys(blocks).map((blockId) => {
  //   const blockItem = blocks[blockId]
  //   return {
  //     status: blockItem.status,
  //     id: blockItem.id,
  //     name: blockItem.name,
  //     mid_vars: blockItem.mid_vars,
  //     inputs: blockItem.inputs,
  //     const_params: blockItem.const_params,
  //     cron: blockItem.cron,
  //     var_params: blockItem.var_params,
  //     result: blockItem.result,
  //     mid_var_key_mapping: blockItem.mid_var_key_mapping,
  //     inputs_key_mapping: blockItem.inputs_key_mapping,
  //     result_params: blockItem.result_params,
  //     output_key_mapping: blockItem.output_key_mapping,
  //     block_output_key_mapping: blockItem.block_output_key_mapping,
  //     model_type: blockItem.model_type,
  //     model: blockItem.model, // 添加model字段监听，用于MCP、FC和Agent节点
  //     block_args: blockItem.block_args,
  //     apis: blockItem.apis, // 添加纳米 mcp, agent节点
  //     meta: blockItem.meta,
  //     desc: blockItem.desc,
  //     block_version: blockItem.block_version
  //   }
  // }))])

  const getPosition = (resNodeList:any) => {
    const nodePosition: {
      [key: string]: any;
    } = {};
    resNodeList.map((item: any) => {
      nodePosition[item.id] = item.meta.position;
      if (item.type = 'nami_agent_loop') {
        item?.blocks?.map((childItem:any)  => {
          const x = childItem.meta.position.x;
          const y = childItem.meta.position.y;
          if((x || x == 0)  && (y || y == 0)) {
            // 避免存null的情况
            nodePosition[childItem.id] = childItem.meta.position;
          }
        })
      }
    });
    // console.log('--oldposition--nodePosition', nodePosition);
    return nodePosition;
  }

  const createEdge = useRecoilCallback( ({ snapshot }) => async (sourceId: any, targetId: any, sourcePort: any) => { 
    const isDisabled = await snapshot.getPromise(isDisabledAtom);
    if(isDisabled) {
      message.info('运行中，请稍后再试。')
      return;
    }
    const currentEdges = await snapshot.getPromise(edgesAtom);
    const blocks = await snapshot.getPromise(blocksAtom);
    const l = await snapshot.getPromise(loopConfigsAtom);
    let loopConfigs = clone(l)
    const loop_id = blocks[sourceId]?.loop_id || '';
    
    const e = {
      sourceNodeID: sourceId,
      targetNodeID: targetId,
      sourcePortID: sourcePort,
      targetPortID: '',
    }

    const res = canAddLine(sourceId, targetId)
    console.log(e,res,'=====createEdge===')
    if(!res) {
      return;
    }

    let isSameEdge = false
    currentEdges.forEach(e => {
      if(e.sourceNodeID == sourceId && e.targetNodeID == targetId){
        isSameEdge = true
      }
    })
    if(isSameEdge) {
      // 起点、终点一样，或者port不一样，起点、终点一样 都不可以。
      const allLines = ref.current.document.linesManager.getAllLines();
      allLines?.forEach((l:any) => {
        const item = l.toJSON();
        console.log(item.sourceNodeID == sourceId && item.targetNodeID == targetId, item.sourcePortID, sourcePort)
        if(item.sourceNodeID == sourceId && item.targetNodeID == targetId && item.sourcePortID?.replace('condition-if-','') == sourcePort) {
          l.dispose();
        }
      })
      return;
    } 
    if(targetId != sourceId) {
      if(loop_id){
        loopConfigs[loop_id].edges.push(e)
        setLoopConfigsAtom({...loopConfigs})
      }else {
        setAtomEdges([...currentEdges, e])
      }
      setCreateEdgeInfo({
        sourceNodeID: sourceId,
        targetNodeID: targetId,
      })
      saveFlow()
    }
  },[])

  // console.log(atomEdges, loopConfigs, atomBlocks, '--ppppppreloadinfopppppp--')

  useEffect(() => { 
    if(deletedBranchInfo.blockid) {
      deleteEdgeByPort(deletedBranchInfo)
    }
  }, [deletedBranchInfo])
  const deleteEdgeByPort = useRecoilCallback( ({ snapshot }) => async(deletedBranchInfo:any) => {
    console.log(deletedBranchInfo,'---deletedBranchId')

    const  {blockid, branch_id, branch_index} = deletedBranchInfo
    const currentEdges = await snapshot.getPromise(edgesAtom);
    const l = await snapshot.getPromise(loopConfigsAtom);
    const blocks = await snapshot.getPromise(blocksAtom);
    let loopConfigs = clone(l)
    const loop_id = blocks[blockid]?.loop_id;


    setBlocks((prev:any) => {
      return {
        ...prev,
        [blockid]: {
          ...prev[blockid],
          branches: prev[blockid].branches.filter((item:any, index:number) => index !== branch_index)
        }
      }
    })
    
    if(loop_id) {
      // 迭代体内删除分支
      setLoopConfigsAtom({
        ...loopConfigs,
        [loop_id]: {
          ...loopConfigs[loop_id],
          blocks: {},
          edges: loopConfigs[loop_id].edges.filter(e => !e.sourcePortID || e.sourcePortID != branch_id)
        }
      })
    }else {
      setAtomEdges(currentEdges.filter(e => !e.sourcePortID || e.sourcePortID != branch_id))
    }
    saveFlow()
  },[])

  const deleteEdgeFunc  = useRecoilCallback( ({ snapshot }) =>  async(deleteEdge:any, addEdge:any='') => {
    // 触发删除边逻辑
    if(deleteEdge.sourceId  && deleteEdge.targetId) {
      const isDisabled = await snapshot.getPromise(isDisabledAtom);
      if(isDisabled) {
        message.info('运行中，请稍后再试。')
        return;
      }

      const res = canAddLine(addEdge.sourceNodeID, addEdge.targetNodeID)

      if(!res && addEdge){
        // 添加边 失败
        return;
      }

      const currentEdges = await snapshot.getPromise(edgesAtom);
      const l = await snapshot.getPromise(loopConfigsAtom);
      let loopConfigs = clone(l)
      const blocks = await snapshot.getPromise(blocksAtom);

      let sourceId = deleteEdge.sourceId
      let targetId = deleteEdge.targetId
      let sourcePortId = deleteEdge.sourcePortId
      let loop_id = blocks[sourceId]?.loop_id || '';
      console.log('loop_id',sourceId,blocks,loop_id)
      const resEdges = []
      if(loop_id) {
        loopConfigs[loop_id].edges.forEach((e) => {
          console.log(deleteEdge,e,e.sourceNodeID == sourceId && e.targetNodeID == targetId && (e.sourcePortID == sourcePortId || !sourcePortId),'---deleteEdge')
          if(e.sourceNodeID == sourceId && e.targetNodeID == targetId && (e.sourcePortID == sourcePortId || !sourcePortId)){

          }else{
            resEdges.push(e)
          }
        })

        if(addEdge){
          resEdges.push(addEdge)

          setCreateEdgeInfo({
            sourceNodeID: addEdge.sourceNodeID,
            targetNodeID: addEdge.targetNodeID,
          })
        }

        loopConfigs[loop_id].edges = resEdges
        setLoopConfigsAtom({...loopConfigs})
      }else {
        currentEdges.forEach((e) => {
            if(e.sourceNodeID == sourceId && e.targetNodeID == targetId && (e.sourcePortID == sourcePortId || !sourcePortId)){

            }else{
              resEdges.push(e)
            }
        })
        if(addEdge){
          resEdges.push(addEdge)

          setCreateEdgeInfo({
            sourceNodeID: addEdge.sourceNodeID,
            targetNodeID: addEdge.targetNodeID,
          })
        }
        setAtomEdges(resEdges)
      }
      saveFlow()
    }
  },[chainId, deleteEdge.sourceId, deleteEdge.targetId, deleteEdge.sourcePortId, deleteNodeId]);
  
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Delete' || event.key === 'Backspace') {
      const target = event.target as HTMLElement;
      const isInputOrTextarea = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA';
      const isContentEditable = target.isContentEditable;

      console.log(isInputOrTextarea,isContentEditable,3321312)
      // 如果不是输入元素，则阻止浏览器回退
      if (!isInputOrTextarea && !isContentEditable) {
          event.preventDefault();
      }
      if(deleteNodeId) {
        // onNodeDeleteHandle(deleteNodeId)
        deleteNodeId = ''
      }
      const t:any = event.target
      if(t.getAttribute('class')?.includes('gedit-playground')){
        // 触发删除边逻辑
        deleteEdgeFunc(deleteEdge)
        deleteEdge = initDeleteEdge;
      }
    };

    if((event.metaKey || event.ctrlKey) && event.key != 'c'){
      setIsCommandPressed(true)
    }
  }

  const handleKeyUp = (event) => {
    setIsCommandPressed(false)
  }

  const debounceSavePosition = debounce((p:any) => {
    reqSaveFlowPosition({
      template_id: chainId,
      meta: JSON.stringify(p)
    })
  }, 1000)

  useEffect(() => {
    // 监听画布变化 延迟 1 秒 保存数据, 避免画布频繁更新
    const toDispose = ref.current.document.onContentChange(() => {
      // 通过 toJSON 获取画布最新的数据
      const resNodeList = ref.current.document.toJSON().nodes;
      
      console.log('保存位置',resNodeList);
      const p = getPosition(clone(resNodeList))
      debounceSavePosition(p)
      setBlocksPosition(p)
    })

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    // 初始化置为空
    localStorage.setItem('showDropTips','')
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      toDispose.dispose();
    }
  },[])

  const handleDrop = useCallback(
    (event: { preventDefault: () => void; clientX: any; clientY: any, target:any }, position) => {
      event.preventDefault();
      if(dragType == 'version-api') {
        // api不走这里的逻辑
        return;
      }
      const target = event.target;
      console.log(target,'----target')
      const node = target.closest('div.gedit-flow-activity-node')
      let sourceId = '', targetId ='', branchId = '', sourcePortId = '';
      // const localData = localStorage.getItem(chainId + teamId)
      const oldPosition = blocksPositionRef.current
      if(target.className?.includes('gedit-playground-layer')){
        // 画布上 添加节点
        sourceId = '';
        console.log(sourceId,'xxxxx111111')
      }else if (target.className?.includes('sub-canvas') || target.className?.includes('loop-node-wrapper')) {
        // 子画布上
        console.log(sourceId,'xxxxx22222')
        sourceId = target.closest('div.gedit-flow-activity-node').getAttribute('data-node-id') || ''
        if(oldPosition[sourceId].x) {
          position.x = position.x - oldPosition[sourceId].x
          position.y = position.y - oldPosition[sourceId].y
        }
        console.log(sourceId,position,oldPosition[sourceId],'xxxxx222222')
      }else if(node) {

        console.log(node,'xxxxx22222')
        // 在循环体内节点上
        const nodeId = node.getAttribute('data-node-id')
        
        // 循环体内+号上 本期没有
        sourceId = blocks[node.getAttribute('data-node-id')]?.loop_id || ''


        console.log(node,nodeId,sourceId, 'xxxxx22222')
        console.log(sourceId,11223344)
        if(oldPosition[sourceId]?.x) {
          position.x = position.x - oldPosition[sourceId].x
          position.y = position.y - oldPosition[sourceId].y
        }
        console.log(sourceId,'xxxxxx333333')
      }else{
        // +img 或者 边 
        sourceId = target.getAttribute('d-sourceid') || target.parentElement.getAttribute('d-sourceid')
        targetId = target.getAttribute('d-targetid') || target.parentElement.getAttribute('d-targetid')
        sourcePortId = target.getAttribute('d-sourceportid') || target.parentElement.getAttribute('d-sourceportid') || ''
        sourcePortId = sourcePortId?.replace('condition-if-', '')
        branchId = target.getAttribute('d-branchid') || target.parentElement.getAttribute('d-branchid') || ''

        console.log(sourceId,'xxxxxx4444444')
        // todo 拖拽到循环体里的+或者边 也需要调整位置
      }
      if(!dragType) {
        return
      }

      console.log(position, blocks, event.clientX,event.clientY,dragType, sourceId, branchId,'---onAddNode')
			console.log(`event.clientX: ${event.clientX}, event.clientY: ${event.clientY}, dragType: ${dragType}, sourceId: ${sourceId}, targetId: ${targetId}, sourcePortId: ${sourcePortId}, branchId: ${branchId}`)

      if(blocks[sourceId]?.type === 'nami_agent_loop') {
        if(!loopConfigs[sourceId]?.all_block_keys?.length) {
          // 循环节点内第一个节点
          position.x = 50
          position.y = 50
        }
      }
      onAddNode({
        blocks, 
        type: dragType, 
        sourceId, 
        targetId,
        sourcePortId,
        position: {
          x: position.x,
          y: position.y
        }
      })
    },
    [dragType, activeBlockId]
  );

  const handleDragOver = (evt:any) => {
    evt.preventDefault()
  }
  const flowLoadingRef = useRef(flowLoading);
  const blockLoadingRef = useRef(blockLoading);

  useEffect(() => {
    flowLoadingRef.current = flowLoading;
    blockLoadingRef.current = blockLoading;
  }, [flowLoading, blockLoading]);

  const canDeleteLine = () => {
    return !(flowLoadingRef.current || blockLoadingRef.current || isVersion);
  }

  const canAddLine = (sourceNodeID:any, targetNodeID:any) => {
    if (sourceNodeID === targetNodeID) {
      return false;
    }
    const curBlocks = blocksRef.current;

    console.log(curBlocks,'canaddline')

    if(!curBlocks[sourceNodeID] || !curBlocks[sourceNodeID]) {
      // 起点或者终点不存在
      return
    }
    
    if(curBlocks[sourceNodeID]?.loop_id != curBlocks[targetNodeID]?.loop_id) {
      // 不能创建不同维度的边
      const allLines = ref.current.document.linesManager.getAllLines();
      // console.log(allLines,'--canAddLine--')
      allLines?.forEach((l:any) => {
        const item = l.toJSON();
        if(curBlocks[item.sourceNodeID]?.loop_id != curBlocks[item.targetNodeID]?.loop_id) {
          l.dispose();
        }
      })
      return false;
    }
    
    return true;
    
  }

  let editorProps = useEditorProps(flowType, onAddNode, isVersion, createEdge, deleteEdgeFunc, blocks, setBlocks, setOpenAgent, setActiveBlockId, setMousePosition, canDeleteLine, canAddLine );
  return (
    <>
      <FreeLayoutEditorProvider ref={ref} {...editorProps}>
				<Canvas
					onClick={async(e) => {
            // console.log(e,'click')
            const target:any = e.target;
            const sourceId = target.getAttribute('d-sourceid') || target.parentElement.getAttribute('d-sourceid')
            const targetId = target.getAttribute('d-targetid') || target.parentElement.getAttribute('d-targetid')
            const sourcePortId = target.getAttribute('d-sourceportid') || target.parentElement.getAttribute('d-sourceportid')
            if(sourceId && targetId) {
              deleteEdge = {
                sourceId,
                targetId,
                sourcePortId: sourcePortId?.replace('condition-if-','')
              }
            }
          }}
					handleDrop={handleDrop}
					handleDragOver={handleDragOver}
				>
					<EditorRenderer />
				</Canvas>

        <ToolBar
          isShowRight={isShowRight} 
          setActiveBlockId={setActiveBlockId}
          isVersion={isVersion}
          flowType={flowType} 
          onAddNode={onAddNode} 
          blocks={blocks}
          activeBlockId={activeBlockId}
          chainId={chainId}
          teamId={teamId}
          blockLoading={blockLoading}
          flowLoading={flowLoading}
          setOpenAgent={setOpenAgent}
          openAgent={openAgent}
          isUserFullScreen={isUserFullScreen}
          firstInitMATValue={firstInitMATValue}
        /> 
        <div style={{
          position: "absolute",
          top: mousePosition?.x || 0,
          left: mousePosition?.y || 0,
          zIndex: 1111,
          background: "#fff",
          borderRadius: 8,
          display: mousePosition?.x ? 'block' : 'none'
        }}>
         <AddBlockContent />
        </div>
      </FreeLayoutEditorProvider>
    </>
  );
};
