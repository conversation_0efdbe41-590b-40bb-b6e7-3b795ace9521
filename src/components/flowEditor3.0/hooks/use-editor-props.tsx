import { useMemo } from 'react';

import { createMinimapPlugin } from '@flowgram.ai/minimap-plugin';
import { createFreeSnapPlugin } from '@flowgram.ai/free-snap-plugin';
import { createFreeLinesPlugin } from '@q/flowgram.ai.free-lines-plugin';
import { createFreeNodePanelPlugin } from '@flowgram.ai/free-node-panel-plugin';

import {
  WorkflowNodePanelService,
  WorkflowNodePanelUtils
} from '@flowgram.ai/free-node-panel-plugin';

import { NodeIntoContainerService } from '@flowgram.ai/free-container-plugin';


import {
  WorkflowDragService,
} from '@flowgram.ai/free-layout-editor';

import {
  FreeLayoutProps,
  WorkflowNodeProps,
  WorkflowNodeRenderer,
  useNodeRender,
  useService,
  WorkflowLinesManager,
} from '@q/flowgram.ai.free-layout-editor';
import { nodeRegistries } from '../nodes/node-registries';

import { renderNode } from '../nodes/index'
import LineAddButton from '../nodes/Components/LineAddButton';
import AddBlockContent from '../AddBlockContent';
import { message } from 'antd';

export const useEditorProps = (flowType:any, onAddNode:any, isVersion:boolean, createEdge:any, deleteEdgeFunc:any, blocks:any, setBlocks:any, setOpenAgent: any, setActiveBlockId:any, setMousePosition:any, canDeleteLine:any, canAddLine:any) =>

  useMemo<FreeLayoutProps>(
    
    () => ({
      background: true,
      /**
       * Whether it is read-only or not, the node cannot be dragged in read-only mode
       */
      readonly: false,
      initialData: {
        nodes: [],
        edges: []
      },
      /**
       * Node registries
       * 节点注册
       */
      nodeRegistries,
      /**
       * Get the default node registry, which will be merged with the 'nodeRegistries'
       * 提供默认的节点注册，这个会和 nodeRegistries 做合并
       */
      getNodeDefaultRegistry(type) {
        return {
          type,
          meta: {
            defaultExpanded: true,
          },
          formMeta: {}, // 不能删除 否则const { form } = useNodeRender(); from取不到值
        };
      },
      materials: {
        /**
         * Render Node
         */
        renderDefaultNode: (props: WorkflowNodeProps) => {
          const { form } = useNodeRender();
          const linesManager = useService(WorkflowLinesManager);
          const data = form?.initialValues;
          return (
          <WorkflowNodeRenderer node={props.node}>
            {renderNode(data.type, data, form, linesManager, setOpenAgent)}
          </WorkflowNodeRenderer>
          )
        },
      },
      /**
       * Content change
       */
      onContentChange(ctx, event) {
        // console.log('Auto Save: ', event, ctx.document.toJSON());
      },
      // /**
      //  * Node engine enable, you can configure formMeta in the FlowNodeRegistry
      //  */
      nodeEngine: {
        enable: true,
      },
      /**
       * Redo/Undo enable
       */
      history: {
        enable: true,
        enableChangeNode: true, // Listen Node engine data change
      },
      /**
       * Playground init
       */
      onInit: (ctx) => {
        console.log('---- Playground Init ----');
        
        // 注册WorkflowNodePanelService
        if (!ctx.container.isBound(WorkflowNodePanelService)) {
          ctx.container.bind(WorkflowNodePanelService).toSelf();
        }
        // 注册NodeIntoContainerService
        if (!ctx.container.isBound(NodeIntoContainerService)) {
          ctx.container.bind(NodeIntoContainerService).toSelf();
        }
        
      },
      /**
       * Playground render
       */
      onAllLayersRendered(ctx) {
        //  Fitview
        ctx.document.fitView(false);
      },
      /**
       * Playground dispose
       */
      onDispose() {
        console.log('---- Playground Dispose ----');
      },
      isVerticalLine: (ctx, line) => {
        // 循环节点到循环体节点之间 返回竖直线
        return line.info.fromPort == "loop-bottom" && line.info.toPort == "loop-body-top";
      },
      lineColor: {
        hidden: 'transparent',
        default: '#8BC7FF',
        drawing: '#8BC7FF',
        hovered: '#8BC7FF',
        selected: '#64B5F6',
        error: 'red',
      },
      canAddLine(ctx, fromPort, toPort, lines, silent){ 
        const res = canAddLine(fromPort.node?.['_id'], toPort.node?.['_id']);
        console.log(fromPort.node?.['_id'], toPort.node?.['_id'],res,'---canAddLine---')
        return res;
      },  
      canDeleteLine(ctx, line, newLineInfo, silent){
        const res = canDeleteLine()
        if(!res) {
          message.info('运行中，请稍后再试。')
        }
        return res;
      },
      async onDragLineEnd(ctx, params) {
        const { fromPort, toPort, mousePos, line, originLine, event } = params;
        console.log('onDragLineEnd', event );
        const isClick = event.startPos.x == event.endPos.x && event.startPos.y == event.endPos.y
        //原有的边信息
        const ofrom = originLine?.info?.from || line?.info?.from || ''
        const oto = originLine?.info?.to || ''
        const ofromport = (originLine?.info?.fromPort+"").replace('condition-if-','') || ''
        // 现在的边信息
        const cfrom = fromPort.node['_id'];
        const cto = toPort?.node['_id']
        const cfromport = (fromPort.portID+'').replace('condition-if-','') || ''

        if(fromPort.node['_id'] && toPort?.node['_id']) {
          if(ofrom && oto){
            // 拖拽已有边到别的节点 需要删除已有边 再添加新边
            deleteEdgeFunc({
              sourceId: ofrom,
              targetId: oto,
              sourcePortId: ofromport
            }, {
              sourceNodeID: cfrom,
              targetNodeID: cto,
              sourcePortID: cfromport,
              targetPortID: ''
            })
          }else {
            createEdge(cfrom, cto, cfromport)
          }
        }
        // 拖拽已有边 松手 删除边
        if(fromPort.node['_id'] && !toPort?.node['_id']) {
          if(originLine) {
            if(!isClick){
              deleteEdgeFunc({
                sourceId: ofrom,
                targetId: oto,
                sourcePortId: ofromport
              })
            }
          }else {
            const nodePanelService = ctx.get(WorkflowNodePanelService);
            const document = ctx.document;
            const dragService = ctx.get(WorkflowDragService);
            const linesManager = ctx.get(WorkflowLinesManager);
            // 根据 mousePos 打开添加面板
            const containerNode = WorkflowNodePanelUtils.getContainerNode({
              fromPort,
            });

            // 转换节点位置
            const nodePosition = WorkflowNodePanelUtils.adjustNodePosition({
              nodeType: '',
              position: mousePos,
              fromPort,
              toPort,
              containerNode,
              document,
              dragService,
            });

            await nodePanelService.singleSelectNodePanel({
              position: mousePos,
              containerNode,
              panelProps: {
                sourceId: cfrom,
                sourcePortId: cfromport,
                targetId: '',
                addNode: onAddNode,
                blocks, 
                setBlocks,
                setOpenAgent,
                nodePosition
              },
            });
          }
        }
      },
      setLineClassName: (ctx,line) => {
        return 'linecolor'+line.info.from+'-linecolor'+line.info.to+' line-path';
      },
      plugins: () => [
        /**
         * Minimap plugin
         * 缩略图插件
         */
        createMinimapPlugin({
          disableLayer: true,
          canvasStyle: {
            canvasWidth: 182,
            canvasHeight: 102,
            canvasPadding: 50,
            canvasBackground: 'rgba(245, 245, 245, 1)',
            canvasBorderRadius: 10,
            viewportBackground: 'rgba(235, 235, 235, 1)',
            viewportBorderRadius: 4,
            viewportBorderColor: 'rgba(201, 201, 201, 1)',
            viewportBorderWidth: 1,
            viewportBorderDashLength: 2,
            nodeColor: 'rgba(255, 255, 255, 1)',
            nodeBorderRadius: 2,
            nodeBorderWidth: 0.145,
            nodeBorderColor: 'rgba(6, 7, 9, 0.10)',
            overlayColor: 'rgba(255, 255, 255, 0)',
          },
          inactiveDebounceTime: 1,
        }),
        /**
         * Snap plugin
         * 自动对齐及辅助线插件
         */
        createFreeSnapPlugin({
          edgeColor: '#00B2B2',
          alignColor: '#00B2B2',
          edgeLineWidth: 1,
          alignLineWidth: 1,
          alignCrossWidth: 8,
        }),
        /**
         * NodeAddPanel render plugin
         * 节点添加面板渲染插件
         */
        createFreeNodePanelPlugin({
          renderer: (props) => isVersion ? <></> : <AddBlockContent {...props} />,
        }),
        /**
         * Line render plugin
         * 连线渲染插件
         */
        createFreeLinesPlugin({
          renderInsideLine: (props) =>  isVersion ? <></> : <LineAddButton {...props} flowType={flowType} onAddNode={onAddNode} blocks={blocks} setBlocks={setBlocks} setOpenAgent={setOpenAgent} setActiveBlockId={setActiveBlockId} />,
        }),
      ],
    }),
    []
  );
