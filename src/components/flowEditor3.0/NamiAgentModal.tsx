import { useEffect, useState } from "react";
import { Button, Input, Modal, Spin } from "antd";
import { CloseOutlined, SearchOutlined } from "@ant-design/icons";
import styles from '../flow3.0/block/commons/styles/BlockMCP.module.scss';
import ListEmpty from "@/components/commonComponents/listEmpty/ListEmpty";
import { reqGetNmAgentList, reqGetNmAgentDetail } from "@/service/flow3.0";
import { useClientContext  } from '@q/flowgram.ai.free-layout-editor';


import { addAgentDataAtom, curLineInfoAtom, playgroundAtom } from '@/atoms/flowEditorAtoms' 
import { useRecoilCallback, useSetRecoilState, useRecoilValue } from "recoil";
import { default_model_params } from "@/utils/defaultLLMConfig";

import { getRandomMeta } from "@/components/flow3.0/flowFunc";

import { MarketTemplate } from '../flow3.0/block/commons/TemplatePlaceholder'

let isClick  = true;
export default function NamiAgentModal(props: any) {
    const { openAgent, setOpenAgent, addNode, blocks, isMoreAgentTeams } = props;
    const setAddAgentData = useSetRecoilState(addAgentDataAtom)
    const [addIndex, setAddIndex] = useState(0);

    // 画布对象
    const context = useClientContext();

    const [modalLoading, setModalLoading] = useState(false);
    const [curClickTabId, setCurClickTabId] = useState<any>('');
    const [curMCPList, setCurMCPList] = useState<any>([]);
    const [curHoverMCPId, setCurHoverMCPId] = useState<any>('');
    const [searchKey, setSearchKey] = useState(''); // 搜索框关键字
    const [list, setList] = useState<any>([]);
    const playgroundInfo = useRecoilValue(playgroundAtom);
    const [addY, setAddY] = useState(0);

    useEffect(() => {
        setAddY(0)
        setAddIndex(0)
    },[JSON.stringify(playgroundInfo)])

    const initMCPList = async () => {
        setModalLoading(true);
        const { list } = await reqGetNmAgentList({});
        setList([...list])
        setModalLoading(false);
        updataCurTabMCPList('', list);
    }

    const updataCurTabMCPList = (id: any, initList?: any) => {
        setCurMCPList([]);
        let temp = [];
        if (id == '') {
            (initList ? initList : list).forEach(item => {
                temp = temp.concat(item.list)
            });
        } else {
            const found = list.find(item => item.category === id);
            if (found?.list) {
                temp = found.list;
            }
        }

        setCurMCPList(temp);
    }

    const filterMCPList = (key: string) => {
        let temp = [];
        setCurMCPList(temp);
        setCurClickTabId('');
        list.forEach(item => {
            item.list.forEach(i => {
                if (i.title.indexOf(key) > -1) temp.push(i)
            })
        })
        setCurMCPList(temp);
    }

    const handleTabClick = (id: any) => {
        setSearchKey('');
        setCurClickTabId(id);
        updataCurTabMCPList(id);
    }

    const handleAddMCPItem = useRecoilCallback(({ snapshot }) => async (item:any) => { 
        const curLineInfo = await snapshot.getPromise(curLineInfoAtom);
        const res = await reqGetNmAgentDetail({
            id: item.id
        })


        const meta = getRandomMeta('nami_agent') as any;
        // 如果有真实图标URL，添加到meta中
        if (item.icon) {
            meta.iconUrl = item.icon;
        }

        const newBlock = {
            "id": Math.ceil(Math.random() * 1000000) + '',
            "name": res.title || 'Agent',
            "desc": '',
            "meta": meta,
            "type": 'nami_agent',  // 节点类型
            "loop_id": "",
            "agent_id": "",
            "inputs": {},  
            "prompts": {
                "system": "",
                "user": '',
                "user_params": MarketTemplate(res.intro)
            },
            "mcp_list": res.server_list || [],
            "knowledge_list": res.dataset_id ? [res.dataset_id] : [],
            "result": {},
            "status": 1,
            "model_params": {
                ...default_model_params,
                "role_id": res.role_id || default_model_params.role_id,
            }
        }
        setAddAgentData(newBlock)
        // 默认位置
        const addItemY = 146;
		let defaultPosition = context.playground.config.getPosFromMouseEvent({ clientX: 300, clientY: 300 + addY * playgroundInfo.zoom}, true)
        const isDragEdgeAdd = JSON.stringify(curLineInfo.position) != '{}'
        
        addNode({
            blocks,
            type: 'nami_agent_market',
            sourceId: curLineInfo.sourceId,
            targetId: curLineInfo.targetId,
            sourcePortId: curLineInfo.sourcePortId,
            isDragEdgeAdd,
            ...(!curLineInfo.sourceId ? { position: defaultPosition } : (isDragEdgeAdd ?  { position: curLineInfo.position } : {})),
        })
        // !isMoreAgentTeams && setOpenAgent(false)
        setAddIndex(preAddIndex => preAddIndex + 1)
        setAddY(preAddY =>  preAddY + addItemY);

    }, [isMoreAgentTeams, addIndex])
    
    
    const MCPItemOperateStatus = (item: any) => {

        const add = () => {
            return <Button
                type="text"
                className={styles.addMCPItemBtn}
                onClick={(e) => {
                    if(!isClick) {
                        return;
                    }
                    isClick = false;
                    e.stopPropagation();
                    handleAddMCPItem(item);
                    setTimeout(() => {
                        isClick = true
                    }, 1000);
                }}
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="#1D2531" />
                </svg>
                添加
            </Button >
        }

        return add();
    }

    const handleSearchMCP = (e: any) => {
        if (e.target.value?.trim()?.length == 0) {
            setSearchKey('')
            updataCurTabMCPList('');
            return;
        }
        setSearchKey(e.target.value);
        filterMCPList(e.target.value);
    };


    const MCPTotal = () => {
        let total = 0;
        if(list?.length > 0) {
            list.forEach(item => {
                total += item?.list?.length || 0;
            })
        }

        return total
    }

    useEffect(() => {
        initMCPList();
    }, [])

    return <>
        <Modal
            title={null}
            open={openAgent}
            footer={null}
            centered
            closable={false}
            wrapClassName={styles.addMCPModalWrap}
            classNames={{
                'content': styles.addMCPModalContent,
                'body': styles.addMCPModalBodyWrap,
            }}
            width={'70%'}
            style={{
                maxWidth: '1200px',
                minWidth: '680px',
            }}
        >
            <Spin spinning={modalLoading}>
                <div className={styles.addMCPModalBody} style={{height: '80vh', maxHeight: '800px'}}>
                    <div className={styles.tabWrap}>
                        <div className={styles.modalName}>专家智能体</div>
                        <div className={styles.tabList}>
                            <div className={[styles.tabListItem, curClickTabId === '' && styles.active].filter(Boolean).join(' ')} onClick={() => handleTabClick('')}>全部 （{MCPTotal()}）</div>
                            {
                                list?.map(tabitem => {
                                    return <div
                                        key={tabitem.category}
                                        className={[styles.tabListItem, curClickTabId == tabitem.category && styles.active].filter(Boolean).join(' ')}
                                        onClick={() => handleTabClick(tabitem.category)}
                                    >
                                        {tabitem.title} ({tabitem?.list?.length})
                                    </div>
                                })
                            }
                        </div>
                    </div>
                    <div className={styles.tabContainerWrap}>
                        <div className={styles.modalOperate}>
                            <div className={styles.modalOperateDesc}>添加专家智能体，组建你的多智能体团队</div>
                            <Input
                                style={{
                                    width: "240px",
                                }}
                                placeholder={isMoreAgentTeams ? '搜索专家智能体' : "搜索智能体名称"}
                                value={searchKey}
                                onChange={handleSearchMCP}
                                prefix={<SearchOutlined style={{ color: "#9BA7BA" }} />}
                            />
                            <CloseOutlined width={16} height={16} color="#626F84" onClick={() => setOpenAgent(false)} />
                        </div>
                        <div className={styles.mpcListWrap} onMouseLeave={() => { setCurHoverMCPId('') }}>
                            {
                                curMCPList.length > 0 ?
                                    <>
                                        {
                                            curMCPList.map(item => {
                                                return <div
                                                    style={{
                                                        borderRadius: '0px'
                                                    }}
                                                    key={item.server_id}
                                                    className={styles.mpcListItem}
                                                    onMouseEnter={(e) => { e.stopPropagation(); setCurHoverMCPId(item.server_id) }}
                                                >
                                                    <div className={styles.mcpItemInfoWrap}>
                                                        <img className={styles.mcpItemIcon} src={item.icon} alt="" />
                                                        <div className={styles.mcpItemInfo}>
                                                            <div className={styles.mcpItemName}>{item.title}</div>
                                                            <div className={styles.mcpItemDesc}>{item.intro}</div>
                                                        </div>
                                                    </div>
                                                    <>
                                                        {MCPItemOperateStatus(item)}
                                                    </>
                                                </div>
                                            })
                                        }
                                    </>
                                    :
                                    <ListEmpty desc="抱歉，没有找到相关Agent" />
                            }
                        </div>
                    </div>
                </div>
            </Spin>
        </Modal>
    </>
}