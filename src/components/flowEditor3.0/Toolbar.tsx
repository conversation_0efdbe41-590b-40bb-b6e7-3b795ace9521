
import { usePlayground, usePlaygroundTools } from '@q/flowgram.ai.free-layout-editor';
import { Layout, message, Tooltip, Popover,Input, Button} from "antd";
import React, {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useRef,
    useState,
    forwardRef,
    useImperativeHandle,
} from "react";

import flow2Styles from "@/styles/flow3.0/Flow.module.scss";

import search from "@/images/newFlow/icon_search.svg";
import plus from "@/images/newFlow/plus.svg";
import minus from "@/images/newFlow/minus.svg";
import auto from "@/images/newFlow/autoLayout.svg";
import fitview from "@/images/newFlow/fitview.svg";
import unlock from "@/images/newFlow/unlock.svg";
import lock from "@/images/newFlow/lock.svg";

import AddBlockContent from "./AddBlockContent";
import NamiAgentModal from "./NamiAgentModal";
import { useRouter } from 'next/router'
const minZoom = 0.1;
const maxZoom = 1.5;
export const ToolBar = (props: any) => { 
    const {setActiveBlockId, isVersion, flowType, onAddNode, blocks, activeBlockId, chainId, teamId,
        blockLoading, flowLoading, setOpenAgent, openAgent, isShowRight, isUserFullScreen, firstInitMATValue} = props
    const router = useRouter();
    const [searchBlockValue, setSearchBlockValue] = useState('')
    const tools = usePlaygroundTools();

    const playground = usePlayground();
    const [readonly, setReadonly] = useState(false)
    const [open, setOpen] = useState(false);
    const [isInBlockContent, setIsInBlockContent] = useState(false);
    const [interactive, setInteractive] = useState('MOUSE');
    const [isMoreAgentTeams, setIsMoreAgentTeams] = useState(false);

    useEffect(() => {
        if (router.pathname === '/flowDetailV4') {
            setIsMoreAgentTeams(true);
        }
    }, [router.pathname])

    // console.log(tools,playground,'---playgroundtools')

    const autoLayout = useCallback(async () => {
        console.log('autoLayout',tools.autoLayout)
        await tools.autoLayout({
            getFollowNode: (node, context) => {
                // followTo 是跟随节点

                /** 是否存在子节点 */
                // hasChildren: boolean;
                /** 被跟随节点 */
                // followedBy?: string[];
                /** 跟随节点 */
                // followTo?: string;
                return {
                    followTo: node.id || ''
                }
            }
        });
    }, [tools]);

    const toggleReadonly = useCallback(() => {
        playground.config.readonly = !playground.config.readonly;
        setReadonly(playground.config.readonly)
    }, [playground]);

    useEffect(() => {
        if(tools) {
            const MOUSE:any = 'MOUSE'
            tools.setInteractiveType(MOUSE) 
            // tools.setMouseScrollDelta(0.5)
        }
    },[])

    // 展示搜索节点的结果
    const showSearchBlockResult = (i: number, id: any) => {
        console.log(i, '=====>i', id);
        tools.fitView()
        // 搜索
        // const position = JSON.parse(
        //     localStorage.getItem(chainId + teamId) || "{}"
        // );
            
        // const playground = document.getElementsByClassName('gedit-playground-pipeline')[0]
        // const playgroundStyle = playground.getAttribute('style')
        // const styleObj:any = {}
        // // 转换格式
        // playgroundStyle?.split(';').map((item:string) => {
        //     const itemArr = item.split(':')
        //     if(itemArr[0] && itemArr[1]) {
        //         styleObj[itemArr[0]] = itemArr[1].replace('px', '')
        //     }
        // })
        // const left = styleObj.left + window.innerWidth / 2 - position[id].x
        // const top = styleObj.top + window.innerHeight / 2 - position[id].y

        // console.log(styleObj.left,styleObj.top, left, top)
        // document.getElementsByClassName('gedit-playground-pipeline')[0].setAttribute("style", "top:"+top+"px;left:"+left+"px;")

        setActiveBlockId(id);
    }
    // 搜索节点
    const handleSearchBlock = (e: any, value: string) => {
        if (e.key !== 'Enter') {
            return
        }

        // 优先匹配名称
        const blockKeys = Object.keys(blocks)
        for (let i = 0; i < blockKeys.length; i++) {
            const item = blocks[blockKeys[i]]
            if (item.name.includes(value)) {
                showSearchBlockResult(i, blockKeys[i])
                return
            }
        }

        // 匹配节点id
        for (let i = 0; i < blockKeys.length; i++) {
            const item = blockKeys[i]
            console.log(item, value,item.includes(value))
            if (item.includes(value)) {
                showSearchBlockResult(i, blockKeys[i])
                return
            }
        }
        message.info('未搜索到相关节点')
    }
    

    return <div 
        id="reactFlowToolBar"
        className={`${flow2Styles.rightOperationWrapper} ${isShowRight? flow2Styles.withRightMenu : ''} ${isUserFullScreen? flow2Styles.withFullRightMenu : ''}`}
    >
        {/* <Popover
        content={
            <div
            className={
                flow2Styles.rightOperationItem +
                " " +
                flow2Styles.searchBlockWrapper
            }
            >
            <img
                className={flow2Styles.flowBlockSearchIcon}
                src={search.src}
            />
            <Input
                className={flow2Styles.searchBlockInput}
                placeholder="名称 / id"
                value={searchBlockValue}
                onChange={(e) => setSearchBlockValue(e.target.value)}
                onKeyDown={(e) => handleSearchBlock(e,searchBlockValue)}
            />
            </div>
        }
        title=""
        trigger="hover"
        arrow={false}
        overlayClassName={flow2Styles.menubarPopover}
        >
        <Button
            type="link"
        >
            <img src={search.src} alt="" />
        </Button>
        </Popover> */}
        {/* <Popover
        content={ 
            <div style={{padding: 10}}>
            <span style={{padding: 10, margin:10, ...interactive == 'MOUSE' ? {background: '#2A76FF'} : {}}}  onClick={() => { 
                const MOUSE:any = 'MOUSE'
                tools.setInteractiveType(MOUSE)  
                setInteractive('MOUSE')
            }}>鼠标</span>

            |

            <span style={{padding: 10, margin:10, ...interactive == 'PAD' ? {background: '#2A76FF'} : {}}} onClick={() => { 
                const PAD:any = 'PAD'
                tools.setInteractiveType(PAD)  
                setInteractive('PAD')
            }}>PAD</span>
            </div>
        }
        ><span style={{color: '#1D2531',fontSize: '12px',lineHeight: '32px'}}>MOUSE/PAD</span></Popover> */}
        <Button
            onClick={() => tools.zoomin()}
            disabled={tools.zoom >= maxZoom}
            type="link"
            
        >
            <img src={plus.src} alt="" />
            <span>放大</span>    
        </Button>
        <span className={flow2Styles.gapLine}>|</span>
        <Button
            onClick={(e) => tools.zoomout()}
            type="link"
            disabled={tools.zoom <= minZoom}
        >
            <img src={minus.src} alt="" />
            <span>缩小</span>
        </Button>
        <span className={flow2Styles.gapLine}>|</span>
        <Button
            onClick={(e) => {tools.fitView();}}
            type="link"
						id="fitview-btn"
        >
            <img src={fitview.src} alt="" />
            <span>全览</span>
        </Button>
        <span className={flow2Styles.gapLine}>|</span>
        {/* <Tooltip placement="left" title={readonly ? "解锁" : "锁定"}>
        <Button
            onClick={toggleReadonly}
            type="link"
        >
            <img src={readonly ? lock.src : unlock.src} alt="" />
        </Button>
        </Tooltip> */}
        <Button
            onClick={autoLayout}
            type="link"
            id='auto-layout-btn'
        >
            <img src={auto.src} alt="" />
            <span>优化布局</span>
        </Button>
        {isVersion || blockLoading || flowLoading ? '' :<>
            
        <span className={flow2Styles.gapLine}>|</span>
        {isMoreAgentTeams ? <Button
                type="text"
                className={flow2Styles.addBlockBtn}
                onClick={() => {
                    setOpenAgent(true);
                }}
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="rgba(101, 112, 131, 1)"/>
                </svg>
                添加专家智能体
            </Button>: <div
            // id='addBlockWrapper'
            // style={{
            //     position: 'relative'
            // }}
            // onMouseEnter={() => {
            //     setOpen(true)
            // }}
            // onMouseLeave={(e) => {
            //     setIsInBlockContent(false)
            // }}
        ><Popover
            // open={isInBlockContent ? true : (open && !outOpenAuth)}
            // getPopupContainer={() => {
            //     return document.getElementById('addBlockWrapper')
            // }}
            content={
                <AddBlockContent
                    flowType={flowType}
                    addNode={onAddNode}
                    blocks={blocks}
                    activeBlockId={activeBlockId}
                    setOpen={setOpen}
					open={open}
                    setOpenAgent={setOpenAgent}
                    setIsInBlockContent={setIsInBlockContent}
                />
            }
            title=""
            trigger={'hover'}
            arrow={false}
            overlayClassName={flow2Styles.menubarPopover}
        >
            <Button
                type="text"
                className={flow2Styles.addBlockBtn}
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path fill-rule="evenodd" clipRule="evenodd" d="M8.51456 3.26996C8.48403 3.02338 8.27393 2.83237 8.01903 2.83203C7.74288 2.83167 7.51873 3.05523 7.51837 3.33138L7.51291 7.49944H3.33203L3.26931 7.50334C3.02277 7.53419 2.83203 7.74454 2.83203 7.99944C2.83203 8.27558 3.05589 8.49944 3.33203 8.49944H7.5116L7.50614 12.6647L7.50995 12.7274C7.54048 12.974 7.75058 13.165 8.00548 13.1654C8.28162 13.1657 8.50577 12.9422 8.50614 12.666L8.5116 8.49944H12.6654L12.7281 8.49555C12.9746 8.46469 13.1654 8.25434 13.1654 7.99944C13.1654 7.7233 12.9415 7.49944 12.6654 7.49944H8.51291L8.51837 3.33269L8.51456 3.26996Z" fill="rgba(101, 112, 131, 1)"/>
                </svg>
                添加智能体
            </Button>
        </Popover></div>}
        </> 
        }
        <NamiAgentModal
            openAgent={openAgent}
            setOpenAgent={setOpenAgent}
            addNode={onAddNode}
            blocks={blocks}
            isMoreAgentTeams={isMoreAgentTeams}
            firstInitMATValue={firstInitMATValue}
        />
    </div>
}