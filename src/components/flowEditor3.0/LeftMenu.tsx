/*
 * @Author: yh
 * @Date: 2025-06-06 15:59:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-06-12 10:35:20
 * @FilePath: \prompt-web\src\components\flowEditor\AddBlockContent.tsx
 */
import { Avatar, Button, Tooltip, Input, Tabs, Divider, Collapse, Empty } from 'antd';

import { PlusOutlined } from '@ant-design/icons';
import { useEffect, useState, useRef } from 'react';
import Styles from '@/styles/newFlow/leftMenu.module.scss';
import LLM from '@/images/flow3.0/llm.svg';
import Loop from '@/images/flow3.0/loop.svg';
import ArrowLeft from '@/images/flow3.0/arrowLeft.svg';
import Plus from '@/images/flow3.0/plus.svg';
import flodup from '@/images/flow3.0/flodup.svg';
import Condition from '@/images/flow3.0/condition.svg'
import { useClientContext  } from '@q/flowgram.ai.free-layout-editor';

import { curLineInfoAtom, isDisabledAtom } from '@/atoms/flowEditorAtoms' 
import { dragImgObj } from './flowConfig'; 


import { useRouter } from "next/router";

import {
    dragTypeAtom,
} from "@/atoms/flowEditorAtoms";

import { useSetRecoilState, useRecoilValue } from "recoil";


let isClickable = true
const imgDom = new Array();

export default (props: any) => {
    const { addNode, blocks, sourceId, targetId, sourcePortId, setOpenAgent, setIsShowLeft, isShowLeft, addIndex, setAddIndex } = props;

    const setDragType = useSetRecoilState(dragTypeAtom)
    const setCurLineInfo = useSetRecoilState(curLineInfoAtom)
    const isDisabled = useRecoilValue(isDisabledAtom)

    const [dragPosition, setDragPosition] = useState<any>('');
    const dragPositionRef = useRef<any>(null);

    // 画布对象
    const context = useClientContext();
    const router = useRouter();
    const chainId = router.query.id;


    const onDragStart = (event: any, nodeType: any) => {
        if (typeof setDragType === 'function') {
            setDragType(nodeType);
        }
        event.dataTransfer.effectAllowed = 'move';
    };

    useEffect(() => {
        dragPositionRef.current = dragPosition;
    }, [dragPosition]);

    useEffect(()=> {
        // init
        Object.keys(dragImgObj).forEach(t => {
            const dom = document.createElement("img");
            dom.src = dragImgObj[t];
            imgDom.push(dom)
        })
    },[])
      
    return (
        <>
        {isDisabled ? '' : (isShowLeft ? <div className={Styles.leftMenu}>
            <div className={Styles.leftTitle}>
                添加智能体
                <img src={ArrowLeft.src} alt="" className={Styles.arrowLeft} onClick={() => setIsShowLeft(false)} />
            </div>
            <div className={Styles.flowSideList} 
            >   
                {[{
                    icon: LLM.src,
                    title: '专家智能体',
                    desc: '通过大模型自主使用MCP工具、知识库，完成推理任务',
                    type: 'nami_agent'
                },
                {
                    icon: Condition.src,
                    title: '条件智能体',
                    desc: '通过设定条件连接判断下游分支，完成流程分支',
                    type: 'nami_agent_condition'
                },
                {
                    icon: Loop.src,
                    title: '迭代智能体',
                    desc: '通过设定迭代条件和逻辑，重复执行一系列任务',
                    type: 'nami_agent_loop'
                }].map((iItem:any, index) => {
                    return <div
                    className={Styles.flowSideItem}
                    onDragStart={(event) => {
                        onDragStart(event, iItem.type);
                        // event.currentTarget.style.borderRadius = '8px'
                        event.dataTransfer.setDragImage(imgDom[index], 10, 10);
                    }}
                    draggable={true}
                    onClick={() => {
                        if(!isClickable) return;
                        isClickable = false;
                        let position = context.playground.config.getPosFromMouseEvent({ clientX: 400 + addIndex * 30, clientY: 300 + addIndex * 30}, true)
                        
                        addNode({
                            blocks,
                            type: iItem.type,
                            sourceId,
                            targetId,
                            sourcePortId,
                            ...(!sourceId ? { position } : {}),
                        }) 
                        setAddIndex(preAddIndex => preAddIndex + 1)
                        setTimeout(() => {
                            isClickable = true; // 重置为可点击状态
                        }, 1000); 
                    }}
                >
                    <img src={iItem.icon} alt="" />
                    <div>
                        <div className={Styles.title}>{iItem.title}</div>
                    </div>
                    <img src={Plus.src} className={Styles.addBtn} />
                </div>
                })}
            </div>
            <div  className={Styles.btnWrapper} onClick={() => {
                setOpenAgent(true)
                setCurLineInfo({
                    sourceId,
                    targetId,
                    sourcePortId,
                    position: {}
                })
            }}><Button style={{width: '100%'}} type='primary' ghost icon={<PlusOutlined />}>从智能体广场</Button></div>
        </div> : <div className={Styles.flodupBtn} onClick={() => {setIsShowLeft(true)}}>
            <img src={flodup.src} alt="" />
        </div>)}
        </>
    );
};