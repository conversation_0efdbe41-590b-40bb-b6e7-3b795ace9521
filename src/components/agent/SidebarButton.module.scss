.buttonContainer {
  height: 40px;
  display: flex;
  align-items: center;
  color: #1b2532;
  font-size: 14px;
  font-weight: 400;
  padding-left: 12px;
  cursor: pointer;
  margin-top: 4px;

  .icon {
    margin-right: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #1b2532;

  }

  .buttonTitle, .buttonTitleSelected {
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
  }

  .buttonTitleSelected {
    color: #1d7cff;
  }
}

.buttonContainer:hover {
  border-radius: 8px;
  background: #E1E7ED;
}

.buttonContainerSelected {
  border-radius: 8px;
  background: #E1E7ED;
}
