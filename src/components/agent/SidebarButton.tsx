import styles from "./SidebarButton.module.scss";
import { Tooltip } from "antd";

export default function SidebarButton(props: {
  icon: React.ReactNode;
  selectedIcon: React.ReactNode;
  title: string;
  selected: boolean;
  onClick: () => void;
  showIcon?: boolean;
}) {
  const {
    icon,
    selectedIcon,
    title,
    selected,
    onClick,
    showIcon = false,
  } = props;

  return (
    <div
      className={`${styles.buttonContainer} ${
        selected ? styles.buttonContainerSelected : ""
      }`}
      onClick={onClick}
    >
      {showIcon && (
        <div className={styles.icon}>{selected ? selectedIcon : icon}</div>
      )}
      <Tooltip title={title}>
        <div
          className={selected ? styles.buttonTitleSelected : styles.buttonTitle}
        >
          {title}
        </div>
      </Tooltip>
    </div>
  );
}
