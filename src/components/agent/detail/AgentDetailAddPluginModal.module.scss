.agentDetailAddPluginModalContainer {
  :global div.ant-modal-content {
    padding: 0;
    border-radius: 5px;
  }

  .agentDetailAddPluginModalContent {
    display: flex;
    height: 720px;

    .agentDetailAddPluginModalSidebar {
      width: 200px;
      border-right: 2px solid #EBECF0;
      padding: 20px;
      background-color: #EDF1F5;
      border-radius: 8px 0 0 8px;

      .agentSidebarTitle {
        color: #1B2532;
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
        margin-bottom: 28px
      }

      .pluginDivider {
        width: 168px;
        height: 1px;
        background: #ebecf0;
        margin: 20px 0;
      }

      .pluginDiscover {
        color: #1B2532;
        font-size: 12px;
        font-weight: 600;
        line-height: 20px;
      }

      .agentDetailAddPluginModalLabel {
        overflow-y: auto;
        height: calc(100% - 220px);
      }
    }

    .agentDetailAddPluginModalInner {
      display: flex;
      flex-direction: column;
      width: 100%;
      position: relative;
      padding-top: 48px;
      height: 100%;

      .agentDetailAddPluginModalSearch {
        position: absolute;
        top: 12px;
        right: 12px;
      }

      .agentDetailAddPluginModalList {
        flex: 1;
        padding: 0 24px;
        overflow-y: scroll;
        width: 100%;

        .agentDetailAddPluginModalExpandItem {
          background-color: #F7F8FA;
        }
  
        .agentDetailAddPluginModalItem, .agentDetailAddPluginModalExpandItem {
          display: flex;
          width: 100%;
          height: 96px;
          border-bottom: 2px solid #f7f8fa;
          align-items: center;
          padding: 12px 16px;
          border-radius: 4px;
          cursor: pointer;
  
          .pluginIcon {
            width: 44px;
            height: 44px;
            border-radius: 5px;
          }
  
          .pluginContent {
            margin-left: 20px;
            flex: 1;
            width: 0;
            min-width: 0;
            overflow: hidden;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
  
            .pluginTitle {
              color: #262b33;
              font-size: 14px;
              font-weight: 600;
              line-height: 22px;
            }
  
            .pluginDesc {
              color: #626f84;
              font-size: 14px;
              font-weight: 400;
              line-height: 22px;
            }
  
            .pluginDate {
              color: #9ba7ba;
              font-size: 12px;
              font-weight: 400;
              line-height: 20px;
            }
          }

          .foldIcon {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }
  
        .agentDetailAddPluginModalItem:hover {
          background: #f7f8fa;
        }

        .pluginAdded {
          height: 32px;
          width: 78px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #4ec01e;
          cursor: pointer;
          margin-left: 4px;

          .pluginAddedText {
            margin-left: 4px;
          }
        }

        .pluginAdd {
          height: 32px;
          width: 64px;
          border-radius: 4px;
          background: #ebecf0;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          margin-left: 4px;

          .pluginAddText {
            margin-left: 4px;
          }
        }

        .pluginCancel {
          height: 32px;
          width: 92px;
          border-radius: 4px;
          background: #ebecf0;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .pluginCancelText {
            margin-left: 4px;
          }
        }

        .actionList {
          display: flex;
          flex-direction: column;

          .actionExpandItem {
            background-color: #F7F8FA;
          }

          .actionItem, .actionExpandItem {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 12px 12px 12px 80px;
            border-top: 1px solid #EEEFF2;

            .actionItemLeft {
              display: flex;
              flex-direction: column;

              .actionItemTitle {
                color: #1B2532;
                font-size: 14px;
                line-height: 22px;
                font-weight: 600;
                margin-bottom: 4px;
              }

              .actionItemDesc, .actionFlowItemDesc {
                color: #626F84;
                font-size: 12px;
                line-height: 20px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              .actionItemDesc {
                width: 448px;
              }

              .actionFlowItemDesc {
                width: 420px;
              }
            }
          }
        }
      }
    }
  }
}
