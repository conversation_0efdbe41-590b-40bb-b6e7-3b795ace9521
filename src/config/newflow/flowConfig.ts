// newflow icon
import memoryFlow from '@/images/newFlow/memory.svg'
import inputFlow from '@/images/newFlow/start.svg'
import outputFlow from '@/images/newFlow/end.svg'
import llmFlow from '@/images/flow3.0/llm.svg'
import codeFlow from '@/images/newFlow/code.svg'
import grabFlow from '@/images/newFlow/grab.svg'
import searchFlow from '@/images/newFlow/search.svg'
import apiFlow from '@/images/newFlow/api.svg'
import functioncallFlow from '@/images/newFlow/function-call.svg'
import conditionFlow from '@/images/newFlow/condition.svg';
import interaction from '@/images/newFlow/interaction.svg';
import GUI from '@/images/newFlow/GUI.svg';
import loop from '@/images/newFlow/loop.svg';
import loopEnd from '@/images/newFlow/loopEnd.svg';
import setVar from '@/images/newFlow/setVar.svg';
import flowDateTime from '@/images/newFlow/flowDateTime.svg'
import imgToText from '@/images/newFlow/imgToText.svg'
import flowCombinedText from '@/images/flowCombinedText.svg';
import flowSplitText from '@/images/flowSplitText.svg';
import flowReplaceText from '@/images/flowReplaceText.svg';
import randomValue from '@/images/newFlow/randomValue.svg';
import queryRewritingValue from '@/images/newFlow/queryRewriting.svg';
import intentionRecognitionValue from '@/images/newFlow/intentionRecognition.svg';
import MCP from '@/images/newFlow/mcp.svg';
import aiAgent from '@/images/newFlow/aiAgent.svg';
import mergeVar from '@/images/newFlow/mergeVar.svg';
import KNOWLEDGEADD from '@/images/flow/flow_konwledgeadd_icon.svg'
import KNOWLEDGERECALL from '@/images/flow/flow_konwledgerecall_icon.svg'
import KNOWLEDGEQA from '@/images/flow/flow_konwledgeqa_icon.svg'
import FORMINTERACTIVE from '@/images/flow/flow_forminteractive_icon.svg' // https://s1.ssl.qhimg.com/static/a5c171f4e1ef6d71.svg
import Condition from '@/images/flow3.0/condition.svg'


interface iTypeObj {
     [input:string]: string;
     output: string;
     llm: string;
     "web-search": string;
     http: string;
     code: string;
     "web-crawl" : string;
     "web-crawl-batch" : string;
     "web-search-batch": string;
     'function-call': string;
     "interaction": string;
     "flow_plugin": string;
    "random_value": string;
 }

export const typeImgObj:iTypeObj = {
    'input': inputFlow.src,
    'output': outputFlow.src,
    'llm': llmFlow.src,
    'llm-batch': llmFlow.src,
    'web-search': searchFlow.src,
    'http': apiFlow.src,
    'api': apiFlow.src,
    'version-api': apiFlow.src,
    'api-batch': apiFlow.src,
    'version-api-batch': apiFlow.src,
    'code': codeFlow.src,
    'web-crawl': grabFlow.src,
    'web-crawl-batch': grabFlow.src,
    'knowledge-base': memoryFlow.src,
    'knowledge': memoryFlow.src,
    'knowledge_add': KNOWLEDGEADD.src,
    'knowledge_recall': KNOWLEDGERECALL.src,
    'knowledge_qa': KNOWLEDGEQA.src,
    'form_interaction': FORMINTERACTIVE.src,
    "web-search-batch": searchFlow.src,
    "function-call": functioncallFlow.src,
    'version-function-call': functioncallFlow.src,
    'mcp': MCP.src,
    'version-mcp': MCP.src,
    'agent': aiAgent.src,
    'version-agent': aiAgent.src,
    "condition_start": conditionFlow.src,
    "condition_end": conditionFlow.src,
		"condition": conditionFlow.src,
    "interaction": GUI.src,
    "flow_plugin": interaction.src,
    'loop':loop.src,
    "loop_break": loopEnd.src,
    "set_variable": setVar.src,
    "image_to_text_llm": imgToText.src,
    "datetime": flowDateTime.src,
    "combined_text": flowCombinedText.src,
    "split_text": flowSplitText.src,
    "replace_text": flowReplaceText.src,
    "random_value": randomValue.src,
    'query_optimization': queryRewritingValue.src,
    'query_classification': intentionRecognitionValue.src,
    "variable_merge": mergeVar.src,
    "nami_agent": aiAgent.src,
    "nami_agent_condition": Condition.src,
    "nami_mcp": MCP.src,
    "nami_mcp_batch": MCP.src,
}

// 连接器节点, functionCall type
export const blockConnectorType = ['api', 'version-api', 'api-batch', 'version-api-batch'];
export const blockConnectorTypeApi = ['api', 'version-api'];
export const blockConnectorTypeApiBatch = ['api-batch', 'version-api-batch'];
export const blockFunctionCallType = ['function-call', 'version-function-call'];
export const blockApiVersion = ['version-api', 'version-api-batch', 'version-function-call', 'version-mcp', 'version-agent'];
export const blockConnectorTypeVersion = ['version-api', 'version-api-batch'];
export const blockImgToText = ['image_to_text_llm'];
export const blockMCPType = ['mcp', 'version-mcp'];
export const blockQueryRewritingType = ['query_optimization'];
export const blockIntentionRecognitionType = ['query_classification'];
export const blockAIAgentType = ['agent', 'version-agent'];
export const blockNamiType = ['nami_agent', 'nami_mcp', 'nami_mcp_batch'];
