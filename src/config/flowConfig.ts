import memoryFlow from '@/images/memoryFlow.svg'
import inputFlow from '@/images/inputFlow.png'
import outputFlow from '@/images/outputFlow.png'
import llmFlow from '@/images/llmFlow.png'
import codeFlow from '@/images/codeFlow.png'
import grabFlow from '@/images/grabFlow.png'
import searchFlow from '@/images/searchFlow.png'
import apiFlow from '@/images/apiFlow.png'
import functioncallFlow from '@/images/functioncallFlow.png';
import conditionFlow from '@/images/conditionFlow.svg';
import interaction from '@/images/interactionFlow.svg';
import gui from '@/images/guiFlow.svg';
import dragLlm from '@/images/dragLlm.png'
import dragCode from '@/images/dragCode.png'
import dragSearch from '@/images/dragSearch.png'
import dragGrab from '@/images/dragGrab.png'
import dragApi from '@/images/dragApi.png'
import dragMemory from '@/images/dragMemory.png'
import dragFc from '@/images/dragFc.png'
import dragCondition from '@/images/dragCondition.png'
import dragInteraction from '@/images/dragInteraction.png'
import dragGui from '@/images/dragGui.png'

import success from '@/images/success.png';
import error from '@/images/error.png';
import toTest from '@/images/toTest.png';
import update from '@/images/update.png';
import loading from '@/images/loading.png';
import stop from '@/images/stop.png';
import successApp from '@/images/app/successApp.svg';
import KNOWLEDGEADD from '@/images/flow/flow_konwledgeadd_icon.svg'
import KNOWLEDGERECALL from '@/images/flow/flow_konwledgerecall_icon.svg'
import KNOWLEDGEQA from '@/images/flow/flow_konwledgeqa_icon.svg'
import FORMINTERACTIVE from '@/images/flow/flow_forminteractive_icon.svg' // https://s1.ssl.qhimg.com/static/a5c171f4e1ef6d71.svg
import { default_model_params } from '@/utils/defaultLLMConfig'
import  { DefaultTemplate } from '../components/flow3.0/block/commons/TemplatePlaceholder'



const aiAgentBlock = {
    "id": Math.ceil(Math.random() * 1000000) + '',
    "name": '专家智能体',
    "desc": '',
    "meta": {
        color: '#4DD0E1',
        colorIndex: 5,
        iconIndex: 0
    },
    "type": 'nami_agent',  // 节点类型
    "loop_id": "",
    "agent_id": "",
    "inputs": {},  
    "prompts": {
        "system": "",
        "user": "",
        "user_params": DefaultTemplate()
    },
    "mcp_list": [],
    "knowledge_list": [],
    "result": {},
    "status": 1,
    "model_params": default_model_params
        
};
export const initInteractionBlockList = [
    aiAgentBlock
]

export const initBlockList = [
    aiAgentBlock
]


// 临时需求 演示id不能删除 不能部署 不能取消部署
export const disabledId: any = ['ac9uqnms2311095DzxMT', 'acqeDPnG231109xyvhJw', 'acqWJU7n231109x1f5fV', 'acs2saZz2311100LX19A', 'acxyLpBY231110tNs54b', 'acoVdsWs231110msvvNe']


export const defalutScript = "def func(inputs):\n    import json\n    output = {\n        \"test\": \"test123\",\n        **inputs\n    }\n    return dict(output=output)";
export const maxTokensObj: any = {
    "gpt-3.5-turbo": 2048,
    "gpt-3.5-turbo-16k": 4096,
    "gpt-4": 4096,
    "360gpt-pro": 2048,
    "360gpt-turbo-intent": 4096,
    "360gpt-turbo-32k": 4096,
    "360gpt-turbo": 2048,
    "moonshot-v1-8k": 4096,
    "moonshot-v1-32k": 4096,
    "moonshot-v1-128k": 4096,
    "360gpt-turbo-16k-aisearch-summary-240528": 2048,
    "360gpt-turbo-16k-aisearch-summary-240517": 2048
}

export const initPromptList = [
    {
        "template_id": "",
        "name": '自定义',
        "prompt": '',
        "llm": {
            "temperature": 0,
            "model_type": "360GPT_S2",
            "max_tokens": 2048,
            "frequency_penalty": 0
        }
    }
]


interface iTypeObj {
    [input: string]: string;
    output: string;
    llm: string;
    "web-search": string;
    http: string;
    code: string;
    "web-crawl": string;
    "web-crawl-batch": string;
    "web-search-batch": string;
    'function-call': string;
    "interaction": string;
    "flow_plugin": string;
}

export const typeNameObj: iTypeObj = {
    'input': '开始',
    'output': '结束',
    'llm': 'LLM',
    'llm-batch': 'LLM',
    'web-search': '网页搜索',
    'http': '连接器',
    'api': '连接器',
    'api-batch': '连接器',
    'version-api': '连接器',
    'version-api-batch': '连接器',
    'code': '执行脚本',
    'web-crawl': '网页抓取',
    'web-crawl-batch': '网页抓取',
    'knowledge-base': '知识库',
    'knowledge': '知识库',
    "web-search-batch": '网页搜索',
    'function-call': 'Functioncall',
    'version-function-call': 'Functioncall',
    "interaction": 'GUI卡片',
    "flow_plugin": 'PlayBook'
    // 'condition_start': '条件节点',
    // 'condition_end': '条件结束节点'
}

export const typeObj: iTypeObj = {
    'input': 'input',
    'output': 'output',
    'llm': 'llm',
    'llm-batch': 'llm',
    'web-search': 'search',
    'http': 'api',
    'api': 'api',
    'api-batch': 'api',
    'version-api': 'api',
    'version-api-batch': 'api',
    'code': 'code',
    'web-crawl': 'grab',
    'web-crawl-batch': 'grab',
    'knowledge-base': 'knowledge',
    'knowledge': 'knowledge',
    'knowledge_add': 'knowledge_add',
    'knowledge_recall': 'knowledge_recall',
    'knowledge_qa': 'knowledge_qa',
    'form_interaction': 'form_interaction',
    "web-search-batch": 'web-search',
    "function-call": 'function-call',
    "version-function-call": 'function-call',
    "condition_start": 'condition',
    "condition_end": 'condition',
    "interaction": 'interaction',
    "flow_plugin": 'gui'
}

export const typeImgObj: iTypeObj = {
    'input': inputFlow.src,
    'output': outputFlow.src,
    'llm': llmFlow.src,
    'llm-batch': llmFlow.src,
    'web-search': searchFlow.src,
    'http': apiFlow.src,
    'api': apiFlow.src,
    'api-batch': apiFlow.src,
    'version-api': apiFlow.src,
    'version-api-batch': apiFlow.src,
    'code': codeFlow.src,
    'web-crawl': grabFlow.src,
    'web-crawl-batch': grabFlow.src,
    'knowledge-base': memoryFlow.src,
    'knowledge': memoryFlow.src,
    'knowledge_add': KNOWLEDGEADD.src,
    'knowledge_recall': KNOWLEDGERECALL.src,
    'knowledge_qa': KNOWLEDGEQA.src,
    'form_interaction': FORMINTERACTIVE.src,
    "web-search-batch": searchFlow.src,
    "function-call": functioncallFlow.src,
    "version-function-call": functioncallFlow.src,
    "condition_start": conditionFlow.src,
    "condition_end": conditionFlow.src,
    "condition": conditionFlow.src,
    "interaction": interaction.src,
    "flow_plugin": gui.src
}

export const dragImgObj: iTypeObj = {
    'input': '',
    'output': '',
    'llm': dragLlm.src,
    'llm-batch': dragLlm.src,
    'web-search': dragSearch.src,
    'http': dragApi.src,
    'api': dragApi.src,
    'api-batch': dragApi.src,
    'version-api': dragApi.src,
    'version-api-batch': dragApi.src,
    'code': dragCode.src,
    'web-crawl': dragGrab.src,
    'web-crawl-batch': dragGrab.src,
    'knowledge-base': dragMemory.src,
    'knowledge': dragMemory.src,
    "web-search-batch": dragSearch.src,
    "function-call": dragFc.src,
    "version-function-call": dragFc.src,
    "condition_start": dragCondition.src,
    "interaction": dragInteraction.src,
    "flow_plugin": dragGui.src
}

interface iStatusObj {
    [n: string]: string;
    "1": string;
    "2": string;
    "3": string;
    "4": string;
    "5": string;
    "6": string;
}
export const statusImgObj: iStatusObj = {
    "1": toTest.src, // 未调试
    "2": loading.src,  // 调试中
    "3": success.src, // 调试成功
    "4": error.src, // 调试失败
    "5": update.src, // 已编辑待调试
    "6": stop.src, // 已取消
    "8": toTest.src, // 分支上的未调试
}
export const appStatusImgObj: iStatusObj = {
    "1": toTest.src, // 未调试
    "2": loading.src,  // 调试中
    "3": 'https://s5.ssl.qhres2.com/static/3257d83d33b98842.svg', // 调试成功
    "4": error.src, // 调试失败
    "5": update.src, // 已编辑待调试
    "6": stop.src, // 已取消
    "8": toTest.src, // 分支上的未调试
}

export const type = ['llm', 'code', 'http', 'version-api', 'web-crawl', 'web-search', 'knowledge-base', 'knowledge', 'version-function-call', 'condition_start', 'interaction', 'flow_plugin'];

export const cardUrl = {
    'flowid': [
        {
            label: '页面1 (https://www.so.com/?src=so.com)',
            value: 'https://www.so.com/?src=so.com'
        },
        {
            label: '页面2 (https://www.so.com/s?q=%E4%B8%AD%E7%A7%8B%E5%B0%86%E6%9C%89%E8%B6%85%E7%BA%A7%E6%9C%88%E4%BA%AE&src=know_side_nlp_sohot&tn=news&sp=af4&cp=094f40009a&fr=know_side_nlp_sohot)',
            value: 'https://www.so.com/s?q=%E4%B8%AD%E7%A7%8B%E5%B0%86%E6%9C%89%E8%B6%85%E7%BA%A7%E6%9C%88%E4%BA%AE&src=know_side_nlp_sohot&tn=news&sp=af4&cp=094f40009a&fr=know_side_nlp_sohot'
        },
        {
            label: '页面3 (https://news.china.com/socialgd/10000169/20240914/47220325.html)',
            value: 'https://news.china.com/socialgd/10000169/20240914/47220325.html'
        },
        {
            label: '页面4 (https://news.china.com/socialgd/10000169/20240914/47216911.html)',
            value: 'https://news.china.com/socialgd/10000169/20240914/47216911.html'
        },
        {
            label: '页面5 (https://www.hunantoday.cn/news/xhn/202409/20670982.html)',
            value: 'https://www.hunantoday.cn/news/xhn/202409/20670982.html'
        },
        {
            label: '页面6 (https://www.360kuai.com/pc/9fda8b9cdba1bb43a?kuai_so=1&sign=360_6aa05217&cota=3&refer_scene=so_52)',
            value: 'https://www.360kuai.com/pc/9fda8b9cdba1bb43a?kuai_so=1&sign=360_6aa05217&cota=3&refer_scene=so_52'
        },
        {
            label: '页面7 (https://www.360kuai.com/pc/9f88002014f678a38?kuai_so=1&sign=360_6aa05217&cota=3&refer_scene=so_52)',
            value: 'https://www.360kuai.com/pc/9f88002014f678a38?kuai_so=1&sign=360_6aa05217&cota=3&refer_scene=so_52'
        }

    ]
}

export const cardData: any = {
    // "https://card.yue.360.com/upload?mod=video": "{\n    \"input\": \"\",\n    \"time\": \"\",\n    \"title\": \"\",\n    \"coverPicture\": \"\",\n    \"list\": [{\n        \"url\": \"\",\n        \"time\": \"\",\n        \"title\": \"\",\n        \"coverPicture\": \"\"\n    }]\n}",
    "https://card.yue.360.com/upload?mod=video": {
        "input": "",
        "time": "",
        "title": "",
        "coverPicture": "",
        "list": [{
            "url": "",
            "time": "",
            "title": "",
            "coverPicture": ""
        }]
    },
    // "https://card.yue.360.com/view?mod=video&feature=fulltext": "{\n        \"time\": \"\",\n        \"title\": \"\",\n        \"coverPicture\": \"\",\n        \"input\": \"\",\n        \"isTranslation\":  false,\n        \"isTone\": false,\n        \"content\": [{\n            \"from\": \"\",\n            \"to\": \"\",\n            \"title\": \"\",\n            \"paragraph\": [{\n                \"original\": \"\",\n                \"translation\": \"\",\n                \"tone\": \"\",\n                \"content\": \"\"\n            }]\n        }]\n    }",
    "https://card.yue.360.com/view?mod=video&feature=fulltext": {
        "time": "",
        "title": "",
        "coverPicture": "",
        "input": "",
        "isTranslation": false,
        "isTone": false,
        "content": [{
            "from": "",
            "to": "",
            "title": "",
            "paragraph": [{
                "original": "",
                "translation": "",
                "tone": "",
                "content": ""
            }]
        }]
    },

    // "https://card.yue.360.com/view?mod=video&feature=editor": "{\n    \"type\": \"\",\n    \"time\": \"\",\n    \"title\": \"\", \n    \"coverPicture\": \"\",\n    \"input\": \"\",\n    \"content\": \"\"\n}",

    'https://card.yue.360.com/view?mod=video&feature=editor': {
        "type": "",
        "time": "",
        "title": "",
        "coverPicture": "",
        "input": "",
        "content": ""
    },

    // "https://card.yue.360.com/view?mod=video&feature=title":  "{\n    \"selectedTitle\": \"\",\n    \"list\": [{\n        \"title\": \"\",\n        \"xiaohongshu\": \"\",\n        \"shipinhao\": \"\",\n        \"douyin\": \"\"\n    }]\n}",

    "https://card.yue.360.com/view?mod=video&feature=title": {
        "selectedTitle": "",
        "list": [{
            "title": "",
            "xiaohongshu": "",
            "shipinhao": "",
            "douyin": ""
        }]
    },

    // "https://card.yue.360.com/download?mod=video": "{\n        \"title\": \"\",\n        \"content\": \"\"\n    }"

    "https://card.yue.360.com/download?mod=video": {
        "title": "",
        "content": ""
    }
}
export const cardListData = [
    {
        card_id: 1,
        name: "视频文稿",
        desc: "根据视频地址生成口播稿件",
        imgUrl: 'https://p4.ssl.qhimg.com/d/inn/e190e24f32d1/gui-1.png',
        url: "https://card.yue.360.com/upload?mod=video",
        card_data: cardData['https://card.yue.360.com/upload?mod=video']
    },
    {
        card_id: 2,
        name: "AI生成全文",
        desc: "根据视频地址生成口播全文",
        imgUrl: 'https://p3.ssl.qhimg.com/d/inn/e190e24f32d1/gui-3.png',
        url: 'https://card.yue.360.com/view?mod=video&feature=fulltext',
        card_data: cardData['https://card.yue.360.com/view?mod=video&feature=fulltext']
    },
    {
        card_id: 3,
        name: "AI生成口播稿",
        desc: "根据视频地址生成口播稿",
        imgUrl: 'https://p5.ssl.qhimg.com/d/inn/e190e24f32d1/gui-5.png',
        url: 'https://card.yue.360.com/view?mod=video&feature=editor',
        card_data: cardData['https://card.yue.360.com/view?mod=video&feature=editor']
    },
    {
        card_id: 4,
        name: "AI生成标题",
        desc: "根据视频地址生成口播段落标题",
        imgUrl: 'https://p0.ssl.qhimg.com/d/inn/e190e24f32d1/gui-4.png',
        url: 'https://card.yue.360.com/view?mod=video&feature=title',
        card_data: cardData['https://card.yue.360.com/view?mod=video&feature=title']
    },
    {
        card_id: 5,
        name: "多格式导出",
        desc: "口播稿件多格式导出",
        imgUrl: 'https://p5.ssl.qhimg.com/d/inn/e190e24f32d1/gui-2.png',
        url: 'https://card.yue.360.com/download?mod=video',
        card_data: cardData['https://card.yue.360.com/download?mod=video']
    },
];

// 连接器节点, functionCall type
export const blockConnectorType = ['api', 'version-api', 'api-batch', 'version-api-batch'];
export const blockConnectorTypeApi = ['api', 'version-api'];
export const blockConnectorTypeApiBatch = ['api-batch', 'version-api-batch'];
export const blockFunctionCallType = ['function-call', 'version-function-call'];
export const blockApiVersion = ['version-api', 'version-api-batch', 'version-function-call'];
export const blockConnectorTypeVersion = ['version-api', 'version-api-batch'];

