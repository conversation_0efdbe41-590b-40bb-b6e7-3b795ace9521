export const defaultAiModuleConfig = [
    {
        name: '温度',
        component: 'slider',
        key: 'temperature',
        value: 0,
        step: 0.1,
        min: 0,
        max: 2,
        sliderMarks: {
            0: '0',
            2: '2'
        }
    },
    {
        name: '最大输出限制',
        component: 'input',
        key: 'max_tokens',
        value: 2048,
        step: 1,
        min: 1,
        max: 2048
    },
    // {
    //     name: '顶部p选择',
    //     component: 'slider',
    //     key: 'top_p',
    //     value: 1,
    //     step: 0.1,
    //     min: 0,
    //     max: 1,
    //     sliderMarks: {
    //         0: '0',
    //         1: '1'
    //     }
    // },
    {
        name: '频率惩罚',
        component: 'slider',
        key: 'frequency_penalty',
        value: 1.0,
        step: 0.1,
        min: 1.0,
        max: 2.0,
        sliderMarks: {
            "1.0": '1.0',
            "2.0": '2.0',
        }
    },
    // {
    //     name: '出现惩罚',
    //     component: 'slider',
    //     key: 'presence_penalty',
    //     value: 0,
    //     step: 0.1,
    //     min: -2.0,
    //     max: 2.0,
    //     sliderMarks: {
    //         "-2.0": '-2.0',
    //         "2.0": '2.0',
    //     }
    // }
]

export const defaultAiModuleConfigNew = [
    {
        name: 'top_k',
        component: 'slider',
        key: 'top_k',
        value: 0,
        step: 1,
        min: 0,
        max: 1024,
        // sliderMarks: {
        //     0: '0',
        //     1024: '1024'
        // }
    },
    {
        name: 'top_p',
        component: 'slider',
        key: 'top_p',
        value: 1,
        step: 0.1,
        min: 0,
        max: 1,
        // sliderMarks: {
        //     0: '0',
        //     1: '1'
        // }
    },
    {
        name: 'temperature',
        component: 'slider',
        key: 'temperature',
        value: 1,
        step: 0.1,
        min: 0,
        max: 1,
        // sliderMarks: {
        //     0: '0',
        //     1: '1'
        // }
    },
    {
        name: 'num_beams',
        component: 'slider',
        key: 'num_beams',
        value: 1,
        step: 1,
        min: 1,
        max: 5,
        // sliderMarks: {
        //     1: '1',
        //     5: '5'
        // }
    },
    {
        name: 'repetition_penalty',
        component: 'slider',
        key: 'repetition_penalty',
        value: 1.2,
        step: 0.01,
        min: 1.0,
        max: 2.0,
        // sliderMarks: {
        //     "1.0": '1.0',
        //     "2.0": '2.0',
        // }
    },
    {
        name: 'max_tokens',
        component: 'input',
        key: 'max_tokens',
        value: 2048,
        step: 1,
        min: 1,
        max: 2048
    }
]