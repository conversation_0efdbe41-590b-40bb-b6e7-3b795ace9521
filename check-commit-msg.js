const emoji = {
  fail: "❌",
  pass: "✅",
  warn: "⚠️",
};
const fs = require("fs");

// 获取提交信息文件的路径
const commitMsgFile = process.argv[2];
if (!commitMsgFile) {
  console.error(emoji.fail + " 提交信息不能为空!");
  process.exit(1);
}

// 读取提交信息
const commitMsg = fs.readFileSync(commitMsgFile, "utf8").trim();
const len = commitMsg.length;
console.log(`\n${emoji.warn}   提交信息的格式为：类型 备注 id(无id,写0)\n`);

console.log("提交信息内容为：" + commitMsg);
console.log("长度为：" + len + "个字符\n");

// merge时，直接跳过
if (commitMsg.startsWith("Merge branch")) {
  console.log(emoji.warn + "   提交信息为merge, 跳过检查!");
  process.exit(0);
}

// 在这里添加你的检查逻辑，例如简单的检查提交信息长度
if (len <= 2 || len > 50) {
  console.error(emoji.fail + "  提交信息的字符长度不能超过50个字符,且不能为空!");
  process.exit(1);
}

if (!(commitMsg.includes("feat") || commitMsg.includes("bug"))) {
  console.error(emoji.fail + "  提交信息必须包含feat或bug, 来标识功能或bug!");
  process.exit(1);
}

if (commitMsg.includes(" ") && commitMsg.split(" ").length < 3) {
  console.error(
    emoji.fail + "  提交信息至少包含两个空格,用于区分,类型-备注-需求id!"
  );
    process.exit(1);
} else {
  let result = commitMsg.split(" ");
//   console.log(result)
  if (!['feat', 'feat:', 'bug', 'bug:'].includes(result[0])) {
    console.error(emoji.fail + "  提交信息-类型, 必须是feat 或 bug !");
    process.exit(1);
  } else {
    console.log(emoji.pass + "  提交信息-类型, 通过检查!");
  }

  if (!/^\d+$/.test(result[result.length - 1])) {
    console.error(emoji.fail + "  提交信息最后一项是id, 是纯数字，无id时, 填写0 !");
    process.exit(1);
  } else {
    console.log(emoji.pass + "  提交信息-id, 通过检查!");
  }
  if (result.slice(1, result.length - 1).join(" ").length < 6) {
    console.error(emoji.fail + "  提交信息-备注, 长度至少大于等于6个字符");
    process.exit(1);
  } else {
    console.log(emoji.pass + "  提交信息-备注, 通过检查!");
  }
}

console.log('\n' + emoji.pass + "  提交信息通过检查!");
// 如果检查通过，则退出
process.exit(0);

/*
提交规范:

git commit -m "类型 备注 需求id"
注意: 三者中间用空格隔开,且顺序不可变, 否则会报错
      备注长度不能少于6个字符

feat 表示 需求类型
示例1: git commit -m "feat: 新增功能 111111" // feat后的冒号非必填
示例2: git commit -m "feat: 新增功能 111111"
示例3: git commit -m "feat: 新增功能 0" // 无需求 id,写0

bug 表示 bug类型
示例1: git commit -m "bug: 修复bug 111111" // bug后的冒号非必填
示例2: git commit -m "bug: 修复bug 111111"
示例3: git commit -m "bug: 修复bug 0" // 无bug id,写0

单次不走commit-msg的校验: git commit --no-verify -m "xxx"

*/
